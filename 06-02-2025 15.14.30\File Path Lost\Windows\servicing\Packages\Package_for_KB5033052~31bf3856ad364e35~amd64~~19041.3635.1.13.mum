﻿<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for KB5033052" displayName="default" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="https://support.microsoft.com/help/5033052" creationTimeStamp="2023-12-07T22:25:47Z" lastUpdateTimeStamp="2023-12-07T22:25:47Z">
  <assemblyIdentity name="Package_for_KB5033052" version="19041.3635.1.13" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5033052" applicabilityEvaluation="deep" releaseType="Update" restart="possible">
    <mum2:customInformation Dependency="Language Pack" Version="10.0.19041.3635" xmlns:mum2="urn:schemas-microsoft-com:asm.v3" PackageFormat="PSFX" PackageSupportedFeatures="0x1" />
    <parent buildCompare="EQ" integrate="separate" disposition="detect">
      <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-UtilityVMEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-WinPE-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="Wrapper-96873A70822AC4D7529ED94D954A738A76F0B431A77861DE0156AEF2CB2A79C9_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-96873A70822AC4D7529ED94D954A738A76F0B431A77861DE0156AEF2CB2A79C9" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-A41C4F58099D8DD98F4524115D69352D67A5A029D688B216ADEDA5044F066944_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-A41C4F58099D8DD98F4524115D69352D67A5A029D688B216ADEDA5044F066944" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-16399E6991C5279E326B65974719E5B1C0D5D94BC665603658EC2B9CCC73A8F6_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-16399E6991C5279E326B65974719E5B1C0D5D94BC665603658EC2B9CCC73A8F6" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-88B32D48E4E776482586054D93A14122977F8DE508900ED815457D26DDC8C077_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-88B32D48E4E776482586054D93A14122977F8DE508900ED815457D26DDC8C077" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-0AEA366FE7226E7BBBD4DCEDA5541F2D9E55F867AADB1EED590CF70A7D924B1B_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-0AEA366FE7226E7BBBD4DCEDA5541F2D9E55F867AADB1EED590CF70A7D924B1B" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-0D16CCB544446D29E78606818CC4E51A4BC8CD01DCFC8BA0FDDC5774EF9CDE5B_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-0D16CCB544446D29E78606818CC4E51A4BC8CD01DCFC8BA0FDDC5774EF9CDE5B" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-26A278384A32FC5A512A72A7708D75362506296D30E6EDEDCA2CA79486C7F52D_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-26A278384A32FC5A512A72A7708D75362506296D30E6EDEDCA2CA79486C7F52D" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-62F4EBE6CDB5B3C3C38FE08B26206AF61EF5B043DE5C62C8ACDA6CFCC5505414_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-62F4EBE6CDB5B3C3C38FE08B26206AF61EF5B043DE5C62C8ACDA6CFCC5505414" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-5D43CC57E231A2BE1022D6ED28B9AFAD916C08B54FB610C7E1C685F3419124D1_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-5D43CC57E231A2BE1022D6ED28B9AFAD916C08B54FB610C7E1C685F3419124D1" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-69B0082C1A9ED38786C77EC87A19DB8F7066DE3A9F6CD0A2ABD03F2C4FA4EBE6_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-69B0082C1A9ED38786C77EC87A19DB8F7066DE3A9F6CD0A2ABD03F2C4FA4EBE6" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-C375DB81F2478DA87C63ADF2628DF46A20DC4B1CF0FF5938619ABB1B732D85A7_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-C375DB81F2478DA87C63ADF2628DF46A20DC4B1CF0FF5938619ABB1B732D85A7" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-F7592FFADEDC43E995CC56545C98317CE170D250A1AF469D7CFE4720B652D227_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-F7592FFADEDC43E995CC56545C98317CE170D250A1AF469D7CFE4720B652D227" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-5F32600BDD769B92FD454A0FC75EC23BD79BFD90EF87089B6E606ADB3321D219_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-5F32600BDD769B92FD454A0FC75EC23BD79BFD90EF87089B6E606ADB3321D219" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-3AD16D0950FF287FF34226117937CD44C8A2198FB9C7ED8FD347C3FC6DFCEB40_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-3AD16D0950FF287FF34226117937CD44C8A2198FB9C7ED8FD347C3FC6DFCEB40" version="10.0.19041.3635" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <mum2:packageExtended xmlns:mum2="urn:schemas-microsoft-com:asm.v3" completelyOfflineCapable="undetermined" packageSize="25065410" />
  </package>
</assembly>