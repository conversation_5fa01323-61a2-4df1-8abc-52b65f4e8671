<?xml version="1.0" encoding="us-ascii"?>
<!--
    Please refer to machine.config.comments for a description and
    the default values of each configuration section.

    For a full documentation of the schema please refer to
    http://go.microsoft.com/fwlink/?LinkId=42127

    To improve performance, machine.config should contain only those
    settings that differ from their defaults.
-->
<configuration>
    <configSections>
        <section name="appSettings" type="System.Configuration.AppSettingsSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" restartOnExternalChanges="false" requirePermission="false" />
        <section name="connectionStrings" type="System.Configuration.ConnectionStringsSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" requirePermission="false" />
        <section name="mscorlib" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
        <section name="runtime" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
        <section name="assemblyBinding" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
        <section name="satelliteassemblies" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
        <section name="startup" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
        <section name="system.codedom" type="System.CodeDom.Compiler.CodeDomConfigurationHandler, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="system.data" type="System.Data.Common.DbProviderFactoriesConfigurationHandler, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="system.data.dataset" type="System.Configuration.NameValueFileSectionHandler, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" restartOnExternalChanges="false" />
        <section name="system.data.odbc" type="System.Data.Common.DbProviderConfigurationHandler, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="system.data.oledb" type="System.Data.Common.DbProviderConfigurationHandler, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="system.data.oracleclient" type="System.Data.Common.DbProviderConfigurationHandler, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="system.data.sqlclient" type="System.Data.Common.DbProviderConfigurationHandler, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="system.diagnostics" type="System.Diagnostics.SystemDiagnosticsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="system.runtime.remoting" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
        <section name="system.windows.forms" type="System.Windows.Forms.WindowsFormsSection, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <section name="windows" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowLocation="false" />
        <section name="uri" type="System.Configuration.UriSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <sectionGroup name="system.runtime.caching" type="System.Runtime.Caching.Configuration.CachingSectionGroup, System.Runtime.Caching, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
            <section name="memoryCache" type="System.Runtime.Caching.Configuration.MemoryCacheSection, System.Runtime.Caching, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
        </sectionGroup>
        <sectionGroup name="system.xml.serialization" type="System.Xml.Serialization.Configuration.SerializationSectionGroup, System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="schemaImporterExtensions" type="System.Xml.Serialization.Configuration.SchemaImporterExtensionsSection, System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="dateTimeSerialization" type="System.Xml.Serialization.Configuration.DateTimeSerializationSection, System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="xmlSerializer" type="System.Xml.Serialization.Configuration.XmlSerializerSection, System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
        <sectionGroup name="system.net" type="System.Net.Configuration.NetSectionGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="authenticationModules" type="System.Net.Configuration.AuthenticationModulesSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="connectionManagement" type="System.Net.Configuration.ConnectionManagementSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="defaultProxy" type="System.Net.Configuration.DefaultProxySection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <sectionGroup name="mailSettings" type="System.Net.Configuration.MailSettingsSectionGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                <section name="smtp" type="System.Net.Configuration.SmtpSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            </sectionGroup>
            <section name="requestCaching" type="System.Net.Configuration.RequestCachingSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="settings" type="System.Net.Configuration.SettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="webRequestModules" type="System.Net.Configuration.WebRequestModulesSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        </sectionGroup>
        <sectionGroup name="system.runtime.serialization" type="System.Runtime.Serialization.Configuration.SerializationSectionGroup, System.Runtime.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="dataContractSerializer" type="System.Runtime.Serialization.Configuration.DataContractSerializerSection, System.Runtime.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        </sectionGroup>
        <sectionGroup name="system.serviceModel" type="System.ServiceModel.Configuration.ServiceModelSectionGroup, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="behaviors" type="System.ServiceModel.Configuration.BehaviorsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="bindings" type="System.ServiceModel.Configuration.BindingsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="client" type="System.ServiceModel.Configuration.ClientSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="comContracts" type="System.ServiceModel.Configuration.ComContractsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="commonBehaviors" type="System.ServiceModel.Configuration.CommonBehaviorsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowDefinition="MachineOnly" allowExeDefinition="MachineOnly" />
            <section name="diagnostics" type="System.ServiceModel.Configuration.DiagnosticSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="extensions" type="System.ServiceModel.Configuration.ExtensionsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="machineSettings" type="System.ServiceModel.Configuration.MachineSettingsSection, SMDiagnostics, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowDefinition="MachineOnly" allowExeDefinition="MachineOnly" />
            <section name="protocolMapping" type="System.ServiceModel.Configuration.ProtocolMappingSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="serviceHostingEnvironment" type="System.ServiceModel.Configuration.ServiceHostingEnvironmentSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowDefinition="MachineToApplication" />
            <section name="services" type="System.ServiceModel.Configuration.ServicesSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="standardEndpoints" type="System.ServiceModel.Configuration.StandardEndpointsSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="routing" type="System.ServiceModel.Routing.Configuration.RoutingSection, System.ServiceModel.Routing, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
            <section name="tracking" type="System.ServiceModel.Activities.Tracking.Configuration.TrackingSection, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        </sectionGroup>
        <sectionGroup name="system.serviceModel.activation" type="System.ServiceModel.Activation.Configuration.ServiceModelActivationSectionGroup, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="diagnostics" type="System.ServiceModel.Activation.Configuration.DiagnosticSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="net.pipe" type="System.ServiceModel.Activation.Configuration.NetPipeSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <section name="net.tcp" type="System.ServiceModel.Activation.Configuration.NetTcpSection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        </sectionGroup>
        <sectionGroup name="system.transactions" type="System.Transactions.Configuration.TransactionsSectionGroup, System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, Custom=null">
            <section name="defaultSettings" type="System.Transactions.Configuration.DefaultSettingsSection, System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, Custom=null" />
            <section name="machineSettings" type="System.Transactions.Configuration.MachineSettingsSection, System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, Custom=null" allowDefinition="MachineOnly" allowExeDefinition="MachineOnly" />
        </sectionGroup>
        <sectionGroup name="system.web" type="System.Web.Configuration.SystemWebSectionGroup, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
            <section name="anonymousIdentification" type="System.Web.Configuration.AnonymousIdentificationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="authentication" type="System.Web.Configuration.AuthenticationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="authorization" type="System.Web.Configuration.AuthorizationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="browserCaps" type="System.Web.Configuration.HttpCapabilitiesSectionHandler, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="clientTarget" type="System.Web.Configuration.ClientTargetSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="compilation" type="System.Web.Configuration.CompilationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" requirePermission="false" />
            <section name="customErrors" type="System.Web.Configuration.CustomErrorsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="deployment" type="System.Web.Configuration.DeploymentSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineOnly" />
            <section name="deviceFilters" type="System.Web.Mobile.DeviceFiltersSection, System.Web.Mobile, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="fullTrustAssemblies" type="System.Web.Configuration.FullTrustAssembliesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="globalization" type="System.Web.Configuration.GlobalizationSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="healthMonitoring" type="System.Web.Configuration.HealthMonitoringSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="hostingEnvironment" type="System.Web.Configuration.HostingEnvironmentSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="httpCookies" type="System.Web.Configuration.HttpCookiesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="httpHandlers" type="System.Web.Configuration.HttpHandlersSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="httpModules" type="System.Web.Configuration.HttpModulesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="httpRuntime" type="System.Web.Configuration.HttpRuntimeSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="identity" type="System.Web.Configuration.IdentitySection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="machineKey" type="System.Web.Configuration.MachineKeySection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="membership" type="System.Web.Configuration.MembershipSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="mobileControls" type="System.Web.UI.MobileControls.MobileControlsSection, System.Web.Mobile, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="pages" type="System.Web.Configuration.PagesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" requirePermission="false" />
            <section name="partialTrustVisibleAssemblies" type="System.Web.Configuration.PartialTrustVisibleAssembliesSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="processModel" type="System.Web.Configuration.ProcessModelSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineOnly" allowLocation="false" />
            <section name="profile" type="System.Web.Configuration.ProfileSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="protocols" type="System.Web.Configuration.ProtocolsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToWebRoot" />
            <section name="roleManager" type="System.Web.Configuration.RoleManagerSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="securityPolicy" type="System.Web.Configuration.SecurityPolicySection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="sessionPageState" type="System.Web.Configuration.SessionPageStateSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="sessionState" type="System.Web.Configuration.SessionStateSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="siteMap" type="System.Web.Configuration.SiteMapSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="trace" type="System.Web.Configuration.TraceSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="trust" type="System.Web.Configuration.TrustSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="urlMappings" type="System.Web.Configuration.UrlMappingsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            <section name="webControls" type="System.Web.Configuration.WebControlsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="webParts" type="System.Web.Configuration.WebPartsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="webServices" type="System.Web.Services.Configuration.WebServicesSection, System.Web.Services, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <section name="xhtmlConformance" type="System.Web.Configuration.XhtmlConformanceSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <sectionGroup name="caching" type="System.Web.Configuration.SystemWebCachingSectionGroup, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
                <section name="cache" type="System.Web.Configuration.CacheSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
                <section name="outputCache" type="System.Web.Configuration.OutputCacheSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
                <section name="outputCacheSettings" type="System.Web.Configuration.OutputCacheSettingsSection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
                <section name="sqlCacheDependency" type="System.Web.Configuration.SqlCacheDependencySection, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" allowDefinition="MachineToApplication" />
            </sectionGroup>
        </sectionGroup>
        <sectionGroup name="system.web.extensions" type="System.Web.Configuration.SystemWebExtensionsSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
            <sectionGroup name="scripting" type="System.Web.Configuration.ScriptingSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
                <section name="scriptResourceHandler" type="System.Web.Configuration.ScriptingScriptResourceHandlerSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication" />
                <sectionGroup name="webServices" type="System.Web.Configuration.ScriptingWebServicesSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
                    <section name="jsonSerialization" type="System.Web.Configuration.ScriptingJsonSerializationSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="Everywhere" />
                    <section name="profileService" type="System.Web.Configuration.ScriptingProfileServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication" />
                    <section name="authenticationService" type="System.Web.Configuration.ScriptingAuthenticationServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication" />
                    <section name="roleService" type="System.Web.Configuration.ScriptingRoleServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication" />
                </sectionGroup>
            </sectionGroup>
        </sectionGroup>
        <sectionGroup name="system.xaml.hosting" type="System.Xaml.Hosting.Configuration.XamlHostingSectionGroup, System.Xaml.Hosting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
            <section name="httpHandlers" type="System.Xaml.Hosting.Configuration.XamlHostingSection, System.Xaml.Hosting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        </sectionGroup>
        <section name="system.webServer" type="System.Configuration.IgnoreSection, System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
    </configSections>
    <!--
        <appSettings
            file = "" [String]
            >
            <add
                key = "" [String, Collection Key]
                value = "" [String]
            />

        </appSettings>
    -->
    <appSettings file="">
        <clear />
    </appSettings>
    <!--
        <system.diagnostics
            >
            <assert
                assertuienabled = "true" [true|false]
                logfilename = "" [String]
            />
            <performanceCounters
                filemappingsize = "524288" [number]
            />
            <trace
                autoflush = "false" [true|false]
                indentsize = "4" [number]
                useGlobalLock = "true" [true|false]
                >
            </trace>
        </system.diagnostics>
    -->
    <system.diagnostics>
        <assert assertuienabled="true" logfilename="" />
        <performanceCounters filemappingsize="524288" />
        <switches>
            <clear />
        </switches>
        <trace autoflush="false" indentsize="4" useGlobalLock="true">
            <listeners>
                <clear />
                <add initializeData="" type="System.Diagnostics.DefaultTraceListener" name="Default" traceOutputOptions="None">
                    <filter type="" initializeData="" />
                </add>
            </listeners>
        </trace>
    </system.diagnostics>
    <!--
        <system.windows.forms
            jitDebugging = "false" [true|false]
        />
    -->
    <system.windows.forms jitDebugging="false" />
    <!--
        <uri
            >
            <idn
                enabled = "None" [None | AllExceptIntranet | All]
            />
            <iriParsing
                enabled = "false" [true|false]
            />
        </uri>
    -->
    <uri>
        <idn enabled="None" />
        <iriParsing enabled="false" />
        <schemeSettings>
            <clear />
        </schemeSettings>
    </uri>
    <!--
        <configProtectedData
            defaultProvider = "RsaProtectedConfigurationProvider" [String]
            >
            <providers>
                <add
                    name = "" [String, Required, Collection Key]
                    type = "" [String, Required]
                />
            </providers>

        </configProtectedData>
    -->
    <configProtectedData defaultProvider="RsaProtectedConfigurationProvider">
        <providers>
            <clear />
            <add description="Uses RsaCryptoServiceProvider to encrypt and decrypt" keyContainerName="NetFrameworkConfigurationKey" cspProviderName="" useMachineContainer="true" useOAEP="true" name="RsaProtectedConfigurationProvider" type="System.Configuration.RsaProtectedConfigurationProvider,System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <add description="Uses CryptProtectData and CryptUnProtectData Windows APIs to encrypt and decrypt" useMachineProtection="true" keyEntropy="" name="DataProtectionConfigurationProvider" type="System.Configuration.DpapiProtectedConfigurationProvider,System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
        </providers>
    </configProtectedData>
    <!--
        <runtime
        />
    -->
    <runtime />
    <!--
        <connectionStrings
            >
            <add
                name = "" [String, Required, Collection Key]
                connectionString = "" [String, Required]
                providerName = "System.Data.SqlClient" [String]
            />

        </connectionStrings>
    -->
    <connectionStrings>
        <clear />
        <add name="LocalSqlServer" connectionString="data source=.\SQLEXPRESS;Integrated Security=SSPI;AttachDBFilename=|DataDirectory|aspnetdb.mdf;User Instance=true" providerName="System.Data.SqlClient" />
    </connectionStrings>
    <!--
        <system.data
        />
    -->
    <system.data>
        <DbProviderFactories />
    </system.data>
    <system.serviceModel>
        <!--
            <behaviors
                >
            </behaviors>
        -->
        <behaviors>
            <endpointBehaviors>
                <clear />
            </endpointBehaviors>
            <serviceBehaviors>
                <clear />
            </serviceBehaviors>
        </behaviors>
        <!--
            <bindings
                >
            </bindings>
        -->
        <bindings>
            <basicHttpBinding>
                <clear />
            </basicHttpBinding>
            <basicHttpsBinding>
                <clear />
            </basicHttpsBinding>
            <customBinding>
                <clear />
            </customBinding>
            <msmqIntegrationBinding>
                <clear />
            </msmqIntegrationBinding>
            <netMsmqBinding>
                <clear />
            </netMsmqBinding>
            <netNamedPipeBinding>
                <clear />
            </netNamedPipeBinding>
            <netPeerTcpBinding>
                <clear />
            </netPeerTcpBinding>
            <netTcpBinding>
                <clear />
            </netTcpBinding>
            <wsDualHttpBinding>
                <clear />
            </wsDualHttpBinding>
            <wsFederationHttpBinding>
                <clear />
            </wsFederationHttpBinding>
            <ws2007FederationHttpBinding>
                <clear />
            </ws2007FederationHttpBinding>
            <wsHttpBinding>
                <clear />
            </wsHttpBinding>
            <ws2007HttpBinding>
                <clear />
            </ws2007HttpBinding>
            <mexHttpBinding>
                <clear />
            </mexHttpBinding>
            <mexHttpsBinding>
                <clear />
            </mexHttpsBinding>
            <mexNamedPipeBinding>
                <clear />
            </mexNamedPipeBinding>
            <mexTcpBinding>
                <clear />
            </mexTcpBinding>
            <udpBinding>
                <clear />
            </udpBinding>
            <netHttpBinding>
                <clear />
            </netHttpBinding>
            <netHttpsBinding>
                <clear />
            </netHttpsBinding>
            <wsHttpContextBinding>
                <clear />
            </wsHttpContextBinding>
            <netTcpContextBinding>
                <clear />
            </netTcpContextBinding>
            <webHttpBinding>
                <clear />
            </webHttpBinding>
            <basicHttpContextBinding>
                <clear />
            </basicHttpContextBinding>
        </bindings>
        <!--
            <comContracts
                >
            </comContracts>
        -->
        <comContracts>
            <clear />
        </comContracts>
        <!--
            <diagnostics
                wmiProviderEnabled = "false" [true|false]
                performanceCounters = "Default" [Off | ServiceOnly | All | Default]
                etwProviderId = "{c651f5f6-1c0d-492e-8ae1-b4efd7c9d503}" [String]
                >
                <messageLogging
                    logEntireMessage = "false" [true|false]
                    logKnownPii = "false" [true|false]
                    logMalformedMessages = "false" [true|false]
                    logMessagesAtServiceLevel = "false" [true|false]
                    logMessagesAtTransportLevel = "false" [true|false]
                    maxMessagesToLog = "10000" [number]
                    maxSizeOfMessageToLog = "262144" [number]
                    >
                </messageLogging>
                <endToEndTracing
                    propagateActivity = "false" [true|false]
                    activityTracing = "false" [true|false]
                    messageFlowTracing = "false" [true|false]
                />
            </diagnostics>
        -->
        <diagnostics wmiProviderEnabled="false" performanceCounters="Default" etwProviderId="{c651f5f6-1c0d-492e-8ae1-b4efd7c9d503}">
            <messageLogging logEntireMessage="false" logKnownPii="false" logMalformedMessages="false" logMessagesAtServiceLevel="false" logMessagesAtTransportLevel="false" maxMessagesToLog="10000" maxSizeOfMessageToLog="262144">
                <filters>
                    <clear />
                </filters>
            </messageLogging>
            <endToEndTracing propagateActivity="false" activityTracing="false" messageFlowTracing="false" />
        </diagnostics>
        <!--
            <machineSettings
            />
        -->
        <machineSettings enableLoggingKnownPii="false" />
        <!--
            <protocolMapping
                >
            </protocolMapping>
        -->
        <protocolMapping>
            <clear />
            <add scheme="http" binding="basicHttpBinding" bindingConfiguration="" />
            <add scheme="net.tcp" binding="netTcpBinding" bindingConfiguration="" />
            <add scheme="net.pipe" binding="netNamedPipeBinding" bindingConfiguration="" />
            <add scheme="net.msmq" binding="netMsmqBinding" bindingConfiguration="" />
        </protocolMapping>
        <!--
            <routing
                >
                <filters>
                    <filter
                        name = "" [String, Required, Collection Key]
                        filterType = "" [Action | EndpointAddress | PrefixEndpointAddress | And | Custom | EndpointName | MatchAll | XPath, Required]
                        filterData = "" [String]
                        filter1 = "" [String]
                        filter2 = "" [String]
                        customType = "" [String]
                    />
                </filters>

                <filterTables>
                    <filterTable
                        name = "" [String, Required, Collection Key]
                        >
                        <add
                            filterName = "" [String, Required, Collection Key]
                            endpointName = "" [String, Required, Collection Key]
                            priority = "0" [number]
                            backupList = "" [String]
                        />
                    </filterTable>

                </filterTables>

                <backupLists>
                    <backupList
                        name = "" [String, Required, Collection Key]
                        >
                        <add
                            endpointName = "" [String, Required]
                        />
                    </backupList>

                </backupLists>

                <namespaceTable>
                    <add
                        prefix = "" [String, Required, Collection Key]
                        namespace = "" [String, Required]
                    />
                </namespaceTable>

            </routing>
        -->
        <routing>
            <filters>
                <clear />
            </filters>
            <filterTables>
                <clear />
            </filterTables>
            <backupLists>
                <clear />
            </backupLists>
            <namespaceTable>
                <clear />
            </namespaceTable>
        </routing>
        <!--
            <serviceHostingEnvironment
                aspNetCompatibilityEnabled = "false" [true|false]
                closeIdleServicesAtLowMemory = "false" [true|false]
                minFreeMemoryPercentageToActivateService = "5" [number]
                multipleSiteBindingsEnabled = "false" [true|false]
                >
            </serviceHostingEnvironment>
        -->
        <serviceHostingEnvironment aspNetCompatibilityEnabled="false" closeIdleServicesAtLowMemory="false" minFreeMemoryPercentageToActivateService="5" multipleSiteBindingsEnabled="false">
            <clear />
            <baseAddressPrefixFilters>
                <clear />
            </baseAddressPrefixFilters>
            <serviceActivations>
                <clear />
            </serviceActivations>
        </serviceHostingEnvironment>
        <!--
            <services
                >
            </services>
        -->
        <services>
            <clear />
        </services>
        <!--
            <standardEndpoints
                >
            </standardEndpoints>
        -->
        <standardEndpoints>
            <mexEndpoint>
                <clear />
            </mexEndpoint>
            <dynamicEndpoint>
                <clear />
            </dynamicEndpoint>
            <discoveryEndpoint>
                <clear />
            </discoveryEndpoint>
            <udpDiscoveryEndpoint>
                <clear />
            </udpDiscoveryEndpoint>
            <announcementEndpoint>
                <clear />
            </announcementEndpoint>
            <udpAnnouncementEndpoint>
                <clear />
            </udpAnnouncementEndpoint>
            <workflowControlEndpoint>
                <clear />
            </workflowControlEndpoint>
            <webHttpEndpoint>
                <clear />
            </webHttpEndpoint>
            <webScriptEndpoint>
                <clear />
            </webScriptEndpoint>
        </standardEndpoints>
        <!--
            <extensions
                >
            </extensions>
        -->
        <extensions>
            <behaviorExtensions>
                <add name="clientCredentials" type="System.ServiceModel.Configuration.ClientCredentialsElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceCredentials" type="System.ServiceModel.Configuration.ServiceCredentialsElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="callbackDebug" type="System.ServiceModel.Configuration.CallbackDebugElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="clientVia" type="System.ServiceModel.Configuration.ClientViaElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="synchronousReceive" type="System.ServiceModel.Configuration.SynchronousReceiveElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="dispatcherSynchronization" type="System.ServiceModel.Configuration.DispatcherSynchronizationElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceMetadata" type="System.ServiceModel.Configuration.ServiceMetadataPublishingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceDebug" type="System.ServiceModel.Configuration.ServiceDebugElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceAuthenticationManager" type="System.ServiceModel.Configuration.ServiceAuthenticationElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceAuthorization" type="System.ServiceModel.Configuration.ServiceAuthorizationElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceSecurityAudit" type="System.ServiceModel.Configuration.ServiceSecurityAuditElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceThrottling" type="System.ServiceModel.Configuration.ServiceThrottlingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="transactedBatching" type="System.ServiceModel.Configuration.TransactedBatchingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="dataContractSerializer" type="System.ServiceModel.Configuration.DataContractSerializerElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="serviceTimeouts" type="System.ServiceModel.Configuration.ServiceTimeoutsElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="callbackTimeouts" type="System.ServiceModel.Configuration.CallbackTimeoutsElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="useRequestHeadersForMetadataAddress" type="System.ServiceModel.Configuration.UseRequestHeadersForMetadataAddressElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="clear" type="System.ServiceModel.Configuration.ClearBehaviorElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="remove" type="System.ServiceModel.Configuration.RemoveBehaviorElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="persistenceProvider" type="System.ServiceModel.Configuration.PersistenceProviderElement, System.WorkflowServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="workflowRuntime" type="System.ServiceModel.Configuration.WorkflowRuntimeElement, System.WorkflowServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="enableWebScript" type="System.ServiceModel.Configuration.WebScriptEnablingElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="webHttp" type="System.ServiceModel.Configuration.WebHttpElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="serviceDiscovery" type="System.ServiceModel.Discovery.Configuration.ServiceDiscoveryElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="endpointDiscovery" type="System.ServiceModel.Discovery.Configuration.EndpointDiscoveryElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="etwTracking" type="System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="routing" type="System.ServiceModel.Routing.Configuration.RoutingExtensionElement, System.ServiceModel.Routing, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="soapProcessing" type="System.ServiceModel.Routing.Configuration.SoapProcessingExtensionElement, System.ServiceModel.Routing, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="workflowIdle" type="System.ServiceModel.Activities.Configuration.WorkflowIdleElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="workflowUnhandledException" type="System.ServiceModel.Activities.Configuration.WorkflowUnhandledExceptionElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="bufferedReceive" type="System.ServiceModel.Activities.Configuration.BufferedReceiveElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="sendMessageChannelCache" type="System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="sqlWorkflowInstanceStore" type="System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="workflowInstanceManagement" type="System.ServiceModel.Activities.Configuration.WorkflowInstanceManagementElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
            </behaviorExtensions>
            <bindingElementExtensions>
                <add name="binaryMessageEncoding" type="System.ServiceModel.Configuration.BinaryMessageEncodingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="compositeDuplex" type="System.ServiceModel.Configuration.CompositeDuplexElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="oneWay" type="System.ServiceModel.Configuration.OneWayElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="transactionFlow" type="System.ServiceModel.Configuration.TransactionFlowElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="httpsTransport" type="System.ServiceModel.Configuration.HttpsTransportElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="httpTransport" type="System.ServiceModel.Configuration.HttpTransportElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="msmqIntegration" type="System.ServiceModel.Configuration.MsmqIntegrationElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="msmqTransport" type="System.ServiceModel.Configuration.MsmqTransportElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="mtomMessageEncoding" type="System.ServiceModel.Configuration.MtomMessageEncodingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="namedPipeTransport" type="System.ServiceModel.Configuration.NamedPipeTransportElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="peerTransport" type="System.ServiceModel.Configuration.PeerTransportElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="pnrpPeerResolver" type="System.ServiceModel.Configuration.PnrpPeerResolverElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="privacyNoticeAt" type="System.ServiceModel.Configuration.PrivacyNoticeElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="reliableSession" type="System.ServiceModel.Configuration.ReliableSessionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="security" type="System.ServiceModel.Configuration.SecurityElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="sslStreamSecurity" type="System.ServiceModel.Configuration.SslStreamSecurityElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="tcpTransport" type="System.ServiceModel.Configuration.TcpTransportElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="textMessageEncoding" type="System.ServiceModel.Configuration.TextMessageEncodingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="unrecognizedPolicyAssertions" type="System.ServiceModel.Configuration.UnrecognizedPolicyAssertionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="useManagedPresentation" type="System.ServiceModel.Configuration.UseManagedPresentationElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="windowsStreamSecurity" type="System.ServiceModel.Configuration.WindowsStreamSecurityElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="webMessageEncoding" type="System.ServiceModel.Configuration.WebMessageEncodingElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="context" type="System.ServiceModel.Configuration.ContextBindingElementExtensionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="byteStreamMessageEncoding" type="System.ServiceModel.Configuration.ByteStreamMessageEncodingElement, System.ServiceModel.Channels, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="discoveryClient" type="System.ServiceModel.Discovery.Configuration.DiscoveryClientElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
            </bindingElementExtensions>
            <bindingExtensions>
                <add name="basicHttpBinding" type="System.ServiceModel.Configuration.BasicHttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="basicHttpsBinding" type="System.ServiceModel.Configuration.BasicHttpsBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="customBinding" type="System.ServiceModel.Configuration.CustomBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="msmqIntegrationBinding" type="System.ServiceModel.Configuration.MsmqIntegrationBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="netMsmqBinding" type="System.ServiceModel.Configuration.NetMsmqBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="netNamedPipeBinding" type="System.ServiceModel.Configuration.NetNamedPipeBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="netPeerTcpBinding" type="System.ServiceModel.Configuration.NetPeerTcpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="netTcpBinding" type="System.ServiceModel.Configuration.NetTcpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="wsDualHttpBinding" type="System.ServiceModel.Configuration.WSDualHttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="wsFederationHttpBinding" type="System.ServiceModel.Configuration.WSFederationHttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="ws2007FederationHttpBinding" type="System.ServiceModel.Configuration.WS2007FederationHttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="wsHttpBinding" type="System.ServiceModel.Configuration.WSHttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="ws2007HttpBinding" type="System.ServiceModel.Configuration.WS2007HttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="mexHttpBinding" type="System.ServiceModel.Configuration.MexHttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="mexHttpsBinding" type="System.ServiceModel.Configuration.MexHttpsBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="mexNamedPipeBinding" type="System.ServiceModel.Configuration.MexNamedPipeBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="mexTcpBinding" type="System.ServiceModel.Configuration.MexTcpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="udpBinding" type="System.ServiceModel.Configuration.UdpBindingCollectionElement, System.ServiceModel.Channels, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="netHttpBinding" type="System.ServiceModel.Configuration.NetHttpBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="netHttpsBinding" type="System.ServiceModel.Configuration.NetHttpsBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="wsHttpContextBinding" type="System.ServiceModel.Configuration.WSHttpContextBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="netTcpContextBinding" type="System.ServiceModel.Configuration.NetTcpContextBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="webHttpBinding" type="System.ServiceModel.Configuration.WebHttpBindingCollectionElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="basicHttpContextBinding" type="System.ServiceModel.Configuration.BasicHttpContextBindingCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            </bindingExtensions>
            <endpointExtensions>
                <add name="mexEndpoint" type="System.ServiceModel.Configuration.ServiceMetadataEndpointCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add name="dynamicEndpoint" type="System.ServiceModel.Discovery.Configuration.DynamicEndpointCollectionElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="discoveryEndpoint" type="System.ServiceModel.Discovery.Configuration.DiscoveryEndpointCollectionElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="udpDiscoveryEndpoint" type="System.ServiceModel.Discovery.Configuration.UdpDiscoveryEndpointCollectionElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="announcementEndpoint" type="System.ServiceModel.Discovery.Configuration.AnnouncementEndpointCollectionElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="udpAnnouncementEndpoint" type="System.ServiceModel.Discovery.Configuration.UdpAnnouncementEndpointCollectionElement, System.ServiceModel.Discovery, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="workflowControlEndpoint" type="System.ServiceModel.Activities.Configuration.WorkflowControlEndpointCollectionElement, System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="webHttpEndpoint" type="System.ServiceModel.Configuration.WebHttpEndpointCollectionElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
                <add name="webScriptEndpoint" type="System.ServiceModel.Configuration.WebScriptEndpointCollectionElement, System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
            </endpointExtensions>
        </extensions>
        <!--
            <client
                >
            </client>
        -->
        <client>
            <clear />
            <metadata>
                <policyImporters>
                    <clear />
                    <extension type="System.ServiceModel.Channels.PrivacyNoticeBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.UseManagedPresentationBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.TransactionFlowBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.ReliableSessionBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.SecurityBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.CompositeDuplexBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.OneWayBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.MessageEncodingBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.TransportBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.ContextBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL" />
                </policyImporters>
                <wsdlImporters>
                    <clear />
                    <extension type="System.ServiceModel.Description.DataContractSerializerMessageContractImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Description.XmlSerializerMessageContractImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.MessageEncodingBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.TransportBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.StandardBindingImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                    <extension type="System.ServiceModel.Channels.ContextBindingElementImporter, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL" />
                </wsdlImporters>
            </metadata>
        </client>
        <!--
            <tracking
                >
            </tracking>
        -->
        <tracking>
            <profiles>
                <clear />
                <trackingProfile name="" implementationVisibility="RootScope">
                    <workflow activityDefinitionId="*">
                        <workflowInstanceQueries>
                            <workflowInstanceQuery>
                                <states>
                                    <state name="*" />
                                </states>
                            </workflowInstanceQuery>
                        </workflowInstanceQueries>
                        <activityStateQueries>
                            <activityStateQuery activityName="*">
                                <states>
                                    <state name="Faulted" />
                                </states>
                            </activityStateQuery>
                        </activityStateQueries>
                        <faultPropagationQueries>
                            <faultPropagationQuery faultSourceActivityName="*" faultHandlerActivityName="*" />
                        </faultPropagationQueries>
                    </workflow>
                </trackingProfile>
            </profiles>
        </tracking>
        <!--
            <commonBehaviors
                >
            </commonBehaviors>
        -->
        <commonBehaviors />
    </system.serviceModel>
    <system.web>
        <!--
            <deployment
                retail = "false" [true|false]
            />
        -->
        <deployment retail="false" />
        <!--
            <processModel
                enable = "true" [true|false]
                timeout = "10675199.02:48:05.4775807" [HH:MM:SS]
                idleTimeout = "10675199.02:48:05.4775807" [HH:MM:SS]
                shutdownTimeout = "00:00:05" [HH:MM:SS]
                requestLimit = "2147483647" [number]
                requestQueueLimit = "5000" [number]
                restartQueueLimit = "10" [number]
                memoryLimit = "60" [number]
                webGarden = "false" [true|false]
                cpuMask = "0xffffffff" [number]
                userName = "machine" [String]
                password = "AutoGenerate" [String]
                logLevel = "Errors" [None | All | Errors]
                clientConnectedCheck = "00:00:05" [HH:MM:SS]
                comAuthenticationLevel = "Connect" [None | Call | Connect | Default | Pkt | PktIntegrity | PktPrivacy]
                comImpersonationLevel = "Impersonate" [Default | Anonymous | Delegate | Identify | Impersonate]
                responseDeadlockInterval = "00:03:00" [HH:MM:SS]
                responseRestartDeadlockInterval = "00:03:00" [HH:MM:SS]
                autoConfig = "false" [true|false]
                maxWorkerThreads = "20" [number]
                maxIoThreads = "20" [number]
                minWorkerThreads = "1" [number]
                minIoThreads = "1" [number]
                serverErrorMessageFile = "" [String]
                pingFrequency = "10675199.02:48:05.4775807" [HH:MM:SS]
                pingTimeout = "10675199.02:48:05.4775807" [HH:MM:SS]
                maxAppDomains = "2000" [number]
            />
        -->
        <processModel enable="true" timeout="Infinite" idleTimeout="Infinite" shutdownTimeout="00:00:05" requestLimit="Infinite" requestQueueLimit="5000" restartQueueLimit="10" memoryLimit="60" webGarden="false" cpuMask="0xffffffff" userName="machine" password="AutoGenerate" logLevel="Errors" clientConnectedCheck="00:00:05" comAuthenticationLevel="Connect" comImpersonationLevel="Impersonate" responseDeadlockInterval="00:03:00" responseRestartDeadlockInterval="00:03:00" autoConfig="true" maxWorkerThreads="100" maxIoThreads="100" minWorkerThreads="1" minIoThreads="1" serverErrorMessageFile="" pingFrequency="Infinite" pingTimeout="Infinite" maxAppDomains="2000" />
        <!--
            <membership
                defaultProvider = "AspNetSqlMembershipProvider" [String]
                hashAlgorithmType = "" [String]
                userIsOnlineTimeWindow = "15" [in Minutes][number]
                >
                <providers>
                    <add
                        name = "" [String, Required, Collection Key]
                        type = "" [String, Required]
                    />
                </providers>

            </membership>
        -->
        <membership defaultProvider="AspNetSqlMembershipProvider" userIsOnlineTimeWindow="15" hashAlgorithmType="">
            <providers>
                <clear />
                <add connectionStringName="LocalSqlServer" enablePasswordRetrieval="false" enablePasswordReset="true" requiresQuestionAndAnswer="true" applicationName="/" requiresUniqueEmail="false" passwordFormat="Hashed" maxInvalidPasswordAttempts="5" minRequiredPasswordLength="7" minRequiredNonalphanumericCharacters="1" passwordAttemptWindow="10" passwordStrengthRegularExpression="" name="AspNetSqlMembershipProvider" type="System.Web.Security.SqlMembershipProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            </providers>
        </membership>
        <!--
            <profile
                automaticSaveEnabled = "true" [true|false]
                enabled = "true" [true|false]
                defaultProvider = "AspNetSqlProfileProvider" [String]
                inherits = "" [String]
                >
                <providers>
                    <add
                        name = "" [String, Required, Collection Key]
                        type = "" [String, Required]
                    />
                </providers>

            </profile>
        -->
        <profile enabled="true" defaultProvider="AspNetSqlProfileProvider" inherits="" automaticSaveEnabled="true">
            <providers>
                <clear />
                <add connectionStringName="LocalSqlServer" applicationName="/" name="AspNetSqlProfileProvider" type="System.Web.Profile.SqlProfileProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            </providers>
            <properties>
                <clear />
            </properties>
        </profile>
        <!--
            <roleManager
                enabled = "false" [true|false]
                createPersistentCookie = "false" [true|false]
                cacheRolesInCookie = "false" [true|false]
                cookieName = ".ASPXROLES" [String]
                cookieTimeout = "30" [in Minutes][number]
                cookiePath = "/" [String]
                cookieRequireSSL = "false" [true|false]
                cookieSlidingExpiration = "true" [true|false]
                cookieProtection = "All" [None | Validation | Encryption | All]
                defaultProvider = "AspNetSqlRoleProvider" [String]
                domain = "" [String]
                maxCachedResults = "25" [number]
                >
                <providers>
                    <add
                        name = "" [String, Required, Collection Key]
                        type = "" [String, Required]
                    />
                </providers>

            </roleManager>
        -->
        <roleManager enabled="false" cacheRolesInCookie="false" cookieName=".ASPXROLES" cookieTimeout="30" cookiePath="/" cookieRequireSSL="false" cookieSlidingExpiration="true" cookieProtection="All" defaultProvider="AspNetSqlRoleProvider" createPersistentCookie="false" maxCachedResults="25">
            <providers>
                <clear />
                <add connectionStringName="LocalSqlServer" applicationName="/" name="AspNetSqlRoleProvider" type="System.Web.Security.SqlRoleProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
                <add applicationName="/" name="AspNetWindowsTokenRoleProvider" type="System.Web.Security.WindowsTokenRoleProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            </providers>
        </roleManager>
    </system.web>
    <system.net>
        <!--
            <authenticationModules
                >
                <add
                    type = "" [String, Required, Collection Key]
                />

            </authenticationModules>
        -->
        <authenticationModules>
            <clear />
            <add type="System.Net.NegotiateClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add type="System.Net.KerberosClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add type="System.Net.NtlmClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add type="System.Net.DigestClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add type="System.Net.BasicClient, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        </authenticationModules>
        <!--
            <connectionManagement
                >
                <add
                    address = "" [String, Required, Collection Key]
                    maxconnection = "1" [number, Required]
                />

            </connectionManagement>
        -->
        <connectionManagement>
            <clear />
        </connectionManagement>
        <!--
            <defaultProxy
                enabled = "true" [true|false]
                useDefaultCredentials = "false" [true|false]
                >
                <bypasslist>
                    <add
                        address = "" [String, Required, Collection Key]
                    />
                </bypasslist>

                <module
                    type = "" [String]
                />
                <proxy
                    autoDetect = "Unspecified" [False | True | Unspecified]
                    scriptLocation = ""
                    bypassonlocal = "Unspecified" [False | True | Unspecified]
                    proxyaddress = ""
                    usesystemdefault = "Unspecified" [False | True | Unspecified]
                />
            </defaultProxy>
        -->
        <defaultProxy enabled="true" useDefaultCredentials="false">
            <bypasslist>
                <clear />
            </bypasslist>
            <proxy autoDetect="Unspecified" bypassonlocal="Unspecified" usesystemdefault="Unspecified" />
        </defaultProxy>
        <!--
            <requestCaching
                defaultPolicyLevel = "BypassCache" [Default | BypassCache | CacheOnly | CacheIfAvailable | Revalidate | Reload | NoCacheNoStore]
                disableAllCaching = "false" [true|false]
                isPrivateCache = "true" [true|false]
                unspecifiedMaximumAge = "1.00:00:00" [HH:MM:SS]
                >
                <defaultHttpCachePolicy
                    maximumAge = "10675199.02:48:05.4775807" [HH:MM:SS]
                    maximumStale = "-10675199.02:48:05.4775808" [HH:MM:SS]
                    minimumFresh = "-10675199.02:48:05.4775808" [HH:MM:SS]
                    policyLevel = "Default" [Default | BypassCache | CacheOnly | CacheIfAvailable | Revalidate | Reload | NoCacheNoStore | CacheOrNextCacheOnly | Refresh, Required]
                />
                <defaultFtpCachePolicy
                    policyLevel = "Default" [Default | BypassCache | CacheOnly | CacheIfAvailable | Revalidate | Reload | NoCacheNoStore]
                />
            </requestCaching>
        -->
        <requestCaching disableAllCaching="false" defaultPolicyLevel="BypassCache" isPrivateCache="true" unspecifiedMaximumAge="1.00:00:00">
            <defaultHttpCachePolicy maximumAge="10675199.02:48:05.4775807" maximumStale="-10675199.02:48:05.4775808" minimumFresh="-10675199.02:48:05.4775808" policyLevel="Default" />
            <defaultFtpCachePolicy policyLevel="Default" />
        </requestCaching>
        <!--
            <settings
                >
                <httpWebRequest
                    maximumUnauthorizedUploadLength = "-1" [number]
                    maximumErrorResponseLength = "64" [number]
                    maximumResponseHeadersLength = "64" [number]
                    useUnsafeHeaderParsing = "false" [true|false]
                />
                <ipv6
                    enabled = "false" [true|false]
                />
                <servicePointManager
                    checkCertificateName = "true" [true|false]
                    checkCertificateRevocationList = "false" [true|false]
                    dnsRefreshTimeout = "120000" [number]
                    enableDnsRoundRobin = "false" [true|false]
                    encryptionPolicy = "RequireEncryption" [RequireEncryption | AllowNoEncryption | NoEncryption]
                    expect100Continue = "true" [true|false]
                    useNagleAlgorithm = "true" [true|false]
                />
                <socket
                    alwaysUseCompletionPortsForAccept = "false" [true|false]
                    alwaysUseCompletionPortsForConnect = "false" [true|false]
                    ipProtectionLevel = "Unspecified" [Unrestricted | EdgeRestricted | Restricted | Unspecified]
                />
                <webProxyScript
                    downloadTimeout = "00:01:00" [HH:MM:SS]
                />
                <performanceCounters
                    enabled = "false" [true|false]
                />
                <httpListener
                    unescapeRequestUrl = "true" [true|false]
                    >
                    <timeouts
                        entityBody = "0" [HH:MM:SS]
                        drainEntityBody = "0" [HH:MM:SS]
                        requestQueue = "0" [HH:MM:SS]
                        idleConnection = "0" [HH:MM:SS]
                        headerWait = "0" [HH:MM:SS]
                        minSendBytesPerSecond = "0" [number]
                    />
                </httpListener>
                <webUtility
                    unicodeDecodingConformance = "Auto" [Auto | Strict | Compat | Loose]
                    unicodeEncodingConformance = "Auto" [Auto | Strict | Compat]
                />
            </settings>
        -->
        <settings>
            <httpWebRequest maximumResponseHeadersLength="64" maximumErrorResponseLength="64" maximumUnauthorizedUploadLength="-1" useUnsafeHeaderParsing="false" />
            <ipv6 enabled="false" />
            <servicePointManager checkCertificateName="true" checkCertificateRevocationList="false" dnsRefreshTimeout="120000" enableDnsRoundRobin="false" encryptionPolicy="RequireEncryption" expect100Continue="true" useNagleAlgorithm="true" />
            <socket alwaysUseCompletionPortsForAccept="false" alwaysUseCompletionPortsForConnect="false" ipProtectionLevel="Unspecified" />
            <webProxyScript downloadTimeout="00:01:00" />
            <performanceCounters enabled="false" />
            <httpListener unescapeRequestUrl="true">
                <timeouts entityBody="00:00:00" drainEntityBody="00:00:00" requestQueue="00:00:00" idleConnection="00:00:00" headerWait="00:00:00" minSendBytesPerSecond="0" />
            </httpListener>
            <webUtility unicodeDecodingConformance="Auto" unicodeEncodingConformance="Auto" />
        </settings>
        <!--
            <webRequestModules
                >
                <add
                    prefix = "" [String, Required, Collection Key]
                    type = ""
                />

            </webRequestModules>
        -->
        <webRequestModules>
            <clear />
            <add prefix="https:" type="System.Net.HttpRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add prefix="http:" type="System.Net.HttpRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add prefix="file:" type="System.Net.FileWebRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add prefix="ftp:" type="System.Net.FtpWebRequestCreator, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        </webRequestModules>
        <mailSettings>
            <!--
                <smtp
                    deliveryMethod = "Network" [Network | SpecifiedPickupDirectory | PickupDirectoryFromIis]
                    deliveryFormat = "SevenBit" [SevenBit | International]
                    from = "" [String]
                    >
                    <network
                        defaultCredentials = "false" [true|false]
                        host = "" [String]
                        targetName = "" [String]
                        clientDomain = "" [String]
                        password = "" [String]
                        port = "25" [number]
                        userName = "" [String]
                        enableSsl = "false" [true|false]
                    />
                    <specifiedPickupDirectory
                        pickupDirectoryLocation = "" [String]
                    />
                </smtp>
            -->
            <smtp deliveryMethod="Network" deliveryFormat="SevenBit">
                <network defaultCredentials="false" port="25" enableSsl="false" />
            </smtp>
        </mailSettings>
    </system.net>
    <system.runtime.caching>
        <!--
            <memoryCache>
               <namedCaches>
                   <add
                       name = "" [String, Required, Collection Key]
                       physicalMemoryLimitPercentage = "100" [as Percentage][number]
                       pollingInterval = "00:02:00" [HH:MM:SS]
                       cacheMemoryLimitMegabytes = "0" [number]
                   />
                </namedCaches>

            </memoryCache>
        -->
        <memoryCache>
            <namedCaches>
                <clear />
            </namedCaches>
        </memoryCache>
    </system.runtime.caching>
    <system.runtime.serialization>
        <dataContractSerializer>
            <declaredTypes>
                <clear />
            </declaredTypes>
        </dataContractSerializer>
    </system.runtime.serialization>
    <system.serviceModel.activation>
        <!--
            <diagnostics
                performanceCountersEnabled = "true" [true|false]
            />
        -->
        <diagnostics performanceCountersEnabled="true" />
        <!--
            <net.pipe
                maxPendingConnections = "100" [number]
                maxPendingAccepts = "0" [number]
                receiveTimeout = "00:00:30" [HH:MM:SS]
                >
            </net.pipe>
        -->
        <net.pipe maxPendingConnections="100" maxPendingAccepts="0" receiveTimeout="00:00:30">
            <allowAccounts>
                <clear />
                <add securityIdentifier="S-1-5-32-568" />
                <add securityIdentifier="S-1-5-18" />
                <add securityIdentifier="S-1-5-32-544" />
                <add securityIdentifier="S-1-5-19" />
                <add securityIdentifier="S-1-5-20" />
            </allowAccounts>
        </net.pipe>
        <!--
            <net.tcp
                listenBacklog = "0" [number]
                maxPendingConnections = "100" [number]
                maxPendingAccepts = "0" [number]
                receiveTimeout = "00:00:30" [HH:MM:SS]
                teredoEnabled = "false" [true|false]
                >
            </net.tcp>
        -->
        <net.tcp listenBacklog="0" maxPendingConnections="100" maxPendingAccepts="0" receiveTimeout="00:00:30" teredoEnabled="false">
            <allowAccounts>
                <clear />
                <add securityIdentifier="S-1-5-32-568" />
                <add securityIdentifier="S-1-5-18" />
                <add securityIdentifier="S-1-5-32-544" />
                <add securityIdentifier="S-1-5-19" />
                <add securityIdentifier="S-1-5-20" />
            </allowAccounts>
        </net.tcp>
    </system.serviceModel.activation>
    <system.transactions>
        <!--
            <defaultSettings
                distributedTransactionManagerName = "" [String]
                timeout = "00:01:00" [HH:MM:SS]
            />
        -->
        <defaultSettings distributedTransactionManagerName="" timeout="00:01:00" />
        <!--
            <machineSettings
                maxTimeout = "00:10:00" [HH:MM:SS]
            />
        -->
        <machineSettings maxTimeout="00:10:00" />
    </system.transactions>
    <system.web.extensions>
        <scripting>
            <!--
                <scriptResourceHandler
                    enableCaching = "true" [true|false]
                    enableCompression = "true" [true|false]
                />
            -->
            <scriptResourceHandler enableCaching="true" enableCompression="true" />
            <webServices>
                <!--
                    <authenticationService
                        enabled = "false" [true|false]
                        requireSSL = "false" [true|false]
                    />
                -->
                <authenticationService enabled="false" requireSSL="false" />
                <!--
                    <jsonSerialization
                        recursionLimit = "100" [number]
                        maxJsonLength = "102400" [number]
                        >
                        <converters>
                            <add
                                type = "" [String, Required]
                                name = "" [String, Required, Collection Key]
                            />
                        </converters>

                    </jsonSerialization>
                -->
                <jsonSerialization recursionLimit="100" maxJsonLength="102400">
                    <converters>
                        <clear />
                    </converters>
                </jsonSerialization>
                <!--
                    <profileService
                        enabled = "false" [true|false]
                        readAccessProperties = ""
                        writeAccessProperties = ""
                    />
                -->
                <profileService enabled="false" readAccessProperties="" writeAccessProperties="" />
                <!--
                    <roleService
                        enabled = "false" [true|false]
                    />
                -->
                <roleService enabled="false" />
            </webServices>
        </scripting>
    </system.web.extensions>
    <system.xaml.hosting>
        <!--
            <httpHandlers
                >
                <add
                    httpHandlerType = " " [String, Required]
                    xamlRootElementType = " " [String, Required, Collection Key]
                />

            </httpHandlers>
        -->
        <httpHandlers>
            <clear />
        </httpHandlers>
    </system.xaml.hosting>
    <system.xml.serialization>
        <!--
            <dateTimeSerialization
                mode = "Roundtrip" [Default | Roundtrip | Local]
            />
        -->
        <dateTimeSerialization mode="Roundtrip" />
        <!--
            <schemaImporterExtensions
                >
                <add
                    name = "" [String, Required, Collection Key]
                    type = "" [Required]
                />

            </schemaImporterExtensions>
        -->
        <schemaImporterExtensions>
            <clear />
            <add name="SqlTypesSchemaImporterChar" type="System.Data.SqlTypes.TypeCharSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterNChar" type="System.Data.SqlTypes.TypeNCharSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterVarChar" type="System.Data.SqlTypes.TypeVarCharSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterNVarChar" type="System.Data.SqlTypes.TypeNVarCharSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterText" type="System.Data.SqlTypes.TypeTextSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterNText" type="System.Data.SqlTypes.TypeNTextSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterVarBinary" type="System.Data.SqlTypes.TypeVarBinarySchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterBinary" type="System.Data.SqlTypes.TypeBinarySchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterImage" type="System.Data.SqlTypes.TypeVarImageSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterDecimal" type="System.Data.SqlTypes.TypeDecimalSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterNumeric" type="System.Data.SqlTypes.TypeNumericSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterBigInt" type="System.Data.SqlTypes.TypeBigIntSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterInt" type="System.Data.SqlTypes.TypeIntSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterSmallInt" type="System.Data.SqlTypes.TypeSmallIntSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterTinyInt" type="System.Data.SqlTypes.TypeTinyIntSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterBit" type="System.Data.SqlTypes.TypeBitSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterFloat" type="System.Data.SqlTypes.TypeFloatSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterReal" type="System.Data.SqlTypes.TypeRealSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterDateTime" type="System.Data.SqlTypes.TypeDateTimeSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterSmallDateTime" type="System.Data.SqlTypes.TypeSmallDateTimeSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterMoney" type="System.Data.SqlTypes.TypeMoneySchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterSmallMoney" type="System.Data.SqlTypes.TypeSmallMoneySchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <add name="SqlTypesSchemaImporterUniqueIdentifier" type="System.Data.SqlTypes.TypeUniqueIdentifierSchemaImporterExtension, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        </schemaImporterExtensions>
        <!--
            <xmlSerializer
                checkDeserializeAdvances = "false" [true|false]
                tempFilesLocation = "" [String]
                useLegacySerializerGeneration = "false" [true|false]
            />
        -->
        <xmlSerializer checkDeserializeAdvances="false" useLegacySerializerGeneration="false" />
    </system.xml.serialization>
    <system.codedom>
        <!--
            <compiler
               language = "[string]" - semicolon separated list of friendly names.  Notice these have to be unique   across all compilers in this section.  They are case insensitive.
               extensions = "[string]" - semicolon separated list of source file extensions (should include the '.'). Notice these have to be unique   across all compilers in this section.  They are case insensitive.
               type = "[Fully qualified type Name]"
               warningLevel = "[integer]" - warning level to pass to the compilers.
               compilerOptions = "[string]" - options to pass to the compilers.
            />
        -->
        <compilers>
            <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CSharp.CSharpCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.VisualBasic.VBCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <compiler language="js;jscript;javascript" extension=".js" type="Microsoft.JScript.JScriptCodeProvider, Microsoft.JScript, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
            <compiler language="c++;mc;cpp" extension=".h" type="Microsoft.VisualC.CppCodeProvider, CppCodeProvider, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
        </compilers>
    </system.codedom>
</configuration>
