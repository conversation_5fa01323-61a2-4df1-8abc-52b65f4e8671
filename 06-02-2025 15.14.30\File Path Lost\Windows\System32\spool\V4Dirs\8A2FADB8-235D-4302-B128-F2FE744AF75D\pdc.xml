<?xml version="1.0" encoding="utf-8"?>
<PrintDeviceCapabilities
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns:xml="http://www.w3.org/XML/1998/namespace"
  xmlns:psk="http://schemas.microsoft.com/windows/2003/08/printing/printschemakeywords"
  xmlns:psk12="http://schemas.microsoft.com/windows/2013/12/printing/printschemakeywordsv12"
  xmlns:psf="http://schemas.microsoft.com/windows/2003/08/printing/printschemaframework"
  xmlns:psf2="http://schemas.microsoft.com/windows/2013/12/printing/printschemaframework2"
  xmlns="http://schemas.microsoft.com/windows/2013/12/printing/printschemaframework2"
  version="2">

    <psk:JobCopiesAllDocuments psf2:psftype="ParameterDef">
        <psf:DataType xsi:type="xsd:QName" psf2:psftype="Property">xsd:integer</psf:DataType>
        <psf:DefaultValue xsi:type="xsd:integer" psf2:psftype="Property">1</psf:DefaultValue>
        <psf:Mandatory xsi:type="xsd:QName" psf2:psftype="Property">psk:Unconditional</psf:Mandatory>
        <psf:MaxValue xsi:type="xsd:integer" psf2:psftype="Property">999</psf:MaxValue>
        <psf:MinValue xsi:type="xsd:integer" psf2:psftype="Property">1</psf:MinValue>
        <psf:Multiple xsi:type="xsd:integer" psf2:psftype="Property">1</psf:Multiple>
        <psf:UnitType xsi:type="xsd:string" psf2:psftype="Property">copies</psf:UnitType>
    </psk:JobCopiesAllDocuments>

    <psk:PageMediaSize psf2:psftype="Feature">
        <psk:ISOA4 psf2:psftype="Option">
            <psk12:PortraitImageableSize xsi:type="psf2:ImageableAreaType" psf2:psftype="Property">0,0,210000,297000</psk12:PortraitImageableSize>
            <psk:MediaSizeHeight xsi:type="xsd:integer" psf2:psftype="ScoredProperty">297000</psk:MediaSizeHeight>
            <psk:MediaSizeWidth xsi:type="xsd:integer" psf2:psftype="ScoredProperty">210000</psk:MediaSizeWidth>
        </psk:ISOA4>
        <psk:NorthAmericaLetter psf2:psftype="Option" psf2:default="true">
            <psk12:PortraitImageableSize xsi:type="psf2:ImageableAreaType" psf2:psftype="Property">0,0,215900,279400</psk12:PortraitImageableSize>
            <psk:MediaSizeHeight xsi:type="xsd:integer" psf2:psftype="ScoredProperty">279400</psk:MediaSizeHeight>
            <psk:MediaSizeWidth xsi:type="xsd:integer" psf2:psftype="ScoredProperty">215900</psk:MediaSizeWidth>
        </psk:NorthAmericaLetter>
    </psk:PageMediaSize>

    <psk:PageOrientation psf2:psftype="Feature">
        <psk:Landscape psf2:psftype="Option"/>
        <psk:Portrait psf2:psftype="Option" psf2:default="true"/>
    </psk:PageOrientation>

    <psk:JobInputBin psf2:psftype="Feature">
        <psk:AutoSelect psf2:psftype="Option">
            <psk:FeedDirection xsi:type="xsd:QName" psf2:psftype="Property">psk:ShortEdgeFirst</psk:FeedDirection>
        </psk:AutoSelect>
    </psk:JobInputBin>

    <psk:PageOutputColor psf2:psftype="Feature">
        <psk:Color psf2:psftype="Option" psf2:default="true">
            <psk:DeviceBitsPerPixel xsi:type="xsd:integer" psf2:psftype="ScoredProperty">24</psk:DeviceBitsPerPixel>
            <psk:DriverBitsPerPixel xsi:type="xsd:integer" psf2:psftype="ScoredProperty">24</psk:DriverBitsPerPixel>
        </psk:Color>
    </psk:PageOutputColor>

    <DocumentFormatOptions>
        <PwgRasterDocumentTypesSupported>
            <PwgRasterDocumentType psf2:PageOutputColor="psk:Color">Srgb_8</PwgRasterDocumentType>
        </PwgRasterDocumentTypesSupported>
    </DocumentFormatOptions>

</PrintDeviceCapabilities>
