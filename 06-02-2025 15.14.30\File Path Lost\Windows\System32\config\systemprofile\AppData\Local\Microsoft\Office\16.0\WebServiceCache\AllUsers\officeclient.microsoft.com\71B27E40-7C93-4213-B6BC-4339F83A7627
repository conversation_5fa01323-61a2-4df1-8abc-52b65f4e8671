<?xml version="1.0" encoding="utf-8"?>
<o:OfficeConfig xmlns:o="urn:schemas-microsoft-com:office:office">
  <o:services o:GenerationTime="2025-04-09T18:50:33">
    <!--Build: 16.0.18413.40127-->
    <o:default>
      <o:ticket o:headerName="Authorization" o:headerValue="{}" />
    </o:default>
    <o:service o:name="Research">
      <o:url>https://word-edit.officeapps.live.com/we/rrdiscovery.ashx</o:url>
    </o:service>
    <o:service o:name="ORedir">
      <o:url>https://o15.officeredir.microsoft.com/r</o:url>
    </o:service>
    <o:service o:name="ORedirSSL">
      <o:url>https://o15.officeredir.microsoft.com/r</o:url>
    </o:service>
    <o:service o:name="ClViewClientHelpId" o:authentication="1">
      <o:url>https://[MAX.BaseHost]/client/results?fullframe=yes</o:url>
      <o:ticket o:policy="DELEGATION" o:idprovider="1" o:target="[MAX.AuthHost]" o:headerValue="Bearer {}" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[MAX.ResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ClViewClientHome" o:authentication="1">
      <o:url>https://[MAX.BaseHost]/client/results?fullframe=yes</o:url>
      <o:ticket o:policy="DELEGATION" o:idprovider="1" o:target="[MAX.AuthHost]" o:headerValue="Bearer {}" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[MAX.ResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ClViewClientTemplate">
      <o:url>https://ocsa.office.microsoft.com/client/15/help/template</o:url>
    </o:service>
    <o:service o:name="ClViewClientSearch" o:authentication="1">
      <o:url>https://[MAX.BaseHost]/client/results?fullframe=yes</o:url>
      <o:ticket o:policy="DELEGATION" o:idprovider="1" o:target="[MAX.AuthHost]" o:headerValue="Bearer {}" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[MAX.ResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ClViewClientSearchRedir" o:authentication="1">
      <o:url>https://support.office.microsoft.com/client/results</o:url>
      <o:ticket o:policy="DELEGATION" o:idprovider="1" o:target="[MAX.AuthHost]" o:headerValue="Bearer {}" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[MAX.ResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ClViewClientUpdate">
      <o:url>https://ocsa.office.microsoft.com/client/15/help/clvupd</o:url>
    </o:service>
    <o:service o:name="HelpContactSupport" o:authentication="1">
      <o:url>https://support.microsoft.com/ems/clients/inapp</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="prod.emerald.microsoft.com" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="59929c62-edde-413a-b308-a5057f3d1cb5" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MruDocuments" o:authentication="2">
      <o:url>https://ocws.[OSI.BaseHost]/ocs/docs/recent</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MruPlaces" o:authentication="2">
      <o:url>https://ocws.[OSI.BaseHost]/ocs/locations/recent</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MruV2Service" o:authentication="2">
      <o:url>https://ocws.[OSI.BaseHost]/ocs/v2/recent</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MruDocumentsSharedWithMe" o:authentication="2">
      <o:url>https://ocws.[OSI.BaseHost]/ocs/docs/sharedwithme</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MruDocumentsSharedWithMeV2" o:authentication="2">
      <o:url>https://ocws.[OSI.BaseHost]/ocs/docs/v2.0/sharedwithme</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MruQuickaccessLocationsAndJoinedTeams" o:authentication="2">
      <o:url>https://ocws.[OSI.BaseHost]/ocs/quickaccess/sitesandteams</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneDriveLiveUrl">
      <o:url>https://onedrive.live.com</o:url>
    </o:service>
    <o:service o:name="OneDriveLogUploadService" o:authentication="2">
      <o:url>https://storage.live.com/clientlogs/uploadlocation</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://onedrive.live.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ODCInsertMedia" o:authentication="1">
      <o:url>https://insertmedia.bing.office.net/odc/insertmedia</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="InsertMediaTelemetry">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/api/telemetry</o:url>
    </o:service>
    <o:service o:name="OfficeOnlineContent" o:authentication="1">
      <o:url>https://insertmedia.bing.office.net/images/officeonlinecontent/browse?cp=Bing</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentClipArt">
      <o:url>https://insertmedia.bing.office.net/images/officeonlinecontent/browse?cp=ClipArt</o:url>
    </o:service>
    <o:service o:name="OfficeOnlineContentFb" o:authentication="1">
      <o:url>https://insertmedia.bing.office.net/images/officeonlinecontent/browse?cp=Facebook</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentFl" o:authentication="1">
      <o:url>https://insertmedia.bing.office.net/images/officeonlinecontent/browse?cp=Flickr</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentIcons">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/microsofticon?</o:url>
    </o:service>
    <o:service o:name="OfficeOnlineContentM365Icons">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/microsoftcontent?initpivot=icons</o:url>
    </o:service>
    <o:service o:name="OfficeOnlineContentM365Images">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/microsoftcontent?initpivot=stockimages</o:url>
    </o:service>
    <o:service o:name="OfficeOnlineContentM365Videos">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/microsoftcontent?initpivot=stockvideos</o:url>
    </o:service>
    <o:service o:name="OfficeOnlineContentMobilePictures" o:authentication="1">
      <o:url>https://hubblecontent.[OSI.ONETBaseHost]/contentsvc/sharedfilepicker</o:url>
    </o:service>
    <o:service o:name="OfficeOnlineContentImages" o:authentication="1">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/browse?secureurl=1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentOD" o:authentication="1">
      <o:url>https://insertmedia.bing.office.net/images/officeonlinecontent/browse?cp=OneDrive</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentOEmbed" o:authentication="1">
      <o:url>https://hubble.[OSI.BaseHost]/mediasvc/api/media/oembed</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://hubble.[OSI.BaseHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentGetVideoProviderList" o:authentication="1">
      <o:url>https://hubble.[OSI.BaseHost]/mediasvc/api/media/getoembedproviders?type=video&amp;endpoints=1&amp;displayname=0&amp;providerurl=0&amp;requirements=0</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://hubble.[OSI.BaseHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentVideoHostPage" o:authentication="1">
      <o:url>https://hubblecontent.[OSI.ONETBaseHost]/contentsvc/videohostpage/video</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://hubble.[OSI.BaseHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeOnlineContentVideoPicker" o:authentication="1">
      <o:url>https://hubblecontent.[OSI.ONETBaseHost]/contentsvc/videopicker</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://hubble.[OSI.BaseHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeOnlineContent3DModel" o:authentication="1">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/browse?cp=remix3d</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.live.com" />
    </o:service>
    <o:service o:name="OfficeOnlineContentVideos">
      <o:url>https://insertmedia.[OSI.ONETBaseHost]/insertmedia</o:url>
    </o:service>
    <o:service o:name="HubbleLoggingSvc">
      <o:url>https://hubble.[OSI.BaseHost]/mediasvc/api/media/log</o:url>
    </o:service>
    <o:service o:name="BingInsertMediaImmersive">
      <o:url>https://insertmedia.bing.office.net/images/hosted?host=office&amp;adlt=strict&amp;hostType=ImmersiveApp</o:url>
    </o:service>
    <o:service o:name="HomeRealmDiscovery">
      <o:url>https://odc.[OSI.BaseHost]/odc/v2.1/hrd</o:url>
    </o:service>
    <o:service o:name="GetFederationProvider">
      <o:url>https://odc.[OSI.BaseHost]/odc/v2.1/federationProvider</o:url>
    </o:service>
    <o:service o:name="GetIdentityProvider">
      <o:url>https://odc.[OSI.BaseHost]/odc/v2.1/idp</o:url>
    </o:service>
    <o:service o:name="ODCServicesCatalog">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/catalog</o:url>
    </o:service>
    <o:service o:name="ODCSites" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/v{0}/sites</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="ODCUserConnectedServices" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/userconnected</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="ODCManageServiceRedir" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/manageserviceredir.aspx</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" o:headerName="X-Office-Auth-ODCSM" />
    </o:service>
    <o:service o:name="ODCSMAddRepairService" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/liveredir</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" o:headerName="X-Office-Auth-ODCSM" />
    </o:service>
    <o:service o:name="ODCSMManageService" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/liveredir</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" o:parameterName="t" />
    </o:service>
    <o:service o:name="ODCSMServiceAdded" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/serviceadd</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="ODCSMServiceDetails" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/servicedetails</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="ODCSMServiceRemoved" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/serviceremove</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="ODCSMReportServiceError" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/servicemanager/reportserviceerror</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="ODCClientDeveloperHelp">
      <o:url>https://odc.[OSI.BaseHost]/odc/help/clientdeveloper</o:url>
    </o:service>
    <o:service o:name="DSIRoamingSettings" o:authentication="2">
      <o:url>https://roaming.[OSI.BaseHost]/rs/RoamingSoapService.svc</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="RoamingSettings" o:authentication="2">
      <o:url>https://roaming.[OSI.BaseHost]/rs/v1/settings</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeLicensingService15" o:authentication="2">
      <o:url>https://ols.[OSI.BaseHost]/olsc/OlsClient.svc/OlsClient</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:idprovider="3" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeLicensingServiceGlobal">
      <o:url>https://ols.[OSI.BaseHost]/olsc/OlsClient.svc/OlsClient</o:url>
    </o:service>
    <o:service o:name="LicensingRestHost" o:authentication="2">
      <o:url>https://ols.[OSI.BaseHost]/</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:idprovider="3" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeAddInClassifierOfficeEntities">
      <o:url>https://omex.cdn.office.net/addinclassifier/officeentities</o:url>
    </o:service>
    <o:service o:name="OfficeAddInClassifierOfficeEntitiesUpdated">
      <o:url>https://omex.cdn.office.net/addinclassifier/officeentitiesupdated</o:url>
    </o:service>
    <o:service o:name="OfficeAddInClassifierOfficeSharedEntities">
      <o:url>https://omex.cdn.office.net/addinclassifier/officesharedentities</o:url>
    </o:service>
    <o:service o:name="OfficeAddInClassifierOfficeSharedEntitiesUpdated">
      <o:url>https://omex.cdn.office.net/addinclassifier/officesharedentitiesupdated</o:url>
    </o:service>
    <o:service o:name="LumosHost">
      <o:url>https://oloobe.[OSI.BaseHost]/</o:url>
    </o:service>
    <o:service o:name="OfficeHomeStoreCatalogEndpoint">
      <o:url>https://displaycatalog.mp.microsoft.com/v7.0/products/CFQ7TTC0K5DM</o:url>
    </o:service>
    <o:service o:name="OfficeSoloStoreCatalogEndpoint">
      <o:url>https://displaycatalog.mp.microsoft.com/v7.0/products/CFQ7TTC0K5BC</o:url>
    </o:service>
    <o:service o:name="RevereActivities" o:authentication="2">
      <o:url>https://revere.[OSI.ONETBaseHost]/api/v{0}/documents/{1}</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="RMSOnline" o:authentication="2">
      <o:url>-</o:url>
      <o:ticket o:policy="MCMBI" o:idprovider="2" o:target="[RMSOnline.BaseHost]" />
      <o:ticket o:idprovider="3" o:resourceId="https://api.aadrm.com/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="BroadcastService" o:authentication="1">
      <o:url>https://broadcast.[OSI.BaseHost]/m/broadcasthost.asmx</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="AccessNounDefinitionsB2">
      <o:url>https://support.content.office.microsoft.com/en-us/static/AF102833395.xml</o:url>
    </o:service>
    <o:service o:name="AccessNounDefinitionsRTM">
      <o:url>https://support.content.office.microsoft.com/en-us/static/AF102833403.xml</o:url>
    </o:service>
    <o:service o:name="LiveOAuthLoginBase">
      <o:url>https://login.[Live.WebHost]</o:url>
    </o:service>
    <o:service o:name="LiveOAuthLoginEnd">
      <o:url>https://login.[Live.WebHost]/oauth20_desktop.srf</o:url>
    </o:service>
    <o:service o:name="LiveOAuthLoginError">
      <o:url>https://login.[Live.WebHost]/err.srf</o:url>
    </o:service>
    <o:service o:name="LiveOAuthSignOut">
      <o:url>https://login.[Live.WebHost]/logout.srf</o:url>
    </o:service>
    <o:service o:name="LiveOAuthGetToken">
      <o:url>https://login.[Live.WebHost]/oauth20_token.srf</o:url>
    </o:service>
    <o:service o:name="OneDriveNotificationService" o:authentication="1">
      <o:url>https://skyapi.live.net/Activity/{0}</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerValue="WLID1.1 {}" />
    </o:service>
    <o:service o:name="OneDriveBusinessNotificationService" o:authentication="1">
      <o:url>https://api.onedrive.com</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneDriveConsumerApi" o:authentication="2">
      <o:url>https://d.docs.live.net</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="onedrivemobile.[Live.WebHost]" />
    </o:service>
    <o:service o:name="OneDrivePublicApi" o:authentication="2">
      <o:url>https://[OneDriveApi.BaseHost]/v1.0/drive/root</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneDrivePublicApiRoot" o:authentication="2">
      <o:url>https://[OneDriveApi.BaseHost]/v1.0</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SkyDriveSkyDocs" o:authentication="2">
      <o:url>https://[Live.DocumentAPIHost]/SkyDocsService.svc</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
    </o:service>
    <o:service o:name="SkyDriveSharing" o:authentication="2">
      <o:url>https://[Live.DocumentAPIHost]/SharingService.svc</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="SkyDriveSyncClientUpsell">
      <o:url>https://odc.officeapps.live.com/odc/xml?resource=OneDriveSyncClientUpsell</o:url>
    </o:service>
    <o:service o:name="LiveProfileService" o:authentication="2">
      <o:url>https://directory.services.[Live.WebHost]/profile/Profile.asmx</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="LiveProfileServicesGetInfo" o:authentication="2">
      <o:url>https://pf.directory.live.com/profile/mine/WLX.Profiles.IC.json</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerName="PS-MSAAuthTicket" />
    </o:service>
    <o:service o:name="LiveProfileServicesGetPhoneNumber" o:authentication="2">
      <o:url>https://pf.directory.live.com/profile/mine/System.ShortCircuitProfile.json</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="people.directory.[Live.WebHost]" o:headerName="PS-MSAAuthTicket" />
    </o:service>
    <o:service o:name="LiveCreateProfile" o:authentication="1">
      <o:url>https://profile.[Live.WebHost]/cid-%s/</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="PreinstalledAppsQuery" o:authentication="1">
      <o:url>https://addinsinstallation.store.office.com/appinstall/preinstalled</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" />
    </o:service>
    <o:service o:name="LiveEditProfile" o:authentication="1">
      <o:url>https://profile.[Live.WebHost]/home</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="LiveContacts" o:authentication="1">
      <o:url>https://contacts.[MSN.WebHost]/ABService/ABService.asmx</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
    </o:service>
    <o:service o:name="OCSettingsUrl" o:authentication="2">
      <o:url>https://clients.config.office.net/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://clients.config.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OCSettingsTenantAssociationUrl" o:authentication="2">
      <o:url>https://clients.config.office.net/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://clients.config.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OCSettingsTenantAssociationFullUrl" o:authentication="2">
      <o:url>https://clients.config.office.net/user/v1.0/tenantassociationkey</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://clients.config.office.net/user/v1.0/tenantassociationkey" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OCSettingsCloudPolicyServiceUrl" o:authentication="2">
      <o:url>https://clients.config.office.net/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://clients.config.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OCSettingsCloudPolicyServiceMacUrl" o:authentication="2">
      <o:url>https://clients.config.office.net/user/v1.0/mac</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://clients.config.office.net/user/v1.0/mac" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OCSettingsCloudPolicyServiceIOSUrl" o:authentication="2">
      <o:url>https://clients.config.office.net/user/v1.0/ios</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://clients.config.office.net/user/v1.0/ios" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ShellService" o:authentication="1">
      <o:url>https://webshell.suite.office.com</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://webshell.suite.office.com" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OCSettingsCloudPolicyServiceAndroidUrl" o:authentication="2">
      <o:url>https://clients.config.office.net/user/v1.0/android/policies</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://clients.config.office.net/user/v1.0/android/policies" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="SkyDriveSignUpUpsell">
      <o:url>https://odc.officeapps.live.com/odc/xml?resource=OneDriveSignUpUpsell</o:url>
    </o:service>
    <o:service o:name="SkyDriveSignUpUpsellImage">
      <o:url>https://odc.officeapps.live.com/odc/stat/images/OneDriveUpsell.png</o:url>
    </o:service>
    <o:service o:name="OutlookConnectorManifest">
      <o:url>https://support.content.office.microsoft.com/en-us/static/AF102819889.xml</o:url>
    </o:service>
    <o:service o:name="LiveIdFederatedToOrgIdForDesktop">
      <o:url>-</o:url>
      <o:ticket o:policy="MBI_FED_SSL_C14N" o:idprovider="1" o:target="[Live.FederatedAuthHost]" />
    </o:service>
    <o:service o:name="LiveIdFederatedToOrgIdForImmersive">
      <o:url>-</o:url>
      <o:ticket o:policy="LBI_FED_STS_CLEAR" o:idprovider="1" o:target="[Live.FederatedAuthHost]" />
    </o:service>
    <o:service o:name="NLGEnglishAssistance">
      <o:url>https://ssl.bing.com/dict/?view=officemoe&amp;ulang=zh-cn&amp;tlang=en-us</o:url>
    </o:service>
    <o:service o:name="NLGEnglishAssistanceIcon">
      <o:url>https://ssl.bing.com/dict/img/BingDict_E2C.PNG</o:url>
    </o:service>
    <o:service o:name="CISMobileAssociation">
      <o:url>https://partnerservices.getmicrosoftkey.com/PartnerProvisioning.svc/v1/subscriptions</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="partnerservices.getmicrosoftkey.com" />
    </o:service>
    <o:service o:name="CISRedemptionService">
      <o:url>https://pbsub.microsoft.com/RedemptionService/v1/redeem</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="redemptionservices.getmicrosoftkey.com" />
    </o:service>
    <o:service o:name="Nexus">
      <o:url>https://nexus.[OSI.BaseHost]</o:url>
    </o:service>
    <o:service o:name="NexusRules">
      <o:url>https://nexusrules.[OSI.BaseHost]</o:url>
    </o:service>
    <o:service o:name="LiveRestAPI">
      <o:url>https://apis.live.net/v5.0/</o:url>
    </o:service>
    <o:service o:name="FontService">
      <o:url>https://fs.microsoft.com/fs/1.3</o:url>
    </o:service>
    <o:service o:name="FontService2">
      <o:url>https://fs.microsoft.com/fs/2.2</o:url>
    </o:service>
    <o:service o:name="FontService1cdnEndpoint">
      <o:url>https://res.cdn.office.net/mro1cdnstorage/fonts/prod/4.41</o:url>
    </o:service>
    <o:service o:name="ResourceServiceEndpoint2">
      <o:url>https://fs.microsoft.com/fs/4.41</o:url>
    </o:service>
    <o:service o:name="ResourceServiceiOS2">
      <o:url>https://fs.microsoft.com/fs/iOS/2.8</o:url>
    </o:service>
    <o:service o:name="ResourcesOnDemandEndpoint">
      <o:url>https://officecdn.microsoft.com/pr/uiraas/ios/</o:url>
    </o:service>
    <o:service o:name="ProofingDownloadServiceEndpoint">
      <o:url>https://fs.microsoft.com/fs/2.6</o:url>
    </o:service>
    <o:service o:name="ProofingDownloadServiceV2_7">
      <o:url>https://fs.microsoft.com/fs/lexicons/2.7</o:url>
    </o:service>
    <o:service o:name="ProtectionSDKEndpoint">
      <o:url>https://api.aadrm.com</o:url>
    </o:service>
    <o:service o:name="MipSdkProtectionService">
      <o:url>https://api.aadrm.com</o:url>
    </o:service>
    <o:service o:name="MipSdkOneDSCollector">
      <o:url>https://mobile.events.data.microsoft.com/OneCollector/1.0/</o:url>
    </o:service>
    <o:service o:name="OArtResourceServiceEndpoint">
      <o:url>https://fs.microsoft.com/fs/oart/1.5</o:url>
    </o:service>
    <o:service o:name="CustomFontServiceEndpoint">
      <o:url>https://cr.office.com</o:url>
    </o:service>
    <o:service o:name="OArtModel3DResourceServiceEndpoint">
      <o:url>https://fs.microsoft.com/fs/oart/model3d/res/1.0</o:url>
    </o:service>
    <o:service o:name="OArtTeachingPaneResourceServiceEndpoint">
      <o:url>https://fs.microsoft.com/fs/TeachingPane/1.0</o:url>
    </o:service>
    <o:service o:name="ExcelResourceServiceEndpoint">
      <o:url>https://fs.microsoft.com/fs/ExcelResources/1.2</o:url>
    </o:service>
    <o:service o:name="ExcelRichValueIcons">
      <o:url>https://fs.microsoft.com/fs/RichValueIcons/3.29</o:url>
    </o:service>
    <o:service o:name="OutlookFlighting">
      <o:url>http://olkflt.edog.officeapps.live.com/olkflt/outlookflighting.svc/api/glides</o:url>
    </o:service>
    <o:service o:name="OutlookWeatherMSN">
      <o:url>http://weather.service.msn.com/data.aspx</o:url>
    </o:service>
    <o:service o:name="SSExcelCS">
      <o:url>https://excelcs.[OSI.BaseHost]/xlauto/excelautomation.svc/XlAutomation</o:url>
    </o:service>
    <o:service o:name="SSExcelCSREST">
      <o:url>https://excelcs.[OSI.BaseHost]/xlauto/excelautomation.svc/rest</o:url>
    </o:service>
    <o:service o:name="SSExcelPS">
      <o:url>https://excelcs.[OSI.BaseHost]/exlps/excelprint.svc/exlPrint</o:url>
    </o:service>
    <o:service o:name="SSPPTCS">
      <o:url>https://pptcs.[OSI.BaseHost]/pptauto/PowerpointAutomation.svc/PptAutomation</o:url>
    </o:service>
    <o:service o:name="SSPPTCSREST">
      <o:url>https://pptcs.[OSI.BaseHost]/pptauto/PowerpointAutomation.svc/rest</o:url>
    </o:service>
    <o:service o:name="SSPPTOFS">
      <o:url>https://pptsgs.[OSI.BaseHost]/pptsgs/PowerpointSuggestion.svc/OutlineToPPT/Trace</o:url>
    </o:service>
    <o:service o:name="SSPPTOSGS">
      <o:url>https://pptsgs.[OSI.BaseHost]/pptsgs/PowerpointSuggestion.svc/OutlineToPPT/GetSuggestions</o:url>
    </o:service>
    <o:service o:name="SSPPTOTS">
      <o:url>https://pptsgs.[OSI.BaseHost]/pptsgs/PowerpointSuggestion.svc/OutlineToPPT/GetThemeSuggestions</o:url>
    </o:service>
    <o:service o:name="SSPPTPS">
      <o:url>https://pptcs.[OSI.BaseHost]/pptps/powerpointprint.svc/PptPrint</o:url>
    </o:service>
    <o:service o:name="SSPPTSS">
      <o:url>https://pptss.[OSI.BaseHost]/pptss/powerpointsample.svc/PptSample</o:url>
    </o:service>
    <o:service o:name="SSPPTSGS">
      <o:url>https://pptsgs.[OSI.BaseHost]/pptsgs/PowerpointSuggestion.svc/PptSuggestion</o:url>
    </o:service>
    <o:service o:name="StreamAPI">
      <o:url>https://api.microsoftstream.com/api/</o:url>
    </o:service>
    <o:service o:name="StreamVideoBase">
      <o:url>https://web.microsoftstream.com/video/</o:url>
    </o:service>
    <o:service o:name="SSWordCS">
      <o:url>https://wordcs.[OSI.BaseHost]/wordauto/wordautomation.svc/wordautomation</o:url>
    </o:service>
    <o:service o:name="SSWordCSREST">
      <o:url>https://wordcs.[OSI.BaseHost]/wordauto/wordautomation.svc/rest</o:url>
    </o:service>
    <o:service o:name="SSWordPS">
      <o:url>https://wordcs.[OSI.BaseHost]/wrdps/wordprint.svc/wrdprint</o:url>
    </o:service>
    <o:service o:name="PowerBI" o:authentication="2">
      <o:url>https://api.powerbi.com/v1.0/myorg/imports</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALPBIResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="PowerBIGroupsApi" o:authentication="2">
      <o:url>https://api.powerbi.com/v1.0/myorg/groups</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALPBIResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="AzureMachineLearningEndpointServiceUrl">
      <o:url>https://management.azure.com/</o:url>
    </o:service>
    <o:service o:name="BingGeospatialEndpointServiceUrl">
      <o:url>https://dev.virtualearth.net/REST/V1/GeospatialEndpoint/</o:url>
    </o:service>
    <o:service o:name="BingSpellerServiceUrl">
      <o:url>https://www.bing.com/api/v4/spelling/proof</o:url>
    </o:service>
    <o:service o:name="NLProofingServiceUrl">
      <o:url>https://nleditor.[OSI.ONETBaseHost]/NlEditor/CloudSuggest/V1</o:url>
    </o:service>
    <o:service o:name="PolicyTipsSyncService" o:authentication="2">
      <o:url>https://dataservice.protection.outlook.com/PolicySync/PolicySync.svc/SyncFile</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="DLPGraphApi" o:authentication="2">
      <o:url>https://graph.windows.net/</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://graph.windows.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="DLPGraphApiPPE" o:authentication="2">
      <o:url>https://graph.ppe.windows.net/</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://graph.ppe.windows.net" o:authorityUrl="[ADALPPEAuthorityUrl]" />
    </o:service>
    <o:service o:name="PolicyTipsSyncServicePPE" o:authentication="2">
      <o:url>https://dataservice.o365filtering.com/PolicySync/PolicySync.svc/SyncFile</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com" o:authorityUrl="[ADALPPEAuthorityUrl]" />
    </o:service>
    <o:service o:name="InsightsDesktop">
      <o:url>https://uci.officeapps.live.com/OfficeInsights/web/views/insights.desktop.html</o:url>
    </o:service>
    <o:service o:name="InsightsImmersive">
      <o:url>https://uci.officeapps.live.com/OfficeInsights/web/views/insights.immersive.html</o:url>
    </o:service>
    <o:service o:name="InsightsService" o:authentication="2">
      <o:url>http://insights.microsoft.com:8799</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="api://insights.microsoft.com:8799/918dae65-7fb5-4a7f-928a-0161d75d2db8" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="DSCRedemptionService">
      <o:url>https://res.getmicrosoftkey.com/api/redemptionevents</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="https://rpsticket.partnerservices.getmicrosoftkey.com" />
    </o:service>
    <o:service o:name="ACSTokenRedemption">
      <o:url>https://posarprodcssservice.accesscontrol.windows.net/v2/OAuth2-13</o:url>
    </o:service>
    <o:service o:name="IdentityService">
      <o:url>https://identity.[OSI.ONETBaseHost]/v1/token</o:url>
    </o:service>
    <o:service o:name="DSCRedemptionConfig">
      <o:url>https://ols.[OSI.BaseHost]/olsc/olsconfig.svc/redemption/locales</o:url>
    </o:service>
    <o:service o:name="OfficeEntityBaseUrl">
      <o:url>https://entity.[OSI.ONETBaseHost]/</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="https://[OSI.ONETBaseHost]" />
      <o:ticket o:idprovider="3" o:resourceId="https://entity.[OSI.ONETBaseHost]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OfficeEntityResearchUrl">
      <o:url>https://entity.[OSI.ONETBaseHost]/</o:url>
    </o:service>
    <o:service o:name="OfficeEntityMSATarget">
      <o:url>https://[OSI.ONETBaseHost]</o:url>
    </o:service>
    <o:service o:name="OfficeEntity">
      <o:url>https://cdn.entity.[OSI.ONETBaseHost]/OfficeEntity/web/views/juno.desktop.cshtml</o:url>
    </o:service>
    <o:service o:name="OfficeEntityMac">
      <o:url>https://cdn.entity.[OSI.ONETBaseHost]/OfficeEntity/web/views/juno.mac.cshtml</o:url>
    </o:service>
    <o:service o:name="RedemptionFlightingBucket">
      <o:url>https://ols.[OSI.BaseHost]/olsc/olsconfig.svc/redemption/flighting/</o:url>
    </o:service>
    <o:service o:name="MyAccount">
      <o:url>https://go.microsoft.com/fwlink/?LinkID=808147</o:url>
    </o:service>
    <o:service o:name="VoiceRecognitionServiceBaseUrl" o:authentication="2">
      <o:url>https://cortana.ai</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://cortana.ai" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="VoiceRecognitionServiceApiUrl" o:authentication="2">
      <o:url>https://cortana.ai/api</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://cortana.ai/api" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="VoiceRecognitionServiceDevUrl" o:authentication="2">
      <o:url>https://dev.cortana.ai</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dev.cortana.ai" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="VoiceRecognitionServiceStagingUrl" o:authentication="2">
      <o:url>https://staging.cortana.ai</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://staging.cortana.ai" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="VoiceRecognitionServiceProdUrl" o:authentication="2">
      <o:url>https://api.cortana.ai</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.cortana.ai" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="SubstrateDashBaseUrl" o:authentication="2">
      <o:url>https://api.diagnostics.office.com</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.diagnostics.office.com" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV2InitBaseUrl" o:authentication="2">
      <o:url>https://substrate.office.com/search/api/v2/init</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV1SearchHistoryBaseUrl" o:authentication="2">
      <o:url>https://substrate.office.com/search/api/v1/SearchHistory</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="AugloopUrl" o:authentication="2">
      <o:url>https://augloop.office.com/v2</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://augloop.office.com/v2" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="WhatsNewContent">
      <o:url>https://support.content.office.net/whatsnew</o:url>
    </o:service>
    <o:service o:name="OfficeHubSubscription" o:authentication="2">
      <o:url>https://[OMEX.BaseCoSubHost]/myaccount/api/account.svc/officehub</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" />
    </o:service>
    <o:service o:name="OfficeHubCarouselSubscription" o:authentication="2">
      <o:url>https://[OMEX.BaseCoSubHost]/myaccount/api/account.svc/subscription</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" />
    </o:service>
    <o:service o:name="OfficeHubCarouselCdn">
      <o:url>https://contentstorage.[OSI.ONETBaseHost]/getofficecarouselcore/index.html</o:url>
    </o:service>
    <o:service o:name="MsGraphBase">
      <o:url>https://[MsGraphBaseURL]/</o:url>
    </o:service>
    <o:service o:name="MsGraphClassifyTextURL" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphDcsVersion]/dataClassification/classifyText</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphEndpoint" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]</o:url>
      <o:ticket o:policy="DELEGATION" o:idprovider="1" o:target="https://[MsGraphBaseURL]/.default" o:headerValue="{}" />
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphGetCalendarview" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/me/calendarview/</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphGetOutlookMeetingAttachments" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/me/events/{0}/attachments</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphGetOrganization" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/organization</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphPeopleSearch" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/me/people</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphListTeamChannelsURL" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphTeamsVersion]/teams/{0}/channels</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphGetTeamChannelURL" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphTeamsVersion]/teams/{0}/channels/{1}</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphGetPhoto" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/me/photo/%24value</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphDriveSearch" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/me/drive/search%28q%3D%27{0}%27%29?select%3Dname%2Cid%2CwebDavUrl%2Csize%2CcreatedDateTime%2ClastModifiedDateTime</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphGetSignIn" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/auditlogs/signins</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphLabelEvaluateURL" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphDcsVersion]/users/{0}/informationProtection/sensitivityLabels/evaluate</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MsGraphGetUser" o:authentication="2">
      <o:url>https://[MsGraphBaseURL]/[MsGraphVersion]/users</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[MsGraphBaseURL]/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="GraphImportUrl">
      <o:url>https://shredder.[OSI.ONETBaseHost]/ShredderService/web/desktop/views/main.cshtml</o:url>
    </o:service>
    <o:service o:name="EnrichmentUrl">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/web/view/desktop/main.cshtml</o:url>
    </o:service>
    <o:service o:name="EnrichmentDisambiguateUrl" o:authentication="1">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/Resolve/v1</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://enrichment.osi.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="osi.office.net" o:headerName="Authorization" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
    </o:service>
    <o:service o:name="EnrichmentBloomFilterBaseUrl">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/web/Metadata/</o:url>
    </o:service>
    <o:service o:name="EnrichmentMetadataUrl" o:authentication="1">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/web/Metadata/metadata.json</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://enrichment.osi.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="osi.office.net" o:headerName="Authorization" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
    </o:service>
    <o:service o:name="EnrichmentRefreshUrl" o:authentication="1">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/Refresh/v1</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://enrichment.osi.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="osi.office.net" o:headerName="Authorization" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
    </o:service>
    <o:service o:name="EnrichmentReportContentUrl">
      <o:url>https://petrol.office.microsoft.com/v1/feedback</o:url>
    </o:service>
    <o:service o:name="EnrichmentSearchUrl" o:authentication="1">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/Search/v1</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://enrichment.osi.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="osi.office.net" o:headerName="Authorization" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
    </o:service>
    <o:service o:name="EnrichmentHandshakeBaseUrl" o:authentication="1">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/v2.1601652342626</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://enrichment.osi.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="osi.office.net" o:headerName="Authorization" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
    </o:service>
    <o:service o:name="EnrichmentIPCheckUrl">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/ipcheck/v1</o:url>
    </o:service>
    <o:service o:name="EnrichmentWACUrl">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/web/view/web/main.cshtml</o:url>
    </o:service>
    <o:service o:name="EnrichmentStockHistoryUrl" o:authentication="1">
      <o:url>https://enrichment.osi.office.net/OfficeEnrichment/StockHistory/v1</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://enrichment.osi.office.net/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="osi.office.net" o:headerName="Authorization" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
    </o:service>
    <o:service o:name="UrlReputationService" o:authentication="2">
      <o:url>https://safelinks.protection.outlook.com/api/GetPolicy</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="AADGraphGetMe" o:disabled="1" o:authentication="2">
      <o:url>https://[AADGraphBaseURL]/me?api-version=1.6</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://[AADGraphBaseURL]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="PinValidation">
      <o:url>https://ols.[OSI.BaseHost]/olsc/olsconfig.svc/pin/v2/</o:url>
    </o:service>
    <o:service o:name="PinValidationNoVersion">
      <o:url>https://ols.[OSI.BaseHost]/olsc/olsconfig.svc/pin/</o:url>
    </o:service>
    <o:service o:name="SfBAutoDiscover">
      <o:url>https://webdir.online.lync.com/autodiscover/autodiscoverservice.svc/root/</o:url>
    </o:service>
    <o:service o:name="ExchangeAutoDiscover">
      <o:url>https://autodiscover-s.outlook.com/autodiscover/autodiscover.xml</o:url>
    </o:service>
    <o:service o:name="ExchangeBase">
      <o:url>https://[EXO.BaseHost]/</o:url>
    </o:service>
    <o:service o:name="ExchangeWebService" o:authentication="2">
      <o:url>https://[EXO.BaseHost]/ews/exchange.asmx</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[EXO.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="FormsODataApi" o:authentication="2">
      <o:url>https://[Forms.BaseHost]/formapi/api/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="c9a559d2-7aab-4f13-a6ed-e7e9c52aec87" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[Forms.AuthHost]" />
    </o:service>
    <o:service o:name="MicrosoftFormsODataApi" o:authentication="2">
      <o:url>https://[MicrosoftForms.BaseHost]/formapi/api/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="c9a559d2-7aab-4f13-a6ed-e7e9c52aec87" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="FormsDesignPage">
      <o:url>https://[Forms.BaseHost]/Pages/DesignPage.aspx</o:url>
    </o:service>
    <o:service o:name="FormsMobileDesignPage">
      <o:url>https://[Forms.BaseHost]/Pages/DesignPageV2.aspx</o:url>
    </o:service>
    <o:service o:name="FormsMobileHostCreateURL">
      <o:url>https://[Forms.BaseHost]/Pages/DesignPageV2.aspx?lang={0}&amp;Host={1}#Action=Create</o:url>
    </o:service>
    <o:service o:name="StreamWebResource" o:authentication="2">
      <o:url>https://stream.microsoft.com</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="cf53fce8-def6-4aeb-8d30-b158e7b1cf83" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="AugmentationLoopService">
      <o:url>https://augloop.office.com</o:url>
    </o:service>
    <o:service o:name="AugloopPolymer1CdnStorage">
      <o:url>https://res.cdn.office.net/polymer/models</o:url>
    </o:service>
    <o:service o:name="AugloopPolymerMdsService">
      <o:url>https://prod.mds.office.com/mds/api/v1.0/clientmodeldirectory</o:url>
    </o:service>
    <o:service o:name="LinkRequestApiPageTitleRetrieval">
      <o:url>https://uci.[OSI.BaseHost]/OfficeInsights/Insights/v2</o:url>
    </o:service>
    <o:service o:name="PPTCoachRealtimeHandler">
      <o:url>https://voice.[OSI.BaseHost]/coachrealtime.aspx</o:url>
    </o:service>
    <o:service o:name="PPTSpeechHandler">
      <o:url>wss://voice.[OSI.BaseHost]/SpeechHandler.ashx</o:url>
    </o:service>
    <o:service o:name="PPTSpeechCustomHandler">
      <o:url>https://voice.[OSI.BaseHost]/CustomEndpointHandler.ashx</o:url>
    </o:service>
    <o:service o:name="PPTSpeechHttpsHandler">
      <o:url>https://voice.[OSI.BaseHost]/SpeechHandler.ashx</o:url>
    </o:service>
    <o:service o:name="PPTTextTranslationHandler">
      <o:url>https://voice.[OSI.BaseHost]/TextTranslationHandler.ashx</o:url>
    </o:service>
    <o:service o:name="PPTOasisFD">
      <o:url>https://pptsgs.[OSI.BaseHost]/pptsgs/FrontDoor.ashx</o:url>
    </o:service>
    <o:service o:name="WordOasisFD">
      <o:url>https://pptsgs.[OSI.BaseHost]/pptsgs/FrontDoor.ashx</o:url>
    </o:service>
    <o:service o:name="XlOasisFD">
      <o:url>https://excelsgs.[OSI.BaseHost]/xlfrontdoor/FrontDoor.ashx</o:url>
    </o:service>
    <o:service o:name="OMEXInvitesService" o:authentication="2">
      <o:url>https://invites.office.com/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="02830baa-6dd9-4595-9add-fe21304b0301" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OMEXSSOConsentPage" o:authentication="2">
      <o:url>https://[OMEX.BaseHost]/client/consent.aspx</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="c606301c-f764-4e6b-aa45-7caaaea93c9a" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" />
    </o:service>
    <o:service o:name="OMEXSideloadingAddinSSOConsentPage" o:authentication="2">
      <o:url>https://[OMEX.BaseHost]/client/consentsideloading.aspx</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="c606301c-f764-4e6b-aa45-7caaaea93c9a" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" />
    </o:service>
    <o:service o:name="OMEXRemoveAppsAndSsoConsentOrgId" o:authentication="2">
      <o:url>https://addinslicensing.store.office.com/orgid/apps/remove</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="c606301c-f764-4e6b-aa45-7caaaea93c9a" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="IssueServiceSearchURL">
      <o:url>https://issues.office.microsoft.com/api/search</o:url>
    </o:service>
    <o:service o:name="IssueServiceMeTooURL">
      <o:url>https://issues.office.microsoft.com/api/confirmIssue</o:url>
    </o:service>
    <o:service o:name="SubstrateServiceAutoSuggestBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV1EventsBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/events</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV1HistoryBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/searchhistory</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV2QueryBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v2/query</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV2QueryBaseUrlM365Access" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v2/query</o:url>
      <o:ticket o:policy="DELEGATION" o:idprovider="1" o:target="https://[Substrate.AuthHost]/M365.Access" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateSearchServiceAPIV3QueryBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/searchservice/api/v3/query</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateSearchServiceAPIV2InitBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/searchservice/api/v2/init</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV1RecommendationsBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/recommendations</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV1RecommendedDocumentsBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/recommendedDocuments</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateServiceAPIV1SuggestionsBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/suggestions</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateUserConfigService" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/userconfig</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="DynamicCanvasContent">
      <o:url>https://[Growth.CdnBaseHost]/office-growth/resources</o:url>
    </o:service>
    <o:service o:name="DynamicCanvasStaticContent">
      <o:url>https://[Growth.CdnBaseHost]/office-growth/resources/static</o:url>
    </o:service>
    <o:service o:name="TellMeSuggestionsBaseUrl">
      <o:url>https://tellmeservice.[OSI.ONETBaseHost]/tellmeservice/api/suggestions</o:url>
    </o:service>
    <o:service o:name="TellMeAuthenticatedSuggestionsBaseUrl" o:authentication="1">
      <o:url>https://tellmeservice.[OSI.ONETBaseHost]/tellmeservice/api/suggestions</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://tellmeservice.[OSI.ONETBaseHost]" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OneDriveSharesApi" o:authentication="1">
      <o:url>https://[OneDriveApi.BaseHost]/v1.0/shares/</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="InfoProtectSyncService" o:authentication="2">
      <o:url>https://syncservice.protection.outlook.com/PolicySync/PolicySync.svc/SyncFile</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ConsumerMyAccountSubscription" o:authentication="2">
      <o:url>https://[OMEX.BaseCoSubHost]/myaccount/api/account.svc/subscription</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" />
    </o:service>
    <o:service o:name="SubstrateSignalService" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/api/beta/me/Signals</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateSignalServiceV2" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/api/v2.0/me/Signals</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="EditorServiceCheckApiUrl">
      <o:url>https://nleditor.[OSI.ONETBaseHost]/NlEditor/Check/V1</o:url>
    </o:service>
    <o:service o:name="EditorServiceConfigApiUrl">
      <o:url>https://nleditor.[OSI.ONETBaseHost]/NlEditor/Config/V2</o:url>
    </o:service>
    <o:service o:name="EditorServiceInstrumentationApiUrl">
      <o:url>https://nleditor.[OSI.ONETBaseHost]/NlEditor/Instrumentation/V1</o:url>
    </o:service>
    <o:service o:name="EditorServiceLanguageInfoApiUrl">
      <o:url>https://nleditor.[OSI.ONETBaseHost]/NlEditor/LanguageInfo/V1</o:url>
    </o:service>
    <o:service o:name="UAPActivityService" o:authentication="2">
      <o:url>https://outlook.office365.com/api/v1.0/me/Activities</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Outlook.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Outlook.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateAutoDiscoverServiceBaseUrl">
      <o:url>https://outlook.office365.com/autodiscover/autodiscover.json</o:url>
    </o:service>
    <o:service o:name="SubstrateOfficeIntelligenceService" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/OfficeIntelligence/v1.0</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateUserLifecycleProfileService" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/OfficePersonalizationUserLifecycle/api/facts</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="BingSpeechConsumerEndpoint">
      <o:url>wss://speech.platform.bing.com/speech/recognition/dictation/office/v1</o:url>
    </o:service>
    <o:service o:name="BingSpeechCompliantEndpoint">
      <o:url>wss://officespeech.platform.bing.com/speech/recognition/dictation/office/v1</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://sr.outlook.office.net/ws/speech/recognize/assistant/work" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="BingSpeechCompliantEndpointGcc">
      <o:url>wss://officespeech.platform.bing.com/speech/recognition/dictation/office/v1</o:url>
      <o:ticket o:idprovider="3" o:resourceId="https://sr.outlook.office.net/ws/speech/recognize/assistant/work" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="BingSpeechCompliantResource">
      <o:url>https://sr.outlook.office.net/ws/speech/recognize/assistant/work</o:url>
    </o:service>
    <o:service o:name="PowerBIGetDatasetsApi" o:authentication="2">
      <o:url>https://api.powerbi.com/v1.0/myorg/datasets</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALPBIResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="PowerLiftFrontDeskBaseUrl">
      <o:url>https://powerlift-frontdesk.acompli.net</o:url>
    </o:service>
    <o:service o:name="PowerLiftGymBaseUrl">
      <o:url>https://powerlift.acompli.net</o:url>
    </o:service>
    <o:service o:name="SubstrateOfficeIntelligenceInsightsService" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/OfficeIntelligence/v1.0/insights</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateOfficeIntelligenceIngestionService" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/OfficeIntelligence/v1.0/ingestion</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateOfficeFeedDocuments" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v1/recommendedDocuments</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateEdgeworthBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/recommended/api/v1.0/edgeworth</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateOrcaBaseUrl" o:disabled="1" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/orca</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstrateSharingSuggestionBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/sharingsuggestion</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="TeamsAriaCollector">
      <o:url>https://teams.events.data.microsoft.com/Collector/3.0</o:url>
    </o:service>
    <o:service o:name="TeamsAuthEndpoint">
      <o:url>https://[Teams.BaseHost]/api/authsvc/v1.0/authz</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Teams.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="TeamsECSEndpoint">
      <o:url>https://config.teams.microsoft.com</o:url>
    </o:service>
    <o:service o:name="TeamsFEEndpoint">
      <o:url>https://teams.microsoft.com</o:url>
    </o:service>
    <o:service o:name="TeamsOneDSCollector">
      <o:url>https://teams.events.data.microsoft.com/OneCollector/1.0</o:url>
    </o:service>
    <o:service o:name="TeamsSchedulerService">
      <o:url>https://api.scheduler.[Teams.BaseHost]/teams/v1/meetings</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://scheduler.[Teams.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="TeamsMTEndpoint">
      <o:url>https://authsvc.teams.microsoft.com</o:url>
    </o:service>
    <o:service o:name="TeamsUpgradeV2">
      <o:url>https://[Teams.BaseHost]/api/mt/beta/me/settings/teamsupgradev2</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Teams.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="TeamsPresenceService">
      <o:url>https://presence.[Teams.BaseHost]/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://presence.[Teams.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="TeamsPresenceServiceAfD">
      <o:url>https://teams.cloud.microsoft/ups/global/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://presence.[Teams.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="TeamsUserAggregateSettings">
      <o:url>https://[Teams.BaseHost]/api/mt/beta/users/useraggregatesettings</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Teams.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="TeamsDevCenterEndpoint" o:authentication="2">
      <o:url>https://dev.[Teams.BaseHost]</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dev.[Teams.BaseHost]" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="TeamsC2SServiceDiscoveryEndpoint" o:authentication="2">
      <o:url>https://[Teams.BaseHost]/api/authsvc/v1.0/users/region</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Teams.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="FeedbackUploadUrl">
      <o:url>https://sas.office.microsoft.com/upload.ashx</o:url>
    </o:service>
    <o:service o:name="RDCAriaCollectorUrl">
      <o:url>https://mobile.events.data.microsoft.com/Collector/3.0/</o:url>
    </o:service>
    <o:service o:name="AVDClientSettingEndpoints">
      <o:url>https://rdweb.wvd.microsoft.com/</o:url>
    </o:service>
    <o:service o:name="SCTAriaCollectorUri">
      <o:url>https://ic3.events.data.microsoft.com/Collector/3.0/</o:url>
    </o:service>
    <o:service o:name="TelemetryRulesApprovedCDNUrl">
      <o:url>https://otelrules.svc.static.microsoft</o:url>
    </o:service>
    <o:service o:name="TelemetryRulesCDNUrl">
      <o:url>https://otelrules.svc.static.microsoft</o:url>
    </o:service>
    <o:service o:name="EcsExperimentRequestUrl">
      <o:url>https://config.edge.skype.com/config/v1/Office</o:url>
    </o:service>
    <o:service o:name="EcsExperimentRequestUrlV2">
      <o:url>https://config.edge.skype.com/config/v2/Office</o:url>
    </o:service>
    <o:service o:name="EcsExperimentRequestUrlV3">
      <o:url>https://ecs.office.com/config/v2/Office</o:url>
    </o:service>
    <o:service o:name="EdgeExperimentRequestUrl">
      <o:url>https://ocos-office365-s2s.msedge.net/ab</o:url>
    </o:service>
    <o:service o:name="TasExperimentRequestUrl">
      <o:url>https://client-office365-tas.msedge.net/ab</o:url>
    </o:service>
    <o:service o:name="TranslatorService">
      <o:url>https://ogma.osi.office.net/TradukoApi/api/v1.0/</o:url>
    </o:service>
    <o:service o:name="MathSolverService" o:authentication="2">
      <o:url>https://edumathsolver.microsoft.com</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://edumathsolver.microsoft.com" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="MathRecognizerService" o:authentication="2">
      <o:url>https://edumathrecognizer.microsoft.com</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://edumathrecognizer.microsoft.com" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="PlannerAzureBaseUrl">
      <o:url>https://management.azure.com</o:url>
    </o:service>
    <o:service o:name="PlannerBaseUrl">
      <o:url>https://planner.cloud.microsoft</o:url>
    </o:service>
    <o:service o:name="PlannerEcsBaseUrl">
      <o:url>https://ecs.office.com</o:url>
    </o:service>
    <o:service o:name="PlannerGraphBaseUrl">
      <o:url>https://graph.microsoft.com</o:url>
    </o:service>
    <o:service o:name="ProjectGraphBaseUrl">
      <o:url>https://graph.microsoft.com</o:url>
    </o:service>
    <o:service o:name="ProjectLandingPage">
      <o:url>https://portfolio.microsoft.com</o:url>
    </o:service>
    <o:service o:name="XrmAuthorizationUri">
      <o:url>https://login.windows.net/72f988bf-86f1-41af-91ab-2d7cd011db47/oauth2/authorize</o:url>
    </o:service>
    <o:service o:name="XrmGlobalDiscovery">
      <o:url>https://globaldisco.crm.dynamics.com</o:url>
    </o:service>
    <o:service o:name="AriaBrowserPipeUrl">
      <o:url>https://office.pipe.aria.microsoft.com/Collector/3.0/</o:url>
    </o:service>
    <o:service o:name="DSCRedemptionServiceURL">
      <o:url>https://officesetup.getmicrosoftkey.com</o:url>
    </o:service>
    <o:service o:name="SkydocsServiceUrl">
      <o:url>https://d.[Live.DocumentAPIHost]</o:url>
    </o:service>
    <o:service o:name="DSCRedemptionServiceEndpoint">
      <o:url>https://token.cp.microsoft.com/api/redemptions/?subscription-key=f61754ff1afb40b48fa24fbbc2b2b7ba</o:url>
    </o:service>
    <o:service o:name="PrivacyPaneServiceBaseUrl">
      <o:url>https://pptsgs.[OSI.BaseHost]/pptsgs/resources/</o:url>
    </o:service>
    <o:service o:name="AppsForOfficeCDNUrl">
      <o:url>https://appsforoffice.microsoft.com</o:url>
    </o:service>
    <o:service o:name="AppLauncherClientServiceUrliOS">
      <o:url>https://shell.suite.office.com:1443</o:url>
    </o:service>
    <o:service o:name="ExchangeAutoDiscoverV2Url">
      <o:url>https://outlook.office365.com/autodiscover/autodiscover.json</o:url>
    </o:service>
    <o:service o:name="BIVisualHostAgaveUrl">
      <o:url>https://ovisualuiapp.azurewebsites.net/pbiagave/</o:url>
    </o:service>
    <o:service o:name="ODSEntitlementProdUrl">
      <o:url>https://entitlement.diagnostics.office.com</o:url>
    </o:service>
    <o:service o:name="ODSEntitlementSdfUrl">
      <o:url>https://entitlement.diagnosticssdf.office.com</o:url>
    </o:service>
    <o:service o:name="ODSFeedbackUrl" o:authentication="2">
      <o:url>https://api.diagnosticssdf.office.com/v2/feedback</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.diagnosticssdf.office.com/v2/feedback" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ODSFeedbackFileUrl" o:authentication="2">
      <o:url>https://api.diagnosticssdf.office.com/v2/file</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://api.diagnosticssdf.office.com/v2/file" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ODSIncidentsProdUrl">
      <o:url>https://incidents.diagnostics.office.com</o:url>
    </o:service>
    <o:service o:name="ODSIncidentsSdfUrl">
      <o:url>https://incidents.diagnosticssdf.office.com</o:url>
    </o:service>
    <o:service o:name="ODSNudgesUrl">
      <o:url>https://incidents.diagnostics.office.com</o:url>
    </o:service>
    <o:service o:name="ODSPPEUrl">
      <o:url>https://ods-diagnostics-ppe.trafficmanager.net</o:url>
    </o:service>
    <o:service o:name="ODSProdUrl">
      <o:url>https://api.diagnostics.office.com</o:url>
    </o:service>
    <o:service o:name="ODSSDFUrl">
      <o:url>https://api.diagnosticssdf.office.com</o:url>
    </o:service>
    <o:service o:name="ODSProdBackgroundDiagnosticsUrl">
      <o:url>https://api.diagnostics.office.com</o:url>
    </o:service>
    <o:service o:name="ODSSDFBackgroundDiagnosticsUrl">
      <o:url>https://api.diagnosticssdf.office.com</o:url>
    </o:service>
    <o:service o:name="ODSProdFeedbackDiagnosticsUrl">
      <o:url>https://api.diagnostics.office.com</o:url>
    </o:service>
    <o:service o:name="ODSSDFFeedbackDiagnosticsUrl">
      <o:url>https://api.diagnosticssdf.office.com</o:url>
    </o:service>
    <o:service o:name="LiveIDCloudSettingsUrl">
      <o:url>https://settings.outlook.com</o:url>
    </o:service>
    <o:service o:name="IDConfigUrl">
      <o:url>https://login.microsoftonline.com/</o:url>
    </o:service>
    <o:service o:name="LastMileTelemetryConfigUrl">
      <o:url>https://r4.res.office365.com/footprintconfig/v1.7/scripts/fpconfig.json</o:url>
    </o:service>
    <o:service o:name="AutoDetectProdAPIUrl">
      <o:url>https://prod-global-autodetect.acompli.net/autodetect</o:url>
    </o:service>
    <o:service o:name="AutoDetectTestAPIUrl">
      <o:url>https://dev0-api.acompli.net/autodetect</o:url>
    </o:service>
    <o:service o:name="FlowBaseHostUrl">
      <o:url>https://[Flow.BaseHost]</o:url>
    </o:service>
    <o:service o:name="PowerAutomateBaseHostUrl">
      <o:url>https://make.powerautomate.com</o:url>
    </o:service>
    <o:service o:name="ImageToDocServiceEndpoint">
      <o:url>https://imagetodoc.[OSI.BaseHost]</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="FlowEnvironmentsUrl" o:authentication="2">
      <o:url>https://api.[Flow.BaseHost]/providers/Microsoft.ProcessSimple/environments</o:url>
      <o:ticket o:idprovider="3" o:headerName="Authorization" o:headerValue="Bearer {}" o:resourceId="https://service.[Flow.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="PAPIProfileServiceGetProfile" o:authentication="2">
      <o:url>https://[Substrate.BaseHost.FP]/profileb2/v2.0/me/V1Profile</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost.FP]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="IAPIImageServiceGetPhoto" o:authentication="2">
      <o:url>https://[Substrate.BaseHost.FP]/imageB2/v1.0/me/image/resize%28width%3D384%2Cheight%3D384%2CallowResizeUp%3Dtrue%29</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost.FP]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="PinRedemptionAADEndpoint">
      <o:url>https://login.microsoftonline.com/msretailfederation.onmicrosoft.com/oauth2/token</o:url>
    </o:service>
    <o:service o:name="PinRedemptionAADResource">
      <o:url>https://redemptionservices.microsoft.com/</o:url>
    </o:service>
    <o:service o:name="OfficeRecommendationsServiceBaseURL" o:authentication="1">
      <o:url>https://ofcrecsvcapi-int.azurewebsites.net/</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" o:headerValue="{}" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="AADTokenRedemption">
      <o:url>https://login.microsoftonline.com/msretailfederation.onmicrosoft.com/oauth2/token</o:url>
    </o:service>
    <o:service o:name="ActionableMessagesAADTokenUrl">
      <o:url>https://outlook.office365.com/connectors</o:url>
    </o:service>
    <o:service o:name="MroDeviceMgrC2R">
      <o:url>https://mrodevicemgr.[OSI.BaseHost]/mrodevicemgrsvc/api</o:url>
    </o:service>
    <o:service o:name="MroDeviceMgrSdx">
      <o:url>https://mrodevicemgr.[OSI.BaseHost]/mrodevicemgrsvc/api</o:url>
    </o:service>
    <o:service o:name="LokiAutoDiscoverUrl">
      <o:url>https://loki.delve.office.com/api/v1/configuration/officewin32/</o:url>
    </o:service>
    <o:service o:name="LoopHubbleCDN">
      <o:url>https://cdn.hubblecontent.osi.office.net/</o:url>
    </o:service>
    <o:service o:name="LoopWebServiceBaseURL">
      <o:url>https://prod.api.loop.cloud.microsoft/</o:url>
    </o:service>
    <o:service o:name="MeControlLiveIDUrl">
      <o:url>https://account.microsoft.com/?ref=ocpp</o:url>
    </o:service>
    <o:service o:name="MeControlOrgIdUrl">
      <o:url>https://portal.office.com/account/?ref=ClientMeControl</o:url>
    </o:service>
    <o:service o:name="OfficeCIUrl">
      <o:url>https://officeci.azurewebsites.net/api/</o:url>
    </o:service>
    <o:service o:name="IrisArcApi" o:authentication="1">
      <o:url>https://arc.msn.com/v4/api/selection</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="ads.arcct.msn.com" o:headerValue="WLID1.1 {}" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LifecycleUrl" o:authentication="1">
      <o:url>https://lifecycle.office.com</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="https://lifecycle.office.com" />
    </o:service>
    <o:service o:name="UserVoiceExcelIOS">
      <o:url>https://excel.uservoice.com/forums/304936-excel-for-mobile-devices-tablets-phones-android</o:url>
    </o:service>
    <o:service o:name="UserVoiceOfficeMobileIOS">
      <o:url>https://officemobile.uservoice.com/forums/929800-office-app-ios-and-ipad-asks</o:url>
    </o:service>
    <o:service o:name="UserVoicePowerPointIOS">
      <o:url>https://powerpoint.uservoice.com/forums/288952-powerpoint-for-ipad-iphone-ios</o:url>
    </o:service>
    <o:service o:name="UserVoiceVisioIOS">
      <o:url>https://visio.uservoice.com/forums/368202-visio-on-devices</o:url>
    </o:service>
    <o:service o:name="UserVoiceWordIOS">
      <o:url>https://word.uservoice.com/forums/304948-word-for-ipad-iphone-ios</o:url>
    </o:service>
    <o:service o:name="OneDriveWebServiceUrl">
      <o:url>https://www.odwebp.svc.ms</o:url>
    </o:service>
    <o:service o:name="OneDriveClientDownloadSite">
      <o:url>https://onedrive.live.com/about/download/?windows10SyncClientInstalled=false</o:url>
    </o:service>
    <o:service o:name="PdfConvertWord">
      <o:url>https://wordcs.[OSI.BaseHost]/document/export/pdf</o:url>
    </o:service>
    <o:service o:name="PdfConvertExcel">
      <o:url>https://excelcs.[OSI.BaseHost]/document/export/pdf</o:url>
    </o:service>
    <o:service o:name="PdfConvertPPT">
      <o:url>https://pptcs.[OSI.BaseHost]/document/export/pdf</o:url>
    </o:service>
    <o:service o:name="CreateModuleBaseCdnUrl">
      <o:url>https://cdn.designerapp.osi.office.net/designerapp/create-module</o:url>
    </o:service>
    <o:service o:name="DesignerCanvas">
      <o:url>https://designer.microsoft.com</o:url>
    </o:service>
    <o:service o:name="DesignerCanaryCanvas">
      <o:url>https://designerapp.azurewebsites.net</o:url>
    </o:service>
    <o:service o:name="DesignerDAS">
      <o:url>https://designerapp.[OSI.BaseHost]/designerapp</o:url>
    </o:service>
    <o:service o:name="DesignerCanaryDAS">
      <o:url>https://canary.designerapp.[OSI.BaseHost]/designerapp</o:url>
    </o:service>
    <o:service o:name="DesignerECS">
      <o:url>https://ecs.office.com/config/v1/Designer</o:url>
    </o:service>
    <o:service o:name="DesignerFontCDN">
      <o:url>https://cdn.int.designerapp.osi.office.net/fonts</o:url>
    </o:service>
    <o:service o:name="DesignerFontCDNProd">
      <o:url>https://cdn.designerapp.osi.office.net/designerapp/fonts</o:url>
    </o:service>
    <o:service o:name="DesignerFontService">
      <o:url>https://fs.microsoft.com/fs</o:url>
    </o:service>
    <o:service o:name="DesignerFontServiceWithVersion">
      <o:url>https://fs.microsoft.com/fs/DesignerFonts/1.7</o:url>
    </o:service>
    <o:service o:name="DesignerCDN">
      <o:url>https://cdn.designerapp.osi.office.net</o:url>
    </o:service>
    <o:service o:name="DesignerHubble">
      <o:url>https://hubblecontent.osi.office.net/contentsvc/api/pivots/</o:url>
    </o:service>
    <o:service o:name="DesignerHubbleCDN">
      <o:url>https://cdn.hubblecontent.osi.office.net/</o:url>
    </o:service>
    <o:service o:name="DesignerMobileCDN">
      <o:url>https://cdn.designerapp.osi.office.net/designer-mobile</o:url>
    </o:service>
    <o:service o:name="DesignerMobileDynamicStringsBaseUrl">
      <o:url>https://cdn.designerapp.osi.office.net/designerapp/mobile-dynamic-strings</o:url>
    </o:service>
    <o:service o:name="DesignerMobileHomeScreenBaseUrl">
      <o:url>https://cdn.designerapp.osi.office.net/designerapp/mobile-home-screen</o:url>
    </o:service>
    <o:service o:name="DesignerMobileCDNAssetsBaseUrl">
      <o:url>https://cdn.designerapp.osi.office.net/designerapp/mobile-assets</o:url>
    </o:service>
    <o:service o:name="DesignerMobileDynamicToolbarBaseUrl">
      <o:url>https://cdn.designerapp.osi.office.net/designerapp/mobile-toolbar</o:url>
    </o:service>
    <o:service o:name="TodoServiceBase" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/todob2/api/v1</o:url>
      <o:ticket o:policy="DELEGATION" o:idprovider="1" o:target="https://[Substrate.BaseHost]/Todo-Internal.ReadWrite" o:headerValue="Bearer {}" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SafeDocsHashVerificationService" o:authentication="2">
      <o:url>https://unitedstates-api.fp.wd.microsoft.com/file/report/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SafeDocsStartUpload" o:authentication="2">
      <o:url>https://unitedstates-api.fp.wd.microsoft.com/file/startupload/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SafeDocsFinishUpload" o:authentication="2">
      <o:url>https://unitedstates-api.fp.wd.microsoft.com/file/finishupload/</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="BingURLPreviewAPIUrl">
      <o:url>https://www.bingapis.com/api/v7/urlpreview/search?appid=E93048236FE27D972F67C5AF722136866DF65FA2</o:url>
    </o:service>
    <o:service o:name="AzureSpeechEndpointUrlWord">
      <o:url>wss://officespeech.platform.bing.com/speech/recognition/dictation/office/v1</o:url>
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWord" o:authentication="1">
      <o:url>https://learningtools.onenote.com/learningtoolsapi/v2.0/GetFreeformSpeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWordNam" o:authentication="1">
      <o:url>https://nam.learningtools.onenote.com/learningtoolsapi/v2.0/getfreeformspeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWordEur" o:authentication="1">
      <o:url>https://eur.learningtools.onenote.com/learningtoolsapi/v2.0/getfreeformspeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWordApc" o:authentication="1">
      <o:url>https://apc.learningtools.onenote.com/learningtoolsapi/v2.0/getfreeformspeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWordAndroid" o:authentication="1">
      <o:url>https://learningtools.onenote.com/learningtoolsapi/v2.0/GetFreeformSpeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="officeapps.[Live.WebHost]" />
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWordAndroidNam" o:authentication="1">
      <o:url>https://nam.learningtools.onenote.com/learningtoolsapi/v2.0/getfreeformspeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWordAndroidEur" o:authentication="1">
      <o:url>https://eur.learningtools.onenote.com/learningtoolsapi/v2.0/getfreeformspeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LearningToolsEndpointUrlWordAndroidApc" o:authentication="1">
      <o:url>https://apc.learningtools.onenote.com/learningtoolsapi/v2.0/getfreeformspeech</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="UrlWordLearningToolsGetVoices" o:authentication="1">
      <o:url>https://learningtools.onenote.com/learningtoolsapi/v2.0/Getvoices</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
    </o:service>
    <o:service o:name="ClpPolicyFetch" o:authentication="2">
      <o:url>https://dataservice.protection.outlook.com/PsorWebService/v1/ClientSyncFile/MipPolicies</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://dataservice.o365filtering.com/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ClpPolicyFetchSubstrate" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/CompliancePolicy/ClientSyncFile/{0}</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MipSdkPolicySync" o:authentication="2">
      <o:url>https://dataservice.protection.outlook.com/PsorWebService/v1/ClientSyncFile/MipPolicies</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="&quot;https://syncservice.o365syncservice.com/&quot;" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ClpAuditing" o:authentication="2">
      <o:url>https://useraudit.o365auditrealtimeingestion.manage.office.com</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://o365auditrealtimeingestion.manage.office.com/api/userauditrecord" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ClpAuditingGraph" o:authentication="2">
      <o:url>https://graph.microsoft.com</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://graph.microsoft.com" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ClpAuditingV2" o:authentication="2">
      <o:url>https://useraudit.o365auditrealtimeingestion.manage.office.com</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://o365auditrealtimeingestion.manage.office.com/api/userauditrecord" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="ClpDEM">
      <o:url>https://officeclient.microsoft.com/dynamicErrorMessaging</o:url>
    </o:service>
    <o:service o:name="InkAnalysisTextRecoEndpointUrl">
      <o:url>https://input.microsoft.com/Ink/TextRecognition/?api-version=beta</o:url>
    </o:service>
    <o:service o:name="TestInkAnalysisTextRecoEndpointUrl">
      <o:url>https://input-test.microsoft.com/Ink/TextRecognition/?api-version=beta</o:url>
    </o:service>
    <o:service o:name="OfficeSearchSerpletServicePath">
      <o:url>https://uci.cdn.office.net/mirrored/smartlookup/current/</o:url>
    </o:service>
    <o:service o:name="OfficeSearchIntentServiceSearchEndpoint">
      <o:url>wss://augmentation.osi.office.net/officeaugmentation/searchendpoint/</o:url>
    </o:service>
    <o:service o:name="OfficeSearchIntentServiceStaticProdEuEndpoint">
      <o:url>wss://prodeu.augmentation.osi.office.net/officeaugmentation/searchendpoint/</o:url>
    </o:service>
    <o:service o:name="ActivityFeedBaseUrlBeta" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/ows/beta/ActivityFeed</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ActivityFeedBaseUrlV2" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/ows/v2/ActivityFeed</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="UpdateActivityFeedStateUrlV2" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/ows/v2/ActivityFeed/UpdateActivityFeedState</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneSurveyServiceApiUrl" o:authentication="1">
      <o:url>https://oness.microsoft.com/api/v1.0/UserSurvey/AuthRequest</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://oness.microsoft.com/api" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="C2RCDNBackgroundUrl">
      <o:url>http://b.c2r.ts.cdn.office.net/pr</o:url>
    </o:service>
    <o:service o:name="C2RCDNForegroundUrl">
      <o:url>http://f.c2r.ts.cdn.office.net/pr</o:url>
    </o:service>
    <o:service o:name="C2RDorisInteractiveInstallationUrl">
      <o:url>https://clients.config.office.net/c2r/v1.0/InteractiveInstallation</o:url>
    </o:service>
    <o:service o:name="C2RDeltaAdvisoryUrl">
      <o:url>https://clients.config.office.net/c2r/v1.0/DeltaAdvisory</o:url>
    </o:service>
    <o:service o:name="AppServicesCatalog">
      <o:url>https://[AppServices.BaseHost]/catalog</o:url>
    </o:service>
    <o:service o:name="OneDriveConvergedMPCEndpoint" o:authentication="1">
      <o:url>https://my.microsoftpersonalcontent.com</o:url>
      <o:ticket o:policy="MBI" o:idprovider="2" o:target="[OSI.RootHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="AppServicesResource">
      <o:url>https://titles.prod.mos.microsoft.com</o:url>
    </o:service>
    <o:service o:name="AppServicesDiscovery">
      <o:url>https://titles.prod.mos.microsoft.com</o:url>
    </o:service>
    <o:service o:name="UnifiedConsentCheckIn">
      <o:url>https://consent.config.office.com/consentcheckin/v1.0/consents</o:url>
    </o:service>
    <o:service o:name="UnifiedConsentWeb">
      <o:url>https://consent.config.office.com/consentweb/v1.0/consents</o:url>
    </o:service>
    <o:service o:name="UnifiedConsentCheckInJWTToken">
      <o:url>https://consentservice.microsoft.com/checkin/UnifiedUserConsent.Read</o:url>
    </o:service>
    <o:service o:name="UnifiedConsentWebJWTToken">
      <o:url>https://consentservice.microsoft.com/web/UnifiedUserConsent.ReadWrite</o:url>
    </o:service>
    <o:service o:name="UnifiedConsentRPSToken">
      <o:url>https://consentservice.microsoft.com/msa</o:url>
    </o:service>
    <o:service o:name="UnifiedConsentWebViewURL">
      <o:url>https://admin.microsoft.com/centrohost?appname=OfficeUnifiedConsent&amp;feature=host-unified-consent#/</o:url>
    </o:service>
    <o:service o:name="PPTCSThumbnailService">
      <o:url>https://[PPTCS.ThumbnailServiceBaseHost]/thumbnail</o:url>
    </o:service>
    <o:service o:name="StorylineLicenseService">
      <o:url>https://www.yammer.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppGraphAPI">
      <o:url>https://graph.microsoft.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppSubstrateOfficeAPI">
      <o:url>https://substrate.office.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppOutlookOfficeAPI">
      <o:url>https://outlook.office.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppWebShellSuitOfficeAPI">
      <o:url>https://webshell.suite.office.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppClientConfigOfficeAPI">
      <o:url>https://clients.config.office.net</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppPetrolOfficeAPI">
      <o:url>https://petrol.office.microsoft.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppOnlineLoginAPI">
      <o:url>https://login.microsoftonline.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppMobileEventDataAPI">
      <o:url>https://mobile.events.data.microsoft.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppStreamAPI">
      <o:url>https://api.microsoftstream.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppEcsAPI">
      <o:url>https://ecs.office.com</o:url>
    </o:service>
    <o:service o:name="StreamMobileAppSubstrateSearchAPI">
      <o:url>https://outlook.office365.com</o:url>
    </o:service>
    <o:service o:name="OneCameraCDNUrl">
      <o:url>https://res.cdn.office.net</o:url>
    </o:service>
    <o:service o:name="ClpRevokeCompliancePortalUrl">
      <o:url>https://go.microsoft.com/fwlink/?linkid=2243232&amp;</o:url>
    </o:service>
    <o:service o:name="OMEXAddInDetailsQueryUrl">
      <o:url>https://api.addins.omex.office.net/api/addins/search</o:url>
    </o:service>
    <o:service o:name="JarvisCustomerMasterFrontDoor">
      <o:url>https://jcmsfd.account.microsoft.com</o:url>
    </o:service>
    <o:service o:name="HarmonyMTServiceUrl" o:authentication="1">
      <o:url>https://translation.harmony.microsoft.com</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="harmony.microsoft.com" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://harmony.microsoft.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MessageBrokerServiceUrl" o:authentication="1">
      <o:url>https://messagebroker.mobile.m365.svc.cloud.microsoft</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="harmony.microsoft.com" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://harmony.microsoft.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="TemplateMetadataServiceUrl">
      <o:url>https://templatesmetadata.office.net/</o:url>
    </o:service>
    <o:service o:name="OfficePythonServiceEndpointUrl">
      <o:url>https://service.officepy.microsoftusercontent.com/</o:url>
    </o:service>
    <o:service o:name="ToDoMosAppContentUrl" o:authentication="1">
      <o:url>https://[EXO.BaseHost]/tasks?app&amp;hostApp=metaOSHub</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[EXO.BaseHost]/" o:authorityUrl="https://login.windows.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="UrlUnionMTService">
      <o:url>https://apis.mobile.m365.svc.cloud.microsoft</o:url>
    </o:service>
    <o:service o:name="SkypeRegistrarUrlForADAL" o:authentication="1">
      <o:url>https://teams.microsoft.com/registrar/prod</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://ic3.teams.office.com" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SkypeRegistrarUrlForMSA">
      <o:url>https://edge.skype.com/registrar/prod</o:url>
    </o:service>
    <o:service o:name="SkypeRPSServiceUrl" o:authentication="1">
      <o:url>https://edge.skype.com/rps</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="skype.com" />
    </o:service>
    <o:service o:name="SubstratePUDSBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/puds</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SubstratePUDSScanOutputSettingsUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/puds/v1/me/settings/scan/outputSettings</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="AuthOrganizations">
      <o:url>https://login.microsoftonline.com/organizations</o:url>
    </o:service>
    <o:service o:name="PowerAppsAPIUrl">
      <o:url>https://api.bap.microsoft.com</o:url>
    </o:service>
    <o:service o:name="PowerAppsServiceUrl">
      <o:url>https://service.powerapps.com</o:url>
    </o:service>
    <o:service o:name="SubstrateServiceAPIV2EntityServeBaseUrl" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/search/api/v2/entityserve</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="[Substrate.AuthHost]" o:headerValue="Passport1.4 from-PP='{}&amp;p='" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://[Substrate.AuthHost]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="CWCEligibilityUrlV3">
      <o:url>https://mss.office.com</o:url>
    </o:service>
    <o:service o:name="NotificationServiceRegisterEndpoint" o:authentication="1">
      <o:url>https://[NotificationService.BaseHost]/api/v1/register</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://notification.m365.svc.cloud.microsoft/" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="FamilyOnboardingShareableLink">
      <o:url>https://onboardingaggregator.family.microsoft.com/api/v1/shareable-link</o:url>
      <o:ticket o:policy="MBI_SSL" o:idprovider="1" o:target="onboardingaggregator.family.microsoft.com" />
    </o:service>
    <o:service o:name="TemplatesWebsiteExploreAmazingTemplatesForWord">
      <o:url>https://[TOC.BaseHost]/templates-for-word?ocid=oo_toc_client_app_MARVEL_UPS_templates_gopremiumLandingPage</o:url>
    </o:service>
    <o:service o:name="TemplatesWebsiteExploreAmazingTemplatesForExcel">
      <o:url>https://[TOC.BaseHost]/templates-for-excel?ocid=oo_toc_client_app_MARVEL_UPS_templates_gopremiumLandingPage</o:url>
    </o:service>
    <o:service o:name="TemplatesWebsiteExploreAmazingTemplatesForPowerPoint">
      <o:url>https://[TOC.BaseHost]/templates-for-powerpoint?ocid=oo_toc_client_app_MARVEL_UPS_templates_gopremiumLandingPage</o:url>
    </o:service>
    <o:service o:name="TemplatesWebsiteSearchAmazingTemplates">
      <o:url>https://[TOC.BaseHost]/Search/results?ocid=oo_toc_client_app_MARVEL_UPS_templates_gopremiumLandingPage</o:url>
    </o:service>
    <o:service o:name="ODCLogging">
      <o:url>https://metadata.templates.cdn.office.net/client/log</o:url>
    </o:service>
    <o:service o:name="AppAcquisitionLogging">
      <o:url>https://addinsinstallation.store.office.com/app/acquisitionlogging</o:url>
    </o:service>
    <o:service o:name="AddInEmailTemplate">
      <o:url>https://[OMEX.BaseHost]/api/addins/emailtemplate</o:url>
    </o:service>
    <o:service o:name="AddInsPowerBIInClientStore">
      <o:url>https://inclient.store.office.com/gyro/clientstore</o:url>
    </o:service>
    <o:service o:name="AddInsWXPInClientStore">
      <o:url>https://inclient.store.office.com/gyro/clientstore</o:url>
    </o:service>
    <o:service o:name="AddInsInClientStore" o:authentication="1">
      <o:url>https://inclient.store.office.com/gyro/client</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" o:parameterName="t" />
    </o:service>
    <o:service o:name="AddinsAppSourceLandingPage">
      <o:url>https://pages.store.office.com/webapplandingpage.aspx</o:url>
    </o:service>
    <o:service o:name="AwsCgQuery">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/gallery</o:url>
    </o:service>
    <o:service o:name="AppDownload">
      <o:url>https://addinsinstallation.store.office.com/app/download</o:url>
    </o:service>
    <o:service o:name="AppInfoQuery15">
      <o:url>https://api.addins.omex.office.net/appinfo/query</o:url>
    </o:service>
    <o:service o:name="AppInstallInfoQuery15">
      <o:url>https://addinsinstallation.store.office.com/appinstall/unauthenticated</o:url>
    </o:service>
    <o:service o:name="AppLandingPage" o:authentication="1">
      <o:url>https://pages.store.office.com/webapplandingpage.aspx</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" o:parameterName="t" />
    </o:service>
    <o:service o:name="AppQuery15">
      <o:url>https://api.addins.store.office.com/app/query</o:url>
    </o:service>
    <o:service o:name="AppStateQuery15">
      <o:url>https://api.addins.omex.office.net/appstate/query</o:url>
    </o:service>
    <o:service o:name="ClientAppInstallInfoQuery15" o:authentication="2">
      <o:url>https://addinsinstallation.store.office.com/appinstall/authenticated</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="msm-auth.store.office.com" />
    </o:service>
    <o:service o:name="ClientAppInstallInfoQueryOrgId" o:authentication="2">
      <o:url>https://addinsinstallation.store.office.com/orgid/appinstall/authenticated</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="c606301c-f764-4e6b-aa45-7caaaea93c9a" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="CommerceQuery15">
      <o:url>https://addinslicensing.store.office.com/commerce/query</o:url>
    </o:service>
    <o:service o:name="DeepLinkingService">
      <o:url>https://api.addins.store.office.com/addinstemplate</o:url>
    </o:service>
    <o:service o:name="DeepLinkingServiceBlackForest">
      <o:url>https://store.office.de/addinstemplate</o:url>
    </o:service>
    <o:service o:name="DeepLinkingServiceChina">
      <o:url>https://store.office.cn/addinstemplate</o:url>
    </o:service>
    <o:service o:name="DeepLinkingServiceDogfood">
      <o:url>https://api.addins.store.officeppe.com/addinstemplate</o:url>
    </o:service>
    <o:service o:name="DeepLinkingTemplates">
      <o:url>https://omextemplates.content.office.net/support/templates</o:url>
    </o:service>
    <o:service o:name="EntitlementQuery15" o:authentication="2">
      <o:url>https://addinslicensing.store.office.com/entitlement/query</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="msm-auth.store.office.com" />
    </o:service>
    <o:service o:name="EntitlementQueryOrgId" o:authentication="2">
      <o:url>https://addinslicensing.store.office.com/orgid/entitlement/query</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LastStoreCatalogUpdate" o:authentication="2">
      <o:url>https://[OMEX.BaseHost]/catalog/laststoreupdate</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="MyApps" o:authentication="1">
      <o:url>https://[OMEX.BaseHost]/myapps.aspx</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" o:parameterName="t" />
    </o:service>
    <o:service o:name="OutlookWebStore" o:authentication="1">
      <o:url>https://pages.store.office.com/appshome.aspx?productgroup=Outlook</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OMEX.AuthHost]" o:parameterName="t" />
    </o:service>
    <o:service o:name="RemoveApps" o:authentication="2">
      <o:url>https://addinslicensing.store.office.com/apps/remove</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="msm-auth.store.office.com" />
    </o:service>
    <o:service o:name="RemoveAppsOrgId" o:authentication="2">
      <o:url>https://addinslicensing.store.office.com/orgid/apps/remove</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="ReviewQuery15">
      <o:url>https://pages.store.office.com/review/query</o:url>
    </o:service>
    <o:service o:name="TemplateStart">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/start</o:url>
    </o:service>
    <o:service o:name="TemplateStartMac">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/mac15/start</o:url>
    </o:service>
    <o:service o:name="TemplateSearchMac">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/mac15/search</o:url>
    </o:service>
    <o:service o:name="TemplateStartModern">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/modern/start</o:url>
    </o:service>
    <o:service o:name="TemplateSearch">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/search</o:url>
    </o:service>
    <o:service o:name="TemplateSearchModern">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/modern/search</o:url>
    </o:service>
    <o:service o:name="TemplateListV2">
      <o:url>https://[TMS.BaseTemplateHost]/templates/list/v2</o:url>
    </o:service>
    <o:service o:name="TemplateDetails">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/details</o:url>
    </o:service>
    <o:service o:name="TemplateDetailsModern">
      <o:url>https://[TMS.BaseTemplateHost]/client/templates/modern/details</o:url>
    </o:service>
    <o:service o:name="AirTrafficControlRules" o:authentication="1">
      <o:url>https://[OMEX.BaseMessagingHost]/airtrafficcontrol/governancerules</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="office.com" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://messaging.office.com/*" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="CampaignMetadataAggregator" o:authentication="1">
      <o:url>https://messaging.engagement.office.com/campaignmetadataaggregator</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="messaging.engagement.office.com" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://messaging.engagement.office.com/*" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="SetCampaignAction" o:authentication="1">
      <o:url>https://messaging.action.office.com/setcampaignaction</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="messaging.action.office.com" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://messaging.action.office.com/*" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="GetCustomMessage" o:authentication="1">
      <o:url>https://messaging.lifecycle.office.com/getcustommessage16</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="messaging.lifecycle.office.com" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://messaging.lifecycle.office.com/*" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LegacyGetCustomMessage">
      <o:url>https://messaging.lifecycle.office.com/getcustommessage16</o:url>
    </o:service>
    <o:service o:name="StoreUserStatus" o:authentication="2">
      <o:url>https://odc.[OSI.BaseHost]/odc/api/storeuserstatus</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="LegacySetUserAction">
      <o:url>https://messaging.action.office.com/setuseraction16</o:url>
    </o:service>
    <o:service o:name="SendAutoRenewAction">
      <o:url>https://[OMEX.BaseMessagingHost]/lifecycle/SendAutoRenewAction</o:url>
    </o:service>
    <o:service o:name="SetUserAction" o:authentication="2">
      <o:url>https://messaging.action.office.com/setuseraction16</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="messaging.action.office.com" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="https://messaging.action.office.com/*" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="FirstPartyAppCDN">
      <o:url>https://fpastorage.cdn.office.net/%s</o:url>
    </o:service>
    <o:service o:name="FirstPartyAppQuery">
      <o:url>https://fpastorage.cdn.office.net/firstpartyapp/addins.xml</o:url>
    </o:service>
    <o:service o:name="ReadyServiceUrl">
      <o:url>https://ready.[OSI.ONETBaseHost]/orfo</o:url>
    </o:service>
    <o:service o:name="OMEXContentStorageCDN">
      <o:url>https://contentstorage.[OSI.ONETBaseHost]/%s</o:url>
    </o:service>
    <o:service o:name="OneNoteApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/api</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteAugmentationClipperDomEnhancerApi">
      <o:url>https://[OneNote.BaseHost]/onaugmentation/clipperDomEnhancer/v1.0/</o:url>
    </o:service>
    <o:service o:name="OneNoteAugmentationClipperExtractApi">
      <o:url>https://[OneNote.BaseHost]/onaugmentation/clipperextract/v1.0/</o:url>
    </o:service>
    <o:service o:name="OneNoteAugmentationEntitySearchApi">
      <o:url>https://[OneNote.BaseHost]/onaugmentation/entitysearch</o:url>
    </o:service>
    <o:service o:name="OneNoteCloudFilesApi">
      <o:url>https://cloudfiles.onenote.com/upload.aspx</o:url>
    </o:service>
    <o:service o:name="OneNoteCloudFilesConsumerEmbed">
      <o:url>https://onedrive.live.com/embed?</o:url>
    </o:service>
    <o:service o:name="OneNoteHierarchySyncMultiplexerWebSocket">
      <o:url>wss://hierarchyapi.[OneNote.BaseHostRootDomain]/hierarchy/v1/mux</o:url>
    </o:service>
    <o:service o:name="OneNoteHierarchySyncMultiplexerWebSocketNCUS">
      <o:url>wss://ncus.hierarchyapi.[OneNote.BaseHostRootDomain]/hierarchy/v1/mux</o:url>
    </o:service>
    <o:service o:name="OneNoteHierarchySyncMultiplexerWebSocketWUS2">
      <o:url>wss://wus2.hierarchyapi.[OneNote.BaseHostRootDomain]/hierarchy/v1/mux</o:url>
    </o:service>
    <o:service o:name="OneNoteHierarchySyncWebSocket" o:authentication="2">
      <o:url>wss://hierarchyapi.[OneNote.BaseHostRootDomain]/hierarchy/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteHierarchySyncWebSocketWUS2" o:authentication="2">
      <o:url>wss://wus2.hierarchyapi.[OneNote.BaseHostRootDomain]/hierarchy/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteHierarchySyncWebSocketNCUS" o:authentication="2">
      <o:url>wss://ncus.hierarchyapi.[OneNote.BaseHostRootDomain]/hierarchy/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteNotebooksApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/api/v1.0/me/notes/notebooks</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteClassNotebooksApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/api/v1.0/me/notes/classnotebooks</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteNotebookShareApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/sync/notebooks/share</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteOEmbedApi">
      <o:url>https://[OneNote.BaseHost]/externalcontent/embed</o:url>
    </o:service>
    <o:service o:name="OneNoteOEmbedProvidersApi">
      <o:url>https://[OneNote.BaseHost]/externalcontent/providers</o:url>
    </o:service>
    <o:service o:name="OneNoteRealtimeSyncHub" o:authentication="2">
      <o:url>https://devnull.onenote.com</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteRealtimeSyncHubPPE" o:authentication="2">
      <o:url>https://devnull.onenote.com</o:url>
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="https://login.windows-ppe.net/common/oauth2/authorize" />
    </o:service>
    <o:service o:name="OneNoteSharedPagesSyncApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/sync/pages/shared/</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
    </o:service>
    <o:service o:name="OneNoteSyncApi" o:authentication="2">
      <o:url>https://contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncApiDogfood" o:authentication="2">
      <o:url>https://wus2.contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncApiTeamDogfood" o:authentication="2">
      <o:url>https://ncus.contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncAttachment" o:authentication="2">
      <o:url>https://pagecontentsync.[OneNote.BaseHostRootDomain]/pagecontentsync/attachment/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncAttachmentDogfood" o:authentication="2">
      <o:url>https://wus2.pagecontentsync.[OneNote.BaseHostRootDomain]/pagecontentsync/attachment/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncAttachmentTeamDogfood" o:authentication="2">
      <o:url>https://ncus.pagecontentsync.[OneNote.BaseHostRootDomain]/pagecontentsync/attachment/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncGetMissingAttachmentIds" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/sync/v1/attachment/GetMissingAttachmentIds</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncInvitationsApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/sync/v1/invitations</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
    </o:service>
    <o:service o:name="OneNoteSyncMembershipsApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/sync/v1/memberships</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
    </o:service>
    <o:service o:name="OneNoteSyncPagesApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/sync/v1/pages</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
    </o:service>
    <o:service o:name="OneNoteSyncUsersApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/sync/v1/users</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
    </o:service>
    <o:service o:name="OneNoteSyncWebSocket" o:authentication="2">
      <o:url>wss://contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncWebSocketDogfood" o:authentication="2">
      <o:url>wss://wus2.contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncWebSocketTeamDogfood" o:authentication="2">
      <o:url>wss://ncus.contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteSyncWebSocketMux">
      <o:url>wss://contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1/mux</o:url>
    </o:service>
    <o:service o:name="OneNoteSyncWebSocketDogfoodMux">
      <o:url>wss://wus2.contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1/mux</o:url>
    </o:service>
    <o:service o:name="OneNoteSyncWebSocketTeamDogfoodMux">
      <o:url>wss://ncus.contentsync.[OneNote.BaseHostRootDomain]/contentsync/v1/mux</o:url>
    </o:service>
    <o:service o:name="OneNoteWhiteboardApi" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/wb/api</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteRealtimeChannel" o:authentication="2">
      <o:url>https://realtimesync.[OneNote.BaseHostRootDomain]/realtimechannel/v1.0/signalr/hub</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteEducationUser" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/userinfo/v1/IsEducationUser</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteUserInfoSetting" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/userinfo/v1/setting</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteUserInfoWhoIs" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/userinfo/v1/whois</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteTextYourself">
      <o:url>https://asgsmsproxyapi.azurewebsites.net/</o:url>
    </o:service>
    <o:service o:name="OneNoteBulletins">
      <o:url>https://[OneNote.BaseHost]/bulletins</o:url>
    </o:service>
    <o:service o:name="OneNoteGeoLocation" o:authentication="2">
      <o:url>https://lookup.onenote.com/lookup/geolocation/v1</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteMeetingNotes" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/notes</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteMyPagesAPI" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/api/v1.0/me/notes/pages</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteNotificationRegister" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/NotificationsAPI/Register</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNotePremiumFeatures" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/userinfo/v1/settings/IsFeatureEnabled/PremiumFeatures</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteUserTypes" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/userinfo/v1/UserTypes</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="StickyNotesClient" o:authentication="2">
      <o:url>https://[Substrate.BaseHost]/NotesClient</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteUserFacts" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/userinfo/v1/UserFacts</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteNotificationUnregister" o:authentication="2">
      <o:url>https://[OneNote.BaseHost]/NotificationsAPI/Unregister</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteWhatsNextiOS" o:authentication="1">
      <o:url>https://[OneNote.BaseHost]/whatsnext/iOS</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteWhatsNextMac" o:authentication="1">
      <o:url>https://[OneNote.BaseHost]/whatsnext/Mac</o:url>
      <o:ticket o:policy="MBI_SSL_SHORT" o:idprovider="1" o:target="[OneNote.AuthHost]" o:headerValue="WLID1.0 {}&amp;p=" />
      <o:ticket o:idprovider="3" o:headerValue="Bearer {}" o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:service>
    <o:service o:name="OneNoteAccessibilityCheckerAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/accessibilitychecker</o:url>
    </o:service>
    <o:service o:name="OneNoteCustomTagsAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/notetags</o:url>
    </o:service>
    <o:service o:name="OneNoteFeedAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/stickynotes</o:url>
    </o:service>
    <o:service o:name="OneNoteInsertPictureAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/insertonlinepicture</o:url>
    </o:service>
    <o:service o:name="OneNoteJotSpyAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/jotspy</o:url>
    </o:service>
    <o:service o:name="OneNoteMeetingDetailsAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/meetings</o:url>
    </o:service>
    <o:service o:name="OneNoteStickersAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/stickers</o:url>
    </o:service>
    <o:service o:name="OneNoteMathAssistantAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/mathassistant</o:url>
    </o:service>
    <o:service o:name="OneNoteNotesFabric">
      <o:url>https://[Substrate.BaseHost]/NotesFabric</o:url>
    </o:service>
    <o:service o:name="OneNoteNotesFabricOutlookResource">
      <o:url>https://[EXO.BaseHost]/</o:url>
    </o:service>
    <o:service o:name="FormsQuizAgave">
      <o:url>https://[Forms.BaseHost]/Pages/OneNoteMathAddinFunctionPage.aspx</o:url>
    </o:service>
    <o:service o:name="LiveCaptionsAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/livecaptions</o:url>
    </o:service>
    <o:service o:name="ClassNotebookHelpAndFeedbackAgave">
      <o:url>https://[OneNote.BaseHost]/officeaddins/classnotebook</o:url>
    </o:service>
    <o:service o:name="SubstrateBase">
      <o:url>https://[Substrate.BaseHost]</o:url>
    </o:service>
    <o:service o:name="LiveOfficeAppsBase">
      <o:url>https://officeapps.[Live.WebHost]</o:url>
    </o:service>
    <o:service o:name="OneDrivePushChannelBase">
      <o:url>https://pushchannel.1drv.ms</o:url>
    </o:service>
    <o:service o:name="LiveOAuthLoginStart">
      <o:url>https://login.[Live.WebHost]/oauth20_authorize.srf?client_id=[LiveOAuthAppID]&amp;response_type=token&amp;redirect_uri=https://login.[Live.WebHost]/oauth20_desktop.srf&amp;noauthcancel=1</o:url>
    </o:service>
    <o:service o:name="LiveOAuthSignUp">
      <o:url>https://signup.[Live.WebHost]/signup?ru=https://login.[Live.WebHost]/oauth20_authorize.srf%3fclient_id%3d[LiveOAuthAppID]%26scope%3dservice::ssl.[Live.WebHost]::MBI_SSL_SHORT%26response_type%3dtoken%26redirect_uri%3dhttps://login.[Live.WebHost]/oauth20_desktop.srf%26display%3dtouch&amp;uiflavor=host&amp;lic=1&amp;wsucxt=2</o:url>
    </o:service>
  </o:services>
  <o:tokens>
    <o:token o:name="ODC.BaseHost">Worldwide.Host</o:token>
    <o:token o:name="TestEntryForTelemetryUrl">WorldWide.Host</o:token>
    <o:token o:name="EXO.BaseHost">outlook.office365.com</o:token>
    <o:token o:name="Live.DocumentAPIHost">docs.live.net</o:token>
    <o:token o:name="Live.FederatedAuthHost">urn:federation:MicrosoftOnline</o:token>
    <o:token o:name="Live.WebHost">live.com</o:token>
    <o:token o:name="TestInProd.WebHost">officeappsint.com</o:token>
    <o:token o:name="TestInProdRoaming.WebHost">officeapps.live.com</o:token>
    <o:token o:name="LifecycleBaseHost">lifecycle.office.com</o:token>
    <o:token o:name="MAX.AuthHost">https://prod.support.office.com/InAppHelp</o:token>
    <o:token o:name="MAX.BaseHost">support.office.com</o:token>
    <o:token o:name="MAX.ResourceId">447f78cd-93d4-4fe0-b8bd-9eca586cf156</o:token>
    <o:token o:name="HARMONYMT.ResourceId">https://harmony.microsoft.com</o:token>
    <o:token o:name="MSN.WebHost">msn.com</o:token>
    <o:token o:name="OMEX.AddInsStoreQueryBaseHost">api.addins.omex.office.net</o:token>
    <o:token o:name="OMEX.AddInsStoreQueryBaseHostCDN">api.addins.omex.office.net</o:token>
    <o:token o:name="OMEX.AuthHost">office.com</o:token>
    <o:token o:name="OMEX.BaseCoSubHost">stores.office.com</o:token>
    <o:token o:name="OMEX.BaseHost">store.office.com</o:token>
    <o:token o:name="OMEX.BaseMessagingHost">messaging.office.com</o:token>
    <o:token o:name="AppServices.BaseHost">titles.prod.mos.microsoft.com</o:token>
    <o:token o:name="TMS.BaseTemplateHost">metadata.templates.cdn.office.net</o:token>
    <o:token o:name="TMS.AuthHost">office.com</o:token>
    <o:token o:name="TMS.ResourceId">15365b37-6d4d-4f34-a10a-22010c1c0115</o:token>
    <o:token o:name="TMS.AdalAuthority">[ADALAuthorityUrl]</o:token>
    <o:token o:name="TOC.BaseHost">templates.office.com</o:token>
    <o:token o:name="OneDriveApi.BaseHost">api.onedrive.com</o:token>
    <o:token o:name="OSI.BaseHost">officeapps.live.com</o:token>
    <o:token o:name="OSI.BaseHost.Global">officeapps.live.com</o:token>
    <o:token o:name="OSI.ONETBaseHost">osi.office.net</o:token>
    <o:token o:name="OSI.RootHost">officeapps.live.com</o:token>
    <o:token o:name="RMSOnline.BaseHost">discover.aadrm.com</o:token>
    <o:token o:name="SPO.BaseHost">sharepoint.com</o:token>
    <o:token o:name="Forms.BaseHost">forms.office.com</o:token>
    <o:token o:name="Forms.AuthHost">forms.office.com</o:token>
    <o:token o:name="MicrosoftForms.BaseHost">forms.microsoft.com</o:token>
    <o:token o:name="Substrate.AuthHost">substrate.office.com</o:token>
    <o:token o:name="Substrate.AuthHost.FP">substrate.office.com</o:token>
    <o:token o:name="Substrate.BaseHost">substrate.office.com</o:token>
    <o:token o:name="Substrate.BaseHost.FP">substrate.office.com</o:token>
    <o:token o:name="Outlook.AuthHost">outlook.office.com</o:token>
    <o:token o:name="Flow.AuthHost">https://service.[Flow.BaseHost]/</o:token>
    <o:token o:name="Flow.BaseHost">flow.microsoft.com</o:token>
    <o:token o:name="Teams.BaseHost">teams.microsoft.com</o:token>
    <o:token o:name="OfficeApi.AuthHost">api.office.net</o:token>
    <o:token o:name="Growth.CdnBaseHost">res.cdn.office.net</o:token>
    <o:token o:name="PPTCS.ThumbnailServiceBaseHost">thfd.edog.officeapps.live.com:7443</o:token>
    <o:token o:name="NotificationService.BaseHost">notification.m365.svc.cloud.microsoft</o:token>
    <o:token o:name="AADRedemptionServiceResourceId">https://redemptionservices.microsoft.com/</o:token>
    <o:token o:name="ACSClientSecret">gI0PLmGIUVrSRNOZXf1EeQKzZ0JDTWDUbBV%2bL15%2bjzU%3d</o:token>
    <o:token o:name="ACSHost">posarprodcssservice.accesscontrol.windows.net</o:token>
    <o:token o:name="ACSScope">http%3a%2f%2fredemptionservices.microsoft.com%2f</o:token>
    <o:token o:name="ADALAuthorityUrl">https://login.windows.net/common/oauth2/authorize</o:token>
    <o:token o:name="ADALClientIdExcel">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdLoopMobile">0922ef46-e1b9-4f7e-9134-9ad00547eb41</o:token>
    <o:token o:name="ADALClientIdLync">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdOffice">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdOneNote">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdOMEX">c606301c-f764-4e6b-aa45-7caaaea93c9a</o:token>
    <o:token o:name="ADALClientIdOutlook">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdPowerPoint">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdProject">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdRMS">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdSkyDrivePro">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdVisio">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALClientIdWord">d3590ed6-52b3-4102-aeff-aad2292ab01c</o:token>
    <o:token o:name="ADALPPEAuthorityUrl">https://login.windows-ppe.net/common/oauth2/authorize</o:token>
    <o:token o:name="ADALRedirectUrlExcel">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlLoopMobile">msauth.com.microsoft.Loop://auth</o:token>
    <o:token o:name="ADALRedirectUrlLync">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlOneNote">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlOutlook">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlPowerPoint">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlProject">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlRMS">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlSkyDrivePro">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlVisio">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALRedirectUrlWord">urn:ietf:wg:oauth:2.0:oob</o:token>
    <o:token o:name="ADALResourceId">https://officeapps.live.com</o:token>
    <o:token o:name="AugmentationLoopServicePriority">https://augloop.office.com;https://augloop-int.officeppe.com;https://augloop-dogfood.officeppe.com;https://augloop-gcc.office.com;https://augloop.gov.online.office365.us;https://augloop.dod.online.office365.us</o:token>
    <o:token o:name="BingGeospatialServiceKey">As8tQX71mxPDxIW_1SGPqLnpHp_reEWdy6qjMc6n007_8DLlATfivOyqV0tjuI39</o:token>
    <o:token o:name="BingGeospatialServiceKey3DMaps">Ajuvz1xHFIa2kqBFeM2U1q1fViBjPDsW7ZdDVihFbYM8dwRjemGMOvreAjjqRcxH</o:token>
    <o:token o:name="BingSpellerServiceAuthenticationId">FDD7CBDCD2C7BC531CBFCDB30B10E75034BDEC74</o:token>
    <o:token o:name="BingSpellerServiceSupportedLanguages">en-us</o:token>
    <o:token o:name="DSCRedemptionHost">res.getmicrosoftkey.com</o:token>
    <o:token o:name="EnableCccoPipeline">true</o:token>
    <o:token o:name="EnableLinkedInResumeAssistant">true</o:token>
    <o:token o:name="EnableAcronyms">true</o:token>
    <o:token o:name="EnableDigitalPrint">true</o:token>
    <o:token o:name="EnableSimilarityChecker">true</o:token>
    <o:token o:name="EnableExcelPBIEntryPoints">true</o:token>
    <o:token o:name="EnableOutlookInClientStore">true</o:token>
    <o:token o:name="EnablePhotoFromMsGraph">False</o:token>
    <o:token o:name="EnablePPTBCS">true</o:token>
    <o:token o:name="EnablePPTBinaryDecryption">true</o:token>
    <o:token o:name="EnablePPTCopilotSkittle">true</o:token>
    <o:token o:name="EnablePPTDesigner">true</o:token>
    <o:token o:name="EnablePPTOasisFD">true</o:token>
    <o:token o:name="EnablePPTOnlineVideo">true</o:token>
    <o:token o:name="EnablePPTRehearsal">true</o:token>
    <o:token o:name="EnablePPTSpeechHttpsHandler">true</o:token>
    <o:token o:name="EnablePPTTextTranslationHandler">true</o:token>
    <o:token o:name="ForceDisablePPTPresentInTeams">false</o:token>
    <o:token o:name="EnableOfficeMobileVoice">true</o:token>
    <o:token o:name="EnableOfficeMobileFeed">true</o:token>
    <o:token o:name="EnableOfficeMobileMos">true</o:token>
    <o:token o:name="EnableOfficeMobileVideo">true</o:token>
    <o:token o:name="EnableOfficeMobileFileTransfer">true</o:token>
    <o:token o:name="EnableOfficeMobileForms">true</o:token>
    <o:token o:name="EnableOfficeMobilePdfClipper">true</o:token>
    <o:token o:name="EnableOfficeMobileLensLiveText">true</o:token>
    <o:token o:name="EnableOfficeMobileUniversalPrintSecureRelease">true</o:token>
    <o:token o:name="EnableOsfFeatures">true</o:token>
    <o:token o:name="EnableDictationInMobile">true</o:token>
    <o:token o:name="EnableOfficeMobileMediaUploadToCloud">true</o:token>
    <o:token o:name="EnableOutlineViewInPPTMobile">true</o:token>
    <o:token o:name="EnableOutlookInClientStoreOnPrem">false</o:token>
    <o:token o:name="EnableOutlookWeatherUtils">true</o:token>
    <o:token o:name="EnablePPTImmersivePrint">true</o:token>
    <o:token o:name="EnablePPTInkAnalysis">true</o:token>
    <o:token o:name="EnableRehearsePPTMobile">true</o:token>
    <o:token o:name="EnableSkiGraphImport">true</o:token>
    <o:token o:name="EnableSkiInsights">true</o:token>
    <o:token o:name="EnableSkiJuno">true</o:token>
    <o:token o:name="EnableSkiTellme">true</o:token>
    <o:token o:name="EnableSuggestions">false</o:token>
    <o:token o:name="EnableTranscription">true</o:token>
    <o:token o:name="GraphImportEnableGoLocal">true</o:token>
    <o:token o:name="GraphImportFlights">Text=None;;Pdf=None;;ReuseScoreModel=None;;ZeroTermThroughThreeS=None;;ReuseScoreWithPasteInfo=None;;MruWithPasteInfo=None;;LocalAndCloudMLWithFeaturizedPastes=None;;</o:token>
    <o:token o:name="GraphImportGalleryTableHeight">140</o:token>
    <o:token o:name="GraphImportGalleryTableCount">1</o:token>
    <o:token o:name="GraphImportGoLocalExpiredHours">720</o:token>
    <o:token o:name="GraphImportHtmlExpiredHours">24</o:token>
    <o:token o:name="GraphImportPackageExpiredHours">24</o:token>
    <o:token o:name="GraphImportPackageUrlFormat">api/package?tenant={0}&amp;lcid={1}&amp;targetType={2}&amp;zeroTermPackage=false&amp;objectFilterPackage=true&amp;objectStream=true&amp;objectFilter=Table&amp;objectCountForFilter=1</o:token>
    <o:token o:name="GraphImportQFWarmUpUrl">https://outlook.office.com/autosuggest/api/v1/init?cvid={0}&amp;scenario=saturnsearch</o:token>
    <o:token o:name="IsDelveEnabled_UrlFormat">https://shredder.[OSI.ONETBaseHost]/ShredderService/web/static-resources/IsDelveEnabled.txt</o:token>
    <o:token o:name="IsDelveEnabled_RegexToParseJson">a</o:token>
    <o:token o:name="IsDelveEnabled_RegexToParseUserOptOut">\"Status\":[4],</o:token>
    <o:token o:name="TapSearchUrlFormat">api/search/search?tenant={0}&amp;lcid={1}&amp;querytext={2}&amp;rowLimit=30&amp;objectTypeFilter={3}&amp;isEchoScenario=false</o:token>
    <o:token o:name="OutlookInClientPrivateAppsMinExchangeVersion">15.1.517.0</o:token>
    <o:token o:name="OutlookInClientStoreMinExchangeVersion">15.1.354.1</o:token>
    <o:token o:name="OutlookConnectorsSenderWhitelist"><EMAIL>,<EMAIL></o:token>
    <o:token o:name="OutSpaceFileInfoEnableDynamicCanvas">True</o:token>
    <o:token o:name="OutSpaceProfileEnableDynamicCanvas">True</o:token>
    <o:token o:name="PersonalizationEnableInsights">True</o:token>
    <o:token o:name="PersonalizationEnableSignals">True</o:token>
    <o:token o:name="PersonalizationEnableUserActions">True</o:token>
    <o:token o:name="PinRedemptionAADClientId">06c6433f-4fb8-4670-b2cd-408938296b8e</o:token>
    <o:token o:name="PinRedemptionAADClientSecret">ia6LkjM5TSWFnL8isioR4iw0ld9h2jP6eNOfM8W1ruI=</o:token>
    <o:token o:name="PinRedemptionAADHost">login.microsoftonline.com</o:token>
    <o:token o:name="PopHostnames">https://outlook.office365.com|https://outlook.office.com</o:token>
    <o:token o:name="SharepointFilesHostFormat">{tenant}-files{host}</o:token>
    <o:token o:name="SharepointMyFilesHostFormat">{tenant}files{host}</o:token>
    <o:token o:name="SafelinksHostnames">safelinks.protection.com;safelinks.protection.office365.us;safelinks.protection.outlook.com;safelinks.protection.outlook.cn;safelinks.protection.outlook.de;safelinks.o365filtering-int.com</o:token>
    <o:token o:name="SafelinksSpoLofHostnames">sharepoint-df.com;sharepoint.com;sharepoint.de;sharepoint.cn;sharepoint-mil.us;sharepoint.us</o:token>
    <o:token o:name="SafelinksGetParams">url</o:token>
    <o:token o:name="SafelinksDropParams">data;sdata;reserved</o:token>
    <o:token o:name="SafelinksSpoLofDropParams">xsdata;sdata;ovuser;clickParams</o:token>
    <o:token o:name="SuggestionAbortRequestTimeMS">1000</o:token>
    <o:token o:name="SuggestionAuthFlags">0</o:token>
    <o:token o:name="SuggestionMaxRequestSize">2097152</o:token>
    <o:token o:name="SuggestionDisplayPaneTimeOutMS">5000</o:token>
    <o:token o:name="Suggestion4x3Enabled">true</o:token>
    <o:token o:name="SuggestionVectorImagesEnabled">true</o:token>
    <o:token o:name="SuggestionMultipleObjectsPerSlideEnabled">true</o:token>
    <o:token o:name="SuggestionThemesEnabled">7ACABC62-BF99-48CF-A9DC-4DB89C7B13DC;71A07785-5930-41D4-9A83-E23602B48E98;98DFF888-2449-4D28-977C-6306C017633E;5665723A-49BA-4B57-8411-A56F8F207965;7B5DBA9E-B069-418E-9360-A61BDD0615A4;C4BB2A3D-0E93-4C5F-B0D2-9D3FCE089CC5;EC9488ED-E761-4D60-9AC4-764D1FE2C171;7BEAFC2A-325C-49C4-AC08-2B765DA903F9;8984A317-299A-4E50-B45D-BFC9EDE2337A;C0C680CD-088A-49FC-A102-D699147F32B2;EEC9B30E-2747-4D42-BCBE-A02BDEEEA114;3841520A-25F2-4EB8-BE4C-611DB5ABEED9;3577F8C9-A904-41D8-97D2-FD898F53F20E;FC33163D-4339-46B1-8EED-24C834239D99;B8441ADB-2E43-4AF7-B97A-BD870242C6A8;AC372BB4-D83D-411E-B849-B641926BA760;789EC3FE-34FD-429C-9918-760025E6C145;4C5440D6-04D2-4954-96CF-F251137069B2;28CDC826-8792-45C0-861B-85EB3ADEDA33;3388167B-A2EB-4685-9635-1831D9AEF8C4;39EC5628-30ED-4578-ACD8-9820EDB8E15A;5F128B03-DCCA-4EEB-AB3B-CF2899314A46;1306E473-ED32-493B-A2D0-240A757EDD34;C3F70B94-7CE9-428E-ADC1-3269CC2C3385;0507925B-6AC9-4358-8E18-C330545D08F8;4FDF2955-7D9C-493C-B9F9-C205151B46CD;BA0EB5A6-F2D4-4F82-977B-64ADEE4A2A69;7CB32D59-10C0-40DD-B7BD-2E94284A981C;62F939B6-93AF-4DB8-9C6B-D6C7DFDC589F;0AC2F7E7-15F5-431C-B2A2-456FE929F56C;F9A299A0-33D0-4E0F-9F3F-7163E3744208;9697A71B-4AB7-4A1A-BD5B-BB2D22835B57;F226E7A2-7162-461C-9490-D27D9DC04E43;BBFCD31E-59A1-489D-B089-A3EAD7CAE12E;8BEC4385-4EB9-4D53-BFB5-0EA123736B6D;972BB86E-8AFD-4DE8-800F-A08F6EC4E608;B844A9D6-DE23-412A-B497-F6E208B22C16;309C13C0-3BE0-4E8F-8916-1D5516B3B5DD;18E1BE87-7240-45DF-8788-3CAEB7F17AB1;F9915BBD-9749-466F-995C-8C8D6A938EC0;CF1D1A65-FC75-42D2-B7EF-D2991382DC6F;A55DF1DA-22EC-4DA4-B170-D3F0FF81047C;3BFA2149-51D1-489C-9B65-4F9563B089DE;9B55E993-63C4-4E9B-9466-30BCDDC6903B;C2EC3228-ECB7-4E58-8F51-112F019FC712;74669713-C080-4C4B-A409-6275C5640D79;1684E3E2-D34F-4E98-BFE5-F03BFC5861D2;ED3996BA-162B-43C7-B0E2-A5CA4E649741;187088E4-27D7-4455-856F-4A44258D82E2</o:token>
    <o:token o:name="SuggestionJPEGConversionEnabled">false</o:token>
    <o:token o:name="SuggestionKnownPartsExpirationInMinutes">28</o:token>
    <o:token o:name="SuggestionMinTypingIdleTimeMS">0</o:token>
    <o:token o:name="GallatinFederationDomain">partner.microsoftonline.cn</o:token>
    <o:token o:name="GlobalFederationProvider">microsoftonline.com</o:token>
    <o:token o:name="LiveIdTrustedRootDomains">live.net|live.com|live.org|onedrive.com|office.com|office365.com|outlook.com|outlook-sdf.com|hotmail.com|microsoftpersonalcontent.com|microsoftpersonalcontentppe.com</o:token>
    <o:token o:name="LiveOAuthAppID">00000000480728C5</o:token>
    <o:token o:name="LiveOAuthOutlookMobileAppID">0000000048170EF2</o:token>
    <o:token o:name="LiveProfileViewName">Office15.Profile</o:token>
    <o:token o:name="NLProofingServiceTimeout">670</o:token>
    <o:token o:name="OfficeIdentityVersion">0</o:token>
    <o:token o:name="SSExcelCSMaxConvertibleSize">41943040</o:token>
    <o:token o:name="SSExcelPSMaxConvertibleSize">41943040</o:token>
    <o:token o:name="SSPPTCSMaxConvertibleSize">41943040</o:token>
    <o:token o:name="SSPPTPSMaxConvertibleSize">41943040</o:token>
    <o:token o:name="SSWordCSMaxConvertibleSize">41943040</o:token>
    <o:token o:name="SSWordPSMaxConvertibleSize">41943040</o:token>
    <o:token o:name="SSMaxTime">300000</o:token>
    <o:token o:name="SSMaxRetries">10</o:token>
    <o:token o:name="SSDefaultProxyTimeout">900000</o:token>
    <o:token o:name="SSEnableAuth">true</o:token>
    <o:token o:name="SSEnableE1OTransferToService">false</o:token>
    <o:token o:name="SSEnableCache">true</o:token>
    <o:token o:name="SSEnableCacheXL">false</o:token>
    <o:token o:name="SSEnableCacheXLNew">true</o:token>
    <o:token o:name="SSCacheMaxSizeInBytes">78643200</o:token>
    <o:token o:name="SSCacheMaxSizeLowDiskDeviceInBytes">36700160</o:token>
    <o:token o:name="SSCacheMaxSizeXLInBytes">26214400</o:token>
    <o:token o:name="SSCacheMaxSizeLowDiskDeviceXLInBytes">15728640</o:token>
    <o:token o:name="SSCacheMaxDaysOld">14</o:token>
    <o:token o:name="SSCacheMaxFileCount">50</o:token>
    <o:token o:name="SSCacheMaxFileSizeToHashInBytes">78643200</o:token>
    <o:token o:name="SSEnableCacheWord">true</o:token>
    <o:token o:name="SSCacheMaxSizeWordInBytes">26214400</o:token>
    <o:token o:name="SSCacheMaxSizeLowDiskDeviceWordInBytes">15728640</o:token>
    <o:token o:name="ADALPBIResourceId">https://analysis.windows.net/powerbi/api</o:token>
    <o:token o:name="MruMaxLocalItemCount">100</o:token>
    <o:token o:name="MruQuickAccessMinRefreshPeriodMins">15</o:token>
    <o:token o:name="MruQuickAccessInitialSyncPeriodMins">43200</o:token>
    <o:token o:name="MruQuickAccessReportUsageSamplingRate">0</o:token>
    <o:token o:name="EnableAutoRenewOutSpace">true</o:token>
    <o:token o:name="EnableXL2PBIFullFidelity">true</o:token>
    <o:token o:name="EnableXL2PBILocalFiles">true</o:token>
    <o:token o:name="XL2PBIMaxFileSize">250</o:token>
    <o:token o:name="XL2PBIMaxNonModelPartSize">10</o:token>
    <o:token o:name="EnableTargetedMessagingBusBar">true</o:token>
    <o:token o:name="EnableTargetedMessagingDisplayOnce">true</o:token>
    <o:token o:name="EnableTargetedMessagingOutSpace">true</o:token>
    <o:token o:name="GPWord">AlphaArm:2001375553,AlphaX86:2001375557,BetaArm:2001364277,BetaX86:2001364281,GAArm:2001364271,GAX86:2001364275</o:token>
    <o:token o:name="GPExcel">AlphaArm:2001375553,AlphaX86:2001375557,BetaArm:2001364277,BetaX86:2001364281,GAArm:2001364271,GAX86:2001364275</o:token>
    <o:token o:name="GPPPT">AlphaArm:2001375553,AlphaX86:2001375557,BetaArm:2001364277,BetaX86:2001364281,GAArm:2001364271,GAX86:2001364275</o:token>
    <o:token o:name="GPOneNote">AlphaArm:0,AlphaX86:0,BetaArm:0,BetaX86:0,GAArm:1568214825,GAX86:1801344673</o:token>
    <o:token o:name="OfficeAppsEnableLog">Channel:beta,AllUsers:true,App:all,LiveIDs:,GoogleIDs:,DeviceIDs:,LogLevel:info</o:token>
    <o:token o:name="EnableTargetedMessagingBusBarCanvas">true</o:token>
    <o:token o:name="EnableTargetedMessagingCanvasBoot">true</o:token>
    <o:token o:name="EnableTargetedMessagingCanvasSave">true</o:token>
    <o:token o:name="DynamicCanvasContentUrl">https://[OMEX.BaseCoSubHost]/canvas/markup</o:token>
    <o:token o:name="MsGraphBaseURL">graph.microsoft.com</o:token>
    <o:token o:name="MsGraphDcsVersion">beta</o:token>
    <o:token o:name="MsGraphTeamsVersion">beta</o:token>
    <o:token o:name="MsGraphTeamChannelsQueryParameters">select=id,description,displayName,membershipType,tenantId,filesFolderWebUrl,webUrl</o:token>
    <o:token o:name="MsGraphTeamChannelDetailsQueryParameters">select=id,description,displayName,membershipType,tenantId,filesFolderWebUrl,webUrl,summary</o:token>
    <o:token o:name="MsGraphVersion">v1.0</o:token>
    <o:token o:name="MsGraphRegionCheck">true</o:token>
    <o:token o:name="UpdateNotifierWord">VersionARM:**********,DateARM:2017-06-06,VersionX86:**********,DateX86:2017-06-06</o:token>
    <o:token o:name="UpdateNotifierExcel">VersionARM:**********,DateARM:2017-06-06,VersionX86:**********,DateX86:2017-06-06</o:token>
    <o:token o:name="UpdateNotifierPowerPoint">VersionARM:**********,DateARM:2017-06-06,VersionX86:**********,DateX86:2017-06-06</o:token>
    <o:token o:name="AADGraphBaseURL">graph.windows.net</o:token>
    <o:token o:name="CentennialPkgFamilyNameBase">ProjectCentennialDogfood.29088C9C30B23_mbb0nbh50f13t</o:token>
    <o:token o:name="CentennialPDPBase">ms-windows-store://pdp/?ProductId=CFQ7TTC0K5BF&amp;referrer=Office.GetOfficeApp</o:token>
    <o:token o:name="CentennialRateAndReview">ms-windows-store://review/?ProductId=</o:token>
    <o:token o:name="WAMProviderDefaultUrl">https://login.windows.local</o:token>
    <o:token o:name="WAMProviderAADUrl">https://login.microsoft.com</o:token>
    <o:token o:name="WAMProviderMSAUrl">https://login.microsoft.com</o:token>
    <o:token o:name="EnableTargetedMessagingWin32GetOfficeCarousel">true</o:token>
    <o:token o:name="OfficeEntityVersioniOS">2.3</o:token>
    <o:token o:name="ASGProxySubscriptionKey">0f6d01429f3e4224825d317560c7b28b</o:token>
    <o:token o:name="AuthOutlookMobileCobrandId">1e360405-d045-40c1-ad47-34f5d0129655</o:token>
    <o:token o:name="EquivalentAuthorityHosts">login.windows.net|login.microsoftonline.com</o:token>
    <o:token o:name="PlannerMobileMinVersion">v1</o:token>
    <o:token o:name="RegionalAndLanguageSettingsPollMinutes">1440</o:token>
    <o:token o:name="RegionalAndLanguageSettingsSubPathMsGraph">beta/me/settings/regionalAndLanguageSettings</o:token>
    <o:token o:name="ResearcherAutoLaunchWikiPasteLangs">^(en|fr|it|de|es|ja)\.wikipedia\.org$</o:token>
    <o:token o:name="EditorServiceTimeout">670</o:token>
    <o:token o:name="EMEARegionFlag">false</o:token>
    <o:token o:name="BingSpeechSupportedLanguages">en-US;es-ES;zh-CN;en-CA;es-MX;en-GB;fr-FR;de-DE;it-IT;hi-IN;fr-CA;ja-JP;pt-BR;en-AU;en-IN</o:token>
    <o:token o:name="BingSpeechSupportedLanguagesPreview">nb-NO;da-DK;sv-SE;fi-FI;nl-NL;ko-KR;ru-RU;zh-TW;pt-PT;pl-PL;th-TH;tr-TR;ar-BH;he-IL;cs-Cz;hu-HU;el-GR;sk-SK;vi-VN;hr-HR;ro-RO;lt-LT;lv-LV;et-EE;ar-EG;ar-SA;zh-HK;en-NZ;ga-IE;mt-MT;gu-IN;mr-IN;ta-IN;te-IN;sl-SI;bg-BG</o:token>
    <o:token o:name="BingSpeechSupportedPunctuationLanguages">en-US;es-ES;zh-CN;en-CA;es-MX;en-GB;fr-FR;de-DE;it-IT;en-AU;en-IN;fr-CA;pt-BR;ja-JP;nb-NO;da-DK;sv-SE;fi-FI;nl-NL;ko-KR;hi-IN;ru-RU;zh-TW;pt-PT;pl-PL;tr-TR;ar-BH;he-IL;cs-Cz;hu-HU;el-GR;sk-SK;vi-VN;hr-HR;ro-RO;lt-LT;lv-LV;et-EE;ar-EG;ar-SA;zh-HK;en-NZ;ga-IE;mt-MT;gu-IN;mr-IN;ta-IN;te-IN;sl-SI;bg-BG</o:token>
    <o:token o:name="BingSpeechSupportedCommandingLanguages">en-US;en-CA;en-GB;en-AU;en-IN;en-NZ</o:token>
    <o:token o:name="BingSpeechSupportedProfanityFilterLanguages">en-US;es-ES;zh-CN;en-CA;es-MX;en-GB;fr-FR;de-DE;it-IT;en-AU;en-IN;fr-CA;pt-BR;ja-JP;nb-NO;da-DK;sv-SE;fi-FI;nl-NL;tr-TR;ar-BH;he-IL;cs-Cz;hu-HU;el-GR;sk-SK;vi-VN;hr-HR;ro-RO;lt-LT;lv-LV;et-EE;ar-EG;ar-SA;zh-HK;en-NZ;ga-IE;mt-MT;gu-IN;mr-IN;ta-IN;te-IN;sl-SI;bg-BG</o:token>
    <o:token o:name="SharePointGraphAPIUrlSuffix">/_api/v2.0</o:token>
    <o:token o:name="PowerQueryDataCatalogSearchEnabled">true</o:token>
    <o:token o:name="PowerQueryEnvironmentSettings">{"AADUri":"https://login.microsoftonline.com","ARMUri":"https://management.azure.com","ARMAadResource":"https://management.core.windows.net/","ASUriSuffix":"core.windows.net","ASAadResource":"https://storage.azure.com/","GraphUri":"https://graph.microsoft.com","PBIUri":"https://api.powerbi.com","PBIAadResource":"https://analysis.windows.net/powerbi/api","Cloud":"global"}</o:token>
    <o:token o:name="PowerQueryMicrosoftExchangeConnectorEnabled">true</o:token>
    <o:token o:name="TokenAriaUploadOnly">False</o:token>
    <o:token o:name="TokenDisableAllTelemetry">False</o:token>
    <o:token o:name="TokenExcelCopilotResourceId">CopilotChat.URL</o:token>
    <o:token o:name="TokenTelemetryRestrictedSovereigns">microsoftonline.us;microsoftonline.mil</o:token>
    <o:token o:name="IsITARToken">False</o:token>
    <o:token o:name="TokenIdeasEnabled">true</o:token>
    <o:token o:name="TokenIdeasResourceId">Insights.Https.Bundle</o:token>
    <o:token o:name="TokenImageToDocEnabled">true</o:token>
    <o:token o:name="TokenMsFormsEnabled">true</o:token>
    <o:token o:name="FederationProviderToken">1</o:token>
    <o:token o:name="CalendarsJapaneseEras">1868_01_01_明治_明_Meiji_M|1912_07_30_大正_大_Taisho_T|1926_12_25_昭和_昭_Showa_S|1989_01_08_平成_平_Heisei_H|2019_05_01_令和_令_Reiwa_R</o:token>
    <o:token o:name="CalendarsJapaneseErasTest1">1868_01_01_明治_明_Meiji_M|1912_07_30_大正_大_Taisho_T|1926_12_25_昭和_昭_Showa_S|1989_01_08_平成_平_Heisei_H|2011_02_02_Aa_A_Aaaaaa_A|2019_05_01_Bb_B_Bbbbbb_B|2019_11_11_Cc_C_Cccccc_C</o:token>
    <o:token o:name="CalendarsJapaneseErasTest2">1868_01_01_明治_明_Meiji_M|1912_07_30_大正_大_Taisho_T|1926_12_25_昭和_昭_Showa_S|1989_01_08_平成_平_Heisei_H|2019_05_01_Zz_Z_Zzzzzz_Z</o:token>
    <o:token o:name="OMEXReadyToolkitScanning">1.0.0.0:1</o:token>
    <o:token o:name="OMEXReadyUsageScanning">1.0.0.0:1</o:token>
    <o:token o:name="MonitoredFileExtensionsList">msix;msixbundle;slk</o:token>
    <o:token o:name="BlockedFileExtensionsList">accdb;accde;accdw;accdt;accda;accdr;accdu;ade;adp;app;appcontent-ms;application;appref-ms;appx;appxbundle;asa;asp;aspx;asx;bas;bat;bgi;cab;cer;chm;cmd;cnt;com;cpl;crt;csh;der;deskthemepack;diagcab;exe;fxp;gadget;grp;hlp;hpj;hta;htc;img;inf;ins;iso;isp;its;jar;jnlp;js;jse;ksh;lnk;mad;maf;mag;mam;maq;mar;mas;mat;mau;mav;maw;mcf;mda;mdb;mde;mdt;mdw;mdz;msc;msh;msh1;msh2;msh1xml;msh2xml;mshxml;msi;msix;msixbundle;msp;msstyles;mst;msu;ops;osd;pcd;pif;pl;plg;ppkg;prf;prg;printerexport;ps1;ps2;ps1xml;ps2xml;psc1;psc2;psd1;psdm1;pst;py;pyc;pyo;pyw;pyz;pyzw;rat;reg;scf;scr;sct;settingcontent-ms;shb;shs;theme;themepack;tmp;url;vb;vbe;vbp;vbs;vhd;vhdx;vsmacros;vsw;webpnp;website;ws;wsc;wsf;wsh;xbap;xll;xnk</o:token>
    <o:token o:name="AMSIMaxStreamSizeForScanInBytes">16777216</o:token>
    <o:token o:name="AMSIContentTypesToScan">($^|embeddings)</o:token>
    <o:token o:name="ActivationFilterGUIDs">FFFF;b;{DF630910-1C1D-11D0-AE36-8C0F5E000000}|FFFF;t;{16D51579-A30B-4C8B-A276-0FF4DC41E755}|FFFF;t;{2CF0F912-098E-423C-BED5-15A7D0E52944}|FFFF;t;{6F54557F-7D6E-4fAD-8AA3-7907EB385A2F}|FFFF;t;{842A1268-6E6A-465C-868F-8BC445B9828F}|FFFF;t;{CC5BBEC3-DB4A-4BED-828D-08D78EE3E1ED}|FFFF;t;{CF646147-D47B-48DD-8476-C942DBEBE9A2}|FFFF;t;{EDFDC420-2CF5-4F72-ACBF-4BB0A235B445}|FFFF;t;{F414C260-6AC0-11CF-B6D1-00AA00BBBB58}|FFFF;t;{F414C262-6AC0-11CF-B6D1-00AA00BBBB58}|</o:token>
    <o:token o:name="OneNotePdfConversionKey">0e3c51927ec44a3db5dbf4a2fcc18d0f</o:token>
    <o:token o:name="OneNoteEnableWebConnectedExperiences">true</o:token>
    <o:token o:name="IsIntuneSupported">true</o:token>
    <o:token o:name="AllowedServiceNamesInDisconnectedState">GetFederationProvider,GetIdentityProvider,HomeRealmDiscovery,IAPIImageServiceGetPhoto,IdentityService,LicensingRestHost,LiveCreateProfile,LiveEditProfile,LiveIdFederatedToOrgIdForDesktop,LiveIdFederatedToOrgIdForImmersive,LiveOAuthGetToken,LiveOAuthLoginBase,LiveOAuthLoginEnd,LiveOAuthLoginError,LiveOAuthLoginStart,LiveOAuthSignOut,LiveOAuthSignUp,LiveProfileService,LiveProfileServicesGetInfo,LiveProfileServicesGetPhoneNumber,LumosHost,MsGraphGetPhoto,Nexus,NexusRules,OfficeLicensingService,OfficeLicensingService15,OCSettingsUrl,ConfigOCSettingsTenantAssociationFullUrl,OCSettingsTenantAssociationUrl,OCSettingsCloudPolicyServiceUrl,OCSettingsCloudPolicyServiceMacUrl,OCSettingsCloudPolicyServicIOSUrl,OCSettingsCloudPolicyServiceAndroidUrl,ONetUrl,ORedir,PAPIProfileServiceGetProfile,TestODCService,TestUTAllowOffline,OneNoteHierarchySyncWebSocket,OneNoteHierarchySyncWebSocketNCUS,OneNoteHierarchySyncWebSocketWUS2,OneNoteHierarchySyncMultiplexerWebSocket,OneNoteHierarchySyncMultiplexerWebSocketNCUS,OneNoteHierarchySyncMultiplexerWebSocketWUS2,OneNoteSyncApi,OneNoteSyncApiDogfood,OneNoteSyncApiTeamDogfood,OneNoteSyncAttachment,OneNoteSyncAttachmentDogfood,OneNoteSyncAttachmentTeamDogfood,OneNoteSyncGetMissingAttachmentIds,OneNoteSyncWebSocket,OneNoteSyncWebSocketDogfood,OneNoteSyncWebSocketTeamDogfood,C2RCDNBackgroundUrl,C2RCDNForegroundUrl,C2RDorisInteractiveInstallationUrl,C2RDeltaAdvisoryUrl,TeamsAuthEndpoint,TeamsSchedulerService,TeamsUpgradeV2,TeamsUserAggregateSettings,MroDeviceMgrC2R</o:token>
    <o:token o:name="ControllerServiceNames">AddInEmailTemplate,AddinsAppSourceLandingPage,AddInsInClientStore,AddInsPowerBIInClientStore,AddInsWXPInClientStore,AppAcquisitionLogging,AppDownload,AppInfoQuery15,AppInstallInfoQuery15,AppLandingPage,AppQuery15,AppsForOfficeCDNUrl,AppStateQuery15,BingGeospatialEndpointServiceUrl,BingInsertMediaImmersive,ClientAppInstallInfoQuery15,CommerceQuery15,ConsumerMyAccountSubscription,DeepLinkingService,DeepLinkingServiceBlackForest,DeepLinkingServiceChina,DeepLinkingServiceDogfood,DeepLinkingTemplates,EnrichmentReportContentUrl,EntitlementQuery15,EntitlementQueryOrgId,FirstPartyAppCDN,FirstPartyAppQuery,HubbleLoggingSvc,InsertMediaTelemetry,LastStoreCatalogUpdate,MyApps,ODCInsertMedia,OfficeHomeStoreCatalogEndpoint,OfficeHubCarouselSubscription,OfficeHubSubscription,OfficeOnlineContent,OfficeOnlineContent3DModel,OfficeOnlineContentClipArt,OfficeOnlineContentFb,OfficeOnlineContentFl,OfficeOnlineContentImages,OfficeOnlineContentOD,OfficeOnlineContentOEmbed,OfficeOnlineContentGetVideoProviderList,OfficeOnlineContentVideoHostPage,OfficeOnlineContentVideos,OfficeSoloStoreCatalogEndpoint,OMEXRemoveAppsAndSsoConsentOrgId,OMEXSideloadingAddinSSOConsentPage,OMEXSSOConsentPage,OutlookWebStore,PreinstalledAppsQuery,RemoveApps,RemoveAppsOrgId,ReviewQuery15,StoreUserStatus,InsightsDesktop,HelpContactSupport,OutlookWeatherMSN</o:token>
    <o:token o:name="LastSignInPortalURL">https://mysignins.microsoft.com/</o:token>
    <o:token o:name="SafeDocsMaxRetry">3</o:token>
    <o:token o:name="C2rLap16ProofBaseline">af;ar;as;az-latn;bg;bn-bd;bn-in;bs-latn;ca;ca-es-valencia;cs;cy;da;de;el;en;es;et;eu;fa;fi;fr;ga;gd;gl;gu;ha-latn;he;hi;hr;hu;hy;id;ig;is;it;ja;ka;kk(cyrl,kz);kn;ko;kok;ky;lb;lt;lv;mi;mk;ml;mr;ms;mt;nb;ne;nl;nn;nso;or;pa(guru,in);pl;ps;pt;rm;ro;ru;rw;si;sk;sl;sq;sr(cyrl,latn);sv;sw;ta;te;th;tn;tr;tt(cyrl,ru);uk;ur;uz-latn;vi;wo;xh;yo;zh(cn,hans,hant,hk,mo,sg,tw);zu</o:token>
    <o:token o:name="C2rLap16DisplayBaseline">af-za;am-et;ar-sa;as-in;az-latn-az;be-by;bg-bg;bn-bd;bn-in;bs-latn-ba;ca-es;ca-es-valencia;cs-cz;cy-gb;da-dk;de-de;el-gr;en-us;es-es;et-ee;eu-es;fa-ir;fi-fi;fil-ph;fr-fr;ga-ie;gd-gb;gl-es;gu-in;he-il;hi-in;hr-hr;hu-hu;hy-am;id-id;is-is;it-it;ja-jp;ka-ge;kk-kz;km-kh;kn-in;ko-kr;kok-in;ky-kg;lb-lu;lt-lt;lv-lv;mi-nz;mk-mk;ml-in;mn-mn;mr-in;ms-my;mt-mt;nb-no;ne-np;nl-nl;nn-no;or-in;pa-in;pl-pl;prs-af;pt-br;pt-pt;quz-pe;ro-ro;ru-ru;sd-arab-pk;si-lk;sk-sk;sl-si;sq-al;sr-cyrl-ba;sr-cyrl-rs;sr-latn-rs;sv-se;sw-ke;ta-in;te-in;th-th;tk-tm;tr-tr;tt-ru;ug-cn;uk-ua;ur-pk;uz-latn-uz;vi-vn;zh-cn;zh-tw</o:token>
    <o:token o:name="C2rLap16DisplayDelta">~b14913;+;en-gb;es-mx;fr-ca</o:token>
    <o:token o:name="C2rLap16FWLink32MapBaseline">af:2106129;ar:2106247;az:2106263;bg:2106216;bn-in:2106249;bs:2106242;ca:2106227;cs:2106116;da:2106154;de:2106114;el:2106245;en:2106243;es:2106142;et:2106121;fa:2106133;fi:2106122;fil:2106228;fr:2106221;gu:2106167;he:2106172;hi:2106261;hr:2106253;hu:2106120;id:2106254;is:2106238;it:2106225;ja:2106132;ka:2106127;km:2106231;ko:2106236;lt:2106215;lv:2106180;mi:2106140;mk:2106229;ml:2106250;mr:2106143;ms:2106139;nb:2106130;nl:2106134;pl:2106241;pt:2106135;pt(ao,cv,gw,mo,mz,pt,st,tl):2106244;ro:2106264;ru:2106174;sk:2106237;sl:2106259;sq:2106123;sr-cyrl-ba:2106115;sr-cyrl-rs:2106117;sr-latn:2106248;sv:2106119;ta:2106251;te:2106246;th:2106160;tr:2106146;uk:2106137;ur:2106218;vi:2106257;zh(cn,hans,sg):2106234;zh(hant,hk,mo,tw):2106141</o:token>
    <o:token o:name="C2rLap16FWLink32MapDelta">~b14913;!;en;+;en:2172767;en(as,bz,ca,er,gu,lr,mh,fm,mp,pw,ph,pr,um,vi,us):2106243;es(mx):2172769;fr(ca,pm):2172768</o:token>
    <o:token o:name="C2rLap16FWLink64MapBaseline">af:2106266;ar:2106147;az:2106149;bg:2106268;bn-in:2106152;bs:2106150;ca:2106270;cs:2106156;da:2106272;de:2106273;el:2106165;en:2106163;es:2106162;et:2106164;fa:2106276;fi:2106275;fil:2106277;fr:2106169;gu:2106173;he:2106181;hi:2106280;hr:2106183;hu:2106284;id:2106281;is:2106186;it:2106282;ja:2106285;ka:2106188;km:2106185;ko:2106288;lt:2106287;lv:2106189;mi:2106289;mk:2106191;ml:2106290;mr:2106192;ms:2106193;nb:2106315;nl:2106194;pl:2106195;pt:2106404;pt(ao,cv,gw,mo,mz,pt,st,tl):2106196;ro:2106301;ru:2106302;sk:2106304;sl:2106298;sq:2106414;sr-cyrl-ba:2106406;sr-cyrl-rs:2106401;sr-latn:2106306;sv:2106407;ta:2106308;te:2106405;th:2106411;tr:2106317;uk:2106410;ur:2106415;vi:2106412;zh(cn,hans,sg):2106408;zh(hant,hk,mo,tw):2106313</o:token>
    <o:token o:name="C2rLap16FWLink64MapDelta">~b14913;!;en;+;en:2172665;en(as,bz,ca,er,gu,lr,mh,fm,mp,pw,ph,pr,um,vi,us):2106163;es(mx):2172667;fr(ca,pm):2172666</o:token>
    <o:token o:name="SafeDocsFileDetonationTimeOut">3</o:token>
    <o:token o:name="SafeDocsFileScanTimeOut">12001</o:token>
    <o:token o:name="SafeDocsMaxFileUploadSize">64</o:token>
    <o:token o:name="SafeDocsPollingRate">3000</o:token>
    <o:token o:name="AzureSpeechSubscriptionKeyWord">d8fac3296d45413592e4192fb41ea600</o:token>
    <o:token o:name="TranslatorServiceIsEnabled">true</o:token>
    <o:token o:name="InkAnalysisTextRecoSubscriptionKey">f6d16f1331c843c7a8347ee79b35c7dc</o:token>
    <o:token o:name="InkAnalysisTextRecoSubscriptionKeyExcel">c24ae30e66db488bab5080fd78bd26e7</o:token>
    <o:token o:name="InkAnalysisTextRecoSubscriptionKeyWord">808ee2c320b64b3c9971aece963aa4bc</o:token>
    <o:token o:name="TestInkAnalysisTextRecoSubscriptionKey">8daab3e3a5de4c909682abcc1359f576</o:token>
    <o:token o:name="SPOAuthContextEndPoint">/_api/sp.oauth.nativeclient/authenticate</o:token>
    <o:token o:name="PowerBIOrigin">PowerBi.Url</o:token>
    <o:token o:name="AllowedServiceNamesInDisconnectedStateOutlookSpecific">AutoDetectProdAPIUrl,ODCServicesCatalog,ODCUserConnectedServices</o:token>
    <o:token o:name="OfficeServicesManagerAllowedServiceIdsInDisconnectedState">TP_GOOGLE_IMAP</o:token>
    <o:token o:name="OfficeFamilyOfAudiences">[{"urls":["https://outlook.office365.com/","https://outlook.office.com/","https://autodiscover-s.outlook.com/"],"clientId":"00000002-0000-0ff1-ce00-000000000000"}]</o:token>
    <o:token o:name="OutlookAuthHost">outlook.office.com</o:token>
    <o:token o:name="UnsupportedRegionsForMapCharts">CN</o:token>
    <o:token o:name="UnsupportedRegionsFor3DMaps">CN</o:token>
    <o:token o:name="ThirdPartyStorageForBoxEnabled">true</o:token>
    <o:token o:name="TokenOfficeScriptsEnabled">true</o:token>
    <o:token o:name="IsFloodgateSurveyEnabled">true</o:token>
    <o:token o:name="SovereignFederationDMSToken">Production</o:token>
    <o:token o:name="AllowPublicAddInProviders">true</o:token>
    <o:token o:name="OfficeScriptsServiceEndpointUrl">https://api.officescripts.microsoftusercontent.com/api</o:token>
    <o:token o:name="OneCDNBaseHost_FullDomain">res.cdn.office.net</o:token>
    <o:token o:name="OneCDNBaseHost_SubDomain">public.cdn.office.net</o:token>
    <o:token o:name="SdxCdnUrlBase">https://|1.resources.office.net/|0/|2/|3_|4</o:token>
    <o:token o:name="SdxCdnFfn">033f92d3-bc6d-439a-858a-a17acf70360a</o:token>
    <o:token o:name="SubstrateQuickAccessDismissPath">/search/api/v1/recommendeddocuments/dismissed</o:token>
    <o:token o:name="IsFeedbackEnabled">true</o:token>
    <o:token o:name="MIPSDKCloud">3</o:token>
    <o:token o:name="AuthOfficePythonServiceResourceId">https://officepyservice.office.net/</o:token>
    <o:token o:name="AuthOfficePythonServiceScope">https://officepyservice.office.net/service.functionality</o:token>
    <o:token o:name="SDXManifestsTokenValueList">CDNDOMAIN_STDVARIANT|res.cdn.office.net^CDNDOMAIN_SUBDOMAINVARIANT|public.cdn.office.net^FA000000054_PBIWFEURL|https://app.powerbi.com^FA000000054_PBISHAREDURL|https://analysis.windows.net/powerbi/api^APPCHAT_COPILOT_TASKPANE|taskpane^APPCHAT_COPILOT_MSGEXTDIALOG|msgExtDialog^APPCHAT_COPILOT_BYOADIALOG|byoaDialog^APPCHAT_COPILOT_OPERA|opera^APPCHAT_COPILOT_CLOUDFILEPICKERDIALOG|CloudFilePickerDialog^FA000000129_PPTDESIGNERURL|https://designerappservice.officeapps.live.com^FA000000125_WORDDESIGNERURL|https://designerappservice.officeapps.live.com^FA000000005_INSIGHTSURL|https://insights.microsoft.com</o:token>
    <o:token o:name="DSCRedemptionServiceMsaTarget">officesetup.getmicrosoftkey.com</o:token>
    <o:token o:name="WritingAssistanceEditorOverviewPanePrivacy">true</o:token>
    <o:token o:name="EditorServiceClientFeatureSupported">true</o:token>
    <o:token o:name="GeneralOfficeFeatureEnabled">true</o:token>
    <o:token o:name="EnableOneDriveMounting">true</o:token>
    <o:token o:name="EnableOneDriveBusinessMounting">true</o:token>
    <o:token o:name="EnableThirdPartyStorageMounting">true</o:token>
    <o:token o:name="FeedbackTenantCloudType">Worldwide</o:token>
    <o:token o:name="FeedbackDefaultCPSBehaviour">true</o:token>
    <o:token o:name="NotificationServiceRegisterScope">https://notification.m365.svc.cloud.microsoft/PushNotifications.Register</o:token>
    <o:token o:name="LinksOpenRightTuiImageLight100Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-light-100.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageLight150Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-light-150.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageLight200Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-light-200.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageDark100Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-dark-100.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageDark150Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-dark-150.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageDark200Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-dark-200.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageHC100Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-hc-100.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageHC150Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-hc-150.png</o:token>
    <o:token o:name="LinksOpenRightTuiImageHC200Url">https://statics.teams.cdn.office.net/evergreen-assets/illustrations/win32/m365-device-desktop-hc-200.png</o:token>
    <o:token o:name="IsRestrictedEnvironment">false</o:token>
    <o:token o:name="OneNote.AuthHost">onenote.com</o:token>
    <o:token o:name="OneNote.BaseHost">www.onenote.com</o:token>
    <o:token o:name="OneNote.BaseHostRootDomain">onenote.com</o:token>
    <o:token o:name="OneNoteADALResourceURL">https://outlook.office365.com</o:token>
    <o:token o:name="OneNoteEnableFeed">true</o:token>
    <o:token o:name="OneNoteEnableGetNotebooksAPI">true</o:token>
    <o:token o:name="OneNoteEnableModernSync">true</o:token>
    <o:token o:name="OneNoteEnableSignalLogging">true</o:token>
    <o:token o:name="OneNoteEnableSN">true</o:token>
    <o:token o:name="OneNoteEnableTelemetryAndExperimentation">true</o:token>
    <o:token o:name="OneNoteSubstrateMSAScope">https://substrate.office.com/Notes-Internal.ReadWrite</o:token>
  </o:tokens>
  <o:signin>
    <o:tickets o:idprovider="1">
      <o:ticket o:policy="MBI_SSL_SHORT" o:target="ssl.[Live.WebHost]" />
      <o:ticket o:policy="MBI_SSL_SHORT" o:target="officeapps.[Live.WebHost]" />
    </o:tickets>
    <o:tickets o:idprovider="2">
      <o:ticket o:policy="MBI" o:target="[SPO.BaseHost]" />
      <o:ticket o:policy="MBI" o:target="[OSI.RootHost]" />
    </o:tickets>
    <o:tickets o:idprovider="3">
      <o:ticket o:resourceId="[ADALResourceId]" o:authorityUrl="[ADALAuthorityUrl]" />
      <o:ticket o:resourceId="https://api.office.net" o:authorityUrl="[ADALAuthorityUrl]" />
    </o:tickets>
  </o:signin>
</o:OfficeConfig>