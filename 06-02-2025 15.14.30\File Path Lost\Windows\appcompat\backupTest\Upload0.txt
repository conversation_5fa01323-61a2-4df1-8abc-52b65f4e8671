##fF<## S-1-5-21-246032611-354955300-348842393-1001_ACC.json
##tT<## 20250118_145217
{"Install" : [{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - es-mx",
"programId": "000029b84ebffd5e41f06188519dfd47f60f0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - en-us",
"programId": "000055093bdd3f687d76cbb0ee3e5c1b7bd50000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : [{"productName": "microsoft office",
"originalFileName": "msaccess.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msaccess.exe"
},{"aumid": "Microsoft.Office.MSACCESS.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\access.lnk"
},{"productName": "microsoft office",
"originalFileName": "appvlp.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18227.0",
"binFileVersion": "16.0.18227.20002",
"path": "%programfiles%\\microsoft office\\root\\client\\appvlp.exe"
},{"aumid": "Microsoft.Office.DATABASECOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\database compare.lnk"
},{"productName": "microsoft office",
"originalFileName": "excel.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\excel.exe"
},{"aumid": "Microsoft.Office.EXCEL.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\excel.lnk"
},{"productName": "microsoft office",
"originalFileName": "setlang.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\setlang.exe"
},{"aumid": "Microsoft.Office.SETLANG.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\office language preferences.lnk"
},{"productName": "microsoft onenote",
"originalFileName": "onenote.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\onenote.exe"
},{"aumid": "Microsoft.Office.ONENOTE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onenote.lnk"
},{"productName": "microsoft outlook",
"originalFileName": "outlook.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\outlook.exe"
},{"aumid": "Microsoft.Office.OUTLOOK.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\outlook (classic).lnk"
},{"productName": "microsoft office",
"originalFileName": "powerpnt.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\powerpnt.exe"
},{"aumid": "Microsoft.Office.POWERPNT.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\powerpoint.lnk"
},{"productName": "microsoft office",
"originalFileName": "mspub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\mspub.exe"
},{"aumid": "Microsoft.Office.MSPUB.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\publisher.lnk"
},{"productName": "microsoft office",
"originalFileName": "ocpubmgr.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\ocpubmgr.exe"
},{"aumid": "Microsoft.Office.OcPubMgr.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\skype for business recording manager.lnk"
},{"productName": "microsoft office",
"originalFileName": "lync.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\lync.exe"
},{"aumid": "Microsoft.Office.lync.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\skype for business.lnk"
},{"aumid": "Microsoft.Office.SPREADSHEETCOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\spreadsheet compare.lnk"
},{"aumid": "Microsoft.Office.OneNote.MemoryPreview",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\sticky notes (new).lnk"
},{"productName": "microsoft office",
"originalFileName": "msoev.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msoev.exe"
},{"aumid": "Microsoft.Office.msoev.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\telemetry log for office.lnk"
},{"productName": "microsoft office",
"originalFileName": "winword.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\winword.exe"
},{"aumid": "Microsoft.Office.WINWORD.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\word.lnk"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\VLC media player",
"programId": "0000863d5786bd660050e532b4fa5b66170e0000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files\\VideoLAN\\VLC",
"files" : [{"path": "%programfiles%\\videolan\\vlc\\documentation.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\documentation.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\documentation.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\news.txt"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\news.txt",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\release notes.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\videolan website.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\videolan website.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\videolan website.lnk"
},{"productName": "vlc media player",
"originalFileName": "vlc.exe",
"companyName": "videolan",
"binProductVersion": "3.0.18.0",
"binFileVersion": "3.0.18.0",
"path": "%programfiles%\\videolan\\vlc\\vlc.exe"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{51325390-AE6A-68FC-A315-0950CC83A166}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player - reset preferences and cache files.lnk"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%public%\\desktop\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{30BD9A02-CB9A-93FD-A859-09C8803F2346}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player skinned.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\uninstall.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - fr-ca",
"programId": "000091d9c85e9191ed4199cc21e4e403fcff0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft Edge",
"programId": "000092b6db162d4cfc1f8e8b492d66dbb3760000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files (x86)\\Microsoft\\Edge\\Application",
"files" : [{"productName": "microsoft edge",
"originalFileName": "cookie_exporter.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\cookie_exporter.exe"
},{"productName": "microsoft edge",
"originalFileName": "elevation_service.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\elevation_service.exe"
},{"productName": "microsoft edge",
"originalFileName": "identity_helper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\identity_helper.exe"
},{"productName": "ietoedge bho",
"originalFileName": "ie_to_edge_stub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\bho\\ie_to_edge_stub.exe"
},{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\msedge.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1FC1A6C2-576E-489A-9B4A-92D21F542136}",
"programId": "00009e26d04962c79c9b1834c0c8068bafa600000000",
"compatFlags": "0",
"files" : [{"productName": "microsoft® windows® operating system",
"originalFileName": "expediteupdater",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\expediteupdater.exe"
},{"productName": "microsoft® windows® operating system",
"originalFileName": "uhssvc",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\uhssvc.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{731F6BAA-A986-45A4-8936-7C3AAAAA760B}",
"programId": "0000a8571596f0d284d93f5c20946e72f4f600000904",
"compatFlags": "4",
"restoreAction": "RootDirPath|C:\\Program Files\\Microsoft Office\\root\\integration\\Addons\\",
"files" : [{"productName": "microsoft teams",
"originalFileName": "setup.exe",
"companyName": "microsoft corporation",
"binProductVersion": "1.5.0.30767",
"binFileVersion": "1.5.0.30767",
"path": "%programfiles(x86)%\\teams installer\\teams.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\OneDriveSetup.exe",
"programId": "0000bf803468de7ad95869e0f9e481c147100000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|c:\\program files\\microsoft onedrive\\24.244.1204.0003",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "filecoauth.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filecoauth.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesyncconfig.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesyncconfig.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesynchelper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesynchelper.exe"
},{"productName": "microsoft sharepoint",
"originalFileName": "microsoft.sharepoint.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.exe"
},{"productName": "microsoft sharepoint native messaging client",
"originalFileName": "microsoft.sharepoint.nativemessaging.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.nativemessagingclient.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft EdgeWebView",
"programId": "0000e96975da1b54f6379ab67db1bf4e2b560000ffff",
"compatFlags": "4",
"restoreAction": "RootDirPath|C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application",
"files" : [{"productName": "microsoft edge",
"originalFileName": "cookie_exporter.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\cookie_exporter.exe"
},{"productName": "microsoft edge",
"originalFileName": "elevation_service.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\elevation_service.exe"
},{"productName": "microsoft edge",
"originalFileName": "identity_helper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\identity_helper.exe"
},{"productName": "ietoedge bho",
"originalFileName": "ie_to_edge_stub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\bho\\ie_to_edge_stub.exe"
},{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\msedge.exe"
}]
},{"path": "c:\\program files (x86)\\microsoft\\edge\\application\\msedge.exe",
"programId": "0000eb85c5a8a2001da5f2bdeb7a030b31970000ffff",
"compatFlags": "0",
"files" : [{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\msedge.exe"
},{"aumid": "MSEdge",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft edge.lnk"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F132AF7F-7BCA-4EDE-8A7C-958108FE7DBC}",
"programId": "0000f3ba61ac2186f429d7e48ceb429a7bb60000ffff",
"compatFlags": "16384",
"restoreAction": "RootDirPath|C:\\Program Files\\Realtek\\Audio\\HDA",
"files" : [{"productName": "hd audio background process",
"originalFileName": "rthdvbgproc.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.225",
"binFileVersion": "1.0.0.225",
"path": "%programfiles%\\realtek\\audio\\hda\\ravbg64.exe"
},{"productName": "hd audio control panel",
"originalFileName": "rtdcpl.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "1.0.0.13",
"binFileVersion": "1.0.0.13",
"path": "%programfiles%\\realtek\\audio\\hda\\rtdcpl64.exe"
},{"productName": "realtek audio service",
"originalFileName": "rtkaudioservice.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.68",
"binFileVersion": "1.0.0.68",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkaudioservice64.exe"
},{"productName": "realtek hd audio manager",
"originalFileName": "rtkngui.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.502.0",
"binFileVersion": "1.0.502.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkngui64.exe"
},{"productName": "realtek hd auido update and remove driver tool",
"originalFileName": "rtlupd.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "3.0.0.0",
"binFileVersion": "3.0.0.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtlupd64.exe"
}]
},{"path": "c:\\program files\\microsoft onedrive\\onedrive.exe",
"programId": "0006bf803468de7ad95869e0f9e481c1471000000904",
"compatFlags": "0",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "onedrive.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\onedrive.exe"
},{"aumid": "Microsoft.SkyDrive.Desktop",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onedrive.lnk"
}]
}],
"Update" : [],
"Uninstall" : [{"programId": "@ÀÃ­ßystemRoot]}
##tT>##
##tT<## 20250409_115508
{"Install" : [{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - es-mx",
"programId": "000029b84ebffd5e41f06188519dfd47f60f0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - en-us",
"programId": "000055093bdd3f687d76cbb0ee3e5c1b7bd50000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : [{"productName": "microsoft office",
"originalFileName": "msaccess.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msaccess.exe"
},{"aumid": "Microsoft.Office.MSACCESS.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\access.lnk"
},{"productName": "microsoft office",
"originalFileName": "appvlp.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18227.0",
"binFileVersion": "16.0.18227.20002",
"path": "%programfiles%\\microsoft office\\root\\client\\appvlp.exe"
},{"aumid": "Microsoft.Office.DATABASECOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\database compare.lnk"
},{"productName": "microsoft office",
"originalFileName": "excel.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\excel.exe"
},{"aumid": "Microsoft.Office.EXCEL.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\excel.lnk"
},{"productName": "microsoft office",
"originalFileName": "setlang.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\setlang.exe"
},{"aumid": "Microsoft.Office.SETLANG.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\office language preferences.lnk"
},{"productName": "microsoft onenote",
"originalFileName": "onenote.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\onenote.exe"
},{"aumid": "Microsoft.Office.ONENOTE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onenote.lnk"
},{"productName": "microsoft outlook",
"originalFileName": "outlook.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\outlook.exe"
},{"aumid": "Microsoft.Office.OUTLOOK.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\outlook (classic).lnk"
},{"productName": "microsoft office",
"originalFileName": "powerpnt.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\powerpnt.exe"
},{"aumid": "Microsoft.Office.POWERPNT.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\powerpoint.lnk"
},{"productName": "microsoft office",
"originalFileName": "mspub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\mspub.exe"
},{"aumid": "Microsoft.Office.MSPUB.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\publisher.lnk"
},{"productName": "microsoft office",
"originalFileName": "ocpubmgr.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\ocpubmgr.exe"
},{"aumid": "Microsoft.Office.OcPubMgr.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\skype for business recording manager.lnk"
},{"productName": "microsoft office",
"originalFileName": "lync.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\lync.exe"
},{"aumid": "Microsoft.Office.lync.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\skype for business.lnk"
},{"aumid": "Microsoft.Office.SPREADSHEETCOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\spreadsheet compare.lnk"
},{"aumid": "Microsoft.Office.OneNote.MemoryPreview",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\sticky notes (new).lnk"
},{"productName": "microsoft office",
"originalFileName": "msoev.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msoev.exe"
},{"aumid": "Microsoft.Office.msoev.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\telemetry log for office.lnk"
},{"productName": "microsoft office",
"originalFileName": "winword.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\winword.exe"
},{"aumid": "Microsoft.Office.WINWORD.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\word.lnk"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\VLC media player",
"programId": "0000863d5786bd660050e532b4fa5b66170e0000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files\\VideoLAN\\VLC",
"files" : [{"path": "%programfiles%\\videolan\\vlc\\documentation.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\documentation.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\documentation.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\news.txt"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\news.txt",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\release notes.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\videolan website.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\videolan website.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\videolan website.lnk"
},{"productName": "vlc media player",
"originalFileName": "vlc.exe",
"companyName": "videolan",
"binProductVersion": "3.0.18.0",
"binFileVersion": "3.0.18.0",
"path": "%programfiles%\\videolan\\vlc\\vlc.exe"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{51325390-AE6A-68FC-A315-0950CC83A166}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player - reset preferences and cache files.lnk"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%public%\\desktop\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{30BD9A02-CB9A-93FD-A859-09C8803F2346}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player skinned.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\uninstall.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - fr-ca",
"programId": "000091d9c85e9191ed4199cc21e4e403fcff0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1FC1A6C2-576E-489A-9B4A-92D21F542136}",
"programId": "00009e26d04962c79c9b1834c0c8068bafa600000000",
"compatFlags": "0",
"files" : [{"productName": "microsoft® windows® operating system",
"originalFileName": "expediteupdater",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\expediteupdater.exe"
},{"productName": "microsoft® windows® operating system",
"originalFileName": "uhssvc",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\uhssvc.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{731F6BAA-A986-45A4-8936-7C3AAAAA760B}",
"programId": "0000a8571596f0d284d93f5c20946e72f4f600000904",
"compatFlags": "4",
"restoreAction": "RootDirPath|C:\\Program Files\\Microsoft Office\\root\\integration\\Addons\\",
"files" : [{"productName": "microsoft teams",
"originalFileName": "setup.exe",
"companyName": "microsoft corporation",
"binProductVersion": "1.5.0.30767",
"binFileVersion": "1.5.0.30767",
"path": "%programfiles(x86)%\\teams installer\\teams.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\OneDriveSetup.exe",
"programId": "0000bf803468de7ad95869e0f9e481c147100000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|c:\\program files\\microsoft onedrive\\24.244.1204.0003",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "filecoauth.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filecoauth.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesyncconfig.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesyncconfig.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesynchelper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesynchelper.exe"
},{"productName": "microsoft sharepoint",
"originalFileName": "microsoft.sharepoint.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.exe"
},{"productName": "microsoft sharepoint native messaging client",
"originalFileName": "microsoft.sharepoint.nativemessaging.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.nativemessagingclient.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft Edge",
"programId": "0000c4d0f33b9aab940ec62ff604c4863eb10000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files (x86)\\Microsoft\\Edge\\Application",
"files" : [{"productName": "microsoft edge",
"originalFileName": "cookie_exporter.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\cookie_exporter.exe"
},{"productName": "microsoft edge",
"originalFileName": "elevation_service.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\elevation_service.exe"
},{"productName": "microsoft edge",
"originalFileName": "identity_helper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\identity_helper.exe"
},{"productName": "ietoedge bho",
"originalFileName": "ie_to_edge_stub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\bho\\ie_to_edge_stub.exe"
},{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\msedge.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F132AF7F-7BCA-4EDE-8A7C-958108FE7DBC}",
"programId": "0000f3ba61ac2186f429d7e48ceb429a7bb60000ffff",
"compatFlags": "16384",
"restoreAction": "RootDirPath|C:\\Program Files\\Realtek\\Audio\\HDA",
"files" : [{"productName": "hd audio background process",
"originalFileName": "rthdvbgproc.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.225",
"binFileVersion": "1.0.0.225",
"path": "%programfiles%\\realtek\\audio\\hda\\ravbg64.exe"
},{"productName": "hd audio control panel",
"originalFileName": "rtdcpl.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "1.0.0.13",
"binFileVersion": "1.0.0.13",
"path": "%programfiles%\\realtek\\audio\\hda\\rtdcpl64.exe"
},{"productName": "realtek audio service",
"originalFileName": "rtkaudioservice.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.68",
"binFileVersion": "1.0.0.68",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkaudioservice64.exe"
},{"productName": "realtek hd audio manager",
"originalFileName": "rtkngui.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.502.0",
"binFileVersion": "1.0.502.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkngui64.exe"
},{"productName": "realtek hd auido update and remove driver tool",
"originalFileName": "rtlupd.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "3.0.0.0",
"binFileVersion": "3.0.0.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtlupd64.exe"
}]
},{"path": "c:\\program files\\microsoft onedrive\\onedrive.exe",
"programId": "0006bf803468de7ad95869e0f9e481c1471000000904",
"compatFlags": "0",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "onedrive.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\onedrive.exe"
},{"aumid": "Microsoft.SkyDrive.Desktop",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onedrive.lnk"
}]
}],
"Update" : [{"path": "c:\\program files (x86)\\microsoft\\edge\\application\\msedge.exe",
"programId": "000092b6db162d4cfc1f8e8b492d66dbb3760000ffff",
"compatFlags": "0",
"files" : [{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\msedge.exe"
},{"aumid": "MSEdge",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft edge.lnk"
}]
}],
"Uninstall" : [{"programId": "0000e96975da1b54f6379ab67db1bf4e2b560000ffff",
"files" : [{"aumid": "MSEdge"
}]
},{"programId": "0000eb85c5a8a2001da5f2bdeb7a030b31970000ffff",
"files" : [{"aumid": "MSEdge"
}]
},{"programId": "@ÀF`ÈystemRoot]}
##tT>##
##fF>##
##fF<## KDrv.txt
ntoskrnl.exe
hal.dll
kd.dll
mcupdate_GenuineIntel.dll
CLFS.SYS
tm.sys
PSHED.dll
BOOTVID.dll
FLTMGR.SYS
msrpc.sys
ksecdd.sys
clipsp.sys
cmimcext.sys
werkernel.sys
ntosext.sys
CI.dll
cng.sys
Wdf01000.sys
WDFLDR.SYS
WppRecorder.sys
SleepStudyHelper.sys
acpiex.sys
msseccore.sys
SgrmAgent.sys
ACPI.sys
WMILIB.SYS
intelpep.sys
WindowsTrustedRT.sys
IntelTA.sys
WindowsTrustedRTProxy.sys
pcw.sys
msisadrv.sys
pci.sys
vdrvroot.sys
pdc.sys
CEA.sys
partmgr.sys
spaceport.sys
volmgr.sys
volmgrx.sys
mountmgr.sys
iaStorA.sys
storport.sys
EhStorClass.sys
fileinfo.sys
Wof.sys
WdFilter.sys
Ntfs.sys
Fs_Rec.sys
ndis.sys
NETIO.SYS
ksecpkg.sys
tcpip.sys
fwpkclnt.sys
wfplwfs.sys
fvevol.sys
volume.sys
volsnap.sys
rdyboost.sys
mup.sys
iorate.sys
disk.sys
CLASSPNP.SYS
crashdmp.sys
cdrom.sys
filecrypt.sys
tbs.sys
Null.SYS
Beep.SYS
dxgkrnl.sys
watchdog.sys
BasicDisplay.sys
BasicRender.sys
Npfs.SYS
Msfs.SYS
CimFS.SYS
tdx.sys
TDI.SYS
netbt.sys
afunix.sys
afd.sys
vwififlt.sys
pacer.sys
ndiscap.sys
netbios.sys
serial.sys
Vid.sys
winhvr.sys
rdbss.sys
csc.sys
nsiproxy.sys
npsvctrig.sys
mssmbios.sys
gpuenergydrv.sys
dfsc.sys
fastfat.SYS
bam.sys
ahcache.sys
CompositeBus.sys
kdnic.sys
umbus.sys
igdkmd64.sys
HDAudBus.sys
portcls.sys
drmk.sys
ks.sys
USBXHCI.SYS
ucx01000.sys
TeeDriverW8x64.sys
serenum.sys
e1d65x64.sys
usbehci.sys
USBPORT.SYS
intelppm.sys
wmiacpi.sys
NdisVirtualBus.sys
swenum.sys
rdpbus.sys
usbhub.sys
USBD.SYS
UsbHub3.sys
RTDVHD64.sys
ksthunk.sys
usbccgp.sys
win32k.sys
hidusb.sys
HIDCLASS.SYS
HIDPARSE.SYS
win32kbase.sys
mouhid.sys
mouclass.sys
win32kfull.sys
kbdhid.sys
kbdclass.sys
dump_diskdump.sys
dump_iaStorA.sys
dump_dumpfve.sys
dxgmms2.sys
monitor.sys
cdd.dll
luafv.sys
wcifs.sys
cldflt.sys
storqosflt.sys
UCPD.sys
bindflt.sys
mslldp.sys
lltdio.sys
wanarp.sys
rspndr.sys
ndisuio.sys
nwifi.sys
msquic.sys
HTTP.sys
mpsdrv.sys
bowser.sys
mrxsmb.sys
mrxsmb20.sys
condrv.sys
srvnet.sys
mmcss.sys
peauth.sys
Ndu.sys
tcpipreg.sys
srv2.sys
rassstp.sys
NDProxy.sys
AgileVpn.sys
rasl2tp.sys
raspptp.sys
raspppoe.sys
ndistapi.sys
ndiswan.sys
WdNisDrv.sys
##fF>##
##fF<## SMenu.txt
[Access.lnk] "C:\Program Files\Microsoft Office\root\Office16\MSACCESS.EXE" "" "Build a professional app quickly to manage data."
[Excel.lnk] "C:\Program Files\Microsoft Office\root\Office16\EXCEL.EXE" "" "Easily discover, visualize, and share insights from your data."
[Immersive Control Panel.lnk] "%windir%\System32\Control.exe" "" "Change settings and customize the functionality of your computer"
[Microsoft Edge.lnk] "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" "" "Browse the web"
[OneDrive.lnk] "C:\Program Files\Microsoft OneDrive\OneDrive.exe" "" "Keep your most important files with you wherever you go, on any device."
[OneNote.lnk] "C:\Program Files\Microsoft Office\root\Office16\ONENOTE.EXE" "" "Take notes and have them when you need them."
[Outlook (classic).lnk] "C:\Program Files\Microsoft Office\root\Office16\OUTLOOK.EXE" "" "Manage your email, schedules, contacts, and to-dos."
[PowerPoint.lnk] "C:\Program Files\Microsoft Office\root\Office16\POWERPNT.EXE" "" "Design and deliver beautiful presentations with ease and confidence."
[Publisher.lnk] "C:\Program Files\Microsoft Office\root\Office16\MSPUB.EXE" "" "Create professional-grade publications that make an impact."
[Skype for Business.lnk] "C:\Program Files\Microsoft Office\root\Office16\lync.exe" "" "Connect with people everywhere through voice and video calls, Skype Meetings, and IM."
[Sticky Notes (new).lnk] "C:\Program Files\Microsoft Office\root\Office16\ONENOTE.EXE" "/memoryWindow start" "Take notes and have them when you need them."
[Word.lnk] "C:\Program Files\Microsoft Office\root\Office16\WINWORD.EXE" "" "Create beautiful documents, easily work with others, and enjoy the read."
[Windows PowerShell\Windows PowerShell ISE (x86).lnk] "%windir%\syswow64\WindowsPowerShell\v1.0\PowerShell_ISE.exe" "" "Windows PowerShell Integrated Scripting Environment. Performs object-based (command-line) functions"
[Windows PowerShell\Windows PowerShell ISE.lnk] "%windir%\system32\WindowsPowerShell\v1.0\PowerShell_ISE.exe" "" "Windows PowerShell Integrated Scripting Environment. Performs object-based (command-line) functions"
[VideoLAN\Documentation.lnk] "C:\Program Files\VideoLAN\VLC\Documentation.url" "" ""
[VideoLAN\Release Notes.lnk] "C:\Program Files\VideoLAN\VLC\NEWS.txt" "" ""
[VideoLAN\VideoLAN Website.lnk] "C:\Program Files\VideoLAN\VLC\VideoLAN Website.url" "" ""
[VideoLAN\VLC media player - reset preferences and cache files.lnk] "C:\Program Files\VideoLAN\VLC\vlc.exe" "--reset-config --reset-plugins-cache vlc://quit" ""
[VideoLAN\VLC media player skinned.lnk] "C:\Program Files\VideoLAN\VLC\vlc.exe" "-Iskins" ""
[VideoLAN\VLC media player.lnk] "C:\Program Files\VideoLAN\VLC\vlc.exe" "" ""
[System Tools\Task Manager.lnk] "%windir%\system32\taskmgr.exe" "/7" "Manage running apps and view system performance"
[Microsoft Office Tools\Database Compare.lnk] "C:\Program Files\Microsoft Office\root\Client\AppVLP.exe" ""C:\Program Files (x86)\Microsoft Office\Office16\DCF\DATABASECOMPARE.EXE"" "Compare versions of an Access database."
[Microsoft Office Tools\Office Language Preferences.lnk] "C:\Program Files\Microsoft Office\root\Office16\SETLANG.EXE" "" "Change the language preferences for Office applications."
[Microsoft Office Tools\Skype for Business Recording Manager.lnk] "C:\Program Files\Microsoft Office\root\Office16\OcPubMgr.exe" "" "Manage all your Skype for Business recordings in one place."
[Microsoft Office Tools\Spreadsheet Compare.lnk] "C:\Program Files\Microsoft Office\root\Client\AppVLP.exe" ""C:\Program Files (x86)\Microsoft Office\Office16\DCF\SPREADSHEETCOMPARE.EXE"" "Compare versions of an Excel workbook."
[Microsoft Office Tools\Telemetry Log for Office.lnk] "C:\Program Files\Microsoft Office\root\Office16\msoev.exe" "" "View critical errors, compatibility issues and workaround information for your Office solutions by using Office Telemetry Log."
[Administrative Tools\Component Services.lnk] "%windir%\system32\comexp.msc" "" "Manage COM+ applications, COM and DCOM system configuration, and the Distributed Transaction Coordinator."
[Administrative Tools\Computer Management.lnk] "%windir%\system32\compmgmt.msc" "/s" "Manages disks and provides access to other tools to manage local and remote computers."
[Administrative Tools\dfrgui.lnk] "%windir%\system32\dfrgui.exe" "" "Optimizes files and fragments on your volumes so that your computer runs faster and more efficiently."
[Administrative Tools\Disk Cleanup.lnk] "%windir%\system32\cleanmgr.exe" "" "Enables you to clear your disk of unnecessary files."
[Administrative Tools\Event Viewer.lnk] "%windir%\system32\eventvwr.msc" "/s" "View monitoring and troubleshooting messages from windows and other programs."
[Administrative Tools\iSCSI Initiator.lnk] "%windir%\system32\iscsicpl.exe" "" "Connect to remote iSCSI targets and configure connection settings."
[Administrative Tools\Memory Diagnostics Tool.lnk] "%windir%\system32\MdSched.exe" "" "Check your computer for memory problems."
[Administrative Tools\ODBC Data Sources (32-bit).lnk] "%windir%\syswow64\odbcad32.exe" "" ""
[Administrative Tools\ODBC Data Sources (64-bit).lnk] "%windir%\system32\odbcad32.exe" "" "Maintains ODBC data sources and drivers."
[Administrative Tools\Performance Monitor.lnk] "%windir%\system32\perfmon.msc" "/s" "Diagnose performance issues and collect performance data."
[Administrative Tools\Print Management.lnk] "%systemroot%\system32\printmanagement.msc" "" "Manages local printers and remote print servers."
[Administrative Tools\RecoveryDrive.lnk] "%windir%\system32\RecoveryDrive.exe" "" "Create a recovery drive"
[Administrative Tools\Registry Editor.lnk] "%windir%\regedit.exe" "" "Registry Editor"
[Administrative Tools\Resource Monitor.lnk] "%windir%\system32\perfmon.exe" "/res" "Monitor the usage and performance of the following resources in real time: CPU, Disk, Network and Memory."
[Administrative Tools\Security Configuration Management.lnk] "%windir%\system32\secpol.msc" "/s" "View and modify local security policy, such as user rights and audit policies."
[Administrative Tools\services.lnk] "%windir%\system32\services.msc" "" "Starts, stops, and configures Windows services."
[Administrative Tools\System Configuration.lnk] "%windir%\system32\msconfig.exe" "" "Perform advanced troubleshooting and system configuration"
[Administrative Tools\System Information.lnk] "%windir%\system32\msinfo32.exe" "" "Display detailed information about your computer."
[Administrative Tools\Task Scheduler.lnk] "%windir%\system32\taskschd.msc" "/s" "Schedule computer tasks to run automatically."
[Administrative Tools\Windows Defender Firewall with Advanced Security.lnk] "%windir%\system32\WF.msc" "" "Configure policies that provide enhanced network security for Windows computers."
[Accessories\Math Input Panel.lnk] "%CommonProgramFiles%\Microsoft Shared\Ink\mip.exe" "" "Math Input Panel"
[Accessories\Notepad.lnk] "%windir%\system32\notepad.exe" "" "Creates and edits text files using basic text formatting."
[Accessories\Paint.lnk] "%windir%\system32\mspaint.exe" "" "Create and edit drawings."
[Accessories\Quick Assist.lnk] "%windir%\system32\quickassist.exe" "" "Connect to another user's computer to help troubleshoot problems"
[Accessories\Remote Desktop Connection.lnk] "%windir%\system32\mstsc.exe" "" "Use your computer to connect to a computer that is located elsewhere and run programs or access files."
[Accessories\Snipping Tool.lnk] "%windir%\system32\SnippingTool.exe" "" "Capture a portion of your screen so you can save, annotate, or share the image."
[Accessories\Steps Recorder.lnk] "%windir%\system32\psr.exe" "" "Capture steps with screenshots to save or share."
[Accessories\Windows Fax and Scan.lnk] "%windir%\system32\WFS.exe" "" "Send and receive faxes or scan pictures and documents."
[Accessories\Windows Media Player.lnk] "%ProgramFiles(x86)%\Windows Media Player\wmplayer.exe" "/prefetch:1" ""
[Accessories\Wordpad.lnk] "%ProgramFiles%\Windows NT\Accessories\wordpad.exe" "" "Creates and edits text documents with complex formatting."
[Accessories\System Tools\Character Map.lnk] "%windir%\system32\charmap.exe" "" "Selects special characters and copies them to your document."
[Accessibility\Speech Recognition.lnk] "%windir%\Speech\Common\sapisvr.exe" "-SpeechUX" "Dictate text and control your computer by voice."
[OneDrive.lnk] "%USERPROFILE%\AppData\Local\Microsoft\OneDrive\OneDrive.exe" "" ""
[Windows PowerShell\Windows PowerShell (x86).lnk] "%SystemRoot%\syswow64\WindowsPowerShell\v1.0\powershell.exe" "" "Performs object-based (command-line) functions"
[Windows PowerShell\Windows PowerShell.lnk] "%SystemRoot%\system32\WindowsPowerShell\v1.0\powershell.exe" "" "Performs object-based (command-line) functions"
[System Tools\Administrative Tools.lnk] "%windir%\system32\control.exe" "/name Microsoft.AdministrativeTools" "Windows Administrative Tools"
[System Tools\Command Prompt.lnk] "%windir%\system32\cmd.exe" "" "Performs text-based (command-line) functions."
[System Tools\computer.lnk] "" "" ""
[System Tools\Control Panel.lnk] "" "" ""
[System Tools\File Explorer.lnk] "" "" "Displays the files and folders on your computer."
[System Tools\Run.lnk] "" "" ""
[Accessibility\Magnify.lnk] "%windir%\system32\magnify.exe" "" "Enlarges selected text and other on-screen items for easier viewing."
[Accessibility\Narrator.lnk] "%windir%\system32\narrator.exe" "" "Reads on-screen text, dialog boxes, menus, and buttons aloud if speakers or a sound output device is installed."
[Accessibility\On-Screen Keyboard.lnk] "%windir%\system32\osk.exe" "" "Displays a keyboard that is controlled by a mouse or switch input device."
##fF>##
##fF<## ARP.txt
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AddressBook]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Connection Manager]
"SystemComponent"=dword:1

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\DirectDrawEx]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\DXM_Runtime]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Fontcore]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\IE40]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\IE4Data]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\IE5BAKEX]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\IEData]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MobileOptionPack]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MPlayer2]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\mspaint-b330ad9e-f80b-4c96-9949-4b4228be9a6e]
"DisplayIcon"="C:\Windows\System32\mspaint.exe"
"DisplayName_Localized"="@C:\Windows\System32\mspaint.exe,-57344"
"InstallLocation"="C:\Windows\System32\"
"NoModify"=dword:1
"Publisher"="Microsoft Corporation"
"UninstallString"=""C:\Windows\System32\mspaint.exe" /uninstall"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\mstsc-4b0a31aa-df6a-4307-9b47-d5cc50009643]
"DisplayIcon"="C:\Windows\System32\mstsc.exe"
"DisplayName_Localized"="@C:\Windows\System32\mstsc.exe,-4000"
"InstallLocation"="C:\Windows\System32\"
"NoModify"=dword:1
"Publisher"="Microsoft Corporation"
"UninstallString"=""C:\Windows\System32\mstsc.exe" /uninstall"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\O365ProPlusRetail - en-us]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\O365ProPlusRetail - es-mx]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\O365ProPlusRetail - fr-ca]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\OneDriveSetup.exe]
"DisplayName"="Microsoft OneDrive"
"DisplayIcon"="C:\Program Files\Microsoft OneDrive\24.244.1204.0003\OneDriveSetup.exe,-101"
"DisplayVersion"="24.244.1204.0003"
"HelpLink"="https://go.microsoft.com/fwlink/?LinkID=215117"
"Publisher"="Microsoft Corporation"
"UninstallString"=""C:\Program Files\Microsoft OneDrive\24.244.1204.0003\OneDriveSetup.exe"  /uninstall  /allusers "
"UrlUpdateInfo"="https://go.microsoft.com/fwlink/?LinkID=223554"
"EstimatedSize"=dword:60135
"NoRepair"=dword:1
"NoModify"=dword:1

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SchedulingAgent]

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SnippingTool-ee6eb196-db28-4d99-816d-fa9a63b4a377]
"DisplayIcon"="C:\Windows\System32\SnippingTool.exe"
"DisplayName_Localized"="@C:\Windows\System32\SnippingTool.exe,-15051"
"InstallLocation"="C:\Windows\System32\"
"NoModify"=dword:1
"Publisher"="Microsoft Corporation"
"UninstallString"=""C:\Windows\System32\SnippingTool.exe" /uninstall"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\VLC media player]
"DisplayName"="VLC media player"
"UninstallString"=""C:\Program Files\VideoLAN\VLC\uninstall.exe""
"InstallLocation"="C:\Program Files\VideoLAN\VLC"
"DisplayIcon"="C:\Program Files\VideoLAN\VLC\vlc.exe"
"DisplayVersion"="3.0.18"
"URLInfoAbout"="https://www.videolan.org/"
"Publisher"="VideoLAN"
"VersionMajor"="3"
"VersionMinor"="0"
"MementoSectionUsed"=""
"MementoSection_SEC01"=dword:1
"MementoSection_SEC02a"=dword:1
"MementoSection_SEC02b"=dword:1
"MementoSection_SEC03"=dword:1
"MementoSection_SEC04"=dword:1
"MementoSection_SEC05"=dword:1
"MementoSection_SEC_EXT_Audio_.3ga"=dword:1
"MementoSection_SEC_EXT_Audio_.669"=dword:1
"MementoSection_SEC_EXT_Audio_.a52"=dword:1
"MementoSection_SEC_EXT_Audio_.aac"=dword:1
"MementoSection_SEC_EXT_Audio_.ac3"=dword:1
"MementoSection_SEC_EXT_Audio_.adt"=dword:1
"MementoSection_SEC_EXT_Audio_.adts"=dword:1
"MementoSection_SEC_EXT_Audio_.aif"=dword:1
"MementoSection_SEC_EXT_Audio_.aifc"=dword:1
"MementoSection_SEC_EXT_Audio_.aiff"=dword:1
"MementoSection_SEC_EXT_Audio_.au"=dword:1
"MementoSection_SEC_EXT_Audio_.amr"=dword:1
"MementoSection_SEC_EXT_Audio_.aob"=dword:1
"MementoSection_SEC_EXT_Audio_.ape"=dword:1
"MementoSection_SEC_EXT_Audio_.caf"=dword:1
"MementoSection_SEC_EXT_Audio_.cda"=dword:1
"MementoSection_SEC_EXT_Audio_.dts"=dword:1
"MementoSection_SEC_EXT_Audio_.flac"=dword:1
"MementoSection_SEC_EXT_Audio_.it"=dword:1
"MementoSection_SEC_EXT_Audio_.m4a"=dword:1
"MementoSection_SEC_EXT_Audio_.m4p"=dword:1
"MementoSection_SEC_EXT_Audio_.mid"=dword:1
"MementoSection_SEC_EXT_Audio_.mka"=dword:1
"MementoSection_SEC_EXT_Audio_.mlp"=dword:1
"MementoSection_SEC_EXT_Audio_.mod"=dword:1
"MementoSection_SEC_EXT_Audio_.mp1"=dword:1
"MementoSection_SEC_EXT_Audio_.mp2"=dword:1
"MementoSection_SEC_EXT_Audio_.mp3"=dword:1
"MementoSection_SEC_EXT_Audio_.mpc"=dword:1
"MementoSection_SEC_EXT_Audio_.mpga"=dword:1
"MementoSection_SEC_EXT_Audio_.oga"=dword:1
"MementoSection_SEC_EXT_Audio_.oma"=dword:1
"MementoSection_SEC_EXT_Audio_.opus"=dword:1
"MementoSection_SEC_EXT_Audio_.qcp"=dword:1
"MementoSection_SEC_EXT_Audio_.ra"=dword:1
"MementoSection_SEC_EXT_Audio_.rmi"=dword:1
"MementoSection_SEC_EXT_Audio_.snd"=dword:1
"MementoSection_SEC_EXT_Audio_.s3m"=dword:1
"MementoSection_SEC_EXT_Audio_.spx"=dword:1
"MementoSection_SEC_EXT_Audio_.tta"=dword:1
"MementoSection_SEC_EXT_Audio_.voc"=dword:1
"MementoSection_SEC_EXT_Audio_.vqf"=dword:1
"MementoSection_SEC_EXT_Audio_.w64"=dword:1
"MementoSection_SEC_EXT_Audio_.wav"=dword:1
"MementoSection_SEC_EXT_Audio_.wma"=dword:1
"MementoSection_SEC_EXT_Audio_.wv"=dword:1
"MementoSection_SEC_EXT_Audio_.xa"=dword:1
"MementoSection_SEC_EXT_Audio_.xm"=dword:1
"MementoSection_SEC_EXT_Video_.3g2"=dword:1
"MementoSection_SEC_EXT_Video_.3gp"=dword:1
"MementoSection_SEC_EXT_Video_.3gp2"=dword:1
"MementoSection_SEC_EXT_Video_.3gpp"=dword:1
"MementoSection_SEC_EXT_Video_.amv"=dword:1
"MementoSection_SEC_EXT_Video_.asf"=dword:1
"MementoSection_SEC_EXT_Video_.avi"=dword:1
"MementoSection_SEC_EXT_Video_.bik"=dword:1
"MementoSection_SEC_EXT_Video_.dav"=dword:1
"MementoSection_SEC_EXT_Video_.divx"=dword:1
"MementoSection_SEC_EXT_Video_.drc"=dword:1
"MementoSection_SEC_EXT_Video_.dv"=dword:1
"MementoSection_SEC_EXT_Video_.dvr-ms"=dword:1
"MementoSection_SEC_EXT_Video_.evo"=dword:1
"MementoSection_SEC_EXT_Video_.f4v"=dword:1
"MementoSection_SEC_EXT_Video_.flv"=dword:1
"MementoSection_SEC_EXT_Video_.gvi"=dword:1
"MementoSection_SEC_EXT_Video_.gxf"=dword:1
"MementoSection_SEC_EXT_Video_.m1v"=dword:1
"MementoSection_SEC_EXT_Video_.m2t"=dword:1
"MementoSection_SEC_EXT_Video_.m2v"=dword:1
"MementoSection_SEC_EXT_Video_.m2ts"=dword:1
"MementoSection_SEC_EXT_Video_.m4v"=dword:1
"MementoSection_SEC_EXT_Video_.mkv"=dword:1
"MementoSection_SEC_EXT_Video_.mov"=dword:1
"MementoSection_SEC_EXT_Video_.mp2v"=dword:1
"MementoSection_SEC_EXT_Video_.mp4"=dword:1
"MementoSection_SEC_EXT_Video_.mp4v"=dword:1
"MementoSection_SEC_EXT_Video_.mpa"=dword:1
"MementoSection_SEC_EXT_Video_.mpe"=dword:1
"MementoSection_SEC_EXT_Video_.mpeg"=dword:1
"MementoSection_SEC_EXT_Video_.mpeg1"=dword:1
"MementoSection_SEC_EXT_Video_.mpeg2"=dword:1
"MementoSection_SEC_EXT_Video_.mpeg4"=dword:1
"MementoSection_SEC_EXT_Video_.mpg"=dword:1
"MementoSection_SEC_EXT_Video_.mpv2"=dword:1
"MementoSection_SEC_EXT_Video_.mts"=dword:1
"MementoSection_SEC_EXT_Video_.mtv"=dword:1
"MementoSection_SEC_EXT_Video_.mxf"=dword:1
"MementoSection_SEC_EXT_Video_.nsv"=dword:1
"MementoSection_SEC_EXT_Video_.nuv"=dword:1
"MementoSection_SEC_EXT_Video_.ogg"=dword:1
"MementoSection_SEC_EXT_Video_.ogm"=dword:1
"MementoSection_SEC_EXT_Video_.ogx"=dword:1
"MementoSection_SEC_EXT_Video_.ogv"=dword:1
"MementoSection_SEC_EXT_Video_.rec"=dword:1
"MementoSection_SEC_EXT_Video_.rm"=dword:1
"MementoSection_SEC_EXT_Video_.rmvb"=dword:1
"MementoSection_SEC_EXT_Video_.rpl"=dword:1
"MementoSection_SEC_EXT_Video_.thp"=dword:1
"MementoSection_SEC_EXT_Video_.tod"=dword:1
"MementoSection_SEC_EXT_Video_.tp"=dword:1
"MementoSection_SEC_EXT_Video_.ts"=dword:1
"MementoSection_SEC_EXT_Video_.tts"=dword:1
"MementoSection_SEC_EXT_Video_.vob"=dword:1
"MementoSection_SEC_EXT_Video_.vro"=dword:1
"MementoSection_SEC_EXT_Video_.webm"=dword:1
"MementoSection_SEC_EXT_Video_.wmv"=dword:1
"MementoSection_SEC_EXT_Video_.wtv"=dword:1
"MementoSection_SEC_EXT_Video_.xesc"=dword:1
"MementoSection_SEC_EXT_Other_.asx"=dword:1
"MementoSection_SEC_EXT_Other_.b4s"=dword:1
"MementoSection_SEC_EXT_Other_.cue"=dword:1
"MementoSection_SEC_EXT_Other_.ifo"=dword:1
"MementoSection_SEC_EXT_Other_.m3u"=dword:1
"MementoSection_SEC_EXT_Other_.m3u8"=dword:1
"MementoSection_SEC_EXT_Other_.pls"=dword:1
"MementoSection_SEC_EXT_Other_.ram"=dword:1
"MementoSection_SEC_EXT_Other_.sdp"=dword:1
"MementoSection_SEC_EXT_Other_.vlc"=dword:1
"MementoSection_SEC_EXT_Other_.wvx"=dword:1
"MementoSection_SEC_EXT_Other_.xspf"=dword:1
"MementoSection_SEC_EXT_Other_.wpl"=dword:1
"MementoSection_SEC_EXT_Other_.zpl"=dword:1
"MementoSection_SEC_EXT_SKIN_.vlt"=dword:0
"MementoSection_SEC_EXT_SKIN_.wsz"=dword:0
"MementoSection_SEC_EXT_Other_.iso"=dword:0
"MementoSection_SEC_EXT_Other_.zip"=dword:0
"MementoSection_SEC_EXT_Other_.rar"=dword:0
"MementoSection_SEC07"=dword:1
"MementoSection_SEC08"=dword:0

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\WIC]
"NoRemove"=dword:1

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{1FC1A6C2-576E-489A-9B4A-92D21F542136}]
"AuthorizedCDFPrefix"=""
"Comments"=""
"Contact"=""
"DisplayVersion"="********"
"HelpLink"=""
"HelpTelephone"=""
"InstallDate"="20240909"
"InstallLocation"=""
"InstallSource"="C:\Windows\TEMP\C54C2B43-0270-4CFB-B1F6-1D5C55D1B696\"
"ModifyPath"="MsiExec.exe /X{1FC1A6C2-576E-489A-9B4A-92D21F542136}"
"NoModify"=dword:1
"NoRepair"=dword:1
"Publisher"="Microsoft Corporation"
"Readme"=""
"Size"=""
"EstimatedSize"=dword:41a
"UninstallString"="MsiExec.exe /X{1FC1A6C2-576E-489A-9B4A-92D21F542136}"
"URLInfoAbout"=""
"URLUpdateInfo"=""
"VersionMajor"=dword:3
"VersionMinor"=dword:4a
"WindowsInstaller"=dword:1
"Version"=dword:34a0000
"Language"=dword:0
"DisplayName"="Microsoft Update Health Tools"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{85C69797-7336-4E83-8D97-32A7C8465A3B}]
"AuthorizedCDFPrefix"=""
"Comments"=""
"Contact"=""
"DisplayVersion"="8.94.0.0"
"HelpLink"="http://support.microsoft.com/kb/5001716"
"HelpTelephone"=""
"InstallDate"="20240909"
"InstallLocation"=""
"InstallSource"="C:\Windows\SoftwareDistribution\Download\288e5bcf02690b1773d1985d53d1ed9b\img\"
"ModifyPath"="MsiExec.exe /X{85C69797-7336-4E83-8D97-32A7C8465A3B}"
"NoModify"=dword:1
"NoRepair"=dword:1
"Publisher"="Microsoft Corporation"
"Readme"=""
"Size"=""
"EstimatedSize"=dword:344
"UninstallString"="MsiExec.exe /X{85C69797-7336-4E83-8D97-32A7C8465A3B}"
"URLInfoAbout"="http://support.microsoft.com/kb/5001716"
"URLUpdateInfo"=""
"VersionMajor"=dword:8
"VersionMinor"=dword:5e
"WindowsInstaller"=dword:1
"Version"=dword:85e0000
"Language"=dword:0
"DisplayName"="Update for Windows 10 for x64-based Systems (KB5001716)"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{90160000-007E-0000-1000-0000000FF1CE}]
"AuthorizedCDFPrefix"=""
"Comments"=""
"Contact"=""
"DisplayVersion"="16.0.18324.20168"
"HelpLink"=""
"HelpTelephone"=""
"InstallDate"="20250118"
"InstallLocation"=""
"InstallSource"="c:\program files\microsoft office\root\integration\"
"ModifyPath"="MsiExec.exe /I{90160000-007E-0000-1000-0000000FF1CE}"
"Publisher"="Microsoft Corporation"
"Readme"=""
"Size"=""
"EstimatedSize"=dword:1c38
"SystemComponent"=dword:1
"UninstallString"="MsiExec.exe /I{90160000-007E-0000-1000-0000000FF1CE}"
"URLInfoAbout"=""
"URLUpdateInfo"=""
"VersionMajor"=dword:10
"VersionMinor"=dword:0
"WindowsInstaller"=dword:1
"Version"=dword:10004794
"Language"=dword:0
"DisplayName"="Office 16 Click-to-Run Licensing Component"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{90160000-008C-0000-1000-0000000FF1CE}]
"AuthorizedCDFPrefix"=""
"Comments"=""
"Contact"=""
"DisplayVersion"="16.0.18324.20194"
"HelpLink"=""
"HelpTelephone"=""
"InstallDate"="20250118"
"InstallLocation"=""
"InstallSource"="c:\program files\microsoft office\root\integration\"
"ModifyPath"="MsiExec.exe /X{90160000-008C-0000-1000-0000000FF1CE}"
"NoModify"=dword:1
"Publisher"="Microsoft Corporation"
"Readme"=""
"Size"=""
"EstimatedSize"=dword:8868
"SystemComponent"=dword:1
"UninstallString"="MsiExec.exe /X{90160000-008C-0000-1000-0000000FF1CE}"
"URLInfoAbout"=""
"URLUpdateInfo"=""
"VersionMajor"=dword:10
"VersionMinor"=dword:0
"WindowsInstaller"=dword:1
"Version"=dword:10004794
"Language"=dword:0
"DisplayName"="Office 16 Click-to-Run Extensibility Component"

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\AddressBook]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Connection Manager]
"SystemComponent"=dword:1

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\DirectDrawEx]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\DXM_Runtime]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Fontcore]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\IE40]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\IE4Data]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\IE5BAKEX]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\IEData]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Microsoft Edge]
"DisplayName"="Microsoft Edge"
"DisplayVersion"="135.0.3179.66"
"Version"="135.0.3179.66"
"NoRemove"=dword:1

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Microsoft Edge Update]
"DisplayVersion"="1.3.195.49"
"Version"="1.3.195.49"
"NoRemove"=dword:1

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Microsoft EdgeWebView]
"DisplayName"="Microsoft Edge WebView2 Runtime"

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\MobileOptionPack]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\MPlayer2]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\SchedulingAgent]

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\WIC]
"NoRemove"=dword:1

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\{731F6BAA-A986-45A4-8936-7C3AAAAA760B}]
"AuthorizedCDFPrefix"=""
"Comments"=""
"Contact"=""
"DisplayVersion"="1.5.0.30767"
"HelpLink"=""
"HelpTelephone"=""
"InstallDate"="20231106"
"InstallLocation"=""
"InstallSource"="C:\Program Files\Microsoft Office\root\integration\Addons\"
"ModifyPath"="MsiExec.exe /I{731F6BAA-A986-45A4-8936-7C3AAAAA760B}"
"Publisher"="Microsoft Corporation"
"Readme"=""
"Size"=""
"EstimatedSize"=dword:20f30
"UninstallString"="MsiExec.exe /I{731F6BAA-A986-45A4-8936-7C3AAAAA760B}"
"URLInfoAbout"=""
"URLUpdateInfo"=""
"VersionMajor"=dword:1
"VersionMinor"=dword:5
"WindowsInstaller"=dword:1
"Version"=dword:1050000
"Language"=dword:409
"DisplayName"="Teams Machine-Wide Installer"

[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\{F132AF7F-7BCA-4EDE-8A7C-958108FE7DBC}]
"DisplayName"="Realtek High Definition Audio Driver"
"UninstallString"="C:\Program Files\Realtek\Audio\HDA\RtlUpd64.exe -r -m -nrg2709"
"DisplayIcon"="C:\Program Files\Realtek\Audio\HDA\RtlUpd64.exe"
"Publisher"="Realtek Semiconductor Corp."
"DisplayVersion"="6.0.1.6086"
"InstallLocation"="C:\Program Files\Realtek\Audio\HDA"

##fF>##
