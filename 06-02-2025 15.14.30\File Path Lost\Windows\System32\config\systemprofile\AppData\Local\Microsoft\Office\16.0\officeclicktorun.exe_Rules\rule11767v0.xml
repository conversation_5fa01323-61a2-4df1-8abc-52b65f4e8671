<?xml version="1.0" encoding="utf-8"?>
<R Id="11767" V="0" DC="SM" T="Subrule" DCa="PSU" xmlns="">
  <S>
    <UTS T="1" Id="bmz0a" />
    <UTS T="2" Id="bmz0b" />
    <F T="3">
      <O T="EQ">
        <L>
          <S T="2" F="RequestSuccess" />
        </L>
        <R>
          <V V="true" T="B" />
        </R>
      </O>
    </F>
    <F T="4">
      <O T="EQ">
        <L>
          <S T="2" F="HttpStatus" />
        </L>
        <R>
          <V V="401" T="U32" />
        </R>
      </O>
    </F>
    <F T="5">
      <O T="EQ">
        <L>
          <S T="2" F="HttpStatus" />
        </L>
        <R>
          <V V="403" T="U32" />
        </R>
      </O>
    </F>
    <F T="6">
      <O T="EQ">
        <L>
          <S T="2" F="HttpStatus" />
        </L>
        <R>
          <V V="404" T="U32" />
        </R>
      </O>
    </F>
    <F T="7">
      <O T="AND">
        <L>
          <O T="GE">
            <L>
              <S T="2" F="HttpStatus" />
            </L>
            <R>
              <V V="400" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LT">
            <L>
              <S T="2" F="HttpStatus" />
            </L>
            <R>
              <V V="500" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="8">
      <O T="GE">
        <L>
          <S T="2" F="HttpStatus" />
        </L>
        <R>
          <V V="500" T="U32" />
        </R>
      </O>
    </F>
  </S>
  <C T="G" I="0" O="false">
    <O T="COALESCE">
      <L>
        <S T="1" F="RequestId" />
      </L>
      <R>
        <S T="2" F="RequestId" />
      </R>
    </O>
  </C>
  <C T="W" I="1" O="false">
    <O T="COALESCE">
      <L>
        <S T="1" F="RequestName" />
      </L>
      <R>
        <S T="2" F="RequestName" />
      </R>
    </O>
  </C>
  <C T="I32" I="2" O="false">
    <O T="COALESCE">
      <L>
        <S T="1" F="TypeOfAttempt" />
      </L>
      <R>
        <S T="2" F="TypeOfAttempt" />
      </R>
    </O>
  </C>
  <C T="U32" I="3" O="false">
    <C>
      <S T="3" />
    </C>
  </C>
  <C T="U32" I="4" O="false">
    <C>
      <S T="4" />
    </C>
  </C>
  <C T="U32" I="5" O="false">
    <C>
      <S T="5" />
    </C>
  </C>
  <C T="U32" I="6" O="false">
    <C>
      <S T="6" />
    </C>
  </C>
  <C T="U32" I="7" O="false">
    <C>
      <S T="7" />
    </C>
  </C>
  <C T="U32" I="8" O="false">
    <C>
      <S T="8" />
    </C>
  </C>
  <T>
    <S T="1" />
    <S T="2" />
  </T>
</R>
