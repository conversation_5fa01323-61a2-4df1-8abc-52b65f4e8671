<dcmPS:AdvDiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007" xmlns:wdem="http://diagnostics.microsoft.com/2007/08/WindowsDiagnosticExtendedMetadata">
  <DiagnosticIdentification>
    <ID>MaintenanceDiagnostic</ID>
    <Version>1.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Rootcauses>
    <!--<Rootcause>
      <ID>RC_InaccurateSystemTime</ID>
      <DisplayInformation>
        <Parameters />
        <Name>
          <dcmRS:LocalizeResourceElement comment="This is the title of a computer problem  that is displayed as part of an Advanced User view" index="13">System time is incorrectly set</dcmRS:LocalizeResourceElement>
        </Name>
        <Description>
          <dcmRS:LocalizeResourceElement comment="This is a summary description of a computer problem  that is displayed as part of an Advanced User view" index="14">When the system time is set incorrectly, features that rely on time measurement such as Product Activation, malware scanners, and browser certificates might not work correctly.</dcmRS:LocalizeResourceElement>
        </Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters />
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_InaccurateSystemTime.ps1</FileName>
          <ExtensionPoint />
        </Script>
        <ExtensionPoint />
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_SyncSystemTime</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>AccurateTime</Name>
                <DefaultValue />
              </Parameter>
            </Parameters>
            <Name>
              <dcmRS:LocalizeResourceElement comment="This is the title of a solution to a computer problem that is displayed as part of an Advanced User view" index="15">Synchronize clock time </dcmRS:LocalizeResourceElement>
            </Name>
            <Description>
              <dcmRS:LocalizeResourceElement comment="{NumberedPlaceholder=r&quot;%[a-zA-Z]+%&quot;} This is a summary description of a solution to a computer problem that is displayed as part of an Advanced User view" index="16">Enabling time synchronization and synchronizing the system clock with the time server will help date-sensitive programs work correctly.  Your clock will be synchronized to %AccurateTime%.</dcmRS:LocalizeResourceElement>
            </Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters />
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_SyncSystemTime.ps1</FileName>
            <ExtensionPoint />
          </Script>
          <ExtensionPoint />
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters />
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_InaccurateSystemTime.ps1</FileName>
          <ExtensionPoint />
        </Script>
        <ExtensionPoint />
      </Verifier>
      <ContextParameters />
      <ExtensionPoint />
    </Rootcause>-->
    <Rootcause>
      <ID>RC_WERQueue</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>UnwantedSpace</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-2013</Name>
        <Description>@diagpackage.dll,-2014</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_WERQueue.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_MachineWERQueue</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2015</Name>
            <Description>@diagpackage.dll,-2016</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>UnwantedSpace</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_MachineWERQueue.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
          <ID>RS_UserWERQueue</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2017</Name>
            <Description>@diagpackage.dll,-2018</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>UnwantedSpace</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_UserWERQueue.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_WERQueue.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_DiagnosticHistory</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>UnwantedSpace</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-2019</Name>
        <Description>@diagpackage.dll,-2020</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_DiagnosticHistory.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_UserDiagnosticHistory</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2025</Name>
            <Description>@diagpackage.dll,-2026</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>UnwantedSpace</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_UserDiagnosticHistory.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
          <ID>RS_AdminDiagnosticHistory</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2035</Name>
            <Description>@diagpackage.dll,-2036</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>UnwantedSpace</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_AdminDiagnosticHistory.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_DiagnosticHistory.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
      <SingleResponseInteractions/>
      <MultipleResponseInteractions>
               <MultipleResponseInteraction>
                <AllowDynamicResponses>true</AllowDynamicResponses>
                <Choices/>
                <ID>IT_GetUnusedIconsToCleanup</ID>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-511</Name>
                  <Description>@diagpackage.dll,-514</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint/>
              </MultipleResponseInteraction>
      </MultipleResponseInteractions>
      <TextInteractions/>
      <PauseInteractions/>
      <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Notification>
      <EventID>100</EventID>
      <EventVersion>0</EventVersion>
      <EventData>
        <DefaultStateID>0</DefaultStateID>
        <DisabledStateID>2</DisabledStateID>
        <StateID>1</StateID>
      </EventData>
    </Notification>
    <Maintenance/>
    <Icon>@DiagPackage.dll,-2000</Icon>
    <HelpKeywords>@DiagPackage.dll,-1003</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-1004</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-1005</HelpKeywords>
  </ExtensionPoint>
</dcmPS:AdvDiagnosticPackage>