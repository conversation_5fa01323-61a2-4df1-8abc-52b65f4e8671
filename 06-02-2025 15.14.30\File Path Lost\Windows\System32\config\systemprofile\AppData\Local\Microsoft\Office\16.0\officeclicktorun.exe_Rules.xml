<Rules><R Id="130010" V="0" DC="SM" EN="Office.Telemetry.NonOfficeEventCount" ATT="f998cc5ba4d448d6a1e8e913ff18be94-dd122e0a-fcf8-4dc5-9dbb-6afac5325183-7405" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="4pjk9" />
    <A T="2" E="TelemetryShutdown" />
    <A T="3" E="TelemetrySuspend" />
  </S>
  <G>
    <S T="1">
      <F N="EventName" />
    </S>
  </G>
  <C T="W" I="0" O="false" N="NonOfficeEventName">
    <S T="1" F="EventName" />
  </C>
  <C T="U64" I="1" O="false" N="EventCount">
    <C>
      <S T="1" />
    </C>
  </C>
  <T>
    <S T="2" />
    <S T="3" />
  </T>
  <ST>
    <S T="1" />
  </ST>
</R><R Id="120402" V="21" DC="SM" EN="Office.System.SystemHealthUngracefulAppExitDesktop" ATT="cd836626611c4caaa8fc5b2e728ee81d-3b6d6c45-6377-4bf5-9792-dbf8e1881088-7521" SP="CriticalCensus" DL="A" DCa="PSP" xmlns="">
  <RIS>
    <RI N="Crash" />
    <RI N="Metadata" />
  </RIS>
  <S>
    <UTS T="1" Id="824rc" />
    <SS T="2" G="{68442bc6-3519-4b08-a80c-e0a68fc8cda3}" />
    <TR T="3" />
  </S>
  <C T="FT" I="0" O="false" N="DetectionTime">
    <S T="3" F="TimeStamp100ns" />
  </C>
  <C T="FT" I="1" O="false" N="CrashedProcessSessionInitTime">
    <S T="1" F="CrashedSessionInitTime" />
  </C>
  <C T="G" I="2" O="false" N="CrashedProcessSessionID">
    <S T="1" F="CrashedSessionId" />
  </C>
  <C T="U8" I="3" O="false" N="CrashType">
    <S T="1" F="CrashType" />
  </C>
  <C T="W" I="4" O="true" N="PreviousBuild">
    <S T="1" F="PreviousBuild" M="Ignore" />
  </C>
  <C T="U32" I="5" O="true" N="InstallMethod">
    <S T="1" F="InstallMethod" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="ExceptionCode">
    <S T="1" F="HexExceptionCode" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="ModuleOffset">
    <S T="1" F="HexModuleOffset" M="Ignore" />
  </C>
  <C T="W" I="8" O="true" N="ModuleVersion">
    <S T="1" F="ModuleVersion" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="CrashedEcsETag">
    <S T="1" F="CrashedEcsETag" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="CrashedModuleName">
    <S T="1" F="CollectableModuleName" M="Ignore" />
  </C>
  <C T="W" I="11" O="true" N="CrashedTag">
    <S T="1" F="CrashTag" M="Ignore" />
  </C>
  <C T="U32" I="12" O="true" N="UninitLibletId">
    <S T="1" F="UninitLibletId" M="Ignore" />
  </C>
  <C T="U32" I="13" O="true" N="OfficeUILang">
    <S T="1" F="OfficeUILang" M="Ignore" />
  </C>
  <C T="W" I="14" O="true" N="ProcessorArchitecture">
    <S T="1" F="ProcessorArchitecture" M="Ignore" />
  </C>
  <C T="U64" I="15" O="true" N="SessionFlags">
    <S T="1" F="SessionFlags" M="Ignore" />
  </C>
  <C T="FT" I="16" O="true" N="CrashedSessionUninitTime">
    <S T="1" F="CrashedSessionUninitTime" M="Ignore" />
  </C>
  <C T="W" I="17" O="true" N="OfficeArchitectureText">
    <S T="2" F="ApplicationArchitecture" M="Ignore" />
  </C>
  <C T="W" I="18" O="true" N="AffectedProcessAppVersion">
    <S T="1" F="CrashedAppVersion" M="Ignore" />
  </C>
  <C T="G" I="19" O="true" N="LicenseId">
    <S T="1" F="LicenseId" M="Ignore" />
  </C>
  <C T="U64" I="20" O="true" N="BucketId">
    <S T="1" F="BucketId" M="Ignore" />
  </C>
  <C T="W" I="21" O="true" N="WatsonReportId">
    <S T="1" F="WatsonReportId" M="Ignore" />
  </C>
  <C T="W" I="22" O="true" N="CabGuid">
    <S T="1" F="CabGuid" M="Ignore" />
  </C>
  <C T="U32" I="23" O="true" N="ExceptionInfo">
    <S T="1" F="ExceptionInfo" M="Ignore" />
  </C>
  <C T="U32" I="24" O="true" N="HangTypeCode">
    <S T="1" F="TypeCode" M="Ignore" />
  </C>
  <C T="W" I="25" O="true" N="StackHash">
    <S T="1" F="StackHash" M="Ignore" />
  </C>
  <C T="U64" I="26" O="true" N="AppUsedVirtualMemory">
    <S T="1" F="AppUsedVirtualMemory" M="Ignore" />
  </C>
  <C T="U64" I="27" O="true" N="SystemAvailableMemory">
    <S T="1" F="SystemAvailableMemory" M="Ignore" />
  </C>
  <C T="W" I="28" O="true" N="CallStack">
    <S T="1" F="CallStack" M="Ignore" />
  </C>
  <C T="W" I="29" O="true" N="WatsonBucket">
    <S T="1" F="BucketHash" M="Ignore" />
  </C>
  <C T="W" I="30" O="true" N="CallStackHash">
    <S T="1" F="CallStackHash" M="Ignore" />
  </C>
  <C T="B" I="31" O="true" N="IsCustomerImpacting">
    <S T="1" F="IsCustomerImpacting" M="Ignore" />
  </C>
  <T>
    <S T="1" />
  </T>
</R><R Id="224902" V="2" DC="SM" T="Subrule" xmlns="">
  <S>
    <R T="1" R="120100" />
    <UTS T="2" Id="bbr5q" />
    <SS T="3" G="{a36a970d-45a9-4e0d-9cab-2a235cc9d7c6}" />
  </S>
  <C T="G" I="0" O="falseNoError">
    <S T="3" F="Acid" M="Ignore" />
  </C>
  <C T="W" I="1" O="falseNoError">
    <S T="3" F="MachineKey" M="Ignore" />
  </C>
  <T>
    <S T="1" />
    <S T="2" />
  </T>
</R><R Id="224901" V="11" DC="SM" EN="Office.Licensing.OfficeClientLicensing.DoLicenseValidation" ATT="c1a0db0127964674a0d62fde5ab0fe62-6ec4ac45-cebc-4f80-aa83-b6b9d3a86ed7-7719" SP="CriticalCensus" T="Upload-Medium" DL="A" DCa="PSU" xmlns="">
  <RIS>
    <RI N="Metadata" />
  </RIS>
  <S>
    <R T="1" R="224900" />
    <R T="2" R="224902" />
    <UTS T="3" Id="be7wx" />
    <UTS T="4" Id="be7wy" />
    <SS T="5" G="{a36a970d-45a9-4e0d-9cab-2a235cc9d7c6}" />
    <F T="6">
      <O T="NE">
        <L>
          <S T="2" F="0" M="Ignore" />
        </L>
        <R>
          <V V="{99999999-9999-9999-9999-999999999999}" T="G" />
        </R>
      </O>
    </F>
    <F T="7">
      <O T="NE">
        <L>
          <S T="2" F="1" M="Ignore" />
        </L>
        <R>
          <V V="99999-999-999999" T="W" />
        </R>
      </O>
    </F>
  </S>
  <C T="I32" I="0" O="true" N="FullValidationMode">
    <S T="1" F="0" M="Ignore" />
  </C>
  <C T="G" I="1" O="true" N="LicensingACID">
    <S T="6" F="0" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="OlsLicenseId">
    <S T="7" F="1" M="Ignore" />
  </C>
  <C T="U32" I="3" O="true" N="LicenseStatus">
    <S T="5" F="LicenseStatus" M="Ignore" />
  </C>
  <C T="I32" I="4" O="true" N="LicenseType">
    <S T="5" F="LicenseType" M="Ignore" />
  </C>
  <C T="B" I="5" O="true" N="IsSubscription">
    <S T="5" F="AggregatedSubscription" M="Ignore" />
  </C>
  <C T="TAG" I="6" O="true" N="SkuIdIsNull">
    <S T="3" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="TAG" I="7" O="true" N="SlapiIsNull">
    <S T="4" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="B" I="8" O="true" N="IsSCA">
    <S T="5" F="IsSCA" M="Ignore" />
  </C>
  <C T="B" I="9" O="true" N="IsvNext">
    <S T="5" F="IsvNext" M="Ignore" />
  </C>
  <C T="B" I="10" O="true" N="IsRFM">
    <S T="5" F="IsRFM" M="Ignore" />
  </C>
  <C T="U32" I="11" O="true" N="LicenseCategory">
    <S T="5" F="LicenseCategory" M="Ignore" />
  </C>
  <C T="U32" I="12" O="true" N="LicensingMode">
    <S T="5" F="LicensingMode" M="Ignore" />
  </C>
  <C T="B" I="13" O="true" N="IsEdu">
    <S T="5" F="IsEdu" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="90401" V="3" DC="ESM" EN="Office.Telemetry.SamplingPolicy" ATT="f998cc5ba4d448d6a1e8e913ff18be94-dd122e0a-fcf8-4dc5-9dbb-6afac5325183-7405" DL="A" DCa="PSP PSU" xmlns="">
  <RIS>
    <RI N="Metadata" />
  </RIS>
  <S>
    <UTS T="1" Id="c57bd" />
    <R T="2" R="120100" />
    <SS T="3" G="{805a23b4-c402-413c-bc13-1861553d4b03}" />
  </S>
  <C T="W" I="0" O="false" N="SamplingMethod">
    <O T="COALESCE">
      <L>
        <S T="3" F="SamplingMethod" M="Ignore" />
      </L>
      <R>
        <V V="Unknown" T="W" />
      </R>
    </O>
  </C>
  <C T="W" I="1" O="false" N="SamplingKey">
    <O T="COALESCE">
      <L>
        <S T="3" F="SamplingKey" M="Ignore" />
      </L>
      <R>
        <V V="Unknown" T="W" />
      </R>
    </O>
  </C>
  <C T="B" I="2" O="false" N="MeasuresEnabled">
    <O T="COALESCE">
      <L>
        <S T="3" F="MeasuresEnabled" M="Ignore" />
      </L>
      <R>
        <V V="true" T="B" />
      </R>
    </O>
  </C>
  <C T="D" I="3" O="true" N="SamplingClientIdValue">
    <S T="3" F="SamplingClientIdValue" M="Ignore" />
  </C>
  <T>
    <S T="1" />
    <S T="2" />
  </T>
  <ST>
    <S T="1" />
  </ST>
</R><R Id="120100" V="3" DC="SM" T="Subrule" DCa="PSU" xmlns="">
  <S>
    <A T="1" E="TelemetryStartup" />
    <A T="2" E="TelemetryResume" />
    <TI T="3" I="30s" />
    <R T="4" R="120100" />
    <TH T="5">
      <O T="GE">
        <L>
          <O T="COALESCE">
            <L>
              <S T="3" F="TimeStamp100ns" />
            </L>
            <R>
              <S T="2" F="TimeStamp100ns" />
            </R>
          </O>
        </L>
        <R>
          <O T="ADD">
            <L>
              <O T="COALESCE">
                <L>
                  <S T="4" F="TimeStamp100ns" />
                </L>
                <R>
                  <V V="1" T="FT" />
                </R>
              </O>
            </L>
            <R>
              <V V="864000000000" T="U64" />
            </R>
          </O>
        </R>
      </O>
    </TH>
  </S>
  <T>
    <S T="1" />
    <S T="5" />
  </T>
</R><R Id="701201" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Xaml.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenXaml" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="1ee2f5d6b2c94518b99911f4ba4886c5-02df2dc0-3394-4446-9267-9ca2961635c1-7151" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701200" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Xaml" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenXaml" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="1ee2f5d6b2c94518b99911f4ba4886c5-02df2dc0-3394-4446-9267-9ca2961635c1-7151" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700201" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Word.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenWord" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="bffd26d9e49b4b7db4cb4df3425812de-d3cf8f62-3ad9-4007-99a8-8fb5cc89fd5d-7896" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700200" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Word" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenWord" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="bffd26d9e49b4b7db4cb4df3425812de-d3cf8f62-3ad9-4007-99a8-8fb5cc89fd5d-7896" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702351" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Voice.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenVoice" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9783945ebc2b468fbb8a2890cdab903b-787355a5-74c8-4a89-b06a-9c82635d75fa-7162" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702350" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Voice" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenVoice" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9783945ebc2b468fbb8a2890cdab903b-787355a5-74c8-4a89-b06a-9c82635d75fa-7162" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701251" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Visio.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenVisio" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e056f045a0e64cb0b557a5e04686cbe1-80a95768-b4c0-440d-aac7-b7b2b5d0ddbb-7628" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701250" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Visio" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenVisio" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e056f045a0e64cb0b557a5e04686cbe1-80a95768-b4c0-440d-aac7-b7b2b5d0ddbb-7628" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700051" V="1" DC="SM" EN="Office.Telemetry.Event.Office.UX.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenUX" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c4388c977297413bb054bad1acf0ade1-cc58e53e-f5a4-4f37-b0d2-9a8079e34420-6879" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700050" V="1" DC="SM" EN="Office.Telemetry.Event.Office.UX" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenUX" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c4388c977297413bb054bad1acf0ade1-cc58e53e-f5a4-4f37-b0d2-9a8079e34420-6879" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702951" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Translator.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTranslator" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e91bcbbe30e44ab28e2e2bce83d446e5-a779473f-d500-4783-ae33-75fd57efee29-6985" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702950" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Translator" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTranslator" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e91bcbbe30e44ab28e2e2bce83d446e5-a779473f-d500-4783-ae33-75fd57efee29-6985" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701151" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Text.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTextAndFonts" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9c9dcb87a41b41bebea94330a23d828d-35611aa0-46ca-4351-98e0-5746b84e5be3-6973" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701150" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Text" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTextAndFonts" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9c9dcb87a41b41bebea94330a23d828d-35611aa0-46ca-4351-98e0-5746b84e5be3-6973" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702201" V="1" DC="SM" EN="Office.Telemetry.Event.Office.TellMe.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTellMe" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="31d9dd3e4c7046a696537586281d7ed1-06d11dd6-a946-4281-8ac3-a7c2ab4776f5-7063" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702200" V="1" DC="SM" EN="Office.Telemetry.Event.Office.TellMe" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTellMe" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="31d9dd3e4c7046a696537586281d7ed1-06d11dd6-a946-4281-8ac3-a7c2ab4776f5-7063" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700401" V="2" DC="SM" EN="Office.Telemetry.Event.Office.Telemetry.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTelemetry" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="f998cc5ba4d448d6a1e8e913ff18be94-dd122e0a-fcf8-4dc5-9dbb-6afac5325183-7405" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700400" V="2" DC="SM" EN="Office.Telemetry.Event.Office.Telemetry" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenTelemetry" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="f998cc5ba4d448d6a1e8e913ff18be94-dd122e0a-fcf8-4dc5-9dbb-6afac5325183-7405" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700351" V="1" DC="SM" EN="Office.Telemetry.Event.Office.System.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSystem" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="cd836626611c4caaa8fc5b2e728ee81d-3b6d6c45-6377-4bf5-9792-dbf8e1881088-7521" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700350" V="1" DC="SM" EN="Office.Telemetry.Event.Office.System" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSystem" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="cd836626611c4caaa8fc5b2e728ee81d-3b6d6c45-6377-4bf5-9792-dbf8e1881088-7521" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703901" V="0" DC="SM" EN="Office.Telemetry.Event.Office.ServiceabilityManager.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenServiceabilityManager" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="6e04ead68f85476193d280510e98a915-f08e6ed7-8457-4a21-9eb6-73ead31fbf87-7068" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703900" V="0" DC="SM" EN="Office.Telemetry.Event.Office.ServiceabilityManager" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenServiceabilityManager" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="6e04ead68f85476193d280510e98a915-f08e6ed7-8457-4a21-9eb6-73ead31fbf87-7068" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701501" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Security.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSecurity" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="05e0c2fd5fff432bac7a175220223da5-2eec7cb2-952b-4862-9fd4-16ed083da5ec-7364" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701500" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Security" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSecurity" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="05e0c2fd5fff432bac7a175220223da5-2eec7cb2-952b-4862-9fd4-16ed083da5ec-7364" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702801" V="1" DC="SM" EN="Office.Telemetry.Event.Office.SDX.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSDX" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3ee0b579ec1c40ee92dc70f2f6b3da27-fe1c3f45-4365-4e37-8458-88c9fb1b9e83-6979" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702800" V="1" DC="SM" EN="Office.Telemetry.Event.Office.SDX" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSDX" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3ee0b579ec1c40ee92dc70f2f6b3da27-fe1c3f45-4365-4e37-8458-88c9fb1b9e83-6979" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703351" V="0" DC="SM" EN="Office.Telemetry.Event.Office.ScriptLab.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenScriptLab" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="2b76429bb1b7429c8a2e87e51aa8af6b-0dc6a93e-bf04-44c5-9cf5-8b0cd229d414-7620" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703350" V="0" DC="SM" EN="Office.Telemetry.Event.Office.ScriptLab" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenScriptLab" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="2b76429bb1b7429c8a2e87e51aa8af6b-0dc6a93e-bf04-44c5-9cf5-8b0cd229d414-7620" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703501" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Sandbox.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSandbox" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="1cd66059dc1d41f1887e3770ea8ba967-f1d0108c-8535-4cf9-bf23-ec8227f6afa7-6999" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703500" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Sandbox" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenSandbox" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="1cd66059dc1d41f1887e3770ea8ba967-f1d0108c-8535-4cf9-bf23-ec8227f6afa7-6999" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701801" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Resources.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenResources" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="eb24ada54785433685efe9f034687700-a7e8579a-5cbb-4ea8-9113-7299c0a610b3-6667" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701800" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Resources" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenResources" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="eb24ada54785433685efe9f034687700-a7e8579a-5cbb-4ea8-9113-7299c0a610b3-6667" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701051" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Release.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenRelease" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3a5d8522875d4c0cab910ff387cdbfb5-2c8b80ff-6a32-439d-9197-1ecda9fe5d2c-7140" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701050" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Release" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenRelease" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3a5d8522875d4c0cab910ff387cdbfb5-2c8b80ff-6a32-439d-9197-1ecda9fe5d2c-7140" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702751" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Publisher.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPublisher" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="a3f80a17921e433f9e8f460bcc58d7cf-e160211d-c3f7-4707-b9ef-03f5d6247b2b-7088" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702750" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Publisher" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPublisher" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="a3f80a17921e433f9e8f460bcc58d7cf-e160211d-c3f7-4707-b9ef-03f5d6247b2b-7088" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702301" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Project.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProject" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="ff6a486b8fab45d9981becda8b63d4e3-fa13197c-a787-416b-a2be-e1e7fd24381a-7378" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702300" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Project" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProject" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="ff6a486b8fab45d9981becda8b63d4e3-fa13197c-a787-416b-a2be-e1e7fd24381a-7378" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703401" V="0" DC="SM" EN="Office.Telemetry.Event.Office.ProgrammableSurfaces.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProgrammableSurfaces" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="cf7a002ba9374401bace61695550f784-bc564f37-6fdf-43c3-a3d7-e50a9f6d40b4-7117" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703400" V="0" DC="SM" EN="Office.Telemetry.Event.Office.ProgrammableSurfaces" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProgrammableSurfaces" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="cf7a002ba9374401bace61695550f784-bc564f37-6fdf-43c3-a3d7-e50a9f6d40b4-7117" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702501" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Programmability.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProgrammability" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="98aab172938b4a83a62a76e35df9c1bf-4f71ae9c-e3ac-4f53-a138-c9fbb8e9290e-6809" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702500" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Programmability" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProgrammability" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="98aab172938b4a83a62a76e35df9c1bf-4f71ae9c-e3ac-4f53-a138-c9fbb8e9290e-6809" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700501" V="1" DC="SM" EN="Office.Telemetry.Event.Office.PowerPoint.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPowerPoint" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e97859101370486fbcb67f5a023da3fc-29b89831-2d28-4841-8fa6-338080fae3a4-7206" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700500" V="1" DC="SM" EN="Office.Telemetry.Event.Office.PowerPoint" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPowerPoint" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e97859101370486fbcb67f5a023da3fc-29b89831-2d28-4841-8fa6-338080fae3a4-7206" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702551" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Personalization.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPersonalization" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="4a49412b92ac41aab3d6427678d72c81-25f0a0ee-5d86-4c08-9ba1-6ea878efd334-7416" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702550" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Personalization" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPersonalization" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="4a49412b92ac41aab3d6427678d72c81-25f0a0ee-5d86-4c08-9ba1-6ea878efd334-7416" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701351" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Performance.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPerformance" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9b9b073d5a43495fae37f003b99e8ce3-4845ea41-8177-4f0f-884e-de09a0b35bf3-6838" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701350" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Performance" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPerformance" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9b9b073d5a43495fae37f003b99e8ce3-4845ea41-8177-4f0f-884e-de09a0b35bf3-6838" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702151" V="1" DC="SM" EN="Office.Telemetry.Event.Office.People.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPeople" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9471ad91d4fe4df8935b21dee6f08e4d-26cd8d5f-8e74-4cd1-9c99-4f9e4819d423-7401" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702150" V="1" DC="SM" EN="Office.Telemetry.Event.Office.People" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPeople" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="9471ad91d4fe4df8935b21dee6f08e4d-26cd8d5f-8e74-4cd1-9c99-4f9e4819d423-7401" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703001" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Outlook.Mac.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOutlookMac" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="dcc8b778efa7440daecb2fd35c0bccce-2902687d-f6c7-4411-aaee-7df1b8d5c174-7331" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703000" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Outlook.Mac" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOutlookMac" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="dcc8b778efa7440daecb2fd35c0bccce-2902687d-f6c7-4411-aaee-7df1b8d5c174-7331" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700751" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Outlook.Desktop.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOutlookDesktop" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d807609276744245baf81bf7bc8033f6-2268e374-7766-4976-be44-b6ad5bddc5b6-7813" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700750" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Outlook.Desktop" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOutlookDesktop" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d807609276744245baf81bf7bc8033f6-2268e374-7766-4976-be44-b6ad5bddc5b6-7813" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700151" V="1" DC="SM" EN="Office.Telemetry.Event.Office.OneNote.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOneNote" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3de38b41008a4da5a5381ef307f91597-636c040a-3c10-4912-9e28-de026a94600f-6848" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700150" V="1" DC="SM" EN="Office.Telemetry.Event.Office.OneNote" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOneNote" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3de38b41008a4da5a5381ef307f91597-636c040a-3c10-4912-9e28-de026a94600f-6848" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703451" V="1" DC="SM" EN="Office.Telemetry.Event.Office.OfficeMobile.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOfficeMobile" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="40e6164bad1444c88f0a4a5c151a86b2-573e53d8-5989-4a9a-a4b1-2600ec0405a0-7174" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703450" V="1" DC="SM" EN="Office.Telemetry.Event.Office.OfficeMobile" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOfficeMobile" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="40e6164bad1444c88f0a4a5c151a86b2-573e53d8-5989-4a9a-a4b1-2600ec0405a0-7174" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700901" V="1" DC="SM" EN="Office.Telemetry.Event.Office.NaturalLanguage.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProofing" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="71cc1046851042108843d90e5d3ef6c1-61e5de5c-238c-4de5-95de-3b40d20ea6e5-6899" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700900" V="1" DC="SM" EN="Office.Telemetry.Event.Office.NaturalLanguage" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenProofing" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="71cc1046851042108843d90e5d3ef6c1-61e5de5c-238c-4de5-95de-3b40d20ea6e5-6899" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702251" V="1" DC="SM" EN="Office.Telemetry.Event.Office.ML.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenML" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="655fe2f4f8414e4db3f881ce2bb96fc8-7a0f06e8-b403-4b25-afd7-a54cc7fda27b-7855" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702250" V="1" DC="SM" EN="Office.Telemetry.Event.Office.ML" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenML" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="655fe2f4f8414e4db3f881ce2bb96fc8-7a0f06e8-b403-4b25-afd7-a54cc7fda27b-7855" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702651" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Media.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenMedia" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="a6884ea2e66b4f188008839b476db56c-0fe72aa2-2a4a-4102-829a-4f25940b3e33-7746" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702650" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Media" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenMedia" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="a6884ea2e66b4f188008839b476db56c-0fe72aa2-2a4a-4102-829a-4f25940b3e33-7746" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703101" V="1" DC="SM" EN="Office.Telemetry.Event.Office.MATS.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenMATS" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="faab4ead691e451eb230afc98a28e0f2-4089b390-5e4a-4a54-ac5c-6be4f2ea9321-7247" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703100" V="1" DC="SM" EN="Office.Telemetry.Event.Office.MATS" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenMATS" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="faab4ead691e451eb230afc98a28e0f2-4089b390-5e4a-4a54-ac5c-6be4f2ea9321-7247" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702901" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Manageability.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenManageability" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e28dd45676b34a2dafb516ada48fcfd6-3d883862-8df7-4f32-b6f6-45ab129a7442-8124" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702900" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Manageability" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenManageability" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e28dd45676b34a2dafb516ada48fcfd6-3d883862-8df7-4f32-b6f6-45ab129a7442-8124" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703601" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Maker.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenMaker" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="17e1b135bde6444a97fabb94d77f033d-dd5a9251-6ea3-4e7b-b666-836e13fb9da6-7094" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703600" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Maker" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenMaker" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="17e1b135bde6444a97fabb94d77f033d-dd5a9251-6ea3-4e7b-b666-836e13fb9da6-7094" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703851" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaPicker.UserActions.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaPickerUserActions" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="30b97846c6c640ed90e3b4e06d98a986-b6251ed6-9d7f-4e24-8ace-044e9613d08b-7662" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703850" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaPicker.UserActions" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaPickerUserActions" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="30b97846c6c640ed90e3b4e06d98a986-b6251ed6-9d7f-4e24-8ace-044e9613d08b-7662" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703801" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaPicker.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaPicker" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="67199ce661b3472494bbec0bf2e06c27-c34071ab-96bc-40d9-9162-7a83b521b4dd-7282" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703800" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaPicker" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaPicker" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="67199ce661b3472494bbec0bf2e06c27-c34071ab-96bc-40d9-9162-7a83b521b4dd-7282" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703701" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaCard.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaCard" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="a1bde8e78ab14b6198caa18be9d3126e-9277d376-40b1-48ec-92f5-b2ee3f44345b-7212" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703700" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaCard" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaCard" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="a1bde8e78ab14b6198caa18be9d3126e-9277d376-40b1-48ec-92f5-b2ee3f44345b-7212" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703751" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaCardUserActions.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaCardUserActions" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="f615f561ec88437db01f752caf151695-7cf4617b-dcbd-4b5b-a5eb-0079c1ba008a-6975" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703750" V="0" DC="SM" EN="Office.Telemetry.Event.Office.LivePersonaCardUserActions" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLivePersonaCardUserActions" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="f615f561ec88437db01f752caf151695-7cf4617b-dcbd-4b5b-a5eb-0079c1ba008a-6975" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701301" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Licensing.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLicensing" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c1a0db0127964674a0d62fde5ab0fe62-6ec4ac45-cebc-4f80-aa83-b6b9d3a86ed7-7719" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701300" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Licensing" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLicensing" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c1a0db0127964674a0d62fde5ab0fe62-6ec4ac45-cebc-4f80-aa83-b6b9d3a86ed7-7719" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704051" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Lens.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLens" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="96782f7d148f478d977bfedcc8155217-19985a90-6412-4c10-9127-c4137b806188-6761" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704050" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Lens" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenLens" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="96782f7d148f478d977bfedcc8155217-19985a90-6412-4c10-9127-c4137b806188-6761" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701701" V="1" DC="SM" EN="Office.Telemetry.Event.Office.IntelligentServices.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenIntelligentServices" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="ca7bd155a4374d74b39a378fd265640b-88e0ec23-1ffd-4c59-86b0-53a8e0f5acd5-7115" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701700" V="1" DC="SM" EN="Office.Telemetry.Event.Office.IntelligentServices" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenIntelligentServices" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="ca7bd155a4374d74b39a378fd265640b-88e0ec23-1ffd-4c59-86b0-53a8e0f5acd5-7115" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702051" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Insights.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenInsights" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="250a372c4a854d8fb120ef0366f9410c-dd39bcd8-3b38-4c6c-9f8d-3821e5c28b47-7487" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702050" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Insights" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenInsights" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="250a372c4a854d8fb120ef0366f9410c-dd39bcd8-3b38-4c6c-9f8d-3821e5c28b47-7487" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700701" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Identity.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenIdentity" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="5c65bbc4edbf480d9637ace04d62bd98-12844893-8ab9-4dde-b850-5612cb12e0f2-7822" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700700" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Identity" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenIdentity" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="5c65bbc4edbf480d9637ace04d62bd98-12844893-8ab9-4dde-b850-5612cb12e0f2-7822" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700551" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Help.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenHelp" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="f0ef8c1d59b54013a78c8cce0b75bb44-d4a21f0a-ff29-4a4c-81e7-bc7f69607cc5-7242" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700550" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Help" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenHelp" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="f0ef8c1d59b54013a78c8cce0b75bb44-d4a21f0a-ff29-4a4c-81e7-bc7f69607cc5-7242" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703651" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Groove.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenGroove" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="404070a31d8e4ba4a11fbf048d8f3bcb-c140348c-fa09-4b96-89aa-2e6d35d498ce-7572" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703650" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Groove" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenGroove" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="404070a31d8e4ba4a11fbf048d8f3bcb-c140348c-fa09-4b96-89aa-2e6d35d498ce-7572" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700601" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Graphics.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenGraphics" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="cfcfdb91c68c4329bb8b7cb7babb3cf7-e082c2f2-ef1d-427a-ac4d-b0b700afe7a7-7655" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700600" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Graphics" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenGraphics" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="cfcfdb91c68c4329bb8b7cb7babb3cf7-e082c2f2-ef1d-427a-ac4d-b0b700afe7a7-7655" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703151" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Globalization.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenGlobalization" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="fa2d8726ec914a019f61aa83d1a0f156-ff2e4b12-d084-4558-9515-fbf9e1224e49-7217" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703150" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Globalization" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenGlobalization" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="fa2d8726ec914a019f61aa83d1a0f156-ff2e4b12-d084-4558-9515-fbf9e1224e49-7217" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703951" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Floodgate.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFloodgate" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d526a9052e444f1d8706529ef111b101-1a23544e-ef74-4a87-9979-78386cb3c5e6-6762" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703950" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Floodgate" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFloodgate" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d526a9052e444f1d8706529ef111b101-1a23544e-ef74-4a87-9979-78386cb3c5e6-6762" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702851" V="1" DC="SM" EN="Office.Telemetry.Event.Office.FileSystem.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFileSystem" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="41b3c8e8dde3480085aa650572506bdb-a86248dc-c940-459d-b233-4c5fe75da17e-7262" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702850" V="1" DC="SM" EN="Office.Telemetry.Event.Office.FileSystem" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFileSystem" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="41b3c8e8dde3480085aa650572506bdb-a86248dc-c940-459d-b233-4c5fe75da17e-7262" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700001" V="2" DC="SM" EN="Office.Telemetry.Event.Office.FileIO.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFileIO" S="Medium" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="GE">
            <L>
              <S T="1" F="EventSamplingPolicy" />
            </L>
            <R>
              <V V="191" T="U8" />
            </R>
          </O>
        </L>
        <R>
          <O T="NE">
            <L>
              <S T="1" F="EventName" />
            </L>
            <R>
              <V V="Office.FileIO.MSO.DocumentRecoveryHrGetDrpCore" T="W" />
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d2b065c5ae634beb9cec4813140e22da-eda42ada-ffd9-4e26-92a1-e3b440eaf8bf-7717" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700000" V="2" DC="SM" EN="Office.Telemetry.Event.Office.FileIO" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFileIO" S="Medium" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="LT">
            <L>
              <S T="1" F="EventSamplingPolicy" />
            </L>
            <R>
              <V V="191" T="U8" />
            </R>
          </O>
        </L>
        <R>
          <O T="NE">
            <L>
              <S T="1" F="EventName" />
            </L>
            <R>
              <V V="Office.FileIO.MSO.DocumentRecoveryHrGetDrpCore" T="W" />
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d2b065c5ae634beb9cec4813140e22da-eda42ada-ffd9-4e26-92a1-e3b440eaf8bf-7717" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701401" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Feedback.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFeedback" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d79e824386c4441cb8c1d4ae15690526-bd443309-5494-444a-aba9-0af9eef99f84-7360" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701400" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Feedback" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenFeedback" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d79e824386c4441cb8c1d4ae15690526-bd443309-5494-444a-aba9-0af9eef99f84-7360" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701951" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Extensibility.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOfficeExtensibility" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="db334b301e7b474db5e0f02f07c51a47-a1b5bc36-1bbe-482f-a64a-c2d9cb606706-7439" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701950" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Extensibility" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenOfficeExtensibility" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="db334b301e7b474db5e0f02f07c51a47-a1b5bc36-1bbe-482f-a64a-c2d9cb606706-7439" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700851" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Experimentation.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExperimentation" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e6e58d16cfb94942b795b4918258153a-765be17b-66ea-435e-8b55-5a128f3decd3-6873" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700850" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Experimentation" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExperimentation" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e6e58d16cfb94942b795b4918258153a-765be17b-66ea-435e-8b55-5a128f3decd3-6873" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701851" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Excel.Mobile.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcelMobile" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="5822b8f8b7074f2496f1fcae7f276f6b-4fe161bc-5cd4-4c1b-a07b-e4737f0d8c4e-7725" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701850" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Excel.Mobile" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcelMobile" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="5822b8f8b7074f2496f1fcae7f276f6b-4fe161bc-5cd4-4c1b-a07b-e4737f0d8c4e-7725" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703051" V="3" DC="SM" EN="Office.Telemetry.Event.Office.Excel.InsightsServices.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcelInsightsServices" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="6ba2253e959648eb888b4a1ab6f80b9e-4f844b52-3b6a-4125-a870-2f845aa9f679-7726" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703050" V="3" DC="SM" EN="Office.Telemetry.Event.Office.Excel.InsightsServices" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcelInsightsServices" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="6ba2253e959648eb888b4a1ab6f80b9e-4f844b52-3b6a-4125-a870-2f845aa9f679-7726" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700101" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Excel.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcel" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="19eb5e37de684ce38ce5cf3d5842d3f7-e8722941-bde5-4b98-9cd5-2775ec51482c-6873" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702101" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Excel.Coauth.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcelCoauth" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="0d96bb3db51349c6ad8309ed1858d441-adfbcb5b-0d2f-4ada-ad0a-50f1b1c7035e-7287" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702100" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Excel.Coauth" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcelCoauth" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="0d96bb3db51349c6ad8309ed1858d441-adfbcb5b-0d2f-4ada-ad0a-50f1b1c7035e-7287" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700100" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Excel" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenExcel" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="19eb5e37de684ce38ce5cf3d5842d3f7-e8722941-bde5-4b98-9cd5-2775ec51482c-6873" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700951" V="1" DC="SM" EN="Office.Telemetry.Event.Office.DynamicCanvas.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenWildfire" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="350d24c16a934c2d9734791ed7301d8e-73bafc90-ca43-424c-815a-63b076120b49-6725" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700950" V="1" DC="SM" EN="Office.Telemetry.Event.Office.DynamicCanvas" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenWildfire" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="350d24c16a934c2d9734791ed7301d8e-73bafc90-ca43-424c-815a-63b076120b49-6725" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703551" V="0" DC="SM" EN="Office.Telemetry.Event.Office.DocumentXRay.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDocumentXRay" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e305db0c6da74e7ea3b0ee2467608180-72d9c509-3dff-4377-a4c4-4e9389023412-6994" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703550" V="0" DC="SM" EN="Office.Telemetry.Event.Office.DocumentXRay" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDocumentXRay" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="e305db0c6da74e7ea3b0ee2467608180-72d9c509-3dff-4377-a4c4-4e9389023412-6994" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700451" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Docs.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDocs" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c274b3e05ac5448dae8fbb7466da6acb-fd6dc8de-18b7-409c-a696-4bd66f7a5322-7902" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702701" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Docs.Apple.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDocsApple" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="2715e0b025264ca89ba0ca7062076dd0-2a20aa4b-f7f8-4a9f-933a-de243d5b5d78-7372" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702700" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Docs.Apple" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDocsApple" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="2715e0b025264ca89ba0ca7062076dd0-2a20aa4b-f7f8-4a9f-933a-de243d5b5d78-7372" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700450" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Docs" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDocs" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c274b3e05ac5448dae8fbb7466da6acb-fd6dc8de-18b7-409c-a696-4bd66f7a5322-7902" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701901" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Diagnostics.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDiagnostics" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="70ec13d4fa0b40e6a470b0d5aaf5a827-fcdb1fbe-527a-4cf5-b799-c18287fc6282-7371" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701900" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Diagnostics" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDiagnostics" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="70ec13d4fa0b40e6a470b0d5aaf5a827-fcdb1fbe-527a-4cf5-b799-c18287fc6282-7371" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704001" V="0" DC="SM" EN="Office.Telemetry.Event.Office.DiagnosticsSystem.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDiagnosticsSystem" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c2dec09216c44cb6b3bb8689c0886e81-d94e10bd-39e8-4795-b30c-ecf078ba5374-7373" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704000" V="0" DC="SM" EN="Office.Telemetry.Event.Office.DiagnosticsSystem" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenDiagnosticsSystem" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="c2dec09216c44cb6b3bb8689c0886e81-d94e10bd-39e8-4795-b30c-ecf078ba5374-7373" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703251" V="1" DC="SM" EN="Office.Telemetry.Event.Office.CoreUI.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCoreUI" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="ecd7404a1d7d45a1a54b18b384f8cc8d-3e90f53a-ca63-4b34-8284-d9544625db78-7414" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703250" V="1" DC="SM" EN="Office.Telemetry.Event.Office.CoreUI" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCoreUI" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="ecd7404a1d7d45a1a54b18b384f8cc8d-3e90f53a-ca63-4b34-8284-d9544625db78-7414" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702401" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Compliance.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCompliance" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="7027c5657baa4c8c94c98ed600467c8c-81874f32-dbc1-42fc-b25b-12494e65c25f-7401" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702400" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Compliance" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCompliance" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="7027c5657baa4c8c94c98ed600467c8c-81874f32-dbc1-42fc-b25b-12494e65c25f-7401" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701551" V="1" DC="SM" EN="Office.Telemetry.Event.Office.ClickToRun.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenClickToRun" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701550" V="1" DC="SM" EN="Office.Telemetry.Event.Office.ClickToRun" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenClickToRun" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700301" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Charting.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCharting" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="8984684171524f0c86ea1d654968b2c4-7399462d-cac4-4abd-abdd-d14d00d89e06-7783" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700300" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Charting" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCharting" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="8984684171524f0c86ea1d654968b2c4-7399462d-cac4-4abd-abdd-d14d00d89e06-7783" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702001" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Canvas.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCanvas" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="625e97e3f60f498b93f637a1326ca839-4fa1bdd2-bae5-44a3-8d8c-a510d37d8472-7128" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702000" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Canvas" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCanvas" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="625e97e3f60f498b93f637a1326ca839-4fa1bdd2-bae5-44a3-8d8c-a510d37d8472-7128" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702601" V="1" DC="SM" EN="Office.Telemetry.Event.Office.AutoTemplate.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAutoTemplate" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d122cf13e49d4431b31b61569b7bbcf2-f7f82ae9-fe35-4938-a788-fcc77fbd1448-7244" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702600" V="1" DC="SM" EN="Office.Telemetry.Event.Office.AutoTemplate" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAutoTemplate" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="d122cf13e49d4431b31b61569b7bbcf2-f7f82ae9-fe35-4938-a788-fcc77fbd1448-7244" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703201" V="1" DC="SM" EN="Office.Telemetry.Event.Office.AugLoop.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAugLoop" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3de4087d4de34817b1c376e3d1e6e293-983c4292-5ba9-485a-ab10-9797863c788b-6770" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703200" V="1" DC="SM" EN="Office.Telemetry.Event.Office.AugLoop" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAugLoop" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3de4087d4de34817b1c376e3d1e6e293-983c4292-5ba9-485a-ab10-9797863c788b-6770" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700251" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Apple.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenApple" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="44c1da679ff8463c8a859e2cb25140b1-f21a5362-f2d6-46e6-a1cb-52268260a5c9-7560" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700250" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Apple" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenApple" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="44c1da679ff8463c8a859e2cb25140b1-f21a5362-f2d6-46e6-a1cb-52268260a5c9-7560" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700651" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Android.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAndroid" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="7e90593cb38e43c08344e14a8f21f1a7-33a221bd-23a1-4e0d-9487-eaf4c101acf3-6719" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="700650" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Android" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAndroid" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="7e90593cb38e43c08344e14a8f21f1a7-33a221bd-23a1-4e0d-9487-eaf4c101acf3-6719" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703301" V="0" DC="SM" EN="Office.Telemetry.Event.Office.AirTrafficControl.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAirTrafficControl" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="901aa526bead4160a40128759dbefe1f-272802df-680a-427d-8909-a5dda89d3d62-7481" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="703300" V="0" DC="SM" EN="Office.Telemetry.Event.Office.AirTrafficControl" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAirTrafficControl" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="901aa526bead4160a40128759dbefe1f-272802df-680a-427d-8909-a5dda89d3d62-7481" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701751" V="1" DC="SM" EN="Office.Telemetry.Event.Office.AirSpace.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAirspace" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="da06315a64204a91b8c35628ae841a61-c2fdf120-6e99-4fdd-900e-ec9868e5e64b-7449" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701750" V="1" DC="SM" EN="Office.Telemetry.Event.Office.AirSpace" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAirspace" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="da06315a64204a91b8c35628ae841a61-c2fdf120-6e99-4fdd-900e-ec9868e5e64b-7449" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701651" V="1" DC="SM" EN="Office.Telemetry.Event.Office.ActivityFeed.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenActivityFeed" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="6ec2d1c85a3f48918f637c0d25bab63f-c72c9ec6-73bb-43d4-baa6-14c53511510b-6962" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701650" V="1" DC="SM" EN="Office.Telemetry.Event.Office.ActivityFeed" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenActivityFeed" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="6ec2d1c85a3f48918f637c0d25bab63f-c72c9ec6-73bb-43d4-baa6-14c53511510b-6962" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702451" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Access.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAccess" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="7aa8857dfe484497a82c0d5f613b6c83-b1b85cf7-1a33-4226-82dd-f29282d921d7-7482" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="702450" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Access" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAccess" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="7aa8857dfe484497a82c0d5f613b6c83-b1b85cf7-1a33-4226-82dd-f29282d921d7-7482" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701101" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Accessibility.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAccessibility" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="92468150dd144e02a22a7f14aeb25aec-f4369824-d924-4f31-ba9f-58bb9848c091-7527" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="701100" V="1" DC="SM" EN="Office.Telemetry.Event.Office.Accessibility" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAccessibility" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="92468150dd144e02a22a7f14aeb25aec-f4369824-d924-4f31-ba9f-58bb9848c091-7527" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="230200" V="0" DC="SM" EN="Office.ClickToRun.WildFireC2R.DeviceScreen" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP PSU SS" xmlns="">
  <S>
    <UCSS T="1" C="WildFireC2R" S="Verbose Medium Monitorable Unexpected Assert" />
  </S>
  <C T="FT" I="0" O="true" N="TimeStamp">
    <S T="1" F="TimeStamp100ns" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="ProcessID">
    <S T="1" F="ULS_Process" M="Ignore" />
  </C>
  <C T="U32" I="2" O="true" N="ThreadID">
    <S T="1" F="ULS_ThreadID" M="Ignore" />
  </C>
  <C T="TAG" I="3" O="true" N="EventID">
    <S T="1" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="Severity">
    <S T="1" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Message">
    <S T="1" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="SQMMachineID">
    <S T="1" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="SUSClientID">
    <S T="1" F="SessionID" M="Ignore" />
  </C>
  <C T="U32" I="8" O="true" N="GeoID">
    <S T="1" F="GeoID" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="Version">
    <S T="1" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="Path">
    <S T="1" F="Path" M="Ignore" />
  </C>
  <C T="W" I="11" O="true" N="C2RClientVer">
    <S T="1" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="1" />
  </T>
</R><R Id="230104" V="1" DC="SM" EN="Office.ClickToRun.C2RTaskTelemetryCritical" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" SP="CriticalBusinessImpact" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="annux" />
    <UTS T="2" Id="aoici" />
    <UTS T="3" Id="apicg" />
    <UTS T="4" Id="aoh6s" />
    <UTS T="5" Id="aqzgg" />
    <UTS T="6" Id="bhzy7" />
    <UTS T="7" Id="a1s3p" />
    <UTS T="8" Id="a1s2n" />
    <UTS T="9" Id="a1s21" />
    <UTS T="10" Id="a14xz" />
    <US T="11">
      <S T="1" />
      <S T="2" />
      <S T="3" />
      <S T="4" />
      <S T="5" />
      <S T="6" />
      <S T="7" />
      <S T="8" />
      <S T="9" />
      <S T="10" />
    </US>
  </S>
  <C T="FT" I="0" O="true" N="TimeStamp">
    <S T="11" F="TimeStamp100ns" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Process">
    <S T="11" F="ULS_Process" M="Ignore" />
  </C>
  <C T="U32" I="2" O="true" N="Thread">
    <S T="11" F="ULS_ThreadID" M="Ignore" />
  </C>
  <C T="TAG" I="3" O="true" N="EventID">
    <S T="11" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="Severity">
    <S T="11" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Message">
    <S T="11" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="SQMMachineID">
    <S T="11" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="SUSClientID">
    <S T="11" F="SessionID" M="Ignore" />
  </C>
  <C T="U32" I="8" O="true" N="GeoID">
    <S T="11" F="GeoID" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="Version">
    <S T="11" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="Path">
    <S T="11" F="Path" M="Ignore" />
  </C>
  <C T="W" I="11" O="true" N="ScenarioInstanceID">
    <S T="11" F="ScenarioInstanceId" M="Ignore" />
  </C>
  <C T="G" I="12" O="false" N="TaskID">
    <S T="11" F="TaskId" />
  </C>
  <C T="W" I="13" O="false" N="TaskType">
    <S T="11" F="TaskType" />
  </C>
  <C T="U32" I="14" O="false" N="TaskState">
    <S T="11" F="TaskState" />
  </C>
  <C T="W" I="15" O="false" N="Scenario">
    <S T="11" F="Scenario" />
  </C>
  <C T="W" I="16" O="false" N="InstallID">
    <S T="11" F="InstallID" />
  </C>
  <C T="W" I="17" O="true" N="ScenarioSubType">
    <S T="11" F="ScenarioSubType" M="Ignore" />
  </C>
  <C T="W" I="18" O="true" N="C2RClientVer">
    <S T="11" F="C2RClientVer" M="Ignore" />
  </C>
  <C T="W" I="19" O="true" N="SourceType">
    <S T="11" F="SourceType" M="Ignore" />
  </C>
  <T>
    <S T="1" />
    <S T="2" />
    <S T="3" />
    <S T="4" />
    <S T="5" />
    <S T="6" />
    <S T="7" />
    <S T="8" />
    <S T="9" />
    <S T="10" />
  </T>
</R><R Id="230157" V="0" DC="SM" EN="Office.ClickToRun.C2RUpSupNoFilePaths" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" SP="CriticalBusinessImpact" DCa="PSP" xmlns="">
  <S>
    <UCSS T="1" C="Click-To-Run UpSup Telemetry" S="Verbose Medium Monitorable Unexpected Assert" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="NE">
            <L>
              <S T="1" F="ULS_Tag" />
            </L>
            <R>
              <V V="22814851" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="NE">
            <L>
              <S T="1" F="ULS_Tag" />
            </L>
            <R>
              <V V="22814849" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="FT" I="0" O="true" N="TimeStamp">
    <S T="2" F="TimeStamp100ns" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Process">
    <S T="2" F="ULS_Process" M="Ignore" />
  </C>
  <C T="U32" I="2" O="true" N="Thread">
    <S T="2" F="ULS_ThreadID" M="Ignore" />
  </C>
  <C T="TAG" I="3" O="true" N="EventID">
    <S T="2" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="Severity">
    <S T="2" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Message">
    <S T="2" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="SQMMachineID">
    <S T="2" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="SUSClientID">
    <S T="2" F="SessionID" M="Ignore" />
  </C>
  <C T="U32" I="8" O="true" N="GeoID">
    <S T="2" F="GeoID" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="Version">
    <S T="2" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="C2RClientVer">
    <S T="2" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="230158" V="1" DC="SM" EN="Office.ClickToRun.C2RGrooveTelemetry" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="b293n" />
    <UTS T="2" Id="b293o" />
    <UTS T="3" Id="b293k" />
    <UTS T="4" Id="b293j" />
    <US T="5">
      <S T="1" />
      <S T="2" />
      <S T="4" />
      <S T="3" />
    </US>
  </S>
  <C T="FT" I="0" O="true" N="TimeStamp">
    <S T="5" F="TimeStamp100ns" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Process">
    <S T="5" F="ULS_Process" M="Ignore" />
  </C>
  <C T="U32" I="2" O="true" N="Thread">
    <S T="5" F="ULS_ThreadID" M="Ignore" />
  </C>
  <C T="TAG" I="3" O="true" N="EventID">
    <S T="5" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="Severity">
    <S T="5" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Message">
    <S T="5" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="SQMMachineID">
    <S T="5" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="SUSClientID">
    <S T="5" F="SessionID" M="Ignore" />
  </C>
  <C T="U32" I="8" O="true" N="GeoID">
    <S T="5" F="GeoID" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="Version">
    <S T="5" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="Path">
    <S T="5" F="Path" M="Ignore" />
  </C>
  <C T="W" I="11" O="true" N="C2RClientVer">
    <S T="5" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="5" />
  </T>
</R><R Id="230162" V="1" DC="SM" EN="Office.ClickToRun.C2RTelemetryCriticalNoFilePathsVer2" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" SP="CriticalBusinessImpact" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="bon04" />
    <UTS T="2" Id="ayef5" />
    <UTS T="3" Id="bgodb" />
    <UTS T="4" Id="4trxn" />
    <US T="5">
      <S T="1" />
      <S T="3" />
      <S T="2" />
      <S T="4" />
    </US>
  </S>
  <C T="FT" I="0" O="true" N="TimeStamp">
    <S T="5" F="TimeStamp100ns" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Process">
    <S T="5" F="ULS_Process" M="Ignore" />
  </C>
  <C T="U32" I="2" O="true" N="Thread">
    <S T="5" F="ULS_ThreadID" M="Ignore" />
  </C>
  <C T="TAG" I="3" O="true" N="EventID">
    <S T="5" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="Severity">
    <S T="5" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Message">
    <S T="5" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="SQMMachineID">
    <S T="5" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="SUSClientID">
    <S T="5" F="SessionID" M="Ignore" />
  </C>
  <C T="U32" I="8" O="true" N="GeoID">
    <S T="5" F="GeoID" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="Version">
    <S T="5" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="Path">
    <S T="5" F="Path" M="Ignore" />
  </C>
  <C T="W" I="11" O="true" N="C2RClientVer">
    <S T="5" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="1" />
    <S T="2" />
    <S T="3" />
    <S T="4" />
  </T>
</R><R Id="230164" V="0" DC="SM" EN="Office.ClickToRun.C2RShownUI" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="axs0o" />
    <UTS T="2" Id="a9ber" />
    <UTS T="3" Id="a9bes" />
    <UTS T="4" Id="a9bet" />
    <US T="5">
      <S T="1" />
      <S T="2" />
      <S T="3" />
      <S T="4" />
    </US>
  </S>
  <C T="W" I="0" O="true" N="SUSClientID">
    <S T="5" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="SQMMachineID">
    <S T="5" F="MachineId" M="Ignore" />
  </C>
  <C T="TAG" I="2" O="true" N="EventID">
    <S T="5" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="Message">
    <S T="5" F="ContextData" M="Ignore" />
  </C>
  <T>
    <S T="5" />
  </T>
</R><R Id="230165" V="0" DC="SM" EN="Office.ClickToRun.C2RDelayLoadFailure" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="dexnt" />
  </S>
  <C T="W" I="0" O="true" N="SUSClientID">
    <S T="1" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="SQMMachineID">
    <S T="1" F="MachineId" M="Ignore" />
  </C>
  <C T="TAG" I="2" O="true" N="EventID">
    <S T="1" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="DllName">
    <S T="1" F="DllName" M="Ignore" />
  </C>
  <C T="U32" I="4" O="true" N="Attempt">
    <S T="1" F="attempt" M="Ignore" />
  </C>
  <C T="U32" I="5" O="true" N="LastError">
    <S T="1" F="lastError" M="Ignore" />
  </C>
  <T>
    <S T="1" />
  </T>
</R><R Id="230166" V="0" DC="SM" EN="Office.ClickToRun.C2RStreamPipelineCallback" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="c3hac" />
  </S>
  <C T="W" I="0" O="true" N="SUSClientID">
    <S T="1" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="SQMMachineID">
    <S T="1" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="1" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="Version">
    <S T="1" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="C2RClientVer">
    <S T="1" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="1" />
  </T>
</R><R Id="230167" V="0" DC="SM" EN="Office.ClickToRun.C2RAppVTags" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="annt3" />
    <UTS T="2" Id="cvqkl" />
    <UTS T="3" Id="cwq7a" />
    <US T="4">
      <S T="1" />
      <S T="2" />
      <S T="3" />
    </US>
  </S>
  <C T="W" I="0" O="true" N="SUSClientID">
    <S T="4" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="SQMMachineID">
    <S T="4" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="4" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="Version">
    <S T="4" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="C2RClientVer">
    <S T="4" F="C2RClientVer" M="Ignore" />
  </C>
  <C T="U64" I="5" O="true" N="ErrorCode">
    <S T="4" F="ErrorCode" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="ErrorType">
    <S T="4" F="ErrorType" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="ErrorSource">
    <S T="4" F="AppVErrorSource" M="Ignore" />
  </C>
  <C T="W" I="8" O="true" N="ErrorMessage">
    <S T="4" F="ErrorMessage" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="ErrorDetails">
    <S T="4" F="ErrorDetails" M="Ignore" />
  </C>
  <T>
    <S T="4" />
  </T>
</R><R Id="230168" V="0" DC="SM" EN="Office.ClickToRun.RuleTelemetry.C2RError" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UCSS T="1" C="Click-To-Run Error" S="Monitorable Unexpected Assert" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="19427861" T="U32" />
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="7385183" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="NE">
                <L>
                  <S T="1" F="ULS_Tag" />
                </L>
                <R>
                  <V V="8951314" T="U32" />
                </R>
              </O>
            </R>
          </O>
        </L>
        <R>
          <O T="AND">
            <L>
              <O T="NE">
                <L>
                  <S T="1" F="ULS_Tag" />
                </L>
                <R>
                  <V V="6062750" T="U32" />
                </R>
              </O>
            </L>
            <R>
              <O T="NE">
                <L>
                  <S T="1" F="ULS_Tag" />
                </L>
                <R>
                  <V V="575017095" T="U32" />
                </R>
              </O>
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="TAG" I="0" O="true" N="EventID">
    <S T="2" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Severity">
    <S T="2" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="2" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="SQMMachineID">
    <S T="2" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="SUSClientID">
    <S T="2" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Version">
    <S T="2" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="C2RClientVer">
    <S T="2" F="C2RClientVer" M="Ignore" />
  </C>
  <C T="U64" I="7" O="true" N="ErrorCode">
    <S T="2" F="ErrorCode" M="Ignore" />
  </C>
  <C T="W" I="8" O="true" N="ErrorType">
    <S T="2" F="ErrorType" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="ErrorSource">
    <S T="2" F="AppVErrorSource" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="ErrorMessage">
    <S T="2" F="ErrorMessage" M="Ignore" />
  </C>
  <C T="W" I="11" O="true" N="ErrorDetails">
    <S T="2" F="ErrorDetails" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="230169" V="0" DC="SM" EN="Office.ClickToRun.RuleTelemetry.C2RTaskError" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UCSS T="1" C="Click-To-Run Task Error" S="Monitorable Unexpected Assert" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="18716192" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="3700758" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="7153484" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="7153443" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="3990367" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="8935492" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="9040454" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </L>
        <R>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="8935494" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="4210770" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="5853916" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="576024667" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="20840669" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="38941977" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="TAG" I="0" O="true" N="EventID">
    <S T="2" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Severity">
    <S T="2" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="2" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="SQMMachineID">
    <S T="2" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="SUSClientID">
    <S T="2" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Version">
    <S T="2" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="ScenarioInstanceID">
    <S T="2" F="ScenarioInstanceId" M="Ignore" />
  </C>
  <C T="G" I="7" O="false" N="TaskID">
    <S T="2" F="TaskId" />
  </C>
  <C T="W" I="8" O="false" N="TaskType">
    <S T="2" F="TaskType" />
  </C>
  <C T="U32" I="9" O="false" N="TaskState">
    <S T="2" F="TaskState" />
  </C>
  <C T="W" I="10" O="false" N="Scenario">
    <S T="2" F="Scenario" />
  </C>
  <C T="W" I="11" O="false" N="InstallID">
    <S T="2" F="InstallID" />
  </C>
  <C T="U64" I="12" O="false" N="ErrorCode">
    <S T="2" F="ErrorCode" />
  </C>
  <C T="W" I="13" O="false" N="ErrorType">
    <S T="2" F="ErrorType" />
  </C>
  <C T="W" I="14" O="false" N="ErrorSource">
    <S T="2" F="AppVErrorSource" />
  </C>
  <C T="W" I="15" O="false" N="ErrorMessage">
    <S T="2" F="ErrorMessage" />
  </C>
  <C T="W" I="16" O="false" N="ErrorDetails">
    <S T="2" F="ErrorDetails" />
  </C>
  <C T="W" I="17" O="true" N="ScenarioSubType">
    <S T="2" F="ScenarioSubType" M="Ignore" />
  </C>
  <C T="W" I="18" O="true" N="C2RClientVer">
    <S T="2" F="C2RClientVer" M="Ignore" />
  </C>
  <C T="W" I="19" O="true" N="SourceType">
    <S T="2" F="SourceType" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="230170" V="1" DC="SM" EN="Office.ClickToRun.RuleTelemetry.C2RNonTaskError" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UCSS T="1" C="Click-To-Run Non Task Error" S="Monitorable Unexpected Assert" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19760271" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8131608" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="21071314" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="18400025" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="23598435" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="7627336" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="6422595" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="17856647" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="7209344" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="3725447" T="U32" />
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="7627329" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="3725449" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="4208142" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="3700882" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="3462472" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="3700939" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="6395657" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="3700942" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="4063515" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="4522633" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="4208133" T="U32" />
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="18400028" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </L>
        <R>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="3700876" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8980357" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="9049617" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="3700888" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="24397953" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="3700951" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="5853918" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="18491456" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="9000851" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="575017096" T="U32" />
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="594403674" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19235272" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="575017031" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="39068493" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="594403677" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="51709960" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="39654344" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="40490135" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="575017027" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="8131231" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="539756557" T="U32" />
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="3700491" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="TAG" I="0" O="true" N="EventID">
    <S T="2" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Severity">
    <S T="2" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="2" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="SQMMachineID">
    <S T="2" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="SUSClientID">
    <S T="2" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Version">
    <S T="2" F="Ver" M="Ignore" />
  </C>
  <C T="U64" I="6" O="false" N="ErrorCode">
    <S T="2" F="ErrorCode" />
  </C>
  <C T="W" I="7" O="false" N="ErrorType">
    <S T="2" F="ErrorType" />
  </C>
  <C T="W" I="8" O="false" N="ErrorSource">
    <S T="2" F="AppVErrorSource" M="Ignore" />
  </C>
  <C T="W" I="9" O="false" N="ErrorMessage">
    <S T="2" F="ErrorMessage" />
  </C>
  <C T="W" I="10" O="false" N="ErrorDetails">
    <S T="2" F="ErrorDetails" />
  </C>
  <C T="W" I="11" O="true" N="C2RClientVer">
    <S T="2" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="230171" V="0" DC="SM" EN="Office.ClickToRun.RuleTelemetry.C2RRepoman" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="6la6o" />
    <UTS T="2" Id="6la6n" />
    <US T="3">
      <S T="1" />
      <S T="2" />
    </US>
  </S>
  <C T="W" I="0" O="true" N="SUSClientID">
    <S T="3" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="SQMMachineID">
    <S T="3" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="3" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="Version">
    <S T="3" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="C2RClientVer">
    <S T="3" F="C2RClientVer" M="Ignore" />
  </C>
  <C T="TAG" I="5" O="true" N="EventID">
    <S T="3" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="U64" I="6" O="true" N="ErrorCode">
    <S T="3" F="ErrorCode" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="ErrorType">
    <S T="3" F="ErrorType" M="Ignore" />
  </C>
  <C T="W" I="8" O="true" N="ErrorSource">
    <S T="3" F="AppVErrorSource" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="ErrorMessage">
    <S T="3" F="ErrorMessage" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="ErrorDetails">
    <S T="3" F="ErrorDetails" M="Ignore" />
  </C>
  <T>
    <S T="3" />
  </T>
</R><R Id="230172" V="1" DC="SM" EN="Office.ClickToRun.RuleTelemetry.C2RTelemetry" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UCSS T="1" C="Click-To-Run General Telemetry" S="Verbose Medium Monitorable Unexpected Assert" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="4235714" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8131607" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="3700947" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="7407553" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8999307" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19142164" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19142169" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="7989340" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="4063514" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="20489352" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="6607323" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19142165" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8406941" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="7989336" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="5026637" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="36262983" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="20465238" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19142167" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19142215" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="19142216" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8980295" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="8980256" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="21275461" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="17420294" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="20489353" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8951301" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="36751570" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="6607307" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="558671715" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </L>
        <R>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="7131420" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="41187231" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="41187278" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="24928522" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="590919700" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="574226759" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="18092300" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="18092301" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8406942" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="9180002" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="9180003" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="595142024" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="37560385" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="37560386" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="37560387" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="37560388" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="20489367" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="8980299" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="589108944" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="575017038" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="575017026" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="575017025" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="AND">
                    <L>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="50374364" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="539756558" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="575017024" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="575017029" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="AND">
                        <L>
                          <O T="AND">
                            <L>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="594109772" T="U32" />
                                </R>
                              </O>
                            </L>
                            <R>
                              <O T="NE">
                                <L>
                                  <S T="1" F="ULS_Tag" />
                                </L>
                                <R>
                                  <V V="591397458" T="U32" />
                                </R>
                              </O>
                            </R>
                          </O>
                        </L>
                        <R>
                          <O T="NE">
                            <L>
                              <S T="1" F="ULS_Tag" />
                            </L>
                            <R>
                              <V V="38363744" T="U32" />
                            </R>
                          </O>
                        </R>
                      </O>
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="TAG" I="0" O="true" N="EventID">
    <S T="2" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Severity">
    <S T="2" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="2" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="SQMMachineID">
    <S T="2" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="SUSClientID">
    <S T="2" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Version">
    <S T="2" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="C2RClientVer">
    <S T="2" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="230173" V="0" DC="SM" EN="Office.ClickToRun.RuleTelemetry.C2RTaskTelemetry" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UCSS T="1" C="Click-To-Run Task Telemetry" S="Verbose Medium Monitorable Unexpected Assert" />
    <F T="2">
      <O T="AND">
        <L>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="3462423" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="3702920" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="3965062" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="3700754" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="4297094" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="7202265" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </L>
        <R>
          <O T="AND">
            <L>
              <O T="AND">
                <L>
                  <O T="AND">
                    <L>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="18716193" T="U32" />
                        </R>
                      </O>
                    </L>
                    <R>
                      <O T="NE">
                        <L>
                          <S T="1" F="ULS_Tag" />
                        </L>
                        <R>
                          <V V="7153487" T="U32" />
                        </R>
                      </O>
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="7153421" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </L>
            <R>
              <O T="AND">
                <L>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="7153435" T="U32" />
                    </R>
                  </O>
                </L>
                <R>
                  <O T="NE">
                    <L>
                      <S T="1" F="ULS_Tag" />
                    </L>
                    <R>
                      <V V="19265121" T="U32" />
                    </R>
                  </O>
                </R>
              </O>
            </R>
          </O>
        </R>
      </O>
    </F>
  </S>
  <C T="TAG" I="0" O="true" N="EventID">
    <S T="2" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Severity">
    <S T="2" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="2" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="SQMMachineID">
    <S T="2" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="SUSClientID">
    <S T="2" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Version">
    <S T="2" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="ScenarioInstanceID">
    <S T="2" F="ScenarioInstanceId" M="Ignore" />
  </C>
  <C T="G" I="7" O="false" N="TaskID">
    <S T="2" F="TaskId" />
  </C>
  <C T="W" I="8" O="false" N="TaskType">
    <S T="2" F="TaskType" />
  </C>
  <C T="U32" I="9" O="false" N="TaskState">
    <S T="2" F="TaskState" />
  </C>
  <C T="W" I="10" O="false" N="Scenario">
    <S T="2" F="Scenario" />
  </C>
  <C T="W" I="11" O="false" N="InstallID">
    <S T="2" F="InstallID" />
  </C>
  <C T="W" I="12" O="true" N="ScenarioSubType">
    <S T="2" F="ScenarioSubType" M="Ignore" />
  </C>
  <C T="W" I="13" O="true" N="C2RClientVer">
    <S T="2" F="C2RClientVer" M="Ignore" />
  </C>
  <C T="W" I="14" O="true" N="SourceType">
    <S T="2" F="SourceType" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="230174" V="0" DC="SM" EN="Office.ClickToRun.RuleTelemetry.C2RTag56v15" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP" xmlns="">
  <S>
    <UTS T="1" Id="56v15" />
    <US T="2">
      <S T="1" />
    </US>
  </S>
  <C T="TAG" I="0" O="true" N="EventID">
    <S T="2" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="Severity">
    <S T="2" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="Message">
    <S T="2" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="3" O="true" N="SQMMachineID">
    <S T="2" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="SUSClientID">
    <S T="2" F="SessionID" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Version">
    <S T="2" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="ScenarioInstanceID">
    <S T="2" F="ScenarioInstanceId" M="Ignore" />
  </C>
  <C T="G" I="7" O="false" N="TaskID">
    <S T="2" F="TaskId" />
  </C>
  <C T="W" I="8" O="false" N="TaskType">
    <S T="2" F="TaskType" />
  </C>
  <C T="U32" I="9" O="false" N="TaskState">
    <S T="2" F="TaskState" />
  </C>
  <C T="W" I="10" O="false" N="Scenario">
    <S T="2" F="Scenario" />
  </C>
  <C T="W" I="11" O="false" N="InstallID">
    <S T="2" F="InstallID" />
  </C>
  <C T="U64" I="12" O="false" N="ErrorCode">
    <S T="2" F="ErrorCode" />
  </C>
  <C T="W" I="13" O="false" N="ErrorType">
    <S T="2" F="ErrorType" />
  </C>
  <C T="W" I="14" O="false" N="ErrorSource">
    <S T="2" F="AppVErrorSource" />
  </C>
  <C T="W" I="15" O="false" N="ErrorMessage">
    <S T="2" F="ErrorMessage" />
  </C>
  <C T="W" I="16" O="false" N="ErrorDetails">
    <S T="2" F="ErrorDetails" />
  </C>
  <C T="W" I="17" O="true" N="ScenarioSubType">
    <S T="2" F="ScenarioSubType" M="Ignore" />
  </C>
  <C T="W" I="18" O="true" N="C2RClientVer">
    <S T="2" F="C2RClientVer" M="Ignore" />
  </C>
  <C T="W" I="19" O="true" N="SourceType">
    <S T="2" F="SourceType" M="Ignore" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="120119" V="0" DC="SM" EN="Office.System.SystemHealthRollbackSessionMetadata" ATT="cd836626611c4caaa8fc5b2e728ee81d-3b6d6c45-6377-4bf5-9792-dbf8e1881088-7521" SP="CriticalCensus" DL="A" DCa="PSU" xmlns="">
  <RIS>
    <RI N="Metadata" />
  </RIS>
  <S>
    <R T="1" R="120100" />
    <UTS T="2" Id="awjb7" />
    <UTS T="3" Id="a14x3" />
    <R T="4" R="120119" />
  </S>
  <C T="W" I="0" O="true" N="PreviousBuild">
    <O T="COALESCE">
      <L>
        <O T="COALESCE">
          <L>
            <S T="3" F="PreviousBuild" M="Ignore" />
          </L>
          <R>
            <S T="2" F="PreviousBuild" M="Ignore" />
          </R>
        </O>
      </L>
      <R>
        <S T="4" F="0" M="Ignore" />
      </R>
    </O>
  </C>
  <C T="U32" I="1" O="true" N="InstallMethod">
    <O T="COALESCE">
      <L>
        <O T="COALESCE">
          <L>
            <S T="3" F="InstallMethod" M="Ignore" />
          </L>
          <R>
            <S T="2" F="InstallMethod" M="Ignore" />
          </R>
        </O>
      </L>
      <R>
        <S T="4" F="1" M="Ignore" />
      </R>
    </O>
  </C>
  <T>
    <S T="1" />
    <S T="2" />
    <S T="3" />
  </T>
  <ST>
    <S T="2" />
    <S T="3" />
    <S T="4" />
  </ST>
</R><R Id="224900" V="0" DC="SM" T="Subrule" xmlns="">
  <S>
    <UTS T="1" Id="bbr5m" />
    <F T="2">
      <O T="EQ">
        <L>
          <S T="1" F="DoLicValidationMode" />
        </L>
        <R>
          <V V="0" T="I32" />
        </R>
      </O>
    </F>
  </S>
  <C T="I32" I="0" O="false">
    <S T="2" F="DoLicValidationMode" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704101" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Privacy.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPrivacy" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="0cc923f8e48042f9b5193007cf34d1ae-def86b84-7392-4e7f-8da6-ecea0a375ec4-7160" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704100" V="0" DC="SM" EN="Office.Telemetry.Event.Office.Privacy" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenPrivacy" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="0cc923f8e48042f9b5193007cf34d1ae-def86b84-7392-4e7f-8da6-ecea0a375ec4-7160" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704201" V="0" DC="SM" EN="Office.Telemetry.Event.Office.CommandExecution.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCommandExecution" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="67019aa758914ee4976e974d2670d5e5-ee14fa06-873c-4413-a757-acaef88bf729-6625" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704200" V="0" DC="SM" EN="Office.Telemetry.Event.Office.CommandExecution" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenCommandExecution" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="67019aa758914ee4976e974d2670d5e5-ee14fa06-873c-4413-a757-acaef88bf729-6625" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704151" V="0" DC="SM" EN="Office.Telemetry.Event.Office.AppDocs.Critical" SP="CriticalBusinessImpact" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAppDocs" S="Medium" />
    <F T="2">
      <O T="GE">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3eb9cdc7677e47f4a6ebf3a5dcb3f82f-76503a31-a737-4f01-b227-38af43d97af2-6909" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="704150" V="0" DC="SM" EN="Office.Telemetry.Event.Office.AppDocs" DL="A" xmlns="">
  <RIS>
    <RI N="Event" />
  </RIS>
  <S>
    <UCSS T="1" C="NexusTenantTokenAppDocs" S="Medium" />
    <F T="2">
      <O T="LT">
        <L>
          <S T="1" F="EventSamplingPolicy" />
        </L>
        <R>
          <V V="191" T="U8" />
        </R>
      </O>
    </F>
  </S>
  <C T="W" I="0" O="false" N="EventName">
    <S T="2" F="EventName" />
  </C>
  <C T="W" I="1" O="true" N="EventContract">
    <S T="2" F="EventContract" M="Ignore" />
  </C>
  <C T="U64" I="2" O="false" N="EventFlags">
    <S T="2" F="EventFlags" />
  </C>
  <C T="D" I="3" O="false" N="EventSampleRate">
    <V V="1.0" T="D" />
  </C>
  <C T="W" I="4" O="false" N="EventData">
    <S T="2" F="EventData" />
  </C>
  <C T="W" I="5" O="false" N="EventContractInfo">
    <S T="2" F="EventContractInfo" />
  </C>
  <C T="W" I="6" O="false" N="EventTypeInfo">
    <S T="2" F="EventTypeInfo" />
  </C>
  <C T="W" I="7" O="false" N="EventDataClassificationInfo">
    <S T="2" F="EventDataClassificationInfo" />
  </C>
  <C T="W" I="8" O="true" N="AriaTenantToken">
    <V V="3eb9cdc7677e47f4a6ebf3a5dcb3f82f-76503a31-a737-4f01-b227-38af43d97af2-6909" T="W" />
  </C>
  <T>
    <S T="2" />
  </T>
</R><R Id="226009" V="0" DC="SM" EN="Office.Globalization.StringResources" ATT="fa2d8726ec914a019f61aa83d1a0f156-ff2e4b12-d084-4558-9515-fbf9e1224e49-7217" DCa="PSU" xmlns="">
  <S>
    <UTS T="1" Id="b56v5" />
  </S>
  <C T="U32" I="0" O="false" N="Alias">
    <S T="1" F="alias" />
  </C>
  <C T="U32" I="1" O="false" N="Ids">
    <S T="1" F="ids" />
  </C>
  <C T="W" I="2" O="true" N="Container">
    <S T="1" F="container" />
  </C>
  <T>
    <S T="1" />
  </T>
</R></Rules>