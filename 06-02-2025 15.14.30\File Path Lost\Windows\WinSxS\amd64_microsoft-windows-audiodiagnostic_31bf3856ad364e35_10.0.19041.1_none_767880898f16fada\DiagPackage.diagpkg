<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007" SchemaVersion="1.0" Localized="true">
  <DiagnosticIdentification>
    <ID>AudioDiagnostic</ID>
    <Version>4.5</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>MF_AudioDiagnostic.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_AudioService</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-221</Name>
        <Description>@diagpackage.dll,-222</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_StartAudioService</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-223</Name>
            <Description>@diagpackage.dll,-224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_AudioService.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_AudioService.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_AudioServiceResponse</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-33088</Name>
        <Description>
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_AudioServiceResponse</ID>
          <DisplayInformation>
            <Parameters>
            </Parameters>
            <Name>@diagpackage.dll,-33089</Name>
            <Description>@diagpackage.dll,-33090</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_AudioServiceResponse.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_AudioServiceResponse.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_DisabledInCPL</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-301</Name>
        <Description>@diagpackage.dll,-302</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_EnableInMMSYS</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-303</Name>
            <Description>@diagpackage.dll,-304</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>DeviceID</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_EnableInCPL.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>DeviceID</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_DisabledInCPL.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_Mute</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-401</Name>
        <Description>@diagpackage.dll,-402</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Unmute</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-403</Name>
            <Description>@diagpackage.dll,-404</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>DeviceID</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>DeviceType</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_Unmute.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>DeviceID</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>DeviceType</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_Mute.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_LowVolume</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-501</Name>
        <Description>@diagpackage.dll,-502</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_LowVolume</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-503</Name>
            <Description>@diagpackage.dll,-504</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>DeviceType</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>DeviceID</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_ChangeVolume.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>DeviceType</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>DeviceID</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Volume</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_LowVolume.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_NotDefault</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-601</Name>
        <Description>@diagpackage.dll,-602</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_SetAsDefault</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-603</Name>
            <Description>@diagpackage.dll,-604</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>DeviceID</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_NotDefault.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>DeviceType</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>DeviceID</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_NotDefault.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_UnpluggedIn</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>DeviceType</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-701</Name>
        <Description>@diagpackage.dll,-702</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_PlugInAudioDevice</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>DeviceType</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-703</Name>
            <Description>@diagpackage.dll,-704</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint>
            <RTFDescription>@diagpackage.dll,-705</RTFDescription>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>DeviceType</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>DeviceID</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_UnpluggedIn.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_AudioDevice</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-801</Name>
        <Description>@diagpackage.dll,-802</Description>
      </DisplayInformation>
      <Resolvers/>
      <Verifier/>
      <ContextParameters>
        <Parameter>
          <Name>DEVICEID</Name>
          <DefaultValue/>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_APOLoadFailure</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-33082</Name>
        <Description>
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_APOLoadFailure</ID>
          <DisplayInformation>
            <Parameters>
            </Parameters>
            <Name>@diagpackage.dll,-33084</Name>
            <Description>
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>deviceIDs</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>deviceCount</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_APOLoadFailure.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>action</Name>
              <DefaultValue>Verify</DefaultValue>
            </Parameter>
            <Parameter>
              <Name>defaultDeviceName</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
            <Parameter>
              <Name>defaultDeviceType</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_APOLoadFailure.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_SamplingRate</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-33085</Name>
        <Description>
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_SamplingRate</ID>
          <DisplayInformation>
            <Parameters>
            </Parameters>
            <Name>@diagpackage.dll,-33086</Name>
            <Description>@diagpackage.dll,-33087</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>deviceID</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_SamplingRate.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>deviceID</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_SamplingRate.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
    <ID>RC_HDAudioDriver</ID>
    <DisplayInformation>
      <Parameters/>
      <Name>@diagpackage.dll,-33091</Name>
      <Description>
      </Description>
    </DisplayInformation>
    <Resolvers>
      <Resolver>
        <ID>RS_HDAudioDriver</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-33092</Name>
          <Description>
          </Description>
        </DisplayInformation>
        <RequiresConsent>false</RequiresConsent>
        <Script>
          <Parameters>
            <Parameter>
              <Name>deviceID</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
              <Parameter>
              <Name>PNPDevID</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
            <Parameter>
              <Name>PNPDevName</Name>
              <DefaultValue/>
            </Parameter>
          <Parameter>
              <Name>DeviceType</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>RS_HDAudioDriver.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Resolver>
    </Resolvers>
    <Verifier>
      <Script>
        <Parameters>
          <Parameter>
            <Name>PNPDevID</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
          <Parameter>
            <Name>PNPDevName</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <ProcessArchitecture>Any</ProcessArchitecture>
        <RequiresElevation>true</RequiresElevation>
        <RequiresInteractivity>false</RequiresInteractivity>
        <FileName>VF_HDAudioDriver.ps1</FileName>
        <ExtensionPoint/>
      </Script>
      <ExtensionPoint/>
    </Verifier>
    <ContextParameters/>
    <ExtensionPoint/>
  </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-5</Name>
              <Description/>
            </DisplayInformation>
            <Value>Render</Value>
            <ExtensionPoint>
              <Icon>@DiagPackage.dll,-1001</Icon>
            </ExtensionPoint>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-9</Name>
              <Description/>
            </DisplayInformation>
            <Value>Capture</Value>
            <ExtensionPoint>
              <Icon>@DiagPackage.dll,-1003</Icon>
            </ExtensionPoint>
          </Choice>
        </Choices>
        <ID>IT_GetDeviceType</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-3</Name>
          <Description/>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>true</AllowDynamicResponses>
        <Choices/>
        <ID>IT_GetCertainDevice</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-50</Name>
          <Description/>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>true</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-33093</Name>
              <Description>@diagpackage.dll,-33094</Description>
            </DisplayInformation>
            <Value>IT_PlayTest</Value>
            <ExtensionPoint>
              <NoCache/>
              <CommandLinks/>
            </ExtensionPoint>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-33095</Name>
              <Description>@diagpackage.dll,-33096</Description>
            </DisplayInformation>
            <Value>IT_Skip</Value>
            <ExtensionPoint>
              <NoCache/>
              <CommandLinks/>
            </ExtensionPoint>
          </Choice>
        </Choices>
        <ID>IT_AudioHDTest</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-33097</Name>
          <Description>@diagpackage.dll,-33098</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <CommandLinks/>
        <InteractionIcon>info</InteractionIcon>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>true</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters>
                 <Parameter>
                   <Name>INT_Good_Desc</Name>
                   <DefaultValue>-</DefaultValue>
                </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-33099</Name>
              <Description>@diagpackage.dll,-33100</Description>
            </DisplayInformation>
            <Value>IT_Good</Value>
            <ExtensionPoint>
              <NoCache/>
              <CommandLinks/>
             <InteractionIcon>info</InteractionIcon>
            </ExtensionPoint>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters>
                <Parameter>
                   <Name>INT_Bad_Desc</Name>
                   <DefaultValue>-</DefaultValue>
                </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-33101</Name>
              <Description>@diagpackage.dll,-33102</Description>
            </DisplayInformation>
            <Value>IT_Bad</Value>
            <ExtensionPoint>
              <NoCache/>
              <CommandLinks/>
            </ExtensionPoint>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters>
                <Parameter>
                   <Name>INT_Did_Desc</Name>
                   <DefaultValue>-</DefaultValue>
                </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-33103</Name>
              <Description>@diagpackage.dll,-33104</Description>
            </DisplayInformation>
            <Value>IT_NotHeard</Value>
            <ExtensionPoint>
              <NoCache/>
              <CommandLinks/>
            </ExtensionPoint>
          </Choice>
        </Choices>
        <ID>IT_AudioHD</ID>
        <DisplayInformation>
          <Parameters>
             <Parameter>
                <Name>INT_Desc</Name>
                <DefaultValue>-</DefaultValue>
             </Parameter>
          </Parameters>
          <Name>@diagpackage.dll,-33105</Name>
          <Description>@diagpackage.dll,-33106</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <CommandLinks/>
          <InteractionIcon>info</InteractionIcon>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-33111</Name>
              <Description/>
            </DisplayInformation>
            <Value>1</Value>
            <ExtensionPoint>
            </ExtensionPoint>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-33112</Name>
              <Description/>
            </DisplayInformation>
            <Value>0</Value>
            <ExtensionPoint>
            </ExtensionPoint>
          </Choice>
        </Choices>
        <ID>IT_AudioProperties</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-33107</Name>
          <Description/>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <RTFDescription>@diagpackage.dll,-33108</RTFDescription>
          <InteractionIcon>info</InteractionIcon>
          <CommandLinks/>
        </ExtensionPoint>
      </SingleResponseInteraction>
    </SingleResponseInteractions>
    <MultipleResponseInteractions/>
    <TextInteractions/>
    <PauseInteractions>
      <PauseInteraction>
        <ID>IT_RunOnRemoteSession</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-73</Name>
          <Description>@diagpackage.dll,-74</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>error</InteractionIcon>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>INT_RebootSystem</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-14207</Name>
          <Description>@diagpackage.dll,-14208</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
            <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
      </PauseInteraction>
    </PauseInteractions>
    <LaunchUIInteractions>
      <LaunchUIInteraction>
        <Parameters>
          <Parameter>
            <Name>ID</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <CommandLine>SndVol.exe -r 0 0 %ID%</CommandLine>
        <ID>IT_ChangeVolumeSH</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-943</Name>
          <Description>@diagpackage.dll,-944</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <CommandLinks/>
          <ButtonText>@diagpackage.dll,-947</ButtonText>
         <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
      </LaunchUIInteraction>
      <LaunchUIInteraction>
        <Parameters>
          <Parameter>
            <Name>ID</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <CommandLine>rundll32.exe shell32.dll,Control_RunDLL mmsys.cpl,,%ID%,levels</CommandLine>
        <ID>IT_ChangeVolumeM</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-945</Name>
          <Description>@diagpackage.dll,-946</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <CommandLinks/>
          <ButtonText>@diagpackage.dll,-948</ButtonText>
        <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
      </LaunchUIInteraction>
    </LaunchUIInteractions>
  </Interactions>
  <ExtensionPoint>
    <Icon>@DiagPackage.dll,-1004</Icon>
    <HelpKeywords>@DiagPackage.dll,-12</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-13</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-20</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-21</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-22</HelpKeywords>
    <Feedback>
      <ContextId>68</ContextId>
    </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>