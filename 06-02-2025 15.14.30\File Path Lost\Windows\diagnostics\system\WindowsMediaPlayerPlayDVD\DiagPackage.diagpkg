<dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
    <DiagnosticIdentification>
        <ID>WindowsMediaPlayerDVDDiagnostic</ID>
        <Version>1.1</Version>
    </DiagnosticIdentification>
    <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-1</Name>
        <Description>@diagpackage.dll,-2</Description>
    </DisplayInformation>
    <PrivacyLink>http://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
    <PowerShellVersion>1.0</PowerShellVersion>
    <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
    <Troubleshooter>
        <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>TS_WindowsMediaPlayer.ps1</FileName>
            <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
    </Troubleshooter>
    <Rootcauses>
        <Rootcause>
            <ID>RC_MissingDVDDecoder</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-26</Name>
                <Description>@diagpackage.dll,-27</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_MissingDVDDecoder</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-28</Name>
                        <Description>@diagpackage.dll,-29</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script>
                           <Parameters/>
                           <ProcessArchitecture>Any</ProcessArchitecture>
                           <RequiresElevation>false</RequiresElevation>
                           <RequiresInteractivity>true</RequiresInteractivity>
                           <FileName>RS_DvdDecoder.ps1</FileName>
                           <ExtensionPoint/>
                     </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
          <ID>RC_MissingDVDDevice</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-91</Name>
            <Description>@diagpackage.dll,-92</Description>
          </DisplayInformation>
          <Resolvers>
            <Resolver>
              <ID>RS_MissingDVDDevice</ID>
              <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-93</Name>
                <Description>@diagpackage.dll,-94</Description>
              </DisplayInformation>
              <RequiresConsent>false</RequiresConsent>
              <Script/>
              <ExtensionPoint/>
            </Resolver>
          </Resolvers>
          <Verifier/>
          <ContextParameters/>
          <ExtensionPoint/>
        </Rootcause>
       <Rootcause>
            <ID>RC_WMPUnavailable</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-30</Name>
                <Description>@diagpackage.dll,-31</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_InstallWMP</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-32</Name>
                        <Description>@diagpackage.dll,-33</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script/>
                    <ExtensionPoint>
                        <Link>http://www.microsoft.com/windows/windowsmedia/player/</Link>
                        <LinkText>@diagpackage.dll,-14</LinkText>
                    </ExtensionPoint>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_ProblematicDVDDevice</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-7</Name>
                <Description>@diagpackage.dll,-8</Description>
            </DisplayInformation>
            <Resolvers/>
            <Verifier/>
            <ContextParameters>
                <Parameter>
                    <Name>DEVICEID</Name>
                    <DefaultValue/>
                </Parameter>
            </ContextParameters>
            <ExtensionPoint/>
        </Rootcause>
    </Rootcauses>
  <Interactions>
      <SingleResponseInteractions/>
      <MultipleResponseInteractions/>
      <TextInteractions/>
      <PauseInteractions>
        <PauseInteraction>
          <ID>IT_WMPRuning</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-63</Name>
            <Description>@diagpackage.dll,-64</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <RTFDescription>@diagpackage.dll,-67</RTFDescription>
          </ExtensionPoint>
        </PauseInteraction>
        <PauseInteraction>
            <ID>IT_DvdDecoderHelp</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-101</Name>
                <Description>@diagpackage.dll,-102</Description>
            </DisplayInformation>
            <ContextParameters/>
            <ExtensionPoint>
            <Link>http://www.microsoft.com/windows/windowsmedia/player/plugins.aspx</Link>
            <LinkText>@diagpackage.dll,-505</LinkText>
            </ExtensionPoint>
        </PauseInteraction>
      </PauseInteractions>
      <LaunchUIInteractions/>
    </Interactions>
    <ExtensionPoint>
      <Icon>@DiagPackage.dll,-1001</Icon>
      <HelpKeywords>@DiagPackage.dll,-201</HelpKeywords>
      <HelpKeywords>@DiagPackage.dll,-203</HelpKeywords>
      <HelpKeywords>@DiagPackage.dll,-204</HelpKeywords>
    </ExtensionPoint>
</dcmPS:DiagnosticPackage>