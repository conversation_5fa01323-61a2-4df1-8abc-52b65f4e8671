<?xml version="1.0" encoding="us-ascii"?><Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><cfg:Configuration version="1.1" xmlns:cfg="http://schemas.microsoft.com/Passport/PPCRL"><!--
      When a certificate is rev'd, a line like the following should be 
      added to the cfg:Settings section:
      <cfg:Certificate expired="true">SLCA_BACKUP.CER</cfg:Certificate>
    --><cfg:Settings><cfg:DeviceDNSSuffix>.devicedns.live.com</cfg:DeviceDNSSuffix><cfg:ResolveTimeout>120000</cfg:ResolveTimeout><cfg:ConnectTimeout>60000</cfg:ConnectTimeout><cfg:SendTimeout>30000</cfg:SendTimeout><cfg:ReceiveTimeout>30000</cfg:ReceiveTimeout><cfg:MinMinutesBetweenMetaConfigCheck>1440</cfg:MinMinutesBetweenMetaConfigCheck><cfg:ConfigServerSslURI>https://go.microsoft.com/fwlink/?LinkId=859524</cfg:ConfigServerSslURI><cfg:DIDCOMMetaData><cfg:DIDWithAuth>1</cfg:DIDWithAuth><cfg:AssocPDIDToLDID>1</cfg:AssocPDIDToLDID><cfg:Protocol><cfg:CLSID>{1C109E4C-2F30-4EA3-A57A-A290877A2303}</cfg:CLSID><cfg:DATA><![CDATA[<Input version="1" match="20"><Excluded><Controller>Win32_USBControllerDevice</Controller><Controller>Win32_1394ControllerDevice</Controller><Controller>Win32_PCMCIAControllerDevice</Controller><Controller>Win32_ConnectionShare</Controller></Excluded><Class name="Win32_PhysicalMedia" pos="1" hash="32" match="10"><Data>SerialNumber</Data></Class><Class name="Win32_NetworkAdapter" pos="2" hash="32" match="10"><Data>MACAddress</Data></Class><Class name="Win32_BIOS" pos="3" hash="32" match="10"><Data>Manufacturer</Data><Data>SerialNumber</Data></Class></Input>]]></cfg:DATA><cfg:PREFIX>01</cfg:PREFIX></cfg:Protocol><cfg:Protocol><cfg:CLSID>{B9F1D9B8-1DA6-4F17-962F-69EC82EA2704}</cfg:CLSID><cfg:PREFIX>03</cfg:PREFIX><cfg:LOGICAL>1</cfg:LOGICAL><cfg:SYSTEM>1</cfg:SYSTEM></cfg:Protocol><cfg:Protocol><cfg:CLSID>{B9F1D9B8-1DA6-4F17-962F-69EC82EA2704}</cfg:CLSID><cfg:PREFIX>02</cfg:PREFIX><cfg:LOGICAL>1</cfg:LOGICAL></cfg:Protocol><cfg:CONSENTVERSION>1</cfg:CONSENTVERSION></cfg:DIDCOMMetaData><cfg:Certificate>MIIDGzCCAtugAwIBAgIJAL6pALeLZHDvMAkGByqGSM44BAMwIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MB4XDTE2MDUwOTIwNDA1NVoXDTIxMDUwODIwNDA1NVowIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MIIBtzCCASwGByqGSM44BAEwggEfAoGBAKmnM6FKnJ1osHWBHd8jHFFMDNwygIcof9EfHW6xJbU2uIzVV5WRu251RbRbmOqozesK44vkeBx9Ntl7PSu0W5RJFCEw9o4RLG0J40MWDNDQ+7iqvIsM6lsgZrDjNoWJ2t/Yrnnp0r3F9YNtj7J4vgLCYNOlkr2kcfGiji+SXotXAhUAqGI2zlbARtjz+simu2PihUPkV1MCgYEAnrIIXPvjKvWiXd3uve7eDS8qRHW7yoAseVLgeEIbS4VSj/9iQqunx1weQGlds75CLZgpcrY4Z5wbkxSjnK1EV30qce4+VzgXYGLZIAvl78wbAMFrG0josXccAbmqVM4LX2Z5yu1TEpPZLvgiaV4gJk4jsYhInqPm3Bw2qQ+gZokDgYQAAoGAFozcPQdM9MmszvEWMXuFrfEA8kVMnKIyA60paSi7CIecSAlsRLyw3D9J9pRW6HHdRZgOrKvnNcY63gKB56SKyj6r3nGqtkwEpu9yw1K+k2aSyJcKPHYVNw1Um5McKJgQJ49ShJFPaWXfDZPOCpgMpdzibnWjMcTak5rz4FHSUtOjgZgwgZUwHQYDVR0OBBYEFKNT6lUDzQppxFhwtv/jkSCRx596MFMGA1UdIwRMMEqAFKNT6lUDzQppxFhwtv/jkSCRx596oSekJTAjMSEwHwYDVQQDExhUb2tlbiBTaWduaW5nIFB1YmxpYyBLZXmCCQC+qQC3i2Rw7zASBgNVHRMBAf8ECDAGAQH/AgEAMAsGA1UdDwQEAwIBxjAJBgcqhkjOOAQDAy8AMCwCFGjnp838qB/wTUdvoRt4G3aW0fv0AhRe8cZDdOveTDCbWane65nlVv0Udw==</cfg:Certificate><cfg:Certificate>MIIDHDCCAtugAwIBAgIJAPVhDwwaTUJrMAkGByqGSM44BAMwIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MB4XDTIxMDMxODE4NDIzN1oXDTQxMDMxMzE4NDIzN1owIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MIIBtzCCASsGByqGSM44BAEwggEeAoGBANwZEes+CsJ6G4/Pe5XmGS9e5qki7KPMh9+IU5JE+z0H0Q4v51cKYCcm0OHtStB4oTF3RqTcYT6wYp/k6CcrGUVKo60tWY3C+ohOABpfoAnstLX/bZmWYQ87KzkRpB21WGCAJsoNQ5V0oQbwnEeeWO5sfZ6T3lfP7YgyeY4NatWhAhUAhFP2mQrVRzurTrsPRs5S1aNUWWUCgYBw3lXgm9xw1G9BSs8mP9sv8IefC87tBupniaUY/lz+AlN0qs6tXPHSesobCFxLDNT0Vhw4fSTJBPlq99tvV6ipEQkBtR3ColMVMq0G7ECW8ojnPWUcwdYUCyky7kyV7EuwnUYoAN4nZWBhN9BOj/nO0Ds5HInryR0RnTEvT0uojwOBhQACgYEA2SDhhPBDnKxvmH9RHvTUW0pvaTyAbFXxQ1WP+rcN608akqKmyMiskxCUySoBGAEbE/szcBqTu4VpvskyOBHY6Li6ewYP5cpVe7mARh27+cuGOzz3JZAVwi79D6u9/6OxnLyfbdRpYFq0qlmtUSbqtp895cs0cLn8TIppt+h6iDKjgZgwgZUwHQYDVR0OBBYEFNggH9qn8tEAK/Xtp75ejkmJcgjwMFMGA1UdIwRMMEqAFNggH9qn8tEAK/Xtp75ejkmJcgjwoSekJTAjMSEwHwYDVQQDExhUb2tlbiBTaWduaW5nIFB1YmxpYyBLZXmCCQD1YQ8MGk1CazASBgNVHRMBAf8ECDAGAQH/AgEAMAsGA1UdDwQEAwIBxjAJBgcqhkjOOAQDAzAAMC0CFHDGG3jer0Hzy/Qr08Dx/XpQGPG/AhUAg5mwhsY1EvBXNSor8eaySNirEH0=</cfg:Certificate><cfg:AddressResolutionTTL>900</cfg:AddressResolutionTTL><cfg:ClientTimeOutForRpcCallsNoNetwork>5</cfg:ClientTimeOutForRpcCallsNoNetwork><cfg:ClientTimeoutForRpcCallsWithNetwork>600</cfg:ClientTimeoutForRpcCallsWithNetwork><cfg:LivesspMaxTokenSize>60000</cfg:LivesspMaxTokenSize><cfg:AccountDomain>account.live.com</cfg:AccountDomain><cfg:InterruptResolutionDomain>account.live.com</cfg:InterruptResolutionDomain><cfg:PasswordReset>account.live.com/password/reset</cfg:PasswordReset><cfg:AccountPolicy>sapi</cfg:AccountPolicy><cfg:ConnectAccountPolicy>mbi_ssl</cfg:ConnectAccountPolicy><cfg:StrongAuthPolicy>mbi_ssl_sa</cfg:StrongAuthPolicy><cfg:MinPasswordLength>8</cfg:MinPasswordLength><cfg:MinPasswordCharacterGroups>2</cfg:MinPasswordCharacterGroups><cfg:CookieP3PHeader><![CDATA[CP="CAO DSP COR ADMa DEV CONo TELo CUR PSA PSD TAI IVDo OUR SAMi BUS DEM NAV STA UNI COM INT PHY ONL FIN PUR LOCi CNT"]]></cfg:CookieP3PHeader><cfg:ThrottleFlags>1</cfg:ThrottleFlags><cfg:ThrottleMaxRequests>200</cfg:ThrottleMaxRequests><cfg:ThrottleTotalIntervalSeconds>7200</cfg:ThrottleTotalIntervalSeconds><cfg:ThrottledApplications><cfg:ThrottledApp><cfg:ThrottledAppID>dc2191f2-1801-4fd8-84bd-e776344a34b0</cfg:ThrottledAppID><cfg:ThrottledAppMaxRequests>4</cfg:ThrottledAppMaxRequests></cfg:ThrottledApp></cfg:ThrottledApplications><cfg:NegativeCacheFlags>1</cfg:NegativeCacheFlags><cfg:NegativeCacheMaxRequests>3</cfg:NegativeCacheMaxRequests><cfg:NegativeCacheIntervalSeconds>28800</cfg:NegativeCacheIntervalSeconds><cfg:ConfigVersion>16.000.29743.00</cfg:ConfigVersion><cfg:ThrottleHWBindingMaxRequests>50</cfg:ThrottleHWBindingMaxRequests><cfg:ThrottleHWUpdateMaxRequests>2</cfg:ThrottleHWUpdateMaxRequests><cfg:ThrottleHWUpdateOutOfToleranceMaxRequests>2</cfg:ThrottleHWUpdateOutOfToleranceMaxRequests><cfg:ThrottleTpmBindingMaxRequests>3</cfg:ThrottleTpmBindingMaxRequests><cfg:DeviceProvisioningFailureThreshold>3</cfg:DeviceProvisioningFailureThreshold></cfg:Settings><cfg:ServiceURIs><cfg:DisableDIDCookie>1</cfg:DisableDIDCookie><cfg:MSNDomain>msn.com</cfg:MSNDomain><cfg:DADomain>login.live.com</cfg:DADomain><cfg:WLDomain>live.com</cfg:WLDomain><cfg:SignupDomain>login.live.com,signup.live.com,account.live.com</cfg:SignupDomain><cfg:LoginSrf>login.srf</cfg:LoginSrf><cfg:InvalidName><![CDATA[\%:[],#"<>;()]]></cfg:InvalidName><cfg:InvalidDomain><![CDATA[\%:[],#"<>;'()]]></cfg:InvalidDomain><cfg:InvalidUrl><![CDATA[\[],"<>';()]]></cfg:InvalidUrl><cfg:PostSrf>post.srf</cfg:PostSrf><cfg:ManageEIDsSrf>ManageEIDs.srf</cfg:ManageEIDsSrf><cfg:SecureSrf>secure.srf</cfg:SecureSrf><cfg:LogoutSrf>logout.srf</cfg:LogoutSrf><cfg:TrustedDomain>live.com,msn.com,zune.net,windowsmarketplace.com,workspace.office.live.com,atdmt.com</cfg:TrustedDomain><cfg:URL_PasswordReset>https://login.live.com/resetpw.srf</cfg:URL_PasswordReset><cfg:PassportSHA1Auth>https://login.live.com/ppsecure/SHA1Auth.srf</cfg:PassportSHA1Auth><cfg:DeviceIdTrustedDomain>*.live.com</cfg:DeviceIdTrustedDomain><cfg:DIDManagementDomain>login.live.com,account.live.com</cfg:DIDManagementDomain><cfg:PREFBINCookieDomain>c.live.com,c.msn.com</cfg:PREFBINCookieDomain><cfg:WLIDSTS_WCF>https://login.live.com/RST2.srf</cfg:WLIDSTS_WCF><cfg:URL_DeviceTOU>https://login.live.com/didtou.srf</cfg:URL_DeviceTOU><cfg:DeviceChangeService>https://login.live.com/ppsecure/devicechangecredential.srf</cfg:DeviceChangeService><cfg:DeviceAddService>https://login.live.com/ppsecure/deviceaddcredential.srf</cfg:DeviceAddService><cfg:DeviceRemoveService>https://login.live.com/ppsecure/deviceremovecredential.srf</cfg:DeviceRemoveService><cfg:DeviceAssociateService>https://login.live.com/ppsecure/DeviceAssociate.srf</cfg:DeviceAssociateService><cfg:DeviceDisassociateService>https://login.live.com/ppsecure/DeviceDisassociate.srf</cfg:DeviceDisassociateService><cfg:DeviceQueryService>https://login.live.com/ppsecure/DeviceQuery.srf</cfg:DeviceQueryService><cfg:DeviceUpdateService>https://login.live.com/ppsecure/DeviceUpdate.srf</cfg:DeviceUpdateService><cfg:DeviceEnumerateService>https://login.live.com/ppsecure/EnumerateDevices.srf</cfg:DeviceEnumerateService><cfg:ResolveUserService>https://login.live.com/ppsecure/ResolveUser.srf</cfg:ResolveUserService><!-- Endpoints for enterprise device authentication. These are enterprise focused siblings of similar
           endpoints above. Client-side policy will determine which endpoint to use in a given situation. --><cfg:EnterpriseWLIDSTS_WCF>https://login.microsoftonline.com/MSARST2.srf</cfg:EnterpriseWLIDSTS_WCF><cfg:EnterpriseDeviceChangeService>https://login.microsoftonline.com/ppsecure/devicechangecredential.srf</cfg:EnterpriseDeviceChangeService><cfg:EnterpriseDeviceAddService>https://login.microsoftonline.com/ppsecure/deviceaddmsacredential.srf</cfg:EnterpriseDeviceAddService><cfg:EnterpriseDeviceRemoveService>https://login.microsoftonline.com/ppsecure/deviceremovecredential.srf</cfg:EnterpriseDeviceRemoveService><cfg:EnterpriseDeviceAssociateService>https://login.microsoftonline.com/ppsecure/DeviceAssociate.srf</cfg:EnterpriseDeviceAssociateService><cfg:EnterpriseDeviceDisassociateService>https://login.microsoftonline.com/ppsecure/DeviceDisassociate.srf</cfg:EnterpriseDeviceDisassociateService><cfg:EnterpriseDeviceQueryService>https://login.microsoftonline.com/ppsecure/DeviceQuery.srf</cfg:EnterpriseDeviceQueryService><cfg:EnterpriseDeviceUpdateService>https://login.microsoftonline.com/ppsecure/DeviceUpdate.srf</cfg:EnterpriseDeviceUpdateService><cfg:EnterpriseDeviceEnumerateService>https://login.microsoftonline.com/ppsecure/EnumerateDevices.srf</cfg:EnterpriseDeviceEnumerateService><cfg:EnterpriseResolveUserService>https://login.microsoftonline.com/ppsecure/ResolveUser.srf</cfg:EnterpriseResolveUserService><cfg:AllowGetProperty>EID</cfg:AllowGetProperty><cfg:RealmInfoService>https://login.live.com/getrealminfo.srf</cfg:RealmInfoService><cfg:RealmInfoService6_0>https://login.live.com/getuserrealm.srf</cfg:RealmInfoService6_0><cfg:URL_Retention>https://login.live.com/retention.srf</cfg:URL_Retention><cfg:URL_Registration>https://signup.live.com/signup.aspx</cfg:URL_Registration><cfg:URL_Privacy>http://go.microsoft.com/fwlink/p/?LinkId=253457</cfg:URL_Privacy><cfg:GetUserKeyDataService>https://login.live.com/ppsecure/GetUserKeyData.srf</cfg:GetUserKeyDataService><cfg:CPAddUserSignIn>https://login.live.com/ppsecure/InlineLogin.srf?id=80502</cfg:CPAddUserSignIn><cfg:CPAddUserSignUp>https://account.live.com/InlineSignup.aspx?iww=1&amp;id=80502</cfg:CPAddUserSignUp><cfg:CPAddUserIfExists>https://login.live.com/IfExists.srf?uiflavor=4&amp;id=80502</cfg:CPAddUserIfExists><!-- Auth-up URLs and all 806XX site IDs are used for versions of connect flows that can handle receiving a DA token during connect. --><cfg:OOBESignInAuthUp>https://login.live.com/ppsecure/InlineLogin.srf?id=80600</cfg:OOBESignInAuthUp><cfg:OOBESignUpAuthUp>https://account.live.com/inlinesignup.aspx?iww=1&amp;id=80600</cfg:OOBESignUpAuthUp><cfg:OOBEIfExistsAuthUp>https://login.live.com/IfExists.srf?uiflavor=4&amp;id=80600</cfg:OOBEIfExistsAuthUp><cfg:OOBEConnect>https://login.live.com/ppsecure/InlineConnect.srf?id=80600</cfg:OOBEConnect><cfg:CPSignInAuthUp>https://login.live.com/ppsecure/InlineLogin.srf?id=80601</cfg:CPSignInAuthUp><cfg:CPSignUpAuthUp>https://account.live.com/inlinesignup.aspx?iww=1&amp;id=80601</cfg:CPSignUpAuthUp><cfg:CPIfExistsAuthUp>https://login.live.com/IfExists.srf?uiflavor=4&amp;id=80601</cfg:CPIfExistsAuthUp><cfg:CPChangePwdAuthUp>https://account.live.com/Wizard/Password/Change?id=80601</cfg:CPChangePwdAuthUp><cfg:CPChangePwdOnline>https://login.live.com/ppsecure/InlinePOPAuth.srf?id=80601&amp;fid=cp</cfg:CPChangePwdOnline><cfg:CPConnect>https://login.live.com/ppsecure/InlineConnect.srf?id=80601</cfg:CPConnect><cfg:OOBEUpgradeSignIn>https://login.live.com/ppsecure/InlineLogin.srf?id=80603</cfg:OOBEUpgradeSignIn><cfg:OOBEUpgradeSignUp>https://account.live.com/inlinesignup.aspx?iww=1&amp;id=80603</cfg:OOBEUpgradeSignUp><cfg:OOBEUpgradeConnect>https://login.live.com/ppsecure/InlineConnect.srf?id=80603</cfg:OOBEUpgradeConnect><cfg:CompleteAccountSignIn>https://login.live.com/ppsecure/InlineLogin.srf?id=80604</cfg:CompleteAccountSignIn><cfg:CompleteAccountSignUp>https://account.live.com/inlinesignup.aspx?iww=1&amp;id=80604</cfg:CompleteAccountSignUp><cfg:CompleteAccountConnect>https://login.live.com/ppsecure/InlineConnect.srf?id=80604</cfg:CompleteAccountConnect><cfg:CXHSignInUpsell>https://login.live.com/ppsecure/InlineLogin.srf?id=80604</cfg:CXHSignInUpsell><cfg:NthUserSignIn>https://login.live.com/ppsecure/InlineLogin.srf?id=80605</cfg:NthUserSignIn><cfg:NthUserSignUp>https://account.live.com/inlinesignup.aspx?iww=1&amp;id=80605</cfg:NthUserSignUp><cfg:NthUserConnect>https://login.live.com/ppsecure/InlinePOPAuth.srf?id=80605</cfg:NthUserConnect><cfg:CXHSignIn>https://login.live.com/ppsecure/InlineLogin.srf?id=80606</cfg:CXHSignIn><cfg:CXHTransientSignIn>https://login.live.com/ppsecure/InlineLogin.srf?id=80607</cfg:CXHTransientSignIn><cfg:CXHReAuth>https://login.live.com/ppsecure/InlineLogin.srf?id=80608</cfg:CXHReAuth><cfg:CXHNGCUpsell>https://account.live.com/msangcwam</cfg:CXHNGCUpsell><cfg:URL_AccountSettings>https://account.microsoft.com/?ref=settings</cfg:URL_AccountSettings><cfg:URL_ProofManage>http://go.microsoft.com/fwlink/?LinkId=248215</cfg:URL_ProofManage><cfg:InlineClientAuth>https://login.live.com/ppsecure/InlineClientAuth.srf</cfg:InlineClientAuth><cfg:InlineClientAuthEnd>https://login.live.com/ppsecure/InlineDesktop.srf</cfg:InlineClientAuthEnd><cfg:ManageApprover>https://login.live.com/ManageApprover.srf</cfg:ManageApprover><cfg:ListSessions>https://login.live.com/ListSessions.srf</cfg:ListSessions><cfg:ApproveSession>https://login.live.com/ApproveSession.srf</cfg:ApproveSession><cfg:ManageLoginKeys>https://login.live.com/ManageLoginKeys.srf</cfg:ManageLoginKeys><cfg:GetAppDataService>https://login.live.com/ppsecure/GetAppData.srf</cfg:GetAppDataService></cfg:ServiceURIs></cfg:Configuration></Signature>