<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007" xmlns:resource="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>AppsDiagnostic</ID>
    <Version>4.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>TS_Main.ps1</FileName>
      <ExtensionPoint>
      </ExtensionPoint>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_TemporaryProfile</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-1700</Name>
        <Description>@diagpackage.dll,-1701</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_TemporaryProfile</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-1702</Name>
            <Description>@diagpackage.dll,-1703</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_TemporaryProfile.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_UAC</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>uacConsent</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-19817</Name>
        <Description>
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_UAC</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-19819</Name>
            <Description>@diagpackage.dll,-19820</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>uacConsent</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_UAC.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>uacConsent</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_UAC.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_ConnectedAccount</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-19833</Name>
        <Description>
          <!-- <dcmRS:LocalizeResourceElement comment="This is a comment" index="19834">localized element</dcmRS:LocalizeResourceElement> -->
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_ConnectedAccount</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-19835</Name>
            <Description>
              <!-- <dcmRS:LocalizeResourceElement comment="This is a comment" index="19836">localized element</dcmRS:LocalizeResourceElement> -->
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_ConnectedAccount.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>RC_ConnectedAccount.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_TempInetFolder</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>userSID</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
          <Parameter>
            <Name>localProfilePath</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-21904</Name>
        <Description>
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_TempInetFolder</ID>
          <DisplayInformation>
            <Parameters>
            </Parameters>
            <Name>@diagpackage.dll,-21905</Name>
            <Description>
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>userSID</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>localProfilePath</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_TempInetFolder.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>userSID</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
            <Parameter>
              <Name>localProfilePath</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>RC_TempInetFolder.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_WSReset</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-21906</Name>
        <Description>
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_WSReset</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-21907</Name>
            <Description>
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_WSReset.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>DateProblemDetected</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_WSReset.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters>
              </Parameters>
              <Name>@diagpackage.dll,-19851</Name>
              <Description>
              </Description>
            </DisplayInformation>
            <Value>Y</Value>
            <ExtensionPoint/>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-119715</Name>
              <Description>
              </Description>
            </DisplayInformation>
            <Value>N</Value>
            <ExtensionPoint/>
          </Choice>
        </Choices>
        <ID>INT_ENABLEUAC</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-19825</Name>
          <Description>@diagpackage.dll,-19826</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <CommandLinks/>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>true</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters>
              </Parameters>
              <Name>@diagpackage.dll,-1121905</Name>
              <Description>

              </Description>
            </DisplayInformation>
            <Value>Y</Value>
            <ExtensionPoint/>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-13141</Name>
              <Description>
              </Description>
            </DisplayInformation>
            <Value>N</Value>
            <ExtensionPoint/>
          </Choice>
        </Choices>
        <ID>INT_MOVETIF</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-2221905</Name>
          <Description>@diagpackage.dll,-21914</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <CommandLinks/>
        </ExtensionPoint>
      </SingleResponseInteraction>
    </SingleResponseInteractions>
    <MultipleResponseInteractions/>
    <TextInteractions/>
    <PauseInteractions>
      <PauseInteraction>
        <ID>INT_RestartMachine</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-1119827</Name>
          <Description>@diagpackage.dll,-1119828</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>INT_TemporaryUserProfileLoaded</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-200</Name>
          <Description>@diagpackage.dll,-201</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
          <InteractionIcon>Error</InteractionIcon>
          <LinkText>@diagpackage.dll,-401</LinkText>
          <Link>https://aka.ms/tempuserprofile</Link>
        </ExtensionPoint>
      </PauseInteraction>
    </PauseInteractions>
    <LaunchUIInteractions>
      <LaunchUIInteraction>
        <Parameters/>
        <CommandLine>ms-settings:emailandaccounts</CommandLine>
        <ID>INT_MSA</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-88</Name>
          <Description>@diagpackage.dll,-89</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <ButtonText>@diagpackage.dll,-136</ButtonText>
          <LinkText>@diagpackage.dll,-17721</LinkText>
          <Link>https://aka.ms/ts_apps10</Link>
        </ExtensionPoint>
      </LaunchUIInteraction>
      <LaunchUIInteraction>
        <Parameters/>
        <CommandLine>ms-settings:appsfeatures</CommandLine>
        <ID>INT_LaunchAppsFeatures</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-90</Name>
          <Description>@diagpackage.dll,-91</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <ButtonText>@diagpackage.dll,-137</ButtonText>
        </ExtensionPoint>
      </LaunchUIInteraction>
    </LaunchUIInteractions>
  </Interactions>
  <ExtensionPoint>
      <Icon>@DiagPackage.dll,-1001</Icon>
      <HelpKeywords>@DiagPackage.dll,-10</HelpKeywords>
      <HelpKeywords>@DiagPackage.dll,-11</HelpKeywords>
      <HelpKeywords>@DiagPackage.dll,-12</HelpKeywords>
      <Feedback>
        <ContextId>334</ContextId>
      </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>