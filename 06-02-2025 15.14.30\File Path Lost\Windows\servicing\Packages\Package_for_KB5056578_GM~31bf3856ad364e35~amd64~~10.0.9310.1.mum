<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for KB5056578" displayName="default" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="http://support.microsoft.com/?kbid=5056578" creationTimeStamp="2025-03-20T18:29:56Z" lastUpdateTimeStamp="2025-03-20T18:29:56Z">
  <assemblyIdentity name="Package_for_KB5056578_GM" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5056578" applicabilityEvaluation="deep" releaseType="Update" restart="possible">
    <parent buildCompare="GE" revisionCompare="GE" integrate="separate" disposition="detect">
      <assemblyIdentity name="Package_for_KB5011048" version="10.0.1.8028" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="*******" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="5056578-153_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_1_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-154_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_2_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-155_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_3_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-156_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_4_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-157_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_5_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-158_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_6_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-159_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_7_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-160_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_8_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5056578-161_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_9_for_KB5056578" version="10.0.9310.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
  </package>
</assembly>
