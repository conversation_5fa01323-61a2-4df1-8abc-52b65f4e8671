<?xml version="1.0" encoding="utf-8"?>
<R Id="222100" V="7" DC="SM" EN="Office.NaturalLanguage.Proofing.ProofingEventsRuleGrammarEvent" ATT="71cc1046851042108843d90e5d3ef6c1-61e5de5c-238c-4de5-95de-3b40d20ea6e5-6899" DCa="PSU" xmlns="">
  <S>
    <UTS T="1" Id="a22tu" A="bkoh1 bn8xj" />
  </S>
  <C T="I32" I="0" O="true" N="GrammarEventUndoID">
    <S T="1" F="UndoID" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="GrammarEventCritiqueName">
    <S T="1" F="CritiqueName" M="Ignore" />
  </C>
  <C T="W" I="2" O="true" N="GrammarEventCultureTag">
    <S T="1" F="CultureTag" M="Ignore" />
  </C>
  <C T="W" I="3" O="false" N="GrammarEventEventName">
    <S T="1" F="EventName" />
  </C>
  <C T="I32" I="4" O="true" N="GrammarEventIndexSelectedSuggestion">
    <S T="1" F="IndexSelectedSuggestion" M="Ignore" />
  </C>
  <C T="B" I="5" O="true" N="GrammarEventIsFromApplicationUndo">
    <S T="1" F="IsFromApplicationUndo" M="Ignore" />
  </C>
  <C T="U32" I="6" O="true" N="GrammarEventEndPoint">
    <S T="1" F="EndPoint" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="GrammarEventWritingStyle">
    <S T="1" F="WritingStyle" M="Ignore" />
  </C>
  <C T="I32" I="8" O="true" N="GrammarEventCountSuggestions">
    <S T="1" F="CountSuggestions" M="Ignore" />
  </C>
  <T>
    <S T="1" />
  </T>
</R>
