0 9 / 0 9 / 2 0 2 4   1 5 : 4 7 : 5 7 . 0 2 2   F S M a n a g e r I m p l :   E x p e c t e d   H a s h   o f   f i l e   " J o k e Y a k L o g : \ A m a z e P e w T w o \ P e n F o x R a g \ M u c h G a d S e a \ F o x K i d T a x \ P e w F o r t h F i s h \ W o w P i e F a r . j s o n " ,   H a s h = u S C 8 D o 6 W y + Y Y v 2 B v p 3 F 5 q Y q t V C S f f y 9 a U K R C Z g h I 6 f c = ,   I s S h a w 2 5 6 = t r u e 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 7 : 5 7 . 0 2 2   F S M a n a g e r I m p l :   A c t u a l   H a s h   o f   f i l e   " J o k e Y a k L o g : \ A m a z e P e w T w o \ P e n F o x R a g \ M u c h G a d S e a \ F o x K i d T a x \ P e w F o r t h F i s h \ W o w P i e F a r . j s o n " ,   h r = 0 x 0 0 0 0 0 0 0 0 ,   H a s h = u S C 8 D o 6 W y + Y Y v 2 B v p 3 F 5 q Y q t V C S f f y 9 a U K R C Z g h I 6 f c = 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 7 : 5 7 . 0 2 2   F S M a n a g e r I m p l :   R e s u l t   o f   h a s h   c o m p a r i s o n   =   0 x 0 0 0 0 0 0 0 0 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   u s e W i n H t t p :   f a l s e 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   B e g i n n i n g   A s y n c   D o w n l o a d ,   i d = [ N u c l e u s U p d a t e R i n g C o n f i g J S O N ] ,   h i g h P r i = [ t r u e ] ,   U r i = [ h t t p s : / / g . l i v e . c o m / o d c l i e n t s e t t i n g s / P r o d V 2 ? O n e D r i v e U p d a t e = 5 8 d 5 d 0 a 4 4 e f 0 8 e 2 3 7 5 5 2 d 3 8 f 3 7 6 ] 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   B a c k g r o u n d   t h r e a d   s t a r t e d 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   D o w n l o a d   a t t e m p t   # 1   ( m a x   1 0 ) 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   B e g i n D o w n l o a d A t t e m p t :   u s i n g   B I T S 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   C h k :   ! E R R O R !   ( 0 x 8 0 0 0 4 0 0 5 )   ( w e b c l i e n t . c p p : 1 2 6 8 )   E R R O R :   " "   f a i l e d   w i t h   0 x 8 0 0 0 4 0 0 5   i n   .   
 
 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   B I T S   t e m p o r a r y   f i l e = [ J o k e Y a k L o g : \ F a r G o o d I c y \ O w l A w e P i e \ A w e U r n A w e . t m p ] ,   f o r e g r o u n d   d o w n l o a d = t r u e 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 2 . 0 3 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   W a i t F o r D o w n l o a d A t t e m p t ( ) 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 2 8 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   D o w n l o a d   o f   ' N u c l e u s U p d a t e R i n g C o n f i g J S O N '   f i n i s h e d   s u c c e s s f u l l y . 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 2 8 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   W a i t F o r M u l t i p l e O b j e c t s ( )   T r y C o m p l e t e d   w a s   s i g n a l e d . 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 2 8 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   D o w n l o a d   a t t e m p t   # 0   r e s u l t   h r = 0 x 0 0 0 0 0 0 0 0 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 2 8 8   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   B a c k g r o u n d   t h r e a d   e x i t i n g ,   h r = 0 x 0 0 0 0 0 0 0 0 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 3 0 3   W e b C l i e n t :   W e b C l i e n t ( 0 0 0 0 0 1 8 0 D 5 4 F 7 F 8 0 ) :   d e s t r o y e d 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 3 0 3   F S M a n a g e r I m p l :   E x p e c t e d   H a s h   o f   f i l e   " J o k e Y a k L o g : \ A m a z e P e w T w o \ P e n F o x R a g \ M u c h G a d S e a \ F o x K i d T a x \ P e w F o r t h F i s h \ W o w P i e F a r . j s o n " ,   H a s h = l m + W l 2 F U A 8 c 7 5 D 4 l K y j X + S P p p a E b c / A s 9 O h h v H O S a 7 I = ,   I s S h a w 2 5 6 = t r u e 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 3 0 3   F S M a n a g e r I m p l :   A c t u a l   H a s h   o f   f i l e   " J o k e Y a k L o g : \ A m a z e P e w T w o \ P e n F o x R a g \ M u c h G a d S e a \ F o x K i d T a x \ P e w F o r t h F i s h \ W o w P i e F a r . j s o n " ,   h r = 0 x 0 0 0 0 0 0 0 0 ,   H a s h = l m + W l 2 F U A 8 c 7 5 D 4 l K y j X + S P p p a E b c / A s 9 O h h v H O S a 7 I = 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 3 0 3   F S M a n a g e r I m p l :   R e s u l t   o f   h a s h   c o m p a r i s o n   =   0 x 0 0 0 0 0 0 0 0 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 3 0 3   F S M a n a g e r I m p l :   E x p e c t e d   H a s h   o f   f i l e   " J o k e Y a k L o g : \ A m a z e P e w T w o \ P e n F o x R a g \ M u c h G a d S e a \ F o x K i d T a x \ P e w F o r t h F i s h \ W o w P i e F a r . j s o n " ,   H a s h = l m + W l 2 F U A 8 c 7 5 D 4 l K y j X + S P p p a E b c / A s 9 O h h v H O S a 7 I = ,   I s S h a w 2 5 6 = t r u e 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 3 0 3   F S M a n a g e r I m p l :   A c t u a l   H a s h   o f   f i l e   " J o k e Y a k L o g : \ A m a z e P e w T w o \ P e n F o x R a g \ M u c h G a d S e a \ F o x K i d T a x \ P e w F o r t h F i s h \ W o w P i e F a r . j s o n " ,   h r = 0 x 0 0 0 0 0 0 0 0 ,   H a s h = l m + W l 2 F U A 8 c 7 5 D 4 l K y j X + S P p p a E b c / A s 9 O h h v H O S a 7 I = 
 
 0 9 / 0 9 / 2 0 2 4   1 5 : 4 8 : 0 3 . 3 0 3   F S M a n a g e r I m p l :   R e s u l t   o f   h a s h   c o m p a r i s o n   =   0 x 0 0 0 0 0 0 0 0 
 
 