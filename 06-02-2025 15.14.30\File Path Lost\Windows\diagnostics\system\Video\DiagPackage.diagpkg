<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007" xmlns:resource="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>VideoPlaybackDiagnostic</ID>
    <Version>3.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>TS_Main.ps1</FileName>
      <ExtensionPoint>
      </ExtensionPoint>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
      <Rootcause>
      <ID>RC_ProtectedAudioDisabled</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-20800</Name>
        <Description>
          <!-- <dcmRS:LocalizeResourceElement comment="Name" index="20804">Localized Name</dcmRS:LocalizeResourceElement>-->
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RES_EnableProtectedAudio</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-20801</Name>
            <Description>
              <!--<dcmRS:LocalizeResourceElement comment="Description" index="20806">Localized Description</dcmRS:LocalizeResourceElement>-->
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_aud_reg_settings.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>VF_aud_reg_settings.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_UnsignedVideoDriver</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>DName</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
          <Parameter>
            <Name>DVersion</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-20802</Name>
        <Description>
          <!--<dcmRS:LocalizeResourceElement comment="Name" index="20822">Localized Name</dcmRS:LocalizeResourceElement>-->
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RES_UpdateUnsignedDriver</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>DName</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>DVersion</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-20803</Name>
            <Description>
              <!--<dcmRS:LocalizeResourceElement comment="Description" index="20822">Localized Description</dcmRS:LocalizeResourceElement> -->
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>DName</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>DVersion</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_viddrv_unsigned.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <!--<Script>
        <Parameters />
        <ProcessArchitecture>Any</ProcessArchitecture>
        <RequiresElevation>false</RequiresElevation>
        <RequiresInteractivity>true</RequiresInteractivity>
        <FileName>VF_viddrv_unsigned.ps1</FileName>
        <ExtensionPoint/>
      </Script>
      <ExtensionPoint/>
    </Verifier>-->
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_genericfailure</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>HRESULT</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-20804</Name>
        <Description>
          <!--<dcmRS:LocalizeResourceElement comment="Name" index="20816">Localized Name</dcmRS:LocalizeResourceElement>-->
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RES_UpdateMSFTBasicDriver</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>HRESULT</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-20805</Name>
            <Description>
              <!--<dcmRS:LocalizeResourceElement comment="Description" index="20818">Localized Description</dcmRS:LocalizeResourceElement> -->
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>HRESULT</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_viddrv_genericfailure.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <!--<Script>
        <Parameters />
        <ProcessArchitecture>Any</ProcessArchitecture>
        <RequiresElevation>false</RequiresElevation>
        <RequiresInteractivity>true</RequiresInteractivity>
        <FileName>VF_viddrv_msvideo.ps1</FileName>
        <ExtensionPoint/>
      </Script>
      <ExtensionPoint/>
    </Verifier>-->
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_MSFTBasicVideoDriver</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>DName</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
          <Parameter>
            <Name>DVersion</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-20806</Name>
        <Description>
          <!--<dcmRS:LocalizeResourceElement comment="Name" index="20816">Localized Name</dcmRS:LocalizeResourceElement>-->
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RES_UpdateMSFTBasicDriver</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>DName</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>DVersion</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-20807</Name>
            <Description>
              <!--<dcmRS:LocalizeResourceElement comment="Description" index="20818">Localized Description</dcmRS:LocalizeResourceElement> -->
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>DName</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>DVersion</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_viddrv_msvideo.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <!--<Script>
        <Parameters />
        <ProcessArchitecture>Any</ProcessArchitecture>
        <RequiresElevation>false</RequiresElevation>
        <RequiresInteractivity>true</RequiresInteractivity>
        <FileName>VF_viddrv_msvideo.ps1</FileName>
        <ExtensionPoint/>
      </Script>
      <ExtensionPoint/>
    </Verifier>-->
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
        <ID>RC_DriverBlocklist</ID>
        <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>DName</Name>
                    <DefaultValue>-</DefaultValue>
                </Parameter>
                <Parameter>
                    <Name>DVersion</Name>
                    <DefaultValue>-</DefaultValue>
                </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-20808</Name>
            <Description>
                <!--<dcmRS:LocalizeResourceElement comment="Name" index="20816">Localized Name</dcmRS:LocalizeResourceElement>-->
            </Description>
        </DisplayInformation>
        <Resolvers>
            <Resolver>
                <ID>RES_UpdateDriver</ID>
                <DisplayInformation>
                    <Parameters>
                        <Parameter>
                            <Name>DName</Name>
                            <DefaultValue>-</DefaultValue>
                        </Parameter>
                        <Parameter>
                            <Name>DVersion</Name>
                            <DefaultValue>-</DefaultValue>
                        </Parameter>
                    </Parameters>
                    <Name>@diagpackage.dll,-20809</Name>
                    <Description>@diagpackage.dll,-20810</Description>
                </DisplayInformation>
                <RequiresConsent>true</RequiresConsent>
                <Script>
                    <Parameters>
                        <Parameter>
                            <Name>DName</Name>
                            <DefaultValue>-</DefaultValue>
                        </Parameter>
                        <Parameter>
                            <Name>DVersion</Name>
                            <DefaultValue>-</DefaultValue>
                        </Parameter>
                    </Parameters>
                    <ProcessArchitecture>Any</ProcessArchitecture>
                    <RequiresElevation>true</RequiresElevation>
                    <RequiresInteractivity>true</RequiresInteractivity>
                    <FileName>RS_viddrv_driverblocklist.ps1</FileName>
                    <ExtensionPoint/>
                </Script>
                <ExtensionPoint/>
            </Resolver>
        </Resolvers>
        <Verifier>
            <Script>
              <Parameters/>
              <ProcessArchitecture>Any</ProcessArchitecture>
              <RequiresElevation>true</RequiresElevation>
              <RequiresInteractivity>true</RequiresInteractivity>
              <FileName>VF_viddrv_driverblocklist.ps1</FileName>
              <ExtensionPoint/>
            </Script>
            <ExtensionPoint/>
          </Verifier>
        <ContextParameters/>
        <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_HWDRMNotSupported</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-20811</Name>
        <Description>
          <!--<dcmRS:LocalizeResourceElement comment="Name" index="20816">Localized Name</dcmRS:LocalizeResourceElement>-->
        </Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RES_HWDRM</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-20812</Name>
            <Description>
              <!--<dcmRS:LocalizeResourceElement comment="Description" index="20818">Localized Description</dcmRS:LocalizeResourceElement> -->
            </Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_viddrv_hwdrmcheck.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
          <ID>RC_HevcCodecNotInstalled</ID>
          <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-20813</Name>
              <Description>
                  <!--<dcmRS:LocalizeResourceElement comment="Name" index="20816">Localized Name</dcmRS:LocalizeResourceElement>-->
              </Description>
          </DisplayInformation>
          <Resolvers>
              <Resolver>
                  <ID>RES_InstallHevcCodec</ID>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-20814</Name>
                      <Description>
                          <!--<dcmRS:LocalizeResourceElement comment="Description" index="20818">Localized Description</dcmRS:LocalizeResourceElement> -->
                      </Description>
                  </DisplayInformation>
                  <RequiresConsent>false</RequiresConsent>
                  <Script>
                      <Parameters/>
                      <ProcessArchitecture>Any</ProcessArchitecture>
                      <RequiresElevation>true</RequiresElevation>
                      <RequiresInteractivity>true</RequiresInteractivity>
                      <FileName>RS_viddrv_hevccodeccheck.ps1</FileName>
                      <ExtensionPoint/>
                  </Script>
                  <ExtensionPoint/>
              </Resolver>
          </Resolvers>
          <Verifier/>
          <ContextParameters/>
          <ExtensionPoint/>
      </Rootcause>
      <Rootcause>
          <ID>RC_LowResolution</ID>
          <DisplayInformation>
              <Parameters>
                  <Parameter>
                      <Name>DisplayInfo</Name>
                      <DefaultValue>-</DefaultValue>
                  </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-20815</Name>
              <Description>
                  <!--<dcmRS:LocalizeResourceElement comment="Name" index="20816">Localized Name</dcmRS:LocalizeResourceElement>-->
              </Description>
          </DisplayInformation>
          <Resolvers>
              <Resolver>
                  <ID>RES_LowResolution</ID>
                  <DisplayInformation>
                      <Parameters>
                          <Parameter>
                              <Name>DisplayInfo</Name>
                              <DefaultValue>-</DefaultValue>
                          </Parameter>
                      </Parameters>
                      <Name>@diagpackage.dll,-20816</Name>
                      <Description>
                          <!--<dcmRS:LocalizeResourceElement comment="Description" index="20818">Localized Description</dcmRS:LocalizeResourceElement> -->
                      </Description>
                  </DisplayInformation>
                  <RequiresConsent>false</RequiresConsent>
                  <Script>
                      <Parameters>
                          <Parameter>
                              <Name>DisplayInfo</Name>
                              <DefaultValue>-</DefaultValue>
                          </Parameter>
                      </Parameters>
                      <ProcessArchitecture>Any</ProcessArchitecture>
                      <RequiresElevation>true</RequiresElevation>
                      <RequiresInteractivity>true</RequiresInteractivity>
                      <FileName>RS_viddrv_displaytopology.ps1</FileName>
                      <ExtensionPoint/>
                  </Script>
                  <ExtensionPoint/>
              </Resolver>
          </Resolvers>
          <Verifier>
            <Script>
              <Parameters/>
              <ProcessArchitecture>Any</ProcessArchitecture>
              <RequiresElevation>true</RequiresElevation>
              <RequiresInteractivity>true</RequiresInteractivity>
              <FileName>RC_viddrv_displaytopology.ps1</FileName>
              <ExtensionPoint/>
            </Script>
            <ExtensionPoint/>
          </Verifier>
          <ContextParameters/>
          <ExtensionPoint/>
      </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions>
        <SingleResponseInteraction>
            <AllowDynamicResponses>false</AllowDynamicResponses>
            <Choices>
                <Choice>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-21000</Name>
                        <Description>@diagpackage.dll,-21001</Description>
                    </DisplayInformation>
                    <Value>videoplayback</Value>
                    <ExtensionPoint>
                    </ExtensionPoint>
                </Choice>                       
                <Choice>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-21002</Name>
                        <Description>@diagpackage.dll,-21003</Description>
                    </DisplayInformation>
                    <Value>display</Value>
                    <ExtensionPoint>
                    </ExtensionPoint>
                </Choice>
                <Choice>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-21004</Name>
                        <Description/>
                    </DisplayInformation>
                    <Value>0</Value>
                    <ExtensionPoint>
                    </ExtensionPoint>
                </Choice>
            </Choices>
            <ID>IT_VideoSettings</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-21005</Name>
                <Description>
                </Description>
            </DisplayInformation>
            <ContextParameters/>
            <ExtensionPoint>
                <RTFDescription>@diagpackage.dll,-21006</RTFDescription>
                <InteractionIcon>info</InteractionIcon>
                <CommandLinks/>
            </ExtensionPoint>
        </SingleResponseInteraction>
    </SingleResponseInteractions>
    <MultipleResponseInteractions>
    </MultipleResponseInteractions>
    <TextInteractions>
    </TextInteractions>
    <PauseInteractions>
      <PauseInteraction>
        <ID>INT_unsigned</ID>
        <DisplayInformation>
          <Parameters>
            <Parameter>
              <Name>DriverName</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>DriverVersion</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <Name>@diagpackage.dll,-21007</Name>
          <Description>@diagpackage.dll,-21008</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
          <Link>http://support.microsoft.com/gp/oemcontact</Link>
          <LinkText>@diagpackage.dll,-21009</LinkText>
          <!--<LinkFlushWithText/>-->
          <NoCache/>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>INT_msvideo</ID>
        <DisplayInformation>
          <Parameters>
            <!--<Parameter>
             <Name>DriverName</Name>
             <DefaultValue/>
           </Parameter>
           <Parameter>
             <Name>DriverVersion</Name>
             <DefaultValue/>
           </Parameter>-->
          </Parameters>
          <Name>@diagpackage.dll,-21010</Name>
          <Description>@diagpackage.dll,-21011</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
          <Link>http://support.microsoft.com/gp/oemcontact</Link>
          <LinkText>@diagpackage.dll,-21012</LinkText>
          <!--<LinkFlushWithText/>-->
          <NoCache/>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>INT_driverblocklist</ID>
        <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>DriverName</Name>
                    <DefaultValue/>
                </Parameter>
                <Parameter>
                    <Name>DriverVersion</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-21013</Name>
            <Description>@diagpackage.dll,-21014</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
            <Link>http://support.microsoft.com/gp/oemcontact</Link>
            <LinkText>@diagpackage.dll,-21015</LinkText>
            <!--<LinkFlushWithText/>-->
            <NoCache/>
        </ExtensionPoint>
    </PauseInteraction>
      <PauseInteraction>
        <ID>INT_HevcCodecNotInstalled</ID>
        <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-21016</Name>
            <Description>@diagpackage.dll,-21017</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
            <Link>https://go.microsoft.com/fwlink/?linkid=874501</Link>
            <LinkText>@diagpackage.dll,-21018</LinkText>
            <!--<LinkFlushWithText/>-->
            <NoCache/>
        </ExtensionPoint>
    </PauseInteraction>
      <PauseInteraction>
        <ID>INT_HWDRMNotSupported</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-21019</Name>
          <Description>@diagpackage.dll,-21020</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint/>
      </PauseInteraction>
        <PauseInteraction>
            <ID>INT_genericfailure</ID>
            <DisplayInformation>
              <Parameters>
                <Parameter>
                 <Name>HRESULT</Name>
                 <DefaultValue/>
               </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-21021</Name>
              <Description>@diagpackage.dll,-21022</Description>
            </DisplayInformation>
            <ContextParameters>
            </ContextParameters>
            <ExtensionPoint>
              <Link>http://support.microsoft.com/gp/oemcontact</Link>
              <LinkText>@diagpackage.dll,-21023</LinkText>
              <!--<LinkFlushWithText/>-->
              <NoCache/>
            </ExtensionPoint>
         </PauseInteraction>
    </PauseInteractions> 
    <LaunchUIInteractions>
        <LaunchUIInteraction>
            <Parameters/>
            <CommandLine>ms-settings:display</CommandLine>
            <ID>INT_DisplayTopology</ID>
            <DisplayInformation>
                <Parameters>
                    <Parameter>
                        <Name>DisplayInfo</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <Name>@diagpackage.dll,-80</Name>
                <Description>@diagpackage.dll,-81</Description>
            </DisplayInformation>
            <ContextParameters/>
            <ExtensionPoint>
                <ButtonText>@diagpackage.dll,-82</ButtonText>
                <LinkText>@diagpackage.dll,-83</LinkText>
                <Link>https://support.microsoft.com/en-us/help/4026956/windows-10-change-screen-resolution</Link>
            </ExtensionPoint>
        </LaunchUIInteraction>
     </LaunchUIInteractions>
  </Interactions>
  <ExtensionPoint>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-10</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-20</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-30</HelpKeywords>
    <Feedback>
      <ContextId>534</ContextId>
    </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>