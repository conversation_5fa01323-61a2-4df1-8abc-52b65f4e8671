<?xml version="1.0" encoding="utf-8"?>
<PowerShellMetadata xmlns="http://schemas.microsoft.com/cmdlets-over-objects/2009/11">
    <Class ClassName="root\Microsoft\Windows\Defender\MSFT_MpPreference" ClassVersion="1.0">
        <Version>1.0</Version>
        <DefaultNoun>MpPreference</DefaultNoun>
        <InstanceCmdlets>
            <GetCmdletParameters DefaultCmdletParameterSet="DefaultSet">
            </GetCmdletParameters>
        </InstanceCmdlets>

        <StaticCmdlets>
            <Cmdlet>
                <CmdletMetadata Verb="Set" />
                <Method MethodName="Set">
                    <ReturnValue>
                        <Type PSType="System.Int32" />
                        <CmdletOutputMetadata>
                            <ErrorCode />
                        </CmdletOutputMetadata>
                    </ReturnValue>
                    <Parameters>
                        <Parameter ParameterName="ExclusionPath">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionExtension">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionProcess">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionIpAddress">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RealTimeScanDirection">
                            <Type PSType="MpPreference.ScanDirection" />
                            <CmdletParameterMetadata Aliases="rtsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Both</AllowedValue>
                                    <AllowedValue>Incoming</AllowedValue>
                                    <AllowedValue>Outcoming</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="QuarantinePurgeItemsAfterDelay">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="qpiad">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemediationScheduleDay">
                            <Type PSType="MpPreference.Day" />
                            <CmdletParameterMetadata Aliases="rsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Everyday</AllowedValue>
                                    <AllowedValue>Sunday</AllowedValue>
                                    <AllowedValue>Monday</AllowedValue>
                                    <AllowedValue>Tuesday</AllowedValue>
                                    <AllowedValue>Wednesday</AllowedValue>
                                    <AllowedValue>Thursday</AllowedValue>
                                    <AllowedValue>Friday</AllowedValue>
                                    <AllowedValue>Saturday</AllowedValue>
                                    <AllowedValue>Never</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemediationScheduleTime">
                            <Type PSType="System.DateTime" />
                            <CmdletParameterMetadata Aliases="rst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionConfiguredState">
                            <Type PSType="MpPreference.NetworkBehaviorProtectionConfiguredState" />
                            <CmdletParameterMetadata Aliases="repcs">
                                <ValidateSet>
                                    <AllowedValue>NotConfigured</AllowedValue>
                                    <AllowedValue>Block</AllowedValue>
                                    <AllowedValue>Audit</AllowedValue>
                                    <AllowedValue>Off</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionMaxBlockTime">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="repmbt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionAggressiveness">
                            <Type PSType="MpPreference.Aggressiveness" />
                            <CmdletParameterMetadata Aliases="repa">
                                <ValidateSet>
                                    <AllowedValue>Low</AllowedValue>
                                    <AllowedValue>Medium</AllowedValue>
                                    <AllowedValue>High</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata Aliases="repe">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionConfiguredState">
                            <Type PSType="MpPreference.NetworkBehaviorProtectionConfiguredState" />
                            <CmdletParameterMetadata Aliases="bfpcs">
                                <ValidateSet>
                                    <AllowedValue>NotConfigured</AllowedValue>
                                    <AllowedValue>Block</AllowedValue>
                                    <AllowedValue>Audit</AllowedValue>
                                    <AllowedValue>Off</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionMaxBlockTime">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="bfpmbt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionAggressiveness">
                            <Type PSType="MpPreference.Aggressiveness" />
                            <CmdletParameterMetadata Aliases="bfpa">
                                <ValidateSet>
                                    <AllowedValue>Low</AllowedValue>
                                    <AllowedValue>Medium</AllowedValue>
                                    <AllowedValue>High</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata Aliases="bfpe">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionLocalNetworkBlocking">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="bfplnb">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionSkipLearningPeriod">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="bfpslp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportingAdditionalActionTimeOut">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="raat">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportingCriticalFailureTimeOut">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="rcto">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportingNonCriticalTimeOut">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="rncto">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ServiceHealthReportInterval">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="shri">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportDynamicSignatureDroppedEvent">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="rdsde">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanAvgCPULoadFactor">
                            <Type PSType="System.Byte" />
                            <CmdletParameterMetadata Aliases="saclf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="CheckForSignaturesBeforeRunningScan">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="csbr">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanPurgeItemsAfterDelay">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="spiad">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanOnlyIfIdleEnabled">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="soiie">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanParameters">
                            <Type PSType="MpPreference.ScanType" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>QuickScan</AllowedValue>
                                    <AllowedValue>FullScan</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleDay">
                            <Type PSType="MpPreference.Day" />
                            <CmdletParameterMetadata Aliases="scsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Everyday</AllowedValue>
                                    <AllowedValue>Sunday</AllowedValue>
                                    <AllowedValue>Monday</AllowedValue>
                                    <AllowedValue>Tuesday</AllowedValue>
                                    <AllowedValue>Wednesday</AllowedValue>
                                    <AllowedValue>Thursday</AllowedValue>
                                    <AllowedValue>Friday</AllowedValue>
                                    <AllowedValue>Saturday</AllowedValue>
                                    <AllowedValue>Never</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleQuickScanTime">
                            <Type PSType="System.DateTime" />
                            <CmdletParameterMetadata Aliases="scsqst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleTime">
                            <Type PSType="System.DateTime" />
                            <CmdletParameterMetadata Aliases="scst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThrottleForScheduledScanOnly">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="tfsso">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureFirstAuGracePeriod">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="sigfagp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureAuGracePeriod">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="sigagp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureDefinitionUpdateFileSharesSources">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata Aliases="sigdufss">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureDisableUpdateOnStartupWithoutEngine">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="sigduoswo">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureFallbackOrder">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata Aliases="sfo">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SharedSignaturesPath">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata Aliases="ssp SecurityIntelligenceLocation ssl">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SharedSignaturesPathUpdateAtScheduledTimeOnly">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="sspsch SecurityIntelligenceLocationUpdateAtScheduledTimeOnly">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureScheduleDay">
                            <Type PSType="MpPreference.Day" />
                            <CmdletParameterMetadata Aliases="sigsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Everyday</AllowedValue>
                                    <AllowedValue>Sunday</AllowedValue>
                                    <AllowedValue>Monday</AllowedValue>
                                    <AllowedValue>Tuesday</AllowedValue>
                                    <AllowedValue>Wednesday</AllowedValue>
                                    <AllowedValue>Thursday</AllowedValue>
                                    <AllowedValue>Friday</AllowedValue>
                                    <AllowedValue>Saturday</AllowedValue>
                                    <AllowedValue>Never</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureScheduleTime">
                            <Type PSType="System.DateTime" />
                            <CmdletParameterMetadata Aliases="sigst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureUpdateCatchupInterval">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="siguci">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureUpdateInterval">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="sigui">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureBlobUpdateInterval">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="sigbui">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureBlobFileSharesSources">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata Aliases="sigbfs">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="MeteredConnectionUpdates">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="mcupd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowNetworkProtectionOnWinServer">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="anpws">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableDatagramProcessing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="ddtgp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableConvertWarnToBlock">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="ecwtb">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCpuThrottleOnIdleScans">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="MAPSReporting">
                            <Type PSType="MpPreference.MAPSReportingType" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Disabled</AllowedValue>
                                    <AllowedValue>Basic</AllowedValue>
                                    <AllowedValue>Advanced</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SubmitSamplesConsent">
                            <Type PSType="MpPreference.SubmitSamplesConsentType" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>AlwaysPrompt</AllowedValue>
                                    <AllowedValue>SendSafeSamples</AllowedValue>
                                    <AllowedValue>NeverSend</AllowedValue>
                                    <AllowedValue>SendAllSamples</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableAutoExclusions">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dae">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisablePrivacyMode">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dpm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RandomizeScheduleTaskTimes">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="rstt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SchedulerRandomizationTime">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="srt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableBehaviorMonitoring">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dbm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableIntrusionPreventionSystem">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dips">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableIOAVProtection">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dioavp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRealtimeMonitoring">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="drtm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableScriptScanning">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dscrptsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableArchiveScanning">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="darchsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCatchupFullScan">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dcfsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCatchupQuickScan">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dcqsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableEmailScanning">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="demsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRemovableDriveScanning">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="drdsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRestorePoint">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="drp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableScanningMappedNetworkDrivesForFullScan">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dsmndfsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableScanningNetworkFiles">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dsnf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ApplyDisableNetworkScanningToIOAV">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="adsnftioav">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="UILockdown">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThreatIDDefaultAction_Ids">
                            <Type PSType="int64[]" />
                            <CmdletParameterMetadata Aliases="tiddefaci">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThreatIDDefaultAction_Actions">
                            <Type PSType="MpPreference.ThreatAction[]" />
                            <CmdletParameterMetadata Aliases="tiddefaca">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="UnknownThreatDefaultAction">
                            <Type PSType="MpPreference.ThreatAction" />
                            <CmdletParameterMetadata Aliases="unktdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="LowThreatDefaultAction">
                            <Type PSType="MpPreference.ThreatAction" />
                            <CmdletParameterMetadata Aliases="ltdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ModerateThreatDefaultAction">
                            <Type PSType="MpPreference.ThreatAction" />
                            <CmdletParameterMetadata Aliases="mtdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="HighThreatDefaultAction">
                            <Type PSType="MpPreference.ThreatAction" />
                            <CmdletParameterMetadata Aliases="htdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SevereThreatDefaultAction">
                            <Type PSType="MpPreference.ThreatAction" />
                            <CmdletParameterMetadata Aliases="stdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="cim:operationOption:Force">
                            <Type PSType="System.Management.Automation.SwitchParameter" />
                            <CmdletParameterMetadata PSName="Force">
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableBlockAtFirstSeen">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dbaf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="PUAProtection">
                            <Type PSType="MpPreference.PUAProtectionType" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Disabled</AllowedValue>
                                    <AllowedValue>Enabled</AllowedValue>
                                    <AllowedValue>AuditMode</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="CloudBlockLevel">
                            <Type PSType="MpPreference.CloudBlockLevelType" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Default</AllowedValue>
                                    <AllowedValue>Moderate</AllowedValue>
                                    <AllowedValue>High</AllowedValue>
                                    <AllowedValue>HighPlus</AllowedValue>
                                    <AllowedValue>ZeroTolerance</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="CloudExtendedTimeout">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="cloudextimeout">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableNetworkProtection">
                            <Type PSType="MpPreference.ASRRuleActionType" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Disabled</AllowedValue>
                                    <AllowedValue>Enabled</AllowedValue>
                                    <AllowedValue>AuditMode</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableControlledFolderAccess">
                            <Type PSType="MpPreference.ControlledFolderAccessType" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Disabled</AllowedValue>
                                    <AllowedValue>Enabled</AllowedValue>
                                    <AllowedValue>AuditMode</AllowedValue>
                                    <AllowedValue>BlockDiskModificationOnly</AllowedValue>
                                    <AllowedValue>AuditDiskModificationOnly</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionOnlyExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ControlledFolderAccessAllowedApplications">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ControlledFolderAccessProtectedFolders">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_Ids">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_Actions">
                            <Type PSType="MpPreference.ASRRuleActionType[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableLowCpuPriority">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="elcp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableFileHashComputation">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="efhc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableFullScanOnBatteryPower">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="efsobp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ProxyPacUrl">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata Aliases="ppurl">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ProxyServer">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata Aliases="proxsrv">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ProxyBypass">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata Aliases="proxbps">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ForceUseProxyOnly">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="fupo">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableTlsParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dtlsp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableFtpParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dftpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableHttpParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dhttpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableDnsParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="ddnsp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableDnsOverTcpParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="ddnstcpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableSshParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dsshp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="PlatformUpdatesChannel">
                            <Type PSType="MpPreference.UpdatesChannelType" />
                            <CmdletParameterMetadata Aliases="puc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>NotConfigured</AllowedValue>
                                    <AllowedValue>Beta</AllowedValue>
                                    <AllowedValue>Preview</AllowedValue>
                                    <AllowedValue>Staged</AllowedValue>
                                    <AllowedValue>Broad</AllowedValue>
                                    <AllowedValue>Delayed</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EngineUpdatesChannel">
                            <Type PSType="MpPreference.UpdatesChannelType" />
                            <CmdletParameterMetadata Aliases="euc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>NotConfigured</AllowedValue>
                                    <AllowedValue>Beta</AllowedValue>
                                    <AllowedValue>Preview</AllowedValue>
                                    <AllowedValue>Staged</AllowedValue>
                                    <AllowedValue>Broad</AllowedValue>
                                    <AllowedValue>Delayed</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DefinitionUpdatesChannel">
                            <Type PSType="MpPreference.DefinitionUpdatesChannelType" />
                            <CmdletParameterMetadata Aliases="duc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>NotConfigured</AllowedValue>
                                    <AllowedValue>Staged</AllowedValue>
                                    <AllowedValue>Broad</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableGradualRelease">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dgr">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowNetworkProtectionDownLevel">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="anpdl">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowDatagramProcessingOnWinServer">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="adpows">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableDnsSinkhole">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="ednss">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableInboundConnectionFiltering">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dicf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRdpParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="drdpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableNetworkProtectionPerfTelemetry">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dnppt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="TrustLabelProtectionStatus">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="tlps">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowSwitchToAsyncInspection">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="astai">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleOffset">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="scso">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableTDTFeature">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dtdtf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableTamperProtection">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dtp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableSmtpParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dsmtpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableQuicParsing">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dquicp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="NetworkProtectionReputationMode">
                            <Type PSType="System.UInt32" />
                            <CmdletParameterMetadata Aliases="nprepmode">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableUdpSegmentationOffload">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="euso">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableUdpReceiveOffload">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="euro">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="IntelTDTEnabled">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="itdte">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_RuleSpecificExclusions_Id">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_RuleSpecificExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="OobeEnableRtpAndSigUpdate">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="oobers">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="PerformanceModeStatus">
                            <Type PSType="MpPreference.PerformanceModeStatusType" />
                            <CmdletParameterMetadata Aliases="pms">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Enabled</AllowedValue>
                                    <AllowedValue>Disabled</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="QuickScanIncludeExclusions">
                            <Type PSType="MpPreference.QuickScanIncludeExclusionsType" />
                            <CmdletParameterMetadata Aliases="qsie">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                                <ValidateSet>
                                    <AllowedValue>Disabled</AllowedValue>
                                    <AllowedValue>ScanRtpExclusions</AllowedValue>
                                </ValidateSet>
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCacheMaintenance">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dcm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoveScanningThreadPoolCap">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="rstpc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCoreServiceECSIntegration">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dcsei">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCoreServiceTelemetry">
                            <Type PSType="System.Boolean" />
                            <CmdletParameterMetadata Aliases="dcst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                    </Parameters>
                </Method>
            </Cmdlet>
            <Cmdlet>
                <CmdletMetadata Verb="Add" />
                <Method MethodName="Add">
                    <ReturnValue>
                        <Type PSType="System.Int32" />
                        <CmdletOutputMetadata>
                            <ErrorCode />
                        </CmdletOutputMetadata>
                    </ReturnValue>
                    <Parameters>
                        <Parameter ParameterName="ExclusionPath">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionExtension">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionProcess">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionIpAddress">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThreatIDDefaultAction_Ids">
                            <Type PSType="int64[]" />
                            <CmdletParameterMetadata Aliases="tiddefaci">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThreatIDDefaultAction_Actions">
                            <Type PSType="MpPreference.ThreatAction[]" />
                            <CmdletParameterMetadata Aliases="tiddefaca">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionOnlyExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ControlledFolderAccessAllowedApplications">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ControlledFolderAccessProtectedFolders">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_Ids">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_Actions">
                            <Type PSType="MpPreference.ASRRuleActionType[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_RuleSpecificExclusions_Id">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_RuleSpecificExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata Aliases="repe">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata Aliases="bfpe">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="cim:operationOption:Force">
                            <Type PSType="System.Management.Automation.SwitchParameter" />
                            <CmdletParameterMetadata PSName="Force">
                            </CmdletParameterMetadata>
                        </Parameter>
                    </Parameters>
                </Method>
             </Cmdlet>
            <Cmdlet>
                <CmdletMetadata Verb="Remove" />
                <Method MethodName="Remove" >
                    <ReturnValue>
                        <Type PSType="System.Int32" />
                        <CmdletOutputMetadata>
                            <ErrorCode />
                        </CmdletOutputMetadata>
                    </ReturnValue>
                    <Parameters>
                        <Parameter ParameterName="ExclusionPath">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionExtension">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionProcess">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ExclusionIpAddress">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RealTimeScanDirection">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rtsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="QuarantinePurgeItemsAfterDelay">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="qpiad">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemediationScheduleDay">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemediationScheduleTime">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionConfiguredState">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="repcs">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionMaxBlockTime">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="repmbt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionAggressiveness">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="repa">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoteEncryptionProtectionExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata Aliases="repe">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionConfiguredState">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="bfpcs">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionMaxBlockTime">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="bfpmbt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionAggressiveness">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="bfpa">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata Aliases="bfpe">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionLocalNetworkBlocking">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="bfplnb">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="BruteForceProtectionSkipLearningPeriod">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="bfpslp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportingAdditionalActionTimeOut">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="raat">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportingCriticalFailureTimeOut">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rcto">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportingNonCriticalTimeOut">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rncto">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ServiceHealthReportInterval">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="shri">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ReportDynamicSignatureDroppedEvent">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rdsde">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanAvgCPULoadFactor">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="saclf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="CheckForSignaturesBeforeRunningScan">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="csbr">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanPurgeItemsAfterDelay">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="spiad">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanOnlyIfIdleEnabled">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="soiie">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanParameters">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleDay">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="scsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleQuickScanTime">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="scsqst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleTime">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="scst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThrottleForScheduledScanOnly">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="tfsso">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureFirstAuGracePeriod">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigfagp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureAuGracePeriod">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigagp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureDefinitionUpdateFileSharesSources">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigdufss">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureDisableUpdateOnStartupWithoutEngine">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigduoswo">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureFallbackOrder">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sfo">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SharedSignaturesPath">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ssp SecurityIntelligenceLocation ssl">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SharedSignaturesPathUpdateAtScheduledTimeOnly">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sspsch SecurityIntelligenceLocationUpdateAtScheduledTimeOnly">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureScheduleDay">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigsd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureScheduleTime">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureUpdateCatchupInterval">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="siguci">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureUpdateInterval">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigui">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureBlobUpdateInterval">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigbui">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SignatureBlobFileSharesSources">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="sigbfs">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="MeteredConnectionUpdates">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="mcupd">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowNetworkProtectionOnWinServer">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="anpws">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableDatagramProcessing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ddtgp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableConvertWarnToBlock">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ecwtb">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCpuThrottleOnIdleScans">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="MAPSReporting">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SubmitSamplesConsent">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableAutoExclusions">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dae">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisablePrivacyMode">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dpm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RandomizeScheduleTaskTimes">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rstt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SchedulerRandomizationTime">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="srt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableBehaviorMonitoring">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dbm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableIntrusionPreventionSystem">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dips">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableIOAVProtection">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dioavp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRealtimeMonitoring">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="drtm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableScriptScanning">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dscrptsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableArchiveScanning">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="darchsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCatchupFullScan">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dcfsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCatchupQuickScan">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dcqsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableEmailScanning">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="demsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRemovableDriveScanning">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="drdsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRestorePoint">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="drp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableScanningMappedNetworkDrivesForFullScan">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dsmndfsc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableScanningNetworkFiles">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dsnf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ApplyDisableNetworkScanningToIOAV">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="adsnftioav">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="UILockdown">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThreatIDDefaultAction_Ids">
                            <Type PSType="int64[]" />
                            <CmdletParameterMetadata Aliases="tiddefaci">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ThreatIDDefaultAction_Actions">
                            <Type PSType="MpPreference.ThreatAction[]" />
                            <CmdletParameterMetadata Aliases="tiddefaca">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="UnknownThreatDefaultAction">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="unktdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="LowThreatDefaultAction">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ltdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ModerateThreatDefaultAction">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="mtdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="HighThreatDefaultAction">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="htdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="SevereThreatDefaultAction">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="stdefac">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableBlockAtFirstSeen">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dbaf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="PUAProtection">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="CloudBlockLevel">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="CloudExtendedTimeout">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="cloudextimeout">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableNetworkProtection">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableControlledFolderAccess">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionOnlyExclusions">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ControlledFolderAccessAllowedApplications">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ControlledFolderAccessProtectedFolders">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_Ids">
                            <Type PSType="System.String[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_Actions">
                            <Type PSType="MpPreference.ASRRuleActionType[]" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableLowCpuPriority">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="elcp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableFileHashComputation">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="efhc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableFullScanOnBatteryPower">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="efsobp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ProxyPacUrl">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ppurl">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ProxyServer">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="proxsrv">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ProxyBypass">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="proxbps">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ForceUseProxyOnly">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="fupo">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableTlsParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dtlsp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableFtpParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dftpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableHttpParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dhttpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableDnsParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ddnsp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableDnsOverTcpParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ddnstcpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableSshParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dsshp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="PlatformUpdatesChannel">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="puc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EngineUpdatesChannel">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="euc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DefinitionUpdatesChannel">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="duc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableGradualRelease">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dgr">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowNetworkProtectionDownLevel">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="anpdl">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowDatagramProcessingOnWinServer">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="adpows">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableDnsSinkhole">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="ednss">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableInboundConnectionFiltering">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dicf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableRdpParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="drdpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableNetworkProtectionPerfTelemetry">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dnppt">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="TrustLabelProtectionStatus">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="tlps">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AllowSwitchToAsyncInspection">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="astai">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="ScanScheduleOffset">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="scso">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableTDTFeature">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dtdtf">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableSmtpParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dsmtpp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableQuicParsing">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dquicp">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="NetworkProtectionReputationMode">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="nprepmode">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableUdpSegmentationOffload">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="euso">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="EnableUdpReceiveOffload">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="euro">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>			
                        <Parameter ParameterName="IntelTDTEnabled">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="itdte">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_RuleSpecificExclusions_Id">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="AttackSurfaceReductionRules_RuleSpecificExclusions">
                            <Type PSType="System.String" />
                            <CmdletParameterMetadata>
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="OobeEnableRtpAndSigUpdate">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="oobers">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="PerformanceModeStatus">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="pms">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="QuickScanIncludeExclusions">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="qsie">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCacheMaintenance">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dcm">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="cim:operationOption:Force">
                            <Type PSType="System.Management.Automation.SwitchParameter" />
                            <CmdletParameterMetadata PSName="Force">
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="RemoveScanningThreadPoolCap">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="rstpc">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCoreServiceECSIntegration">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dcsei">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                        <Parameter ParameterName="DisableCoreServiceTelemetry">
                            <Type PSType="switch" />
                            <CmdletParameterMetadata Aliases="dcst">
                                <ValidateNotNull />
                                <ValidateNotNullOrEmpty />
                            </CmdletParameterMetadata>
                        </Parameter>
                    </Parameters>
                </Method>
            </Cmdlet>            
        </StaticCmdlets>
    </Class>
    <Enums>
        <Enum EnumName="MpPreference.MAPSReportingType" UnderlyingType="System.Byte">
            <Value Name="Disabled" Value="0" />
            <Value Name="Basic" Value="1" />
            <Value Name="Advanced" Value="2" />
        </Enum>
        <Enum EnumName="MpPreference.SubmitSamplesConsentType" UnderlyingType="System.Byte">
            <Value Name="AlwaysPrompt" Value="0" />
            <Value Name="SendSafeSamples" Value="1" />
            <Value Name="NeverSend" Value="2" />
            <Value Name="SendAllSamples" Value="3" />
        </Enum>
        <Enum EnumName="MpPreference.Day" UnderlyingType="System.Byte">
            <Value Name="Everyday" Value="0" />
            <Value Name="Sunday" Value="1" />
            <Value Name="Monday" Value="2" />
            <Value Name="Tuesday" Value="3" />
            <Value Name="Wednesday" Value="4" />
            <Value Name="Thursday" Value="5" />
            <Value Name="Friday" Value="6" />
            <Value Name="Saturday" Value="7" />
            <Value Name="Never" Value="8" />
        </Enum>
        <Enum EnumName="MpPreference.ScanType" UnderlyingType="System.Byte">
            <Value Name="QuickScan" Value="1" />
            <Value Name="FullScan" Value="2" />
        </Enum>
        <Enum EnumName="MpPreference.ScanDirection" UnderlyingType="System.Byte">
            <Value Name="Both" Value="0" />
            <Value Name="Incoming" Value="1" />
            <Value Name="Outcoming" Value="2" />
        </Enum>
        <Enum EnumName="MpPreference.ThreatSeverity" UnderlyingType="System.Byte">
            <Value Name="Unknown" Value="0" />
            <Value Name="Low" Value="1" />
            <Value Name="Moderate" Value="2" />
            <Value Name="High" Value="4" />
            <Value Name="Severe" Value="5" />
        </Enum>
        <Enum EnumName="MpPreference.ThreatAction" UnderlyingType="System.Byte">            
            <Value Name="Clean" Value="1" />
            <Value Name="Quarantine" Value="2" />
            <Value Name="Remove" Value="3" />
            <Value Name="Allow" Value="6" />
            <Value Name="UserDefined" Value="8" />
            <Value Name="NoAction" Value="9" />
            <Value Name="Block" Value="10" />
        </Enum>
        <Enum EnumName="MpPreference.PUAProtectionType" UnderlyingType="System.Byte">
           <Value Name="Disabled" Value="0" />
           <Value Name="Enabled" Value="1" />
           <Value Name="AuditMode" Value="2" />
        </Enum>
        <Enum EnumName="MpPreference.CloudBlockLevelType" UnderlyingType="System.Byte">
            <Value Name="Default" Value="0" />
            <Value Name="Moderate" Value="1" />
            <Value Name="High" Value="2" />
            <Value Name="HighPlus" Value="4" />
            <Value Name="ZeroTolerance" Value="6" />
        </Enum>
        <Enum EnumName="MpPreference.ASRRuleActionType" UnderlyingType="System.Byte">
            <Value Name="Disabled" Value="0" />
            <Value Name="Enabled" Value="1" />
            <Value Name="AuditMode" Value="2" />
            <Value Name="NotConfigured" Value="5" />
            <Value Name="Warn" Value="6" />
        </Enum>
        <Enum EnumName="MpPreference.ControlledFolderAccessType" UnderlyingType="System.Byte">
            <Value Name="Disabled" Value="0" />
            <Value Name="Enabled" Value="1" />
            <Value Name="AuditMode" Value="2" />
            <Value Name="BlockDiskModificationOnly" Value="3" />
            <Value Name="AuditDiskModificationOnly" Value="4" />
        </Enum>
        <Enum EnumName="MpPreference.UpdatesChannelType" UnderlyingType="System.Byte">
           <Value Name="NotConfigured" Value="0" />
           <Value Name="Beta" Value="2" />
           <Value Name="Preview" Value="3" />
           <Value Name="Staged" Value="4" />
           <Value Name="Broad" Value="5" />
           <Value Name="Delayed" Value="6" />
        </Enum>
        <Enum EnumName="MpPreference.DefinitionUpdatesChannelType" UnderlyingType="System.Byte">
           <Value Name="NotConfigured" Value="0" />
           <Value Name="Staged" Value="4" />
           <Value Name="Broad" Value="5" />
        </Enum>
        <!-- PerformanceModeStatus is an inversion of DisableAsyncScanOnOpen, so Enabled = 0 -->
        <Enum EnumName="MpPreference.PerformanceModeStatusType" UnderlyingType="System.Byte">
           <Value Name="Enabled" Value="0" />
           <Value Name="Disabled" Value="1" />
        </Enum>
        <Enum EnumName="MpPreference.QuickScanIncludeExclusionsType" UnderlyingType="System.Byte">
           <Value Name="Disabled" Value="0" />
           <Value Name="ScanRtpExclusions" Value="1" />
        </Enum>
        <Enum EnumName="MpPreference.NetworkBehaviorProtectionConfiguredState" UnderlyingType="System.Byte">
           <Value Name="NotConfigured" Value="0" />
           <Value Name="Block" Value="1" />
           <Value Name="Audit" Value="2" />
           <Value Name="Off" Value="4" />
        </Enum>
        <Enum EnumName="MpPreference.Aggressiveness" UnderlyingType="System.Byte">
           <Value Name="Low" Value="0" />
           <Value Name="Medium" Value="1" />
           <Value Name="High" Value="2" />
        </Enum>
    </Enums>
</PowerShellMetadata>

<!-- SIG # Begin signature block -->
<!-- MIIl5gYJKoZIhvcNAQcCoIIl1zCCJdMCAQExDzANBglghkgBZQMEAgEFADB5Bgor -->
<!-- BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG -->
<!-- KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCAybXe89TDpJ8Vq -->
<!-- kXg+4fAcaGYW2zF17fIyIQi1USwwqqCCC1MwggTgMIIDyKADAgECAhMzAAALjqZI -->
<!-- W414yEj+AAAAAAuOMA0GCSqGSIb3DQEBCwUAMHkxCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xIzAhBgNVBAMTGk1pY3Jvc29mdCBXaW5kb3dzIFBD -->
<!-- QSAyMDEwMB4XDTI0MDgyMjIxMzIwNVoXDTI1MDcwNDIxMzIwNVowcDELMAkGA1UE -->
<!-- BhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAc -->
<!-- BgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEaMBgGA1UEAxMRTWljcm9zb2Z0 -->
<!-- IFdpbmRvd3MwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCrxl8EJfzA -->
<!-- QclDL4nqroqJh8zn/bvwptnZ6a99GFLJ5GuBZxQloIPF+ZM+9D1QnH/xeQsnuh4e -->
<!-- CtIztigcnWawfvQAgpsgS47ROqxM5IsMhoGdbmahsAO9ld46iG/7RsKvfDvFCyip -->
<!-- SnaKR/Lz4YJl4diHhpSIAviRMkWBPoAXDZlcwIuGk0AeAuL8mxVYgBPuWUsq+MH5 -->
<!-- HL6ut/M+uzFpfv3atwbdECzGoXERLeHzqtOnpKLkqq+43TJLLWFnz5arpMs0MbjJ -->
<!-- o+40U3iarvp3hMIiM5O06++zz+PMoBO67yKqu9AcdQwJZ+jZ640qkYGDZeVJHNC4 -->
<!-- 55PXywdmGqOtAgMBAAGjggFoMIIBZDAfBgNVHSUEGDAWBgorBgEEAYI3CgMGBggr -->
<!-- BgEFBQcDAzAdBgNVHQ4EFgQU+rpJaEO0Uj4j7pcTCs301fdFu/gwRQYDVR0RBD4w -->
<!-- PKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEWMBQGA1UEBRMN -->
<!-- MjMwMDI4KzUwMjc5NzAfBgNVHSMEGDAWgBTRT6mKBwjO9CQYmOUA//PWeR03vDBT -->
<!-- BgNVHR8ETDBKMEigRqBEhkJodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny -->
<!-- bC9wcm9kdWN0cy9NaWNXaW5QQ0FfMjAxMC0wNy0wNi5jcmwwVwYIKwYBBQUHAQEE -->
<!-- SzBJMEcGCCsGAQUFBzAChjtodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpL2Nl -->
<!-- cnRzL01pY1dpblBDQV8yMDEwLTA3LTA2LmNydDAMBgNVHRMBAf8EAjAAMA0GCSqG -->
<!-- SIb3DQEBCwUAA4IBAQBsZbDNo5NyFwckNKY3ZusiPl4vHyU0CzJKbaV1lLdwxU// -->
<!-- ePCQRH0efL+g1HMnT0+3tYo3S+jcYYjbtVPhLT0V1jSIeYK5Raxwqm1Csb7B+zta -->
<!-- Qt933J/50c+3IiSeWXrNhi4r9Q7/h6f8Fu+TpG0pG9XGF5LmMSQKL16GZTbdOT5R -->
<!-- mqK5J/GW9+S20QDo5cMULRzoWvxRgYKbXIyQWQeUjIpXG4FdzLpPepPiybYpLztT -->
<!-- X8Vpcu62J9k2nG6nth0ExLrX3YdfRIpaP+ALXD7qa7fEAuy8hV1mX5eYVJAVmvTu -->
<!-- yyK779mXw0pqlIbCjbgRd7e0ohoIVzbEap3THr1RMIIGazCCBFOgAwIBAgIKYQxq -->
<!-- GQAAAAAABDANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgT -->
<!-- Cldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29m -->
<!-- dCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNh -->
<!-- dGUgQXV0aG9yaXR5IDIwMTAwHhcNMTAwNzA2MjA0MDIzWhcNMjUwNzA2MjA1MDIz -->
<!-- WjB5MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH -->
<!-- UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSMwIQYDVQQD -->
<!-- ExpNaWNyb3NvZnQgV2luZG93cyBQQ0EgMjAxMDCCASIwDQYJKoZIhvcNAQEBBQAD -->
<!-- ggEPADCCAQoCggEBAMB5uzqx8A+EuK1kKnUWc9C7B/Y+DZ0U5LGfwciUsDh8H9Az -->
<!-- VfW6I2b1LihIU8cWg7r1Uax+rOAmfw90/FmV3MnGovdScFosHZSrGb+vlX2vZqFv -->
<!-- m2JubUu8LzVs3qRqY1pf+/MNTWHMCn4x62wK0E2XD/1/OEbmisdzaXZVaZZM5Njw -->
<!-- NOu6sR/OKX7ET50TFasTG3JYYlZsioGjZHeYRmUpnYMUpUwIoIPXIx/zX99vLM/a -->
<!-- FtgOcgQo2Gs++BOxfKIXeU9+3DrknXAna7/b/B7HB9jAvguTHijgc23SVOkoTL9r -->
<!-- XZ//XTMSN5UlYTRqQst8nTq7iFnho0JtOlBbSNECAwEAAaOCAeMwggHfMBAGCSsG -->
<!-- AQQBgjcVAQQDAgEAMB0GA1UdDgQWBBTRT6mKBwjO9CQYmOUA//PWeR03vDAZBgkr -->
<!-- BgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUw -->
<!-- AwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvXzpoYxDBWBgNVHR8ETzBN -->
<!-- MEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2NybC9wcm9kdWN0 -->
<!-- cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYIKwYBBQUHAQEETjBMMEoG -->
<!-- CCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpL2NlcnRzL01p -->
<!-- Y1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDCBnQYDVR0gBIGVMIGSMIGPBgkrBgEE -->
<!-- AYI3LgMwgYEwPQYIKwYBBQUHAgEWMWh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9Q -->
<!-- S0kvZG9jcy9DUFMvZGVmYXVsdC5odG0wQAYIKwYBBQUHAgIwNB4yIB0ATABlAGcA -->
<!-- YQBsAF8AUABvAGwAaQBjAHkAXwBTAHQAYQB0AGUAbQBlAG4AdAAuIB0wDQYJKoZI -->
<!-- hvcNAQELBQADggIBAC5Bpoa1Bm/wgIX6O8oX6cn65DnClHDDZJTD2FamkI7+5Jr0 -->
<!-- bfVvjlONWqjzrttGbL5/HVRWGzwdccRRFVR+v+6llUIz/Q2QJCTj+dyWyvy4rL/0 -->
<!-- wjlWuLvtc7MX3X6GUCOLViTKu6YdmocvJ4XnobYKnA0bjPMAYkG6SHSHgv1QyfSH -->
<!-- KcMDqivfGil56BIkmobt0C7TQIH1B18zBlRdQLX3sWL9TUj3bkFHUhy7G8JXOqiZ -->
<!-- VpPUxt4mqGB1hrvsYqbwHQRF3z6nhNFbRCNjJTZ3b65b3CLVFCNqQX/QQqbb7yV7 -->
<!-- BOPSljdiBq/4Gw+Oszmau4n1NQblpFvDjJ43X1PRozf9pE/oGw5rduS4j7DC6v11 -->
<!-- 9yxBt5yj4R4F/peSy39ZA22oTo1OgBfU1XL2VuRIn6MjugagwI7RiE+TIPJwX9hr -->
<!-- cqMgSfx3DF3Fx+ECDzhCEA7bAq6aNx1QgCkepKfZxpolVf1Ayq1kEOgx+RJUeRry -->
<!-- DtjWqx4z/gLnJm1hSY/xJcKLdJnf+ZMakBzu3ZQzDkJQ239Q+J9iguymghZ8Zrzs -->
<!-- mbDBWF2osJphFJHRmS9J5D6Bmdbm78rj/T7u7AmGAwcNGw186/RayZXPhxIKXezF -->
<!-- ApLNBZlyyn3xKhAYOOQxoyi05kzFUqOcasd9wHEJBA1w3gI/h+5WoezrtUyFMYIZ -->
<!-- 6TCCGeUCAQEwgZAweTELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x -->
<!-- EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv -->
<!-- bjEjMCEGA1UEAxMaTWljcm9zb2Z0IFdpbmRvd3MgUENBIDIwMTACEzMAAAuOpkhb -->
<!-- jXjISP4AAAAAC44wDQYJYIZIAWUDBAIBBQCggZAwGQYJKoZIhvcNAQkDMQwGCisG -->
<!-- AQQBgjcCAQQwLwYJKoZIhvcNAQkEMSIEIBKOCUzbRES8wjcvo007/U4Ekm/o8Kx6 -->
<!-- adkv52T6acrMMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8AcwBvAGYA -->
<!-- dKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEBBQAEggEA -->
<!-- g0NzREo8eA1wWqdfuGrK3tsg4n77wCeY1SizBcLEROuCiEXbaDdXSbdDVJ3DWC0T -->
<!-- UuESYPLebprPCXNmOWUy0sXOT0+ZjlfD7tQJud7DUwL5xYdTbPXdrtosVcmKWZwX -->
<!-- 6Z8x+iHJsoc7NENNUCrYFizPJs3qdVGt6VnCCJ9SUaVWIFwnWzmj+4r2JkIOqP4Q -->
<!-- uANkd0/Tuw5avQmSnbTDbzfr3cph9/VHsZh1EwMQesZsAEpiKMqIuPLz3wUZctBT -->
<!-- tDe/IrYbxt9BNh7xZWN3NiMfkVCo1/cfKGbf3oBcggvnC1IRKFMKHt8hqFxwT0qN -->
<!-- AWDY0dKFbqNNYOum2W46zKGCF5YwgheSBgorBgEEAYI3AwMBMYIXgjCCF34GCSqG -->
<!-- SIb3DQEHAqCCF28wghdrAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFRBgsqhkiG9w0B -->
<!-- CRABBKCCAUAEggE8MIIBOAIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFlAwQCAQUA -->
<!-- BCAJKKolVT4+Cz/cT39lonrToCDZdc4Wg9WieWyMNe9NWAIGZ/fJXABXGBIyMDI1 -->
<!-- MDQxNjIzMTU0My4zNlowBIACAfSggdGkgc4wgcsxCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xJTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVyaWNhIE9w -->
<!-- ZXJhdGlvbnMxJzAlBgNVBAsTHm5TaGllbGQgVFNTIEVTTjo5MjAwLTA1RTAtRDk0 -->
<!-- NzElMCMGA1UEAxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZaCCEe0wggcg -->
<!-- MIIFCKADAgECAhMzAAACCQgH4PlcjOZVAAEAAAIJMA0GCSqGSIb3DQEBCwUAMHwx -->
<!-- CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt -->
<!-- b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1p -->
<!-- Y3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwMB4XDTI1MDEzMDE5NDI1NVoXDTI2 -->
<!-- MDQyMjE5NDI1NVowgcsxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9u -->
<!-- MRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRp -->
<!-- b24xJTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVyaWNhIE9wZXJhdGlvbnMxJzAlBgNV -->
<!-- BAsTHm5TaGllbGQgVFNTIEVTTjo5MjAwLTA1RTAtRDk0NzElMCMGA1UEAxMcTWlj -->
<!-- cm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZTCCAiIwDQYJKoZIhvcNAQEBBQADggIP -->
<!-- ADCCAgoCggIBAMKUSjD3Lgzd/VL3PXG00QRPBYvW8SKLDSgPtJcR2/ix0/TGxXKJ -->
<!-- 2/ojauYSXw9iz0txmPOxY4cjt1CREvbwY/cJdy9jRmrqdawdjZBqYJkUsXYiVEfo -->
<!-- EfHZGQ3tlEMqazsE6jggYFGUIyRS/033+3A7MCSlY2wzdv8FDFzCFWCxCq1Dw0Q9 -->
<!-- S6JH4ZXmt1AdRPimOKFlOQnCtqWLPRltilRMfk6SLd3cGnH2qI+uIHqGE18Y+OXQ -->
<!-- 8inbcPnv2ulbpmY+o9PyPXYpfvJJnA27Gzc9i8X/DXcaxFeTMhsjIsoQ/OP2XOaa -->
<!-- sXbCO+9SvH0BnDsYtJeTbwOfVdJ/raFuQW5QbA8UuncRtGohWYFnjbBzPmZIggLL -->
<!-- dCz+HCERiFSd2cAGA2kPlq8As5XuxR8mscNldfp/2CBuMgDqPaeFIBIiqXwXkuwo -->
<!-- HDRE+0O7LePYI/G1OZmjNssrxMy3EOIwKDFOl+DmJhS/KFXhqpoMvBEGygFGE7/6 -->
<!-- HDJsqdjBfEp546uw7BAudo4TkGYUlhYE4XPd3zwsEr1BEGB0QfkItWHvCSAwh6H3 -->
<!-- pwfn4fTES+aDq3u7O2VdfZJXvF1Rg/EDe+ONXcSRXtptIcPkcdBlOt3cWqwP9U5g -->
<!-- AJRUE+vEX6RStkZfFgidlOmtgxgSrpQgbUNPikJU/0NxoIsYg5gQnWDTAgMBAAGj -->
<!-- ggFJMIIBRTAdBgNVHQ4EFgQUSYvo0cRdOOW98C9AzbV3MxaTytIwHwYDVR0jBBgw -->
<!-- FoAUn6cVXQBeYl2D9OXSZacbUzUZ6XIwXwYDVR0fBFgwVjBUoFKgUIZOaHR0cDov -->
<!-- L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jcmwvTWljcm9zb2Z0JTIwVGltZS1T -->
<!-- dGFtcCUyMFBDQSUyMDIwMTAoMSkuY3JsMGwGCCsGAQUFBwEBBGAwXjBcBggrBgEF -->
<!-- BQcwAoZQaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0cy9NaWNy -->
<!-- b3NvZnQlMjBUaW1lLVN0YW1wJTIwUENBJTIwMjAxMCgxKS5jcnQwDAYDVR0TAQH/ -->
<!-- BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAOBgNVHQ8BAf8EBAMCB4AwDQYJ -->
<!-- KoZIhvcNAQELBQADggIBAFxefG84PCTiH+NtQGycWUW2tK4EFlvvBJl9rmUpExM1 -->
<!-- 82WZoALht3tajQjmEzGwQlTK6kfCHiQPmqRFlzMhzSMgAFBDXENQFr5ZPGun9QCo -->
<!-- LXuKMUJ49kphWM2sd/8GaPPsVo4jjWTG55GHAs0hxDaCYGoNHlbhNLaG1EljJkCz -->
<!-- uN8mZsO1NxQ4yESXU5aXH8We9xBui3lU/NpTCJPo2J7yXo9mOhCy7GJqy5ICbEoh -->
<!-- B2wecnlCiSrB3KpeLUVkO0RNW9td8Oyh/NO1rh6fap/jyHMRnBS9uTPmya3z3SdU -->
<!-- AruTPZyuvM3eGmd8W5+2n+tctZO/E9Bx9ZeIS4hR3YaDt5HxC3Iq0kNTz48PAQKT -->
<!-- OhomNsYIqrH0RKAUnPOtc3CGFfpFzyDYRT/7reaapZ4IX+Qk4WDZ4nDtq79psRKC -->
<!-- rcRrPIPVWUv4dpf4wEcbNCYe286bdCXjBVM3darxfxsJHryqIXmsVqybhHEXrNqN -->
<!-- l5IcL+pLnffr/howOqxXo7zpGU88JgYk4+1/Yxso7tckl4v9RA3Rze6LHlExOjrp -->
<!-- 1sBPE9QUQbk+Hg8fMaNRsQ7sPfku4QGKIbxiuUxE6QaXd8FCX1tZuDD0IhRBvCrl -->
<!-- xNoTGV8Skx1KjJ0miVRNAPkQsobPVMlqFOJ13bTCXCLkGTfpcibOwfhizXmJdF8C -->
<!-- MIIHcTCCBVmgAwIBAgITMwAAABXF52ueAptJmQAAAAAAFTANBgkqhkiG9w0BAQsF -->
<!-- ADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcT -->
<!-- B1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UE -->
<!-- AxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IDIwMTAwHhcN -->
<!-- MjEwOTMwMTgyMjI1WhcNMzAwOTMwMTgzMjI1WjB8MQswCQYDVQQGEwJVUzETMBEG -->
<!-- A1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWlj -->
<!-- cm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFt -->
<!-- cCBQQ0EgMjAxMDCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAOThpkzn -->
<!-- tHIhC3miy9ckeb0O1YLT/e6cBwfSqWxOdcjKNVf2AX9sSuDivbk+F2Az/1xPx2b3 -->
<!-- lVNxWuJ+Slr+uDZnhUYjDLWNE893MsAQGOhgfWpSg0S3po5GawcU88V29YZQ3MFE -->
<!-- yHFcUTE3oAo4bo3t1w/YJlN8OWECesSq/XJprx2rrPY2vjUmZNqYO7oaezOtgFt+ -->
<!-- jBAcnVL+tuhiJdxqD89d9P6OU8/W7IVWTe/dvI2k45GPsjksUZzpcGkNyjYtcI4x -->
<!-- yDUoveO0hyTD4MmPfrVUj9z6BVWYbWg7mka97aSueik3rMvrg0XnRm7KMtXAhjBc -->
<!-- TyziYrLNueKNiOSWrAFKu75xqRdbZ2De+JKRHh09/SDPc31BmkZ1zcRfNN0Sidb9 -->
<!-- pSB9fvzZnkXftnIv231fgLrbqn427DZM9ituqBJR6L8FA6PRc6ZNN3SUHDSCD/AQ -->
<!-- 8rdHGO2n6Jl8P0zbr17C89XYcz1DTsEzOUyOArxCaC4Q6oRRRuLRvWoYWmEBc8pn -->
<!-- ol7XKHYC4jMYctenIPDC+hIK12NvDMk2ZItboKaDIV1fMHSRlJTYuVD5C4lh8zYG -->
<!-- NRiER9vcG9H9stQcxWv2XFJRXRLbJbqvUAV6bMURHXLvjflSxIUXk8A8FdsaN8cI -->
<!-- FRg/eKtFtvUeh17aj54WcmnGrnu3tz5q4i6tAgMBAAGjggHdMIIB2TASBgkrBgEE -->
<!-- AYI3FQEEBQIDAQABMCMGCSsGAQQBgjcVAgQWBBQqp1L+ZMSavoKRPEY1Kc8Q/y8E -->
<!-- 7jAdBgNVHQ4EFgQUn6cVXQBeYl2D9OXSZacbUzUZ6XIwXAYDVR0gBFUwUzBRBgwr -->
<!-- BgEEAYI3TIN9AQEwQTA/BggrBgEFBQcCARYzaHR0cDovL3d3dy5taWNyb3NvZnQu -->
<!-- Y29tL3BraW9wcy9Eb2NzL1JlcG9zaXRvcnkuaHRtMBMGA1UdJQQMMAoGCCsGAQUF -->
<!-- BwMIMBkGCSsGAQQBgjcUAgQMHgoAUwB1AGIAQwBBMAsGA1UdDwQEAwIBhjAPBgNV -->
<!-- HRMBAf8EBTADAQH/MB8GA1UdIwQYMBaAFNX2VsuP6KJcYmjRPZSQW9fOmhjEMFYG -->
<!-- A1UdHwRPME0wS6BJoEeGRWh0dHA6Ly9jcmwubWljcm9zb2Z0LmNvbS9wa2kvY3Js -->
<!-- L3Byb2R1Y3RzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNybDBaBggrBgEFBQcB -->
<!-- AQROMEwwSgYIKwYBBQUHMAKGPmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2kv -->
<!-- Y2VydHMvTWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3J0MA0GCSqGSIb3DQEBCwUA -->
<!-- A4ICAQCdVX38Kq3hLB9nATEkW+Geckv8qW/qXBS2Pk5HZHixBpOXPTEztTnXwnE2 -->
<!-- P9pkbHzQdTltuw8x5MKP+2zRoZQYIu7pZmc6U03dmLq2HnjYNi6cqYJWAAOwBb6J -->
<!-- 6Gngugnue99qb74py27YP0h1AdkY3m2CDPVtI1TkeFN1JFe53Z/zjj3G82jfZfak -->
<!-- Vqr3lbYoVSfQJL1AoL8ZthISEV09J+BAljis9/kpicO8F7BUhUKz/AyeixmJ5/AL -->
<!-- aoHCgRlCGVJ1ijbCHcNhcy4sa3tuPywJeBTpkbKpW99Jo3QMvOyRgNI95ko+ZjtP -->
<!-- u4b6MhrZlvSP9pEB9s7GdP32THJvEKt1MMU0sHrYUP4KWN1APMdUbZ1jdEgssU5H -->
<!-- LcEUBHG/ZPkkvnNtyo4JvbMBV0lUZNlz138eW0QBjloZkWsNn6Qo3GcZKCS6OEua -->
<!-- bvshVGtqRRFHqfG3rsjoiV5PndLQTHa1V1QJsWkBRH58oWFsc/4Ku+xBZj1p/cvB -->
<!-- QUl+fpO+y/g75LcVv7TOPqUxUYS8vwLBgqJ7Fx0ViY1w/ue10CgaiQuPNtq6TPmb -->
<!-- /wrpNPgkNWcr4A245oyZ1uEi6vAnQj0llOZ0dFtq0Z4+7X6gMTN9vMvpe784cETR -->
<!-- kPHIqzqKOghif9lwY1NNje6CbaUFEMFxBmoQtB1VM1izoXBm8qGCA1AwggI4AgEB -->
<!-- MIH5oYHRpIHOMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQ -->
<!-- MA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9u -->
<!-- MSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25zMScwJQYDVQQL -->
<!-- Ex5uU2hpZWxkIFRTUyBFU046OTIwMC0wNUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jv -->
<!-- c29mdCBUaW1lLVN0YW1wIFNlcnZpY2WiIwoBATAHBgUrDgMCGgMVAHzvras9NB3s -->
<!-- icMJB1vWSAUpCQJEoIGDMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh -->
<!-- c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD -->
<!-- b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw -->
<!-- MTAwDQYJKoZIhvcNAQELBQACBQDrqjBNMCIYDzIwMjUwNDE2MTMzNDA1WhgPMjAy -->
<!-- NTA0MTcxMzM0MDVaMHcwPQYKKwYBBAGEWQoEATEvMC0wCgIFAOuqME0CAQAwCgIB -->
<!-- AAICBrgCAf8wBwIBAAICEewwCgIFAOurgc0CAQAwNgYKKwYBBAGEWQoEAjEoMCYw -->
<!-- DAYKKwYBBAGEWQoDAqAKMAgCAQACAwehIKEKMAgCAQACAwGGoDANBgkqhkiG9w0B -->
<!-- AQsFAAOCAQEAANyl83txLIh6tH5e+iZhh6TCqGi7uNWO7PkY3aOr2k+8LukbgCnq -->
<!-- soedBvvRavcvZdu0xJtM2hhD6X7zb35IknM0qjbQKchVSkyx5zCWLZsyhxTz/+ht -->
<!-- cTvVLQSr6TnWKUzIdzId7IP6RP/ME/M/QVAm57kzLF2kD7YHrB95eXUoa6fGjiXJ -->
<!-- kotoxQz6ybnOutxvYWJIFsWM+dcANOZVCaQRHnPlJe7B1Cg0IGZkhR2doCcoE+4b -->
<!-- yMpQRTTUoVZHaXp6HSdVPS+RDu2cIvKMS/zp49hgrRbjwAF5qdChW96PBC7wSP9J -->
<!-- WeVvdHnmjCt4BiwDKAsh6S/3MXfEHm50gjGCBA0wggQJAgEBMIGTMHwxCzAJBgNV -->
<!-- BAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4w -->
<!-- HAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29m -->
<!-- dCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMzAAACCQgH4PlcjOZVAAEAAAIJMA0GCWCG -->
<!-- SAFlAwQCAQUAoIIBSjAaBgkqhkiG9w0BCQMxDQYLKoZIhvcNAQkQAQQwLwYJKoZI -->
<!-- hvcNAQkEMSIEINBHeerAlOUQumMU7blDqKQIFIeYVbmhww5EgnGAbKn3MIH6Bgsq -->
<!-- hkiG9w0BCRACLzGB6jCB5zCB5DCBvQQgaBssHsi99AIuZQ5RmGN1SorxuKR8HplV -->
<!-- V2hOM3CFEz4wgZgwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGlu -->
<!-- Z3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBv -->
<!-- cmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAIT -->
<!-- MwAAAgkIB+D5XIzmVQABAAACCTAiBCDd/cotdl9G+DwiBbz7Moo08c46ZGbBiAl8 -->
<!-- WnCKMyXAyDANBgkqhkiG9w0BAQsFAASCAgCHASeOQRY5YIhvBnQoPbATZGxjkn92 -->
<!-- OrYRokRM0G45GGg4hiZej2nKbgJblBz31qjr2lafzlsGReXUrO3hBlDcZaZSnEfR -->
<!-- LXU0D7tURWxeaaS9ztlJhrGNNPzjip3jlMRrO12apLEwtGILSO1PbQKrYgI0PjCu -->
<!-- J/feGw0xMZxscXNpeK8OxaYDhuGsd0jA2s8J68p47Bqf2yITtZfihhneMJO8/0oa -->
<!-- GVqJ2Wefzz5X4SteSx1mu6lS6kRMIIODfzMl2rgvzNdGhWxrxbPpqRXIDDDt4kXn -->
<!-- 3usMngXLkByX/IGFgs38Kt9zAQXq3K8KicLcoNhCjn0jroRP3x1UOH/DVLfnU1Om -->
<!-- q8Gm5AZa5XGnDh6CVutZbggInCnN3qJxeQ+BSkv3eymemfZMvBSuATaooPTVWJdr -->
<!-- oxMECPqoJYK2oAL42rlJ11Am6AGuw3WVab8Oda7uGZNdJMc96Vf/RG7nwJ/JWyDt -->
<!-- 91ReV7jesbwcKD8HJKriOjWhHI/iVYivAXphkaGeFDuVCSejUrSQrLWlIkcq+IRK -->
<!-- tlwh4U9a6Vz9KDtw26P5Nhn2JlmCjA53ksSKNv1l161CPJv3E4dcZNe/RoZNegs6 -->
<!-- bpKbp5CN2S3T62ubF+JJpVRB9hTa1OQq4k2mpcpdwKiuLJjhKD1XyZgXrDDDXMWI -->
<!-- ewjeQHLyZD7+iw== -->
<!-- SIG # End signature block -->
