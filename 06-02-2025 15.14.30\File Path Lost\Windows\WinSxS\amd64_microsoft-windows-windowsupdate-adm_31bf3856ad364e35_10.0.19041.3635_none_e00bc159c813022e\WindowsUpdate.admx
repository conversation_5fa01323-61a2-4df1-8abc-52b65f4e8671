<?xml version="1.0" encoding="utf-8"?>
<!--  (c) 2006 Microsoft Corporation  -->
<policyDefinitions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://schemas.microsoft.com/GroupPolicy/2006/07/PolicyDefinitions">
  <policyNamespaces>
    <target prefix="wuau" namespace="Microsoft.Policies.WindowsUpdate" />
    <using prefix="windows" namespace="Microsoft.Policies.Windows" />
  </policyNamespaces>
  <supersededAdm fileName="wuau.adm" />
  <resources minRequiredRevision="1.0" />

  <supportedOn>
    <!--
        For product definitions (products:MicrosoftABC) please consult:
        //depot/winmain/ds/grouppolicy/PolicyDefinitions/WindowsProducts.admx

        For windows policy definitions (windows:XYZ):
        //depot/winmain/ds/grouppolicy/PolicyDefinitions/windows.admx
        //depot/winmain/ds/grouppolicy/PolicyDefinitions/WindowsServer.admx
    -->
    <definitions>
      <!--Windows 7, Windows Server 2008, Windows Vista, Windows XP SP2-->
      <definition name="WU_SUPPORTED_Windows7ToXPSP2" displayName="$(string.WU_SUPPORTED_Windows7ToXPSP2)">
        <or>
          <range ref="products:MicrosoftWindowsXP" minVersionIndex="2"/>
          <range ref="products:MicrosoftWindows" minVersionIndex="4" maxVersionIndex="6"/>
        </or>
      </definition>

      <!--Windows 7, Windows Server 2003, Windows Vista, Microsoft Windows XP Professional Service Pack 2, Microsoft Windows XP Professional Service Pack 1 , Microsoft Windows 2000 Service Pack 4, Microsoft Windows 2000 Service Pack 3-->
      <!--This is based on SUPPORTED_Win2kSP3_Or_XPSP1 from the windows policy definitions-->
      <definition name="WU_SUPPORTED_Windows7_To_Win2kSP3_Or_XPSP1" displayName="$(string.WU_SUPPORTED_Windows7_To_Win2kSP3_Or_XPSP1)">
        <or>
          <range ref="products:MicrosoftWindows2000" minVersionIndex="3"/>
          <range ref="products:MicrosoftWindowsXP" minVersionIndex="1"/>
          <range ref="products:MicrosoftWindows" minVersionIndex="3" maxVersionIndex="6"/>
        </or>
      </definition>

      <!--At least Microsoft Windows 2000 Service Pack 3 or Microsoft Windows XP Professional Service Pack 1, excluding Windows RT-->
      <definition name="WU_SUPPORTED_Win2kSP3_Or_XPSP1_NoWinRT" displayName="$(string.WU_SUPPORTED_Win2kSP3_Or_XPSP1_NoWinRT)">
        <or>
          <range ref="products:MicrosoftWindows2000" minVersionIndex="3"/>
          <range ref="products:MicrosoftWindowsXP" minVersionIndex="1"/>
          <range ref="products:MicrosoftWindows" minVersionIndex="3" maxVersionIndex="8"/>
        </or>
      </definition>

      <!--At least Microsoft Windows XP Professional with SP1 or Windows Server 2003 family, excluding Windows RT-->
      <definition name="WU_SUPPORTED_WindowsXPSP1_NoWinRT" displayName="$(string.WU_SUPPORTED_WindowsXPSP1_NoWinRT)">
        <or>
          <range ref="products:MicrosoftWindowsXP" minVersionIndex="1"/>
          <range ref="products:MicrosoftWindows" minVersionIndex="3" maxVersionIndex="8"/>
        </or>
      </definition>

      <!--At least Windows XP Professional Service Pack 1 or At least Windows 2000 Service Pack 3 through Windows 8.1 or Windows Server 2012 R2 with most current service pack-->
      <definition name="WU_SUPPORTED_Win2kSP3_Or_XPSP1_Through_Win81_or_Server2012R2" displayName="$(string.WU_SUPPORTED_Win2kSP3_Or_XPSP1_Through_Win81_or_Server2012R2)">
        <or>
          <range ref="products:MicrosoftWindows2000" minVersionIndex="3"/>
          <range ref="products:MicrosoftWindowsXP" minVersionIndex="2"/>
          <range ref="products:MicrosoftWindows" minVersionIndex="4" maxVersionIndex="12"/>
        </or>
      </definition>

      <!--At least Windows Vista through Windows 8.1 or Windows Server 2012 R2 with most current service pack-->
      <definition name="WU_SUPPORTED_WindowsVista_Through_Win81_or_Server2012R2" displayName="$(string.WU_SUPPORTED_WindowsVista_Through_Win81_or_Server2012R2)">
        <or>
          <range ref="products:MicrosoftWindows" minVersionIndex="4" maxVersionIndex="12"/>
        </or>
      </definition>

      <!--Windows XP Professional Service Pack 1 or At least Windows 2000 Service Pack 3, Option 7 only supported on servers of at least Windows Server 2012 edition-->
      <definition name="WU_SUPPORTED_XPSP1_or_Win2kSP3_AUOption7_SUPPORTED_Server2016" displayName="$(string.WU_SUPPORTED_XPSP1_or_Win2kSP3_AUOption7_SUPPORTED_Server2016)">
        <or>
          <range ref="products:MicrosoftWindows" minVersionIndex="4" maxVersionIndex="12"/>
        </or>
      </definition>

      <!--Windows Server 2016 through Windows Server 2022, or Windows 10-->
      <definition name="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10" displayName="$(string.WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10)">
        <or>
          <range ref="products:MicrosoftWindows" minVersionIndex="13" maxVersionIndex="14"/>
        </or>
      </definition>

      <!--At least Windows Server 2019, or Windows 10 Version 1809-->
      <definition name="WU_SUPPORTED_Windows_Server_2019_Windows_10_0_1809" displayName="$(string.WU_SUPPORTED_Windows_Server_2019_Windows_10_0_1809)">
        <or>
          <range ref="products:MicrosoftWindows" minVersionIndex="13"/>
        </or>
      </definition>

      <!--At least Windows 10 2004-->
      <definition name="WU_SUPPORTED_Windows_Server_2022_Windows_10_0_2004" displayName="$(string.WU_SUPPORTED_Windows_Server_2022_Windows_10_0_2004)">
        <or>
          <range ref="products:MicrosoftWindows" minVersionIndex="13"/>
        </or>
      </definition>

    </definitions>
  </supportedOn>

  <categories>
    <category name="WindowsUpdateCat" displayName="$(string.WindowsUpdateCat)">
      <parentCategory ref="windows:WindowsComponents" />
    </category>
    <category name="DeferUpdateCat" displayName="$(string.DeferUpdateCat)">
      <parentCategory ref="WindowsUpdateCat" />
    </category>
  </categories>
  <policies>
    <policy name="NoAutoUpdate" class="User" displayName="$(string.NoAutoUpdate)" explainText="$(string.NoAutoUpdate_Help)" key="Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" valueName="NoAutoUpdate">
      <parentCategory ref="windows:System" />
      <supportedOn ref="windows:SUPPORTED_WindowsXPOnly" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="AUDontShowUasPolicy" class="Both" displayName="$(string.AUDontShowUasPolicy)" explainText="$(string.AUDontShowUasHelp)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="NoAUShutdownOption">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows7ToXPSP2" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="AUNoUasDefaultPolicy_User" class="User" displayName="$(string.AUNoUasDefaultPolicy)" explainText="$(string.AUNoUasDefaultHelp_User)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="NoAUAsDefaultShutdownOption">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows7ToXPSP2" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="AUNoUasDefaultPolicy_Mach" class="Machine" displayName="$(string.AUNoUasDefaultPolicy)" explainText="$(string.AUNoUasDefaultHelp_Mach)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="NoAUAsDefaultShutdownOption">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows7ToXPSP2" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="RemoveWindowsUpdate" class="User" displayName="$(string.RemoveWindowsUpdate)" explainText="$(string.RemoveWindowsUpdate_Help)" presentation="$(presentation.RemoveWindowsUpdate)" key="Software\Microsoft\Windows\CurrentVersion\Policies\WindowsUpdate" valueName="DisableWindowsUpdateAccess">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Win2kSP3_Or_XPSP1_Through_Win81_or_Server2012R2" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="RemoveWindowsUpdateMode" valueName="DisableWindowsUpdateAccessMode" required="true">
          <item displayName="$(string.RemoveWindowsUpdateModeAll)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.RemoveWindowsUpdateModeReboot)">
            <value>
              <decimal value="1" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="AutoUpdateCfg" class="Machine" displayName="$(string.AutoUpdateCfg)" explainText="$(string.AutoUpdateCfg_Help)" presentation="$(presentation.AutoUpdateCfg)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="NoAutoUpdate">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_XPSP1_or_Win2kSP3_AUOption7_SUPPORTED_Server2016" />
      <enabledValue>
        <decimal value="0" />
      </enabledValue>
      <disabledValue>
        <decimal value="1" />
      </disabledValue>
      <elements>
        <enum id="AutoUpdateMode" valueName="AUOptions" required="true">
          <item displayName="$(string.AutoUpdateModeNotify)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateModeDownload)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateModeAuto)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateModeAdminChooses)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateModeNotifyToInstallNotifyToReboot)">
            <value>
              <decimal value="7" />
            </value>
          </item>
        </enum>
        <boolean id="AutoUpdateAutomaticMaintenanceEnabled" valueName="AutomaticMaintenanceEnabled" />
        <enum id="AutoUpdateSchDay" valueName="ScheduledInstallDay" required="true">
          <item displayName="$(string.AutoUpdateSchDay_Everyday)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchDay_Sunday)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchDay_Monday)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchDay_Tuesday)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchDay_Wednesday)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchDay_Thursday)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchDay_Friday)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchDay_Saturday)">
            <value>
              <decimal value="7" />
            </value>
          </item>
        </enum>
        <enum id="AutoUpdateSchTime" valueName="ScheduledInstallTime" required="true">
          <item displayName="$(string.AutoUpdateSchTimeAuto)">
            <value>
              <decimal value="24" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime0)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime1)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime5)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime6)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime7)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime19)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime20)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime21)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime22)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.AutoUpdateSchTime23)">
            <value>
              <decimal value="23" />
            </value>
          </item>
        </enum>
        <boolean id="AllowMUUpdateServiceId" valueName="AllowMUUpdateService" />
        <boolean id="AutoUpdateSchEveryWeek" valueName="ScheduledInstallEveryWeek" />
        <boolean id="AutoUpdateSchFirstWeek" valueName="ScheduledInstallFirstWeek" />
        <boolean id="AutoUpdateSchSecondWeek" valueName="ScheduledInstallSecondWeek" />
        <boolean id="AutoUpdateSchThirdWeek" valueName="ScheduledInstallThirdWeek" />
        <boolean id="AutoUpdateSchFourthWeek" valueName="ScheduledInstallFourthWeek" />
      </elements>
    </policy>
    <policy name="CorpWuURL" class="Machine" displayName="$(string.CorpWuURL)" explainText="$(string.CorpWuURL_Help)" presentation="$(presentation.CorpWuURL)" key="Software\Policies\Microsoft\Windows\WindowsUpdate">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Win2kSP3_Or_XPSP1_NoWinRT" />
      <enabledList>
        <item key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="UseWUServer">
          <value>
            <decimal value="1" />
          </value>
        </item>
      </enabledList>
      <disabledList>
        <item key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="UseWUServer">
          <value>
            <decimal value="0" />
          </value>
        </item>
      </disabledList>
      <elements>
        <text id="CorpWUURL_Name" valueName="WUServer" required="true" />
        <text id="CorpWUStatusURL_Name" valueName="WUStatusServer" required="true" />
        <text id="CorpWUContentHost_Name" valueName="UpdateServiceUrlAlternate" required="false" />
        <boolean id="CorpWUFillEmptyContentUrls" valueName="FillEmptyContentUrls" required="false" />
        <boolean id="CorpWUDoNotEnforceEnterpriseTLSCertPinningForUpdateDetection" valueName="DoNotEnforceEnterpriseTLSCertPinningForUpdateDetection" required="false" />
        <enum id="SetProxyBehaviorForUpdateDetection" valueName="SetProxyBehaviorForUpdateDetection" required="true">
          <item displayName="$(string.OnlyUseSystemProxyForUpdateDetection)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.AllowUserProxyAsFallbackForUpdateDetection)">
            <value>
              <decimal value="1" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="UpdateClassPolicySource_Title" class="Machine" displayName="$(string.UpdateClassPolicySource_Title)" explainText="$(string.UpdateClassPolicySource_Help)" presentation="$(presentation.UpdateClassPolicySource_Title)" key="Software\Policies\Microsoft\Windows\WindowsUpdate">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows_Server_2022_Windows_10_0_2004" />
      <enabledList>
        <item key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="UseUpdateClassPolicySource">
          <value>
            <decimal value="1" />
          </value>
        </item>
      </enabledList>
      <disabledList>
        <item key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="UseUpdateClassPolicySource">
          <value>
            <decimal value="0" />
          </value>
        </item>
      </disabledList>
      <elements>
        <enum id="CorpWUSetPolicyDrivenUpdateSourceForFeatureUpdates" valueName="SetPolicyDrivenUpdateSourceForFeatureUpdates" required="true" >
          <item displayName="$(string.UpdateClassPolicySourceSelectionWSUS)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.UpdateClassPolicySourceSelectionWU)">
            <value>
              <decimal value="0" />
            </value>
          </item>
        </enum>
        <enum id="CorpWUSetPolicyDrivenUpdateSourceForQualityUpdates" valueName="SetPolicyDrivenUpdateSourceForQualityUpdates" required="true" >
          <item displayName="$(string.UpdateClassPolicySourceSelectionWSUS)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.UpdateClassPolicySourceSelectionWU)">
            <value>
              <decimal value="0" />
            </value>
          </item>
        </enum>
        <enum id="CorpWUSetPolicyDrivenUpdateSourceForDriverUpdates" valueName="SetPolicyDrivenUpdateSourceForDriverUpdates" required="true" >
          <item displayName="$(string.UpdateClassPolicySourceSelectionWSUS)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.UpdateClassPolicySourceSelectionWU)">
            <value>
              <decimal value="0" />
            </value>
          </item>
        </enum>
        <enum id="CorpWUSetPolicyDrivenUpdateSourceForOtherUpdates" valueName="SetPolicyDrivenUpdateSourceForOtherUpdates" required="true" >
          <item displayName="$(string.UpdateClassPolicySourceSelectionWSUS)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.UpdateClassPolicySourceSelectionWU)">
            <value>
              <decimal value="0" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="DetectionFrequency_Title" class="Machine" displayName="$(string.DetectionFrequency_Title)" explainText="$(string.DetectionFrequency_Help)" presentation="$(presentation.DetectionFrequency_Title)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="DetectionFrequencyEnabled">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Win2kSP3_Or_XPSP1_NoWinRT" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <decimal id="DetectionFrequency_Hour2" valueName="DetectionFrequency" required="true" minValue="1" maxValue="22" />
      </elements>
    </policy>
    <policy name="ElevateNonAdmins_Title" class="Machine" displayName="$(string.ElevateNonAdmins_Title)" explainText="$(string.ElevateNonAdmins_Help)" key="Software\Policies\Microsoft\Windows\WindowsUpdate" valueName="ElevateNonAdmins">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Win2kSP3_Or_XPSP1_Through_Win81_or_Server2012R2" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="ImmediateInstall_Title" class="Machine" displayName="$(string.ImmediateInstall_Title)" explainText="$(string.ImmediateInstall_Help)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="AutoInstallMinorUpdates">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Win2kSP3_Or_XPSP1_Through_Win81_or_Server2012R2" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="IncludeRecommendedUpdates_Title" class="Machine" displayName="$(string.IncludeRecommendedUpdates_Title)" explainText="$(string.IncludeRecommendedUpdates_Help)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="IncludeRecommendedUpdates">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_WindowsVista_Through_Win81_or_Server2012R2" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="FeaturedSoftwareNotification_Title" class="Machine" displayName="$(string.FeaturedSoftwareNotification_Title)" explainText="$(string.FeaturedSoftwareNotification_Help)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="EnableFeaturedSoftware">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows7ToVista" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="AUPowerManagement_Title"
            class="Machine"
            displayName="$(string.AUPowerManagement_Title)"
            explainText="$(string.AUPowerManagement_Help)"
            key="Software\Policies\Microsoft\Windows\WindowsUpdate"
            valueName="AUPowerManagement">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows7ToVistaAndWindows10" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="NoAutoRebootWithLoggedOnUsers_Title" class="Machine" displayName="$(string.NoAutoRebootWithLoggedOnUsers_Title)" explainText="$(string.NoAutoRebootWithLoggedOnUsers_Help)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="NoAutoRebootWithLoggedOnUsers">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Win2kSP3_Or_XPSP1" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="AlwaysAutoRebootAtScheduledTime"
            class="Machine"
            displayName="$(string.AlwaysAutoRebootAtScheduledTime_Title)"
            explainText="$(string.AlwaysAutoRebootAtScheduledTime_Help)"
            presentation="$(presentation.AlwaysAutoRebootAtScheduledTime_Presentation)"
            key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU"
            valueName="AlwaysAutoRebootAtScheduledTime">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows8" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
       <decimal id="AlwaysAutoRebootAtScheduledTime_Minutes" valueName="AlwaysAutoRebootAtScheduledTimeMinutes" required="true" minValue="15" maxValue="180" />
      </elements>
    </policy>
    <policy name="RebootRelaunchTimeout_Title" class="Machine" displayName="$(string.RebootRelaunchTimeout_Title)" explainText="$(string.RebootRelaunchTimeout_Help)" presentation="$(presentation.RebootRelaunchTimeout_Title)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="RebootRelaunchTimeoutEnabled">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows7_To_Win2kSP3_Or_XPSP1" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <decimal id="RebootRelaunchTimeout_Minutes3" valueName="RebootRelaunchTimeout" required="true" minValue="1" maxValue="1440" />
      </elements>
    </policy>
    <policy name="RebootWarningTimeout_Title" class="Machine" displayName="$(string.RebootWarningTimeout_Title)" explainText="$(string.RebootWarningTimeout_Help)" presentation="$(presentation.RebootWarningTimeout_Title)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="RebootWarningTimeoutEnabled">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows7_To_Win2kSP3_Or_XPSP1" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <decimal id="RebootWarningTimeout_Minutes3" valueName="RebootWarningTimeout" required="true" minValue="1" maxValue="30" />
      </elements>
    </policy>
    <policy name="RescheduleWaitTime_Title" class="Machine" displayName="$(string.RescheduleWaitTime_Title)" explainText="$(string.RescheduleWaitTime_Help)" presentation="$(presentation.RescheduleWaitTime_Title)" key="Software\Policies\Microsoft\Windows\WindowsUpdate\AU" valueName="RescheduleWaitTimeEnabled">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows7_To_Win2kSP3_Or_XPSP1" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <decimal id="RescheduleWaitTime_Minutes2" valueName="RescheduleWaitTime" required="true" minValue="1" maxValue="60" />
      </elements>
    </policy>
    <policy name="TargetGroup_Title" class="Machine" displayName="$(string.TargetGroup_Title)" explainText="$(string.TargetGroup_Help)" presentation="$(presentation.TargetGroup_Title)" key="Software\Policies\Microsoft\Windows\WindowsUpdate" valueName="TargetGroupEnabled">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Win2kSP3_Or_XPSP1_NoWinRT" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <text id="TargetGroup_Name" valueName="TargetGroup" required="true" />
      </elements>
    </policy>
    <policy name="TrustedPublisher_Title" class="Machine" displayName="$(string.TrustedPublisher_Title)" explainText="$(string.TrustedPublisher_Help)" key="Software\Policies\Microsoft\Windows\WindowsUpdate" valueName="AcceptTrustedPublisherCerts">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_WindowsXPSP1_NoWinRT" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="DoNotConnectToWindowsUpdateInternetLocations"
            class="Machine"
            displayName="$(string.DoNotConnectToWindowsUpdateInternetLocations_Title)"
            explainText="$(string.DoNotConnectToWindowsUpdateInternetLocations_Help)"
            key="Software\Policies\Microsoft\Windows\WindowsUpdate"
            valueName="DoNotConnectToWindowsUpdateInternetLocations">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_6_3" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="TargetReleaseVersion"
        class="Machine"
        displayName="$(string.TargetReleaseVersion_Title)"
        explainText="$(string.TargetReleaseVersion_Help)"
        key="Software\Policies\Microsoft\Windows\WindowsUpdate"
        presentation="$(presentation.TargetReleaseVersion)"
        valueName="TargetReleaseVersion">
        <parentCategory ref="DeferUpdateCat" />
        <supportedOn ref="windows:SUPPORTED_Windows_10_0_NOARM" />
        <enabledValue>
            <decimal value="1" />
        </enabledValue>
        <disabledValue>
            <decimal value="0" />
        </disabledValue>
        <elements>
          <text id="ProductVersionId" valueName="ProductVersion" maxLength="25" />
          <text id="TargetReleaseVersionId" valueName="TargetReleaseVersionInfo" maxLength="4" />
        </elements>
    </policy>
    <policy name="DisableWUfBSafeguards" class="Machine" displayName="$(string.DisableWUfBSafeguards)" explainText="$(string.DisableWUfBSafeguards_Help)" key="Software\Policies\Microsoft\Windows\WindowsUpdate" valueName="DisableWUfBSafeguards">
      <parentCategory ref="DeferUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_RS6" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="ManagePreviewBuilds"
      class="Machine"
      displayName="$(string.ManagePreviewBuilds_Title)"
      explainText="$(string.ManagePreviewBuilds_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.ManagePreviewBuilds)"
      valueName="ManagePreviewBuildsPolicyValue">
      <parentCategory ref="DeferUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_RS3" />
      <enabledValue>
        <decimal value="2" />
      </enabledValue>
      <disabledValue>
        <decimal value="1" />
      </disabledValue>
      <elements>
        <enum id="BranchReadinessLevelId" valueName="BranchReadinessLevel" required="true">
          <item displayName="$(string.DevChannel)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.BetaChannel)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.ReleasePreviewChannel)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.ReleasePreviewQualityOnly)">
            <value>
              <decimal value="64" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="DeferFeatureUpdates"
        class="Machine"
        displayName="$(string.DeferFeatureUpdates_Title)"
        explainText="$(string.DeferFeatureUpdates_Help)"
        key="Software\Policies\Microsoft\Windows\WindowsUpdate"
        presentation="$(presentation.DeferFeatureUpdates)"
        valueName="DeferFeatureUpdates">
        <parentCategory ref="DeferUpdateCat" />
        <supportedOn ref="windows:SUPPORTED_Windows_10_0_NOARM" />
        <enabledValue>
            <decimal value="1" />
        </enabledValue>
        <disabledValue>
            <decimal value="0" />
        </disabledValue>
        <elements>
          <decimal id="DeferFeatureUpdatesPeriodId" valueName="DeferFeatureUpdatesPeriodInDays" minValue="0" maxValue="365" />
          <text id="PauseFeatureUpdatesStartId" valueName="PauseFeatureUpdatesStartTime" maxLength="10" />
        </elements>
    </policy>
    <policy name="DeferQualityUpdates"
        class="Machine"
        displayName="$(string.DeferQualityUpdates_Title)"
        explainText="$(string.DeferQualityUpdates_Help)"
        key="Software\Policies\Microsoft\Windows\WindowsUpdate"
        presentation="$(presentation.DeferQualityUpdates)"
        valueName="DeferQualityUpdates">
        <parentCategory ref="DeferUpdateCat" />
        <supportedOn ref="windows:SUPPORTED_Windows_10_0_NOARM" />
        <enabledValue>
            <decimal value="1" />
        </enabledValue>
        <disabledValue>
            <decimal value="0" />
        </disabledValue>
        <elements>
          <decimal id="DeferQualityUpdatesPeriodId" valueName="DeferQualityUpdatesPeriodInDays" minValue="0" maxValue="30" />
          <text id="PauseQualityUpdatesStartId" valueName="PauseQualityUpdatesStartTime" maxLength="10" />
        </elements>
    </policy>
    <policy name="ExcludeWUDriversInQualityUpdate"
        class="Machine"
        displayName="$(string.ExcludeWUDriversInQualityUpdate_Title)"
        explainText="$(string.ExcludeWUDriversInQualityUpdate_Help)"
        key="Software\Policies\Microsoft\Windows\WindowsUpdate"
        valueName="ExcludeWUDriversInQualityUpdate">
        <parentCategory ref="WindowsUpdateCat" />
        <supportedOn ref="windows:SUPPORTED_Windows_10_0_NOARM" />
        <enabledValue>
            <decimal value="1" />
        </enabledValue>
        <disabledValue>
            <decimal value="0" />
        </disabledValue>
    </policy>
    <policy name="ActiveHours"
    class="Machine"
    displayName="$(string.ActiveHours_Title)"
    explainText="$(string.ActiveHours_Help)"
    key="Software\Policies\Microsoft\Windows\WindowsUpdate"
    presentation="$(presentation.ActiveHours_Title)"
    valueName="SetActiveHours">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_NOARM" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="ActiveHoursStartTime" valueName="ActiveHoursStart" required="true">
          <item displayName="$(string.ActiveHoursTime12AM)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime1AM)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime2AM)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime3AM)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime4AM)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime5AM)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime6AM)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime7AM)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime8AM)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime9AM)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime10AM)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime11AM)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime12PM)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime1PM)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime2PM)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime3PM)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime4PM)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime5PM)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime6PM)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime7PM)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime8PM)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime9PM)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime10PM)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime11PM)">
            <value>
              <decimal value="23" />
            </value>
          </item>
        </enum>
        <enum id="ActiveHoursEndTime" valueName="ActiveHoursEnd" required="true">
          <item displayName="$(string.ActiveHoursTime12AM)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime1AM)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime2AM)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime3AM)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime4AM)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime5AM)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime6AM)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime7AM)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime8AM)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime9AM)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime10AM)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime11AM)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime12PM)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime1PM)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime2PM)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime3PM)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime4PM)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime5PM)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime6PM)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime7PM)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime8PM)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime9PM)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime10PM)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursTime11PM)">
            <value>
              <decimal value="23" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="AutoRestartDeadline"
      class="Machine"
      displayName="$(string.AutoRestartDeadline_Title)"
      explainText="$(string.AutoRestartDeadline_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.AutoRestartDeadline_Title)"
      valueName="SetAutoRestartDeadline">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="AutoRestartDeadline" valueName="AutoRestartDeadlinePeriodInDays" required="true">
          <item displayName="$(string.AutoRestartDeadlineDay2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay5)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay6)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay7)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay19)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay20)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay21)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay22)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay23)">
            <value>
              <decimal value="23" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay24)">
            <value>
              <decimal value="24" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay25)">
            <value>
              <decimal value="25" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay26)">
            <value>
              <decimal value="26" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay27)">
            <value>
              <decimal value="27" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay28)">
            <value>
              <decimal value="28" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay29)">
            <value>
              <decimal value="29" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
        </enum>
        <enum id="AutoRestartDeadlineForFeatureUpdates" valueName="AutoRestartDeadlinePeriodInDaysForFeatureUpdates" required="true">
          <item displayName="$(string.AutoRestartDeadlineDay2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay5)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay6)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay7)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay19)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay20)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay21)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay22)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay23)">
            <value>
              <decimal value="23" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay24)">
            <value>
              <decimal value="24" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay25)">
            <value>
              <decimal value="25" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay26)">
            <value>
              <decimal value="26" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay27)">
            <value>
              <decimal value="27" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay28)">
            <value>
              <decimal value="28" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay29)">
            <value>
              <decimal value="29" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartDeadlineDay30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="DisableUXWUAccess"
      class="Machine"
      displayName="$(string.DisableUXWUAccess_Title)"
      explainText="$(string.DisableUXWUAccess_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      valueName="SetDisableUXWUAccess">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_NOARM" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="DisablePauseUXAccess"
      class="Machine"
      displayName="$(string.DisablePauseUXAccess_Title)"
      explainText="$(string.DisablePauseUXAccess_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      valueName="SetDisablePauseUXAccess">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_RS5" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="ActiveHoursMaxRange"
      class="Machine"
      displayName="$(string.ActiveHoursMaxRange_Title)"
      explainText="$(string.ActiveHoursMaxRange_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.ActiveHoursMaxRange_Title)"
      valueName="SetActiveHoursMaxRange">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_NOARM" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="ActiveHoursMaxRange" valueName="ActiveHoursMaxRange" required="true">
          <item displayName="$(string.ActiveHoursMaxRange8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.ActiveHoursMaxRange18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="AutoRestartRequiredNotificationDismissal"
      class="Machine"
      displayName="$(string.AutoRestartRequiredNotificationDismissal_Title)"
      explainText="$(string.AutoRestartRequiredNotificationDismissal_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.AutoRestartRequiredNotificationDismissal_Title)"
      valueName="SetAutoRestartRequiredNotificationDismissal">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="AutoRestartRequiredNotificationDismissal" valueName="AutoRestartRequiredNotificationDismissal" required="true">
          <item displayName="$(string.AutoDismissal)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.UserAction)">
            <value>
              <decimal value="2" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="AutoRestartNotificationConfig"
      class="Machine"
      displayName="$(string.AutoRestartNotificationConfig_Title)"
      explainText="$(string.AutoRestartNotificationConfig_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.AutoRestartNotificationConfig_Title)"
      valueName="SetAutoRestartNotificationConfig">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="AutoRestartNotificationSchd" valueName="AutoRestartNotificationSchedule" required="true">
          <item displayName="$(string.AutoRestartNotificationSchd15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartNotificationSchd30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartNotificationSchd60)">
            <value>
              <decimal value="60" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartNotificationSchd120)">
            <value>
              <decimal value="120" />
            </value>
          </item>
          <item displayName="$(string.AutoRestartNotificationSchd240)">
            <value>
              <decimal value="240" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="AutoRestartNotificationDisable"
      class="Machine"
      displayName="$(string.AutoRestartNotificationDisable_Title)"
      explainText="$(string.AutoRestartNotificationDisable_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      valueName="SetAutoRestartNotificationDisable">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="RestartWarnRemind"
      class="Machine"
      displayName="$(string.RestartWarningSchd_Title)"
      explainText="$(string.RestartWarningSchd_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.RestartWarningSchd_Title)"
      valueName="SetRestartWarningSchd">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="RestartWarnRemind" valueName="ScheduleRestartWarning" required="true">
          <item displayName="$(string.RestartWarnRemindHours2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.RestartWarnRemindHours4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.RestartWarnRemindHours8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.RestartWarnRemindHours12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.RestartWarnRemindHours24)">
            <value>
              <decimal value="24" />
            </value>
          </item>
        </enum>
        <enum id="RestartWarn" valueName="ScheduleImminentRestartWarning" required="true">
          <item displayName="$(string.RestartWarnMins15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.RestartWarnMins30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
          <item displayName="$(string.RestartWarnMins60)">
            <value>
              <decimal value="60" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="EngagedRestartTransitionSchedule"
      class="Machine"
      displayName="$(string.EngagedRestartTransitionSchedule_Title)"
      explainText="$(string.EngagedRestartTransitionSchedule_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.EngagedRestartTransitionSchedule_Title)"
      valueName="SetEngagedRestartTransitionSchedule">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="EngagedRestartTransitionSchedule" valueName="EngagedRestartTransitionSchedule" required="true">
          <item displayName="$(string.EngagedRestart_0)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_1)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_5)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_6)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_7)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_19)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_20)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_21)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_22)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_23)">
            <value>
              <decimal value="23" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_24)">
            <value>
              <decimal value="24" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_25)">
            <value>
              <decimal value="25" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_26)">
            <value>
              <decimal value="26" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_27)">
            <value>
              <decimal value="27" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_28)">
            <value>
              <decimal value="28" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_29)">
            <value>
              <decimal value="29" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
        </enum>
        <enum id="EngagedRestartSnoozeSchedule" valueName="EngagedRestartSnoozeSchedule" required="true">
          <item displayName="$(string.EngagedRestartSnooze_1)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestartSnooze_2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestartSnooze_3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
        </enum>
        <enum id="EngagedRestartDeadline" valueName="EngagedRestartDeadline" required="true">
          <item displayName="$(string.EngagedRestart_0)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_5)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_6)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_7)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_19)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_20)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_21)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_22)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_23)">
            <value>
              <decimal value="23" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_24)">
            <value>
              <decimal value="24" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_25)">
            <value>
              <decimal value="25" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_26)">
            <value>
              <decimal value="26" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_27)">
            <value>
              <decimal value="27" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_28)">
            <value>
              <decimal value="28" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_29)">
            <value>
              <decimal value="29" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
        </enum>
        <enum id="EngagedRestartTransitionScheduleForFeatureUpdates" valueName="EngagedRestartTransitionScheduleForFeatureUpdates" required="true">
          <item displayName="$(string.EngagedRestart_0)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_1)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_5)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_6)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_7)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_19)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_20)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_21)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_22)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_23)">
            <value>
              <decimal value="23" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_24)">
            <value>
              <decimal value="24" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_25)">
            <value>
              <decimal value="25" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_26)">
            <value>
              <decimal value="26" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_27)">
            <value>
              <decimal value="27" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_28)">
            <value>
              <decimal value="28" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_29)">
            <value>
              <decimal value="29" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
        </enum>
        <enum id="EngagedRestartSnoozeScheduleForFeatureUpdates" valueName="EngagedRestartSnoozeScheduleForFeatureUpdates" required="true">
          <item displayName="$(string.EngagedRestartSnooze_1)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestartSnooze_2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestartSnooze_3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
        </enum>
        <enum id="EngagedRestartDeadlineForFeatureUpdates" valueName="EngagedRestartDeadlineForFeatureUpdates" required="true">
          <item displayName="$(string.EngagedRestart_0)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_2)">
            <value>
              <decimal value="2" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_3)">
            <value>
              <decimal value="3" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_4)">
            <value>
              <decimal value="4" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_5)">
            <value>
              <decimal value="5" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_6)">
            <value>
              <decimal value="6" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_7)">
            <value>
              <decimal value="7" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_8)">
            <value>
              <decimal value="8" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_9)">
            <value>
              <decimal value="9" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_10)">
            <value>
              <decimal value="10" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_11)">
            <value>
              <decimal value="11" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_12)">
            <value>
              <decimal value="12" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_13)">
            <value>
              <decimal value="13" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_14)">
            <value>
              <decimal value="14" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_15)">
            <value>
              <decimal value="15" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_16)">
            <value>
              <decimal value="16" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_17)">
            <value>
              <decimal value="17" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_18)">
            <value>
              <decimal value="18" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_19)">
            <value>
              <decimal value="19" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_20)">
            <value>
              <decimal value="20" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_21)">
            <value>
              <decimal value="21" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_22)">
            <value>
              <decimal value="22" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_23)">
            <value>
              <decimal value="23" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_24)">
            <value>
              <decimal value="24" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_25)">
            <value>
              <decimal value="25" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_26)">
            <value>
              <decimal value="26" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_27)">
            <value>
              <decimal value="27" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_28)">
            <value>
              <decimal value="28" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_29)">
            <value>
              <decimal value="29" />
            </value>
          </item>
          <item displayName="$(string.EngagedRestart_30)">
            <value>
              <decimal value="30" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="SetEDURestart"
            class="Machine"
            displayName="$(string.SetEDURestart_Title)"
            explainText="$(string.SetEDURestart_Help)"
            key="Software\Policies\Microsoft\Windows\WindowsUpdate"
            valueName="SetEDURestart">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="AllowAutoWindowsUpdateDownloadOverMeteredNetwork"
            class="Machine"
            displayName="$(string.AllowAutoWindowsUpdateDownloadOverMeteredNetwork_Title)"
            explainText="$(string.AllowAutoWindowsUpdateDownloadOverMeteredNetwork_Help)"
            key="Software\Policies\Microsoft\Windows\WindowsUpdate"
            valueName="AllowAutoWindowsUpdateDownloadOverMeteredNetwork">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="DisableDualScan"
        class="Machine"
        displayName="$(string.DisableDualScan_Title)"
        explainText="$(string.DisableDualScan_Help)"
        key="Software\Policies\Microsoft\Windows\WindowsUpdate"
        valueName="DisableDualScan">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_RS1" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
    <policy name="UpdateNotificationLevel"
        class="Machine"
        displayName="$(string.UpdateNotificationLevel_Title)"
        explainText="$(string.UpdateNotificationLevel_Help)"
        key="Software\Policies\Microsoft\Windows\WindowsUpdate"
        presentation="$(presentation.UpdateNotificationLevel_Title)"
        valueName="SetUpdateNotificationLevel">
      <parentCategory ref="WindowsUpdateCat" />
      <supportedOn ref="WU_SUPPORTED_Windows_Server_2019_Windows_10_0_1809" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="UpdateNotificationLevel" valueName="UpdateNotificationLevel" required="true">
          <item displayName="$(string.DefaultNotification)">
            <value>
              <decimal value="0" />
            </value>
          </item>
          <item displayName="$(string.ExcludingRebootWarnings)">
            <value>
              <decimal value="1" />
            </value>
          </item>
          <item displayName="$(string.IncludingRebootWarnings)">
            <value>
              <decimal value="2" />
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="ComplianceDeadline"
      class="Machine"
      displayName="$(string.ComplianceDeadline_Title)"
      explainText="$(string.ComplianceDeadline_Help)"
      key="Software\Policies\Microsoft\Windows\WindowsUpdate"
      presentation="$(presentation.ComplianceDeadline_Title)"
      valueName="SetComplianceDeadline">
      <parentCategory ref="WindowsUpdateCat"/>
      <supportedOn ref="windows:SUPPORTED_Windows_10_0_RS3" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
      <elements>
        <enum id="ConfigureDeadlineForQualityUpdates" valueName="ConfigureDeadlineForQualityUpdates" required="true">
          <item displayName="$(string.ComplianceDeadlineDay0)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay1)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay2)">
            <value>
              <decimal value="2"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay3)">
            <value>
              <decimal value="3"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay4)">
            <value>
              <decimal value="4"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay5)">
            <value>
              <decimal value="5"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay6)">
            <value>
              <decimal value="6"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay7)">
            <value>
              <decimal value="7"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay8)">
            <value>
              <decimal value="8"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay9)">
            <value>
              <decimal value="9"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay10)">
            <value>
              <decimal value="10"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay11)">
            <value>
              <decimal value="11"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay12)">
            <value>
              <decimal value="12"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay13)">
            <value>
              <decimal value="13"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay14)">
            <value>
              <decimal value="14"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay15)">
            <value>
              <decimal value="15"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay16)">
            <value>
              <decimal value="16"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay17)">
            <value>
              <decimal value="17"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay18)">
            <value>
              <decimal value="18"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay19)">
            <value>
              <decimal value="19"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay20)">
            <value>
              <decimal value="20"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay21)">
            <value>
              <decimal value="21"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay22)">
            <value>
              <decimal value="22"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay23)">
            <value>
              <decimal value="23"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay24)">
            <value>
              <decimal value="24"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay25)">
            <value>
              <decimal value="25"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay26)">
            <value>
              <decimal value="26"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay27)">
            <value>
              <decimal value="27"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay28)">
            <value>
              <decimal value="28"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay29)">
            <value>
              <decimal value="29"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay30)">
            <value>
              <decimal value="30"/>
            </value>
          </item>
        </enum>
        <enum id="ConfigureDeadlineForFeatureUpdates" valueName="ConfigureDeadlineForFeatureUpdates" required="true">
          <item displayName="$(string.ComplianceDeadlineDay0)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay1)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay2)">
            <value>
              <decimal value="2"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay3)">
            <value>
              <decimal value="3"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay4)">
            <value>
              <decimal value="4"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay5)">
            <value>
              <decimal value="5"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay6)">
            <value>
              <decimal value="6"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay7)">
            <value>
              <decimal value="7"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay8)">
            <value>
              <decimal value="8"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay9)">
            <value>
              <decimal value="9"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay10)">
            <value>
              <decimal value="10"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay11)">
            <value>
              <decimal value="11"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay12)">
            <value>
              <decimal value="12"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay13)">
            <value>
              <decimal value="13"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay14)">
            <value>
              <decimal value="14"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay15)">
            <value>
              <decimal value="15"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay16)">
            <value>
              <decimal value="16"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay17)">
            <value>
              <decimal value="17"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay18)">
            <value>
              <decimal value="18"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay19)">
            <value>
              <decimal value="19"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay20)">
            <value>
              <decimal value="20"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay21)">
            <value>
              <decimal value="21"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay22)">
            <value>
              <decimal value="22"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay23)">
            <value>
              <decimal value="23"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay24)">
            <value>
              <decimal value="24"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay25)">
            <value>
              <decimal value="25"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay26)">
            <value>
              <decimal value="26"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay27)">
            <value>
              <decimal value="27"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay28)">
            <value>
              <decimal value="28"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay29)">
            <value>
              <decimal value="29"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay30)">
            <value>
              <decimal value="30"/>
            </value>
          </item>
        </enum>
        <enum id="ConfigureDeadlineGracePeriod" valueName="ConfigureDeadlineGracePeriod" required="true">
          <item displayName="$(string.ComplianceDeadlineDay0)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay1)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay2)">
            <value>
              <decimal value="2"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay3)">
            <value>
              <decimal value="3"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay4)">
            <value>
              <decimal value="4"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay5)">
            <value>
              <decimal value="5"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay6)">
            <value>
              <decimal value="6"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay7)">
            <value>
              <decimal value="7"/>
            </value>
          </item>
        </enum>
        <enum id="ConfigureDeadlineGracePeriodForFeatureUpdates" valueName="ConfigureDeadlineGracePeriodForFeatureUpdates" required="true">
          <item displayName="$(string.ComplianceDeadlineDay0)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay1)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay2)">
            <value>
              <decimal value="2"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay3)">
            <value>
              <decimal value="3"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay4)">
            <value>
              <decimal value="4"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay5)">
            <value>
              <decimal value="5"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay6)">
            <value>
              <decimal value="6"/>
            </value>
          </item>
          <item displayName="$(string.ComplianceDeadlineDay7)">
            <value>
              <decimal value="7"/>
            </value>
          </item>
        </enum>
        <boolean id="ConfigureDeadlineNoAutoReboot" valueName="ConfigureDeadlineNoAutoReboot" />
      </elements>
    </policy>
  </policies>
</policyDefinitions>
