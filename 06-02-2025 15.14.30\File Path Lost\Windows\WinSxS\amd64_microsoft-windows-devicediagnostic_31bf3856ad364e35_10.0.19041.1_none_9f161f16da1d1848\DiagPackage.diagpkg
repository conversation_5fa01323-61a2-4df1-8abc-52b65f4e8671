<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" SchemaVersion="1.0" Localized="true" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
    <DiagnosticIdentification>
        <ID>DeviceDiagnostic</ID>
        <Version>4.1</Version>
    </DiagnosticIdentification>
    <DisplayInformation>
        <Parameters/>
      <Name>@diagpackage.dll,-1</Name>
      <Description>@diagpackage.dll,-2</Description>
    </DisplayInformation>
    <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
    <PowerShellVersion>2.0</PowerShellVersion>
    <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
    <Troubleshooter>
        <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>TS_HardwareDeviceMain.ps1</FileName>
            <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
    </Troubleshooter>
    <Rootcauses>
        <Rootcause>
            <ID>RC_PendingRestart</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-2002</Name>
                <Description></Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_PendingRestart</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-2003</Name>
                        <Description></Description>
                    </DisplayInformation>
                    <RequiresConsent>true</RequiresConsent>
                    <Script>
                        <Parameters/>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_PendingRestart.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_DeviceDisabled</ID>
            <DisplayInformation>
                <Parameters>
                    <Parameter>
                        <Name>DeviceName</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <Name>@diagpackage.dll,-33</Name>
                <Description>@diagpackage.dll,-34</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_DeviceDisabled</ID>
                    <DisplayInformation>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <Name>@diagpackage.dll,-35</Name>
                        <Description>@diagpackage.dll,-36</Description>
                    </DisplayInformation>
                    <RequiresConsent>true</RequiresConsent>
                    <Script>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                            <Parameter>
                                <Name>DeviceID</Name>
                                <DefaultValue/>
                            </Parameter>
                            <Parameter>
                                <Name>ErrorCode</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>false</RequiresInteractivity>
                        <FileName>RS_EnableDevice.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier>
                <Script>
                    <Parameters>
                        <Parameter>
                            <Name>DeviceName</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>DeviceID</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>Action</Name>
                            <DefaultValue>Verify</DefaultValue>
                        </Parameter>
                    </Parameters>
                    <ProcessArchitecture>Any</ProcessArchitecture>
                    <RequiresElevation>true</RequiresElevation>
                    <RequiresInteractivity>false</RequiresInteractivity>
                    <FileName>TS_DeviceDisabled.ps1</FileName>
                    <ExtensionPoint/>
                </Script>
                <ExtensionPoint/>
            </Verifier>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_DeviceDriverNeedsUpdate</ID>
            <DisplayInformation>
                <Parameters>
                    <Parameter>
                        <Name>DeviceName</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <Name>@diagpackage.dll,-43</Name>
                <Description>@diagpackage.dll,-44</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_DeviceDriverNeedsUpdate</ID>
                    <DisplayInformation>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <Name>@diagpackage.dll,-45</Name>
                        <Description>@diagpackage.dll,-46</Description>
                    </DisplayInformation>
                    <RequiresConsent>true</RequiresConsent>
                    <Script>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                            <Parameter>
                                <Name>DeviceID</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_DeviceDriverNeedsUpdate.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier>
                <Script>
                    <Parameters>
                        <Parameter>
                            <Name>DeviceName</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>DeviceID</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>Action</Name>
                            <DefaultValue>Verify</DefaultValue>
                        </Parameter>
                    </Parameters>
                    <ProcessArchitecture>Any</ProcessArchitecture>
                    <RequiresElevation>false</RequiresElevation>
                    <RequiresInteractivity>false</RequiresInteractivity>
                    <FileName>TS_DeviceDriverNeedsUpdate.ps1</FileName>
                    <ExtensionPoint/>
                </Script>
                <ExtensionPoint/>
            </Verifier>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_NotWorkingProperly</ID>
            <DisplayInformation>
                <Parameters>
                    <Parameter>
                        <Name>DeviceName</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <Name>@diagpackage.dll,-3</Name>
                <Description>@diagpackage.dll,-4</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_NotWorkingProperly</ID>
                    <DisplayInformation>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <Name>@diagpackage.dll,-5</Name>
                        <Description>@diagpackage.dll,-6</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                            <Parameter>
                                <Name>DeviceID</Name>
                                <DefaultValue/>
                            </Parameter>
                            <Parameter>
                                <Name>ErrorCode</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_RescanAllDevices.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_DriverNotFound</ID>
            <DisplayInformation>
                <Parameters>
                    <Parameter>
                        <Name>DeviceName</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <Name>@diagpackage.dll,-9</Name>
                <Description>@diagpackage.dll,-10</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_RetryInstall</ID>
                    <DisplayInformation>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <Name>@diagpackage.dll,-13</Name>
                        <Description>@diagpackage.dll,-14</Description>
                    </DisplayInformation>
                    <RequiresConsent>true</RequiresConsent>
                    <Script>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                            <Parameter>
                                <Name>DeviceID</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_DeviceDriverNeedsUpdate.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
                <Resolver>
                    <ID>RS_QueryPRSSolution</ID>
                    <DisplayInformation>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <Name>@diagpackage.dll,-11</Name>
                        <Description>@diagpackage.dll,-12</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script>
                        <Parameters>
                            <Parameter>
                                <Name>DeviceName</Name>
                                <DefaultValue/>
                            </Parameter>
                            <Parameter>
                                <Name>DeviceID</Name>
                                <DefaultValue/>
                            </Parameter>
                        </Parameters>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_DriverNotFound.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier>
                <Script>
                    <Parameters>
                        <Parameter>
                            <Name>DeviceName</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>DeviceID</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>Action</Name>
                            <DefaultValue>Verify</DefaultValue>
                        </Parameter>
                    </Parameters>
                    <ProcessArchitecture>Any</ProcessArchitecture>
                    <RequiresElevation>false</RequiresElevation>
                    <RequiresInteractivity>true</RequiresInteractivity>
                    <FileName>TS_DriverNotFound.ps1</FileName>
                    <ExtensionPoint/>
                </Script>
                <ExtensionPoint/>
            </Verifier>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_RescanAllDevices</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-20</Name>
                <Description/>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_RescanAllDevices</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-22</Name>
                        <Description>@diagpackage.dll,-23</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script>
                        <Parameters/>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>false</RequiresInteractivity>
                        <FileName>RS_RescanAllDevices.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier>
                <Script>
                    <Parameters/>
                    <ProcessArchitecture>Any</ProcessArchitecture>
                    <RequiresElevation>true</RequiresElevation>
                    <RequiresInteractivity>false</RequiresInteractivity>
                    <FileName>VF_RescanAllDevices.ps1</FileName>
                    <ExtensionPoint/>
                </Script>
                <ExtensionPoint/>
            </Verifier>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_CheckDriversOnInstall</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-60</Name>
                <Description>@diagpackage.dll,-61</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_CheckDriversOnInstall</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-62</Name>
                        <Description>@diagpackage.dll,-63</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script>
                        <Parameters/>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>true</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_CheckDriversOnInstall.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier>
                <Script>
                    <Parameters/>
                    <ProcessArchitecture>Any</ProcessArchitecture>
                    <RequiresElevation>true</RequiresElevation>
                    <RequiresInteractivity>false</RequiresInteractivity>
                    <FileName>TS_CheckDriversOnInstall.ps1</FileName>
                    <ExtensionPoint/>
                </Script>
                <ExtensionPoint/>
            </Verifier>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_InformCustomer</ID>
            <DisplayInformation>
              <Parameters>
                <Parameter>
                  <Name>DeviceName</Name>
                  <DefaultValue/>
                </Parameter>
              </Parameters>
                <Name>@diagpackage.dll,-1129</Name>
                <Description>@diagpackage.dll,-1130</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_InformCustomer</ID>
                    <DisplayInformation>
                      <Parameters>
                        <Parameter>
                          <Name>DeviceName</Name>
                          <DefaultValue/>
                        </Parameter>
                      </Parameters>
                        <Name>@diagpackage.dll,-1131</Name>
                        <Description>@diagpackage.dll,-1132</Description>
                    </DisplayInformation>
                    <RequiresConsent>true</RequiresConsent>
                    <Script>
                      <Parameters>
                        <Parameter>
                          <Name>DeviceName</Name>
                          <DefaultValue/>
                        </Parameter>
                        <Parameter>
                          <Name>DeviceID</Name>
                          <DefaultValue/>
                        </Parameter>
                      </Parameters>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>false</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_InformCustomer.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
    </Rootcauses>
    <Interactions>
        <SingleResponseInteractions>
            <SingleResponseInteraction>
                <AllowDynamicResponses>false</AllowDynamicResponses>
                <Choices>
                    <Choice>
                        <DisplayInformation>
                            <Parameters>
                            </Parameters>
                            <Name>@diagpackage.dll,-19851</Name>
                            <Description>@diagpackage.dll,-19841</Description>
                        </DisplayInformation>
                        <Value>Y</Value>
                        <ExtensionPoint/>
                    </Choice>
                    <Choice>
                        <DisplayInformation>
                            <Parameters/>
                            <Name>@diagpackage.dll,-119715</Name>
                            <Description>@diagpackage.dll,-19842</Description>
                        </DisplayInformation>
                        <Value>N</Value>
                        <ExtensionPoint/>
                    </Choice>
                </Choices>
                <ID>INT_INSTALLSETTINGS</ID>
                <DisplayInformation>
                    <Parameters>
                    </Parameters>
                    <Name>@diagpackage.dll,-19825</Name>
                    <Description>@diagpackage.dll,-19826</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint>
                    <CommandLinks/>
                </ExtensionPoint>
            </SingleResponseInteraction>
            <SingleResponseInteraction>
                <AllowDynamicResponses>true</AllowDynamicResponses>
                <Choices></Choices>
                <ID>INT_SelectDevices</ID>
                <DisplayInformation>
                    <Parameters></Parameters>
                    <Name>@diagpackage.dll,-7</Name>
                    <Description>@diagpackage.dll,-8</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint>
                    <NoCache/>
                    <InteractionIcon>info</InteractionIcon>
                    <CommandLinks/>
                </ExtensionPoint>
            </SingleResponseInteraction>
         </SingleResponseInteractions>
        <MultipleResponseInteractions/>
        <TextInteractions>
            <TextInteraction>
                <RegularExpression/>
                <ID>IT_SelectDevice</ID>
                <DisplayInformation>
                    <Parameters/>
                    <Name>@diagpackage.dll,-77</Name>
                    <Description>@diagpackage.dll,-88</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint>
                    <NoUI/>
                </ExtensionPoint>
            </TextInteraction>
        </TextInteractions>
        <PauseInteractions>
            <PauseInteraction>
                <ID>INT_RebootSystem</ID>
                <DisplayInformation>
                    <Parameters>
                    </Parameters>
                    <Name>@diagpackage.dll,-2000</Name>
                    <Description>@diagpackage.dll,-2001</Description>
                </DisplayInformation>
                <ContextParameters>
                </ContextParameters>
                <ExtensionPoint/>
            </PauseInteraction>
            <PauseInteraction>
                <ID>INT_DeviceNotConnected</ID>
                <DisplayInformation>
                    <Parameters>
                        <Parameter>
                            <Name>DeviceName</Name>
                            <DefaultValue/>
                        </Parameter>
                    </Parameters>
                    <Name>@diagpackage.dll,-14222</Name>
                    <Description>@diagpackage.dll,-14223</Description>
                </DisplayInformation>
                <ContextParameters></ContextParameters>
                <ExtensionPoint/>
            </PauseInteraction>
            <PauseInteraction>
            <ID>INT_Dynamic</ID>
            <DisplayInformation>
              <Parameters>
                <Parameter>
                  <Name>Title</Name>
                  <DefaultValue>%Title%</DefaultValue>
                </Parameter>
                <Parameter>
                  <Name>DeviceName</Name>
                  <DefaultValue>%DeviceName%</DefaultValue>
                </Parameter>
                <Parameter>
                  <Name>ErrorCode</Name>
                  <DefaultValue>%ErrorCode%</DefaultValue>
                </Parameter>
                <Parameter>
                  <Name>Desc</Name>
                  <DefaultValue>%Desc%</DefaultValue>
                </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-14224</Name>
              <Description>@diagpackage.dll,-14225</Description>
            </DisplayInformation>
            <ContextParameters>
            </ContextParameters>
            <ExtensionPoint/>
          </PauseInteraction>
        </PauseInteractions>
        <LaunchUIInteractions>
            <LaunchUIInteraction>
                <Parameters>
                    <Parameter>
                        <Name>DeviceID</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <CommandLine>rundll32.exe devmgr.dll, DeviceProperties_RunDLL /DeviceId %DeviceID%</CommandLine>
                <ID>IT_OpenProblemWizard</ID>
                <DisplayInformation>
                    <Parameters>
                        <Parameter>
                            <Name>DeviceName</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>Title</Name>
                            <DefaultValue/>
                        </Parameter>
                        <Parameter>
                            <Name>Hint</Name>
                            <DefaultValue/>
                        </Parameter>
                    </Parameters>
                    <Name>@diagpackage.dll,-50</Name>
                    <Description>@diagpackage.dll,-51</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint>
                    <CommandLinks/>
                    <ButtonText>@diagpackage.dll,-17</ButtonText>
                </ExtensionPoint>
            </LaunchUIInteraction>
            <LaunchUIInteraction>
            <Parameters/>
            <CommandLine>control.exe /name Microsoft.WindowsUpdate</CommandLine>
            <ID>INT_OpenWUUpdate</ID>
            <DisplayInformation>
              <Parameters>
                <Parameter>
                  <Name>OEM</Name>
                  <DefaultValue>-</DefaultValue>
                </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-1111160</Name>
              <Description>@diagpackage.dll,-1111161</Description>
            </DisplayInformation>
            <ContextParameters/>
            <ExtensionPoint>
              <CommandLinks/>
              <ButtonText>@diagpackage.dll,-1111162</ButtonText>
            </ExtensionPoint>
          </LaunchUIInteraction>          
            <LaunchUIInteraction>
                <Parameters>
                    <Parameter>
                        <Name>ReportLocation</Name>
                        <DefaultValue/>
                    </Parameter>
                    <Parameter>
                        <Name>ReportType</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <CommandLine>WerMgr.exe -launchresponse "%ReportLocation%" %ReportType%</CommandLine>
                <ID>IT_OpenPRSSolution</ID>
                <DisplayInformation>
                    <Parameters>
                        <Parameter>
                            <Name>DeviceName</Name>
                            <DefaultValue/>
                        </Parameter>
                    </Parameters>
                    <Name>@diagpackage.dll,-15</Name>
                    <Description>@diagpackage.dll,-16</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint>
                    <CommandLinks/>
                    <ButtonText>@diagpackage.dll,-19</ButtonText>
                </ExtensionPoint>
            </LaunchUIInteraction>
            <LaunchUIInteraction>
            <Parameters/>
            <CommandLine>ms-contact-support:</CommandLine>
            <ID>INT_ProblemWithDifferentDevice</ID>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-888</Name>
              <Description>@diagpackage.dll,-898</Description>
            </DisplayInformation>
            <ContextParameters/>
            <ExtensionPoint>
              <ButtonText>@diagpackage.dll,-1368</ButtonText>
            </ExtensionPoint>
          </LaunchUIInteraction>
        </LaunchUIInteractions>
    </Interactions>
    <ExtensionPoint>
      <Icon>@DiagPackage.dll,-100</Icon>
      <HelpKeywords>@DiagPackage.dll,-1001</HelpKeywords>
      <HelpKeywords>@DiagPackage.dll,-1002</HelpKeywords>
      <HelpKeywords>@DiagPackage.dll,-1004</HelpKeywords>
      <Feedback>
        <ContextId>79</ContextId>
      </Feedback>
    </ExtensionPoint>
</dcmPS:DiagnosticPackage>