<?xml version="1.0" encoding="utf-8"?>
<PackageConfiguration xmlns="http://www.microsoft.com/schemas/dcm/configuration/2008">
    <Execution>
        <Package Path="%windir%\diagnostics\system\Networking">
            <Answers Version="1.0">
                <Interaction ID="IT_EntryPoint">
                    <Value>NetworkAdapter</Value>
                </Interaction>
            </Answers>
        </Package>
        <Name>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-10005</Name>
        <Description>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-10006</Description>
        <Icon>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20003</Icon>
        <Glyph>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-21002</Glyph>
    </Execution>
<Index>
    <Id>NetworkDiagnosticsNetworkAdapter</Id>
    <RequiresAdminPrivileges>false</RequiresAdminPrivileges>
    <PrivacyUrl>http://go.microsoft.com/fwlink/?LinkID=534597</PrivacyUrl>
    <Version>3.0</Version>
    <PublisherName>Microsoft Corporation</PublisherName>
    <Category>@%windir%\system32\DiagCpl.dll,-403</Category>
    <Keyword>@%windir%\system32\DiagCpl.dll,-25</Keyword>
    <Keyword>@%windir%\system32\DiagCpl.dll,-24</Keyword>
    <Keyword>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20001</Keyword>
    <Keyword>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20002</Keyword>
    <Keyword>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20030</Keyword>
    <Keyword>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20031</Keyword>
    <Keyword>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20032</Keyword>
    <Keyword>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20033</Keyword>
</Index>
</PackageConfiguration>
