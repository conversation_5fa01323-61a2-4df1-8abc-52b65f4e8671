<dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>KeyboardDiagnostic</ID>
    <Version>1.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>false</RequiresElevation>
      <RequiresInteractivity>false</RequiresInteractivity>
      <FileName>KeyboardDiagnostic.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_Cicero</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16</Name>
        <Description>@diagpackage.dll,-17</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Cicero</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-18</Name>
            <Description>@diagpackage.dll,-19</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_Cicero.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_Cicero.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
   </Rootcauses>
  <Interactions>
      <SingleResponseInteractions/>
      <MultipleResponseInteractions/>
      <TextInteractions/>
      <PauseInteractions/>
      <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Maintenance/>
    <Icon>@DiagPackage.dll,-1001</Icon>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>