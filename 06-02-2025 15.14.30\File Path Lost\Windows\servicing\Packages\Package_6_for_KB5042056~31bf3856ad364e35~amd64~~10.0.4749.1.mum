<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for KB5042056" displayName="default" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="http://support.microsoft.com/?kbid=5042056" creationTimeStamp="2024-07-16T22:32:33Z" lastUpdateTimeStamp="2024-07-16T22:32:33Z">
  <assemblyIdentity name="Package_6_for_KB5042056" version="10.0.4749.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5042056" releaseType="Update" restart="possible">
    <parent buildCompare="EQ" integrate="separate" disposition="staged">
      <assemblyIdentity name="Microsoft-Windows-NetFx2-OC-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <parent buildCompare="EQ" integrate="standalone" disposition="detect">
        <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerAzureStackHCICorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerSolutionEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageWorkgroupEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineCorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerWebEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      </parent>
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="5042056-112_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-MSCORJIT_DLL" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-MSCORJIT_DLL" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="6826849870a6a273588da281feb65de8" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-113_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Security" version="10.0.19200.236" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="6ab06287778905f0af5299f3c0058a43" version="10.0.19200.236" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-114_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-MSCORDACWKS" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-MSCORDACWKS" version="10.0.19200.950" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="bb9fc223df0920513a8da92d06c4f6e4" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-115_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Web" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="System.Web" version="10.0.19200.875" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="8236c2b65a31588c09076d0391b87fe5" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-116_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-MSCORWKS_DLL" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-MSCORWKS_DLL" version="10.0.19200.950" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="c6a452b50ff44467ff34615e33a9c6d0" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-117_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-MSCORPE_DLL" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-MSCORPE_DLL" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="2d8bac29b76c92282235059f5d0cf8e7" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-118_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Configuration" version="10.0.19200.142" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="423312990c4b8735cb7e8fd9d8fdd01e" version="10.0.19200.142" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-119_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.DirectoryServices" version="10.0.19200.580" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="dd793b97c6975cdf7c7fbe307c9bee69" version="10.0.19200.580" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-120_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="mscorlib" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
          <assemblyIdentity name="mscorlib" version="10.0.19200.950" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="db6d3ccb5c6568ac57e98f6dd5faa00a" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-121_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="aspnet_regsql" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="aspnet_regsql" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="9127cd62da37ca445b725d0dd839b2ab" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-122_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-WEB_ENGINE_DLL" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-WEB_ENGINE_DLL" version="10.0.19200.875" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="cdd902e667a4eb8aa97568183657a881" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-123_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Data" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
          <assemblyIdentity name="System.Data" version="10.0.19200.884" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="9d85f834b71d48e1b05dcc17a7c4dc99" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-124_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Runtime.Remoting" version="10.0.19200.884" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="8e72d6ef69edf56cc56aa655f6221845" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-125_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-SOS_DLL" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-SOS_DLL" version="10.0.19200.950" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="cb34879ae56667eef0f659cce3f5ac2b" version="10.0.19200.950" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-126_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-ASPNET_WP_EXE" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-ASPNET_WP_EXE" version="10.0.19200.875" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="bf875196460076809d5f49152394a4d6" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-127_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-System.Web.RegularExpressions" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="839d3fed3aacfe8b3f18719e2ac9ee9b" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-128_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-System.Security" version="10.0.19200.236" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d0f32a755158a118809494900b773e46" version="10.0.19200.236" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-129_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-peverify_dll" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-peverify_dll" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="fc7cb200ee6fc66b3ad9fc48c12ad98f" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-130_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-System.DirectoryServices" version="10.0.19200.580" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d213941c1fa7cc39671135819dc776c2" version="10.0.19200.580" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-131_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-System" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d0de7c0e4dd887977c5492f92a0799b8" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-132_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-VB_Compiler" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-VB_Compiler" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="db0310364b0315f514397e4033f04199" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-133_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-ASPNET_REGIIS_EXE" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx-ASPNET_REGIIS_EXE" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="033d93a09ac1e124b746012db41ca528" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-134_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-System.Runtime.Remoting" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="7f3f208ac344cb72bda950fe5b338249" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-135_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="aspnet_regbrowsers" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="aspnet_regbrowsers" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="acf9642da533d22a084d1b5da2893c03" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-136_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Web.RegularExpressions" version="10.0.19200.875" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="63d8ad6111b9c2f56d9dbbf1176571d2" version="10.0.19200.875" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-137_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx-System.Configuration" version="10.0.19200.142" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="46d38aee1c10b3025aad5869b41fdb78" version="10.0.19200.142" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-138_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System" version="10.0.19200.884" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="2ce50b93a6e69e893c6bd269c397220e" version="10.0.19200.884" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
  </package>
</assembly>
