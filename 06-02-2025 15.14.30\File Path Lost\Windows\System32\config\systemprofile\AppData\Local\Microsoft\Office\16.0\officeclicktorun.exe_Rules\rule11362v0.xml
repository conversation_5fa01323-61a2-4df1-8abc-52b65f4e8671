<?xml version="1.0" encoding="utf-8"?>
<R Id="11362" V="0" DC="SM" EN="Office.Outlook.Desktop.ContactCardCustomFieldStatistics" ATT="d807609276744245baf81bf7bc8033f6-2268e374-7766-4976-be44-b6ad5bddc5b6-7813" DCa="PSU" xmlns="">
  <S>
    <A T="1" E="TelemetryShutdown" />
    <TI T="2" I="Daily" />
    <UTS T="3" Id="by7w3" />
    <F T="4">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="Email" T="W" />
        </R>
      </O>
    </F>
    <F T="5">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="WorkPhone" T="W" />
        </R>
      </O>
    </F>
    <F T="6">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="WorkPhone2" T="W" />
        </R>
      </O>
    </F>
    <F T="7">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="WorkFax" T="W" />
        </R>
      </O>
    </F>
    <F T="8">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="MobilePhone" T="W" />
        </R>
      </O>
    </F>
    <F T="9">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="HomePhone" T="W" />
        </R>
      </O>
    </F>
    <F T="10">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="HomePhone2" T="W" />
        </R>
      </O>
    </F>
    <F T="11">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="OtherPhone" T="W" />
        </R>
      </O>
    </F>
    <F T="12">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="IMAddress" T="W" />
        </R>
      </O>
    </F>
    <F T="13">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="CustomProfile" T="W" />
        </R>
      </O>
    </F>
    <F T="14">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="Office" T="W" />
        </R>
      </O>
    </F>
    <F T="15">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="Company" T="W" />
        </R>
      </O>
    </F>
    <F T="16">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="WorkAddress" T="W" />
        </R>
      </O>
    </F>
    <F T="17">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="HomeAddress" T="W" />
        </R>
      </O>
    </F>
    <F T="18">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="OtherAddress" T="W" />
        </R>
      </O>
    </F>
    <F T="19">
      <O T="EQ">
        <L>
          <S T="3" F="standardLabelName" />
        </L>
        <R>
          <V V="Birthday" T="W" />
        </R>
      </O>
    </F>
  </S>
  <C T="U32" I="0" O="false" N="CustomizedEmailCount">
    <C>
      <S T="4" />
    </C>
  </C>
  <C T="U32" I="1" O="false" N="CustomizedWorkPhoneCount">
    <C>
      <S T="5" />
    </C>
  </C>
  <C T="U32" I="2" O="false" N="CustomizedWorkPhone2Count">
    <C>
      <S T="6" />
    </C>
  </C>
  <C T="U32" I="3" O="false" N="CustomizedWorkFaxCount">
    <C>
      <S T="7" />
    </C>
  </C>
  <C T="U32" I="4" O="false" N="CustomizedMobilePhoneCount">
    <C>
      <S T="8" />
    </C>
  </C>
  <C T="U32" I="5" O="false" N="CustomizedHomePhoneCount">
    <C>
      <S T="9" />
    </C>
  </C>
  <C T="U32" I="6" O="false" N="CustomizedHomePhone2Count">
    <C>
      <S T="10" />
    </C>
  </C>
  <C T="U32" I="7" O="false" N="CustomizedOtherPhoneCount">
    <C>
      <S T="11" />
    </C>
  </C>
  <C T="U32" I="8" O="false" N="CustomizedIMAddressCount">
    <C>
      <S T="12" />
    </C>
  </C>
  <C T="U32" I="9" O="false" N="CustomizedCustomProfileCount">
    <C>
      <S T="13" />
    </C>
  </C>
  <C T="U32" I="10" O="false" N="CustomizedOfficeCount">
    <C>
      <S T="14" />
    </C>
  </C>
  <C T="U32" I="11" O="false" N="CustomizedCompanyCount">
    <C>
      <S T="15" />
    </C>
  </C>
  <C T="U32" I="12" O="false" N="CustomizedWorkAddressCount">
    <C>
      <S T="16" />
    </C>
  </C>
  <C T="U32" I="13" O="false" N="CustomizedHomeAddressCount">
    <C>
      <S T="17" />
    </C>
  </C>
  <C T="U32" I="14" O="false" N="CustomizedOtherAddressCount">
    <C>
      <S T="18" />
    </C>
  </C>
  <C T="U32" I="15" O="false" N="CustomizedBirthdayCount">
    <C>
      <S T="19" />
    </C>
  </C>
  <C T="U32" I="16" O="false" N="AverageCustomLabelNameLength">
    <A T="AVG">
      <S T="3" F="customLabelNameLength" />
    </A>
  </C>
  <C T="U32" I="17" O="false" N="MaximumCustomLabelNameLength">
    <A T="MAX">
      <S T="3" F="customLabelNameLength" />
    </A>
  </C>
  <T>
    <S T="1" />
    <S T="2" />
  </T>
  <ST>
    <S T="3" />
  </ST>
</R>
