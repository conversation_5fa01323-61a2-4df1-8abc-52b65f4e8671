<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>WindowsUpdateDiagnostic</ID>
    <Version>9.3</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-16804</Name>
    <Description>@diagpackage.dll,-16805</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>TS_Main.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_DateTime</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16820</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_DateTime</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>AccurateTime</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-16821</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_DateTime.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>RC_DateTime.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_AppData</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16816</Name>
        <Description>@diagpackage.dll,-16817</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_AppData</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-16818</Name>
            <Description>@diagpackage.dll,-16819</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_AppData.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>RC_AppData.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_GENWUError</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>error</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-16800</Name>
        <Description>@diagpackage.dll,-16801</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_GENWUError</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>error</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>instanceValue</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-16802</Name>
            <Description>@diagpackage.dll,-16803</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>error</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>instanceValue</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_GENWUError.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>error</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>instanceValue</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_GenWUError.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_DataStore</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16833</Name>
        <Description>@diagpackage.dll,-16834</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_DataStore</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>oldTimestamp</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-16835</Name>
            <Description>@diagpackage.dll,-16836</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>oldTimestamp</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_DataStore.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>oldTimestamp</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_DataStore.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_PendingRestart</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16900</Name>
        <Description>@diagpackage.dll,-16901</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_PendingRestart</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-16902</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_PendingRestart.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier></Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_PendingUpdates</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16903</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_PendingUpdates</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-16837</Name>
            <Description>@diagpackage.dll,-16838</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_PendingUpdates.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_PendingUpdates.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
        <Parameter>
          <Name>ScanFailure</Name>
          <DefaultValue/>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_WaaSMedic</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16904</Name>
        <Description>@diagpackage.dll,-16905</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_WaaSMedic</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>error</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-16906</Name>
            <Description>@diagpackage.dll,-16907</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>error</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_WaaSMedic.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions>
    </SingleResponseInteractions>
    <MultipleResponseInteractions/>
    <TextInteractions/>
    <PauseInteractions>
      <PauseInteraction>
        <ID>INT_PendingRestart</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-1119827</Name>
          <Description>@diagpackage.dll,-1119828</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
      </PauseInteraction>
    </PauseInteractions>
    <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-10</HelpKeywords>
    <Feedback>
      <ContextId>158</ContextId>
    </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>