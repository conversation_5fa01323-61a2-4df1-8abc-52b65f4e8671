<?xml version="1.0" encoding="utf-8"?>
<Configuration>
  <ViewDefinitions>
    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.Result</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.Result</TypeName>
      </ViewSelectedBy>
      <CustomControl>
        <CustomEntries>
          <CustomEntry>
            <CustomItem>
              <ExpressionBinding>
                <PropertyName>Overview</PropertyName>
                <ItemSelectionCondition>
                  <ScriptBlock>($_ | gm -Name:'Overview' -MemberType:NoteProperty).Count -gt 0</ScriptBlock>
                </ItemSelectionCondition>
                <CustomControl>
                  <CustomEntries>
                    <CustomEntry>
                      <CustomItem>
                        <NewLine />
                        <Text>Overview</Text>
                        <NewLine />
                        <Text>========</Text>
                        <NewLine />
                        <NewLine />
                        <ExpressionBinding>
                          <CustomControl>
                            <CustomEntries>
                              <CustomEntry>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | fl | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                            </CustomEntries>
                          </CustomControl>
                        </ExpressionBinding>
                      </CustomItem>
                    </CustomEntry>
                  </CustomEntries>
                </CustomControl>
              </ExpressionBinding>            
              <ExpressionBinding>
                <PropertyName>TopFiles</PropertyName>
                <ItemSelectionCondition>
                  <ScriptBlock>($_ | gm -Name:'TopFiles' -MemberType:NoteProperty).Count -gt 0</ScriptBlock>
                </ItemSelectionCondition>
                <CustomControl>
                  <CustomEntries>
                    <CustomEntry>
                      <CustomItem>
                        <NewLine />
                        <Text>TopFiles</Text>
                        <NewLine />
                        <Text>========</Text>
                        <NewLine />
                        <NewLine />
                        <ExpressionBinding>
                          <CustomControl>
                            <CustomEntries>
                              <CustomEntry>
                                <EntrySelectedBy>
                                  <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                  <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                </EntrySelectedBy>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | fc | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                              <CustomEntry>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                            </CustomEntries>
                          </CustomControl>
                        </ExpressionBinding>
                      </CustomItem>
                    </CustomEntry>
                  </CustomEntries>
                </CustomControl>
              </ExpressionBinding>
              <ExpressionBinding>
                <PropertyName>TopExtensions</PropertyName>
                <ItemSelectionCondition>
                  <ScriptBlock>($_ | gm -Name:'TopExtensions' -MemberType:NoteProperty).Count -gt 0</ScriptBlock>
                </ItemSelectionCondition>
                <CustomControl>
                  <CustomEntries>
                    <CustomEntry>
                      <CustomItem>
                        <NewLine />
                        <Text>TopExtensions</Text>
                        <NewLine />
                        <Text>=============</Text>
                        <NewLine />
                        <NewLine />
                        <ExpressionBinding>
                          <CustomControl>
                            <CustomEntries>
                              <CustomEntry>
                                <EntrySelectedBy>
                                  <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                  <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                </EntrySelectedBy>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | fc | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                              <CustomEntry>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                            </CustomEntries>
                          </CustomControl>
                        </ExpressionBinding>
                      </CustomItem>
                    </CustomEntry>
                  </CustomEntries>
                </CustomControl>
              </ExpressionBinding>
              <ExpressionBinding>
                <PropertyName>TopProcesses</PropertyName>
                <ItemSelectionCondition>
                  <ScriptBlock>($_ | gm -Name:'TopProcesses' -MemberType:NoteProperty).Count -gt 0</ScriptBlock>
                </ItemSelectionCondition>
                <CustomControl>
                  <CustomEntries>
                    <CustomEntry>
                      <CustomItem>
                        <NewLine />
                        <Text>TopProcesses</Text>
                        <NewLine />
                        <Text>============</Text>
                        <NewLine />
                        <NewLine />
                        <ExpressionBinding>
                          <CustomControl>
                            <CustomEntries>
                              <CustomEntry>
                                <EntrySelectedBy>
                                  <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                  <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                </EntrySelectedBy>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | fc | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                              <CustomEntry>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                            </CustomEntries>
                          </CustomControl>
                        </ExpressionBinding>
                      </CustomItem>
                    </CustomEntry>
                  </CustomEntries>
                </CustomControl>
              </ExpressionBinding>
              <ExpressionBinding>
                <PropertyName>TopPaths</PropertyName>
                <ItemSelectionCondition>
                  <ScriptBlock>($_ | gm -Name:'TopPaths' -MemberType:NoteProperty).Count -gt 0</ScriptBlock>
                </ItemSelectionCondition>
                <CustomControl>
                  <CustomEntries>
                    <CustomEntry>
                      <CustomItem>
                        <NewLine />
                        <Text>TopPaths</Text>
                        <NewLine />
                        <Text>========</Text>
                        <NewLine />
                        <NewLine />
                        <ExpressionBinding>
                          <CustomControl>
                            <CustomEntries>
                              <CustomEntry>
                                <EntrySelectedBy>
                                  <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                  <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                </EntrySelectedBy>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | fc | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                              <CustomEntry>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                            </CustomEntries>
                          </CustomControl>
                        </ExpressionBinding>
                      </CustomItem>
                    </CustomEntry>
                  </CustomEntries>
                </CustomControl>
              </ExpressionBinding>
              <ExpressionBinding>
                <PropertyName>Folder</PropertyName>
                <ItemSelectionCondition>
                  <ScriptBlock>($_ | gm -Name:'Folder' -MemberType:NoteProperty).Count -gt 0</ScriptBlock>
                </ItemSelectionCondition>
                <CustomControl>
                  <CustomEntries>
                    <CustomEntry>
                      <CustomItem>
                        <NewLine />
                        <Text>Folder</Text>
                        <NewLine />
                        <Text>======</Text>
                        <NewLine />
                        <NewLine />
                        <ExpressionBinding>
                          <CustomControl>
                            <CustomEntries>
                              <CustomEntry>
                                <EntrySelectedBy>
                                  <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                  <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                </EntrySelectedBy>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | fc | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                              <CustomEntry>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                            </CustomEntries>
                          </CustomControl>
                        </ExpressionBinding>
                      </CustomItem>
                    </CustomEntry>
                  </CustomEntries>
                </CustomControl>
              </ExpressionBinding>
              <ExpressionBinding>
                <PropertyName>TopScans</PropertyName>
                <ItemSelectionCondition>
                  <ScriptBlock>($_ | gm -Name:'TopScans' -MemberType:NoteProperty).Count -gt 0</ScriptBlock>
                </ItemSelectionCondition>
                <CustomControl>
                  <CustomEntries>
                    <CustomEntry>
                      <CustomItem>
                        <NewLine />
                        <Text>TopScans</Text>
                        <NewLine />
                        <Text>========</Text>
                        <NewLine />
                        <NewLine />
                        <ExpressionBinding>
                          <CustomControl>
                            <CustomEntries>
                              <CustomEntry>
                                <CustomItem>
                                  <ExpressionBinding>
                                    <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                  </ExpressionBinding>
                                  <NewLine />
                                  <NewLine />
                                </CustomItem>
                              </CustomEntry>
                            </CustomEntries>
                          </CustomControl>
                        </ExpressionBinding>
                      </CustomItem>
                    </CustomEntry>
                  </CustomEntries>
                </CustomControl>
              </ExpressionBinding>
            </CustomItem>
          </CustomEntry>
        </CustomEntries>
      </CustomControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.Result</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.Result</TypeName>
      </ViewSelectedBy>
      <ListControl>
        <ListEntries>
          <ListEntry>
            <ListItems>
              <ListItem>
                <Label>Overview</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Overview</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.Overview | fl | Out-String).TrimStart()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>TopFiles</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.TopFiles</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.TopFiles | fl | Out-String).TrimStart()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>TopExtensions</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.TopExtensions</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.TopExtensions | fl | Out-String).TrimStart()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>TopPaths</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.TopPaths</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.TopPaths | fl | Out-String).TrimStart()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>TopProcesses</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.TopProcesses</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.TopProcesses | fl | Out-String).TrimStart()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>TopScans</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.TopScans</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.TopScans | fl | Out-String).TrimStart()</ScriptBlock>
              </ListItem>
            </ListItems>
          </ListEntry>
        </ListEntries>
      </ListControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.Result</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.Result</TypeName>
      </ViewSelectedBy>
      <WideControl>
        <WideEntries>
          <WideEntry>
            <WideItem>
              <ScriptBlock>
                ForEach($sectionName in @('TopFiles', 'TopExtensions', 'TopProcesses', 'TopScans', 'TopPaths')) {
                    if ($null -ne $_.$sectionName) {
                        $sectionName
                    }
                }
              </ScriptBlock>
            </WideItem>
          </WideEntry>
        </WideEntries>
      </WideControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.Result</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.Result</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Overview</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TopExtensions</Label>
            <Width>13</Width>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TopProcesses</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TopFiles</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TopScans</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TopPaths</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <Wrap />
            <TableColumnItems>
              <TableColumnItem>
                <ScriptBlock>($_.Overview | fl | Out-String).TrimStart()</ScriptBlock>
              </TableColumnItem>
              <TableColumnItem>
                <ScriptBlock>($_.TopExtensions | fw -Column:1 | Out-String).TrimStart()</ScriptBlock>
              </TableColumnItem>
              <TableColumnItem>
                <ScriptBlock>($_.TopProcesses | fw -Column:1 | Out-String).TrimStart()</ScriptBlock>
              </TableColumnItem>
              <TableColumnItem>
                <ScriptBlock>($_.TopFiles | fw -Column:1 | Out-String).TrimStart()</ScriptBlock>
              </TableColumnItem>
              <TableColumnItem>
                <ScriptBlock>($_.TopScans | fw -Column:1 | Out-String).TrimStart()</ScriptBlock>
              </TableColumnItem>
              <TableColumnItem>
                <ScriptBlock>($_.TopPaths | fw -Column:1 | Out-String).TrimStart()</ScriptBlock>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedFilePathStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedFilePathStats</TypeName>
        <TypeName>MpPerformanceReport.ScannedFileExtensionStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedFileExtensionStats</TypeName>
        <TypeName>MpPerformanceReport.ScannedPathStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedPathStats</TypeName>
        <TypeName>MpPerformanceReport.ScannedProcessStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedProcessStats</TypeName>
      </ViewSelectedBy>
      <CustomControl>
        <CustomEntries>
          <CustomEntry>
            <CustomItem>
              <ExpressionBinding>
                <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
              </ExpressionBinding>
              <NewLine />
              <NewLine />
              <Frame>
                <LeftIndent>4</LeftIndent>
                <CustomItem>
                  <ExpressionBinding>
                    <PropertyName>Files</PropertyName>
                    <ItemSelectionCondition>
                      <ScriptBlock>($_ | gm -Name:'Files' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                    </ItemSelectionCondition>
                    <CustomControl>
                      <CustomEntries>
                        <CustomEntry>
                          <CustomItem>
                            <Text>Files:</Text>
                            <NewLine />
                            <NewLine />
                            <ExpressionBinding>
                              <CustomControl>
                                <CustomEntries>
                                  <CustomEntry>
                                    <EntrySelectedBy>
                                      <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                      <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                    </EntrySelectedBy>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <EnumerateCollection/>
                                        <CustomControl>
                                          <CustomEntries>
                                            <CustomEntry>
                                              <CustomItem>
                                                <ExpressionBinding>
                                                  <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                                </ExpressionBinding>
                                                <NewLine />
                                                <NewLine />
                                                <Frame>
                                                  <LeftIndent>4</LeftIndent>
                                                  <CustomItem>
                                                    <ExpressionBinding>
                                                      <PropertyName>Scans</PropertyName>
                                                      <ItemSelectionCondition>
                                                        <ScriptBlock>($_ | gm -Name:'Scans' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                                                      </ItemSelectionCondition>
                                                      <CustomControl>
                                                        <CustomEntries>
                                                          <CustomEntry>
                                                            <CustomItem>
                                                              <Text>Scans:</Text>
                                                              <NewLine />
                                                              <NewLine />
                                                              <ExpressionBinding>
                                                                <ScriptBlock>
                                                                  $properties = 'ScanType',@{Name='Duration'; Expression={$_.Duration}; Alignment='Right'},'Reason','SkipReason'
                                                                  
                                                                  if (($_ | gm -Name:'ProcessName' -MemberType:NoteProperty).Count -eq 1) {
                                                                    $properties += 'ProcessName'
                                                                  }

                                                                  if (($_ | gm -Name:'ProcessPath' -MemberType:NoteProperty).Count -eq 1) {
                                                                    $properties += 'ProcessPath'
                                                                  }

                                                                  ($_ | ft -Property:$properties | Out-String).Trim("`r`n".ToCharArray())
                                                                </ScriptBlock>
                                                              </ExpressionBinding>
                                                              <NewLine />
                                                              <NewLine />
                                                            </CustomItem>
                                                          </CustomEntry>
                                                        </CustomEntries>
                                                      </CustomControl>
                                                    </ExpressionBinding>
                                                  </CustomItem>
                                                </Frame>
                                              </CustomItem>
                                            </CustomEntry>
                                          </CustomEntries>
                                        </CustomControl>
                                      </ExpressionBinding>
                                    </CustomItem>
                                  </CustomEntry>
                                  <CustomEntry>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                      </ExpressionBinding>
                                      <NewLine />
                                      <NewLine />
                                    </CustomItem>
                                  </CustomEntry>
                                </CustomEntries>
                              </CustomControl>
                            </ExpressionBinding>
                          </CustomItem>
                        </CustomEntry>
                      </CustomEntries>
                    </CustomControl>
                  </ExpressionBinding>
                  <ExpressionBinding>
                    <PropertyName>Extensions</PropertyName>
                    <ItemSelectionCondition>
                      <ScriptBlock>($_ | gm -Name:'Extensions' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                    </ItemSelectionCondition>
                    <CustomControl>
                      <CustomEntries>
                        <CustomEntry>
                          <CustomItem>
                            <Text>Extensions:</Text>
                            <NewLine />
                            <NewLine />
                            <ExpressionBinding>
                              <CustomControl>
                                <CustomEntries>
                                  <CustomEntry>
                                    <EntrySelectedBy>
                                      <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                      <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                    </EntrySelectedBy>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <EnumerateCollection/>
                                        <CustomControl>
                                          <CustomEntries>
                                            <CustomEntry>
                                              <CustomItem>
                                                <ExpressionBinding>
                                                  <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                                </ExpressionBinding>
                                                <NewLine />
                                                <NewLine />
                                                <Frame>
                                                  <LeftIndent>4</LeftIndent>
                                                  <CustomItem>
                                                    <ExpressionBinding>
                                                      <PropertyName>Scans</PropertyName>
                                                      <ItemSelectionCondition>
                                                        <ScriptBlock>($_ | gm -Name:'Scans' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                                                      </ItemSelectionCondition>
                                                      <CustomControl>
                                                        <CustomEntries>
                                                          <CustomEntry>
                                                            <CustomItem>
                                                              <Text>Scans:</Text>
                                                              <NewLine />
                                                              <NewLine />
                                                              <ExpressionBinding>
                                                                <ScriptBlock>
                                                                  $properties = 'ScanType',@{Name='Duration'; Expression={$_.Duration}; Alignment='Right'},'Reason','SkipReason','ProcessName','Path'
                                                                  ($_ | ft -Property:$properties | Out-String).Trim("`r`n".ToCharArray())
                                                                </ScriptBlock>
                                                              </ExpressionBinding>
                                                              <NewLine />
                                                              <NewLine />
                                                            </CustomItem>
                                                          </CustomEntry>
                                                        </CustomEntries>
                                                      </CustomControl>
                                                    </ExpressionBinding>
                                                  </CustomItem>
                                                </Frame>
                                              </CustomItem>
                                            </CustomEntry>
                                          </CustomEntries>
                                        </CustomControl>
                                      </ExpressionBinding>
                                    </CustomItem>
                                  </CustomEntry>
                                  <CustomEntry>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                      </ExpressionBinding>
                                      <NewLine />
                                      <NewLine />
                                    </CustomItem>
                                  </CustomEntry>
                                </CustomEntries>
                              </CustomControl>
                            </ExpressionBinding>
                          </CustomItem>
                        </CustomEntry>
                      </CustomEntries>
                    </CustomControl>
                  </ExpressionBinding>
                  <ExpressionBinding>
                    <PropertyName>Folder</PropertyName>
                    <ItemSelectionCondition>
                      <ScriptBlock>($_ | gm -Name:'Folder' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                    </ItemSelectionCondition>
                    <CustomControl>
                      <CustomEntries>
                        <CustomEntry>
                          <CustomItem>
                            <Text>Folder:</Text>
                            <NewLine />
                            <NewLine />
                            <ExpressionBinding>
                              <CustomControl>
                                <CustomEntries>
                                  <CustomEntry>
                                    <EntrySelectedBy>
                                      <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                      <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                    </EntrySelectedBy>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <EnumerateCollection/>
                                        <CustomControl>
                                          <CustomEntries>
                                            <CustomEntry>
                                              <CustomItem>
                                                <ExpressionBinding>
                                                  <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                                </ExpressionBinding>
                                                <NewLine />
                                                <NewLine />
                                                <Frame>
                                                  <LeftIndent>4</LeftIndent>
                                                  <CustomItem>
                                                    <ExpressionBinding>
                                                      <PropertyName>Scans</PropertyName>
                                                      <ItemSelectionCondition>
                                                        <ScriptBlock>($_ | gm -Name:'Scans' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                                                      </ItemSelectionCondition>
                                                      <CustomControl>
                                                        <CustomEntries>
                                                          <CustomEntry>
                                                            <CustomItem>
                                                              <Text>Scans:</Text>
                                                              <NewLine />
                                                              <NewLine />
                                                              <ExpressionBinding>
                                                                <ScriptBlock>
                                                                  $properties = 'ScanType',@{Name='Duration'; Expression={$_.Duration}; Alignment='Right'},'Reason','SkipReason','ProcessName','Path'
                                                                  ($_ | ft -Property:$properties | Out-String).Trim("`r`n".ToCharArray())
                                                                </ScriptBlock>
                                                              </ExpressionBinding>
                                                              <NewLine />
                                                              <NewLine />
                                                            </CustomItem>
                                                          </CustomEntry>
                                                        </CustomEntries>
                                                      </CustomControl>
                                                    </ExpressionBinding>
                                                  </CustomItem>
                                                </Frame>
                                                <Frame>
                                                  <LeftIndent>4</LeftIndent>
                                                  <CustomItem>
                                                    <ExpressionBinding>
                                                      <PropertyName>Folder</PropertyName>
                                                      <ItemSelectionCondition>
                                                        <ScriptBlock>($_ | gm -Name:'Folder' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                                                      </ItemSelectionCondition>
                                                      <CustomControl>
                                                        <CustomEntries>
                                                          <CustomEntry>
                                                            <CustomItem>
                                                              <Text>Folder:</Text>
                                                              <NewLine />
                                                              <NewLine />
                                                              <ExpressionBinding>
                                                                <ScriptBlock>
                                                                  ($_ | Out-String).Trim("`r`n".ToCharArray())
                                                                </ScriptBlock>
                                                              </ExpressionBinding>
                                                              <NewLine />
                                                              <NewLine />
                                                            </CustomItem>
                                                          </CustomEntry>
                                                        </CustomEntries>
                                                      </CustomControl>
                                                    </ExpressionBinding>
                                                  </CustomItem>
                                                </Frame>
                                              </CustomItem>
                                            </CustomEntry>
                                          </CustomEntries>
                                        </CustomControl>
                                      </ExpressionBinding>
                                    </CustomItem>
                                  </CustomEntry>
                                  <CustomEntry>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                      </ExpressionBinding>
                                      <NewLine />
                                      <NewLine />
                                    </CustomItem>
                                  </CustomEntry>
                                </CustomEntries>
                              </CustomControl>
                            </ExpressionBinding>
                          </CustomItem>
                        </CustomEntry>
                      </CustomEntries>
                    </CustomControl>
                  </ExpressionBinding>
                  <ExpressionBinding>
                    <PropertyName>Processes</PropertyName>
                    <ItemSelectionCondition>
                      <ScriptBlock>($_ | gm -Name:'Processes' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                    </ItemSelectionCondition>
                    <CustomControl>
                      <CustomEntries>
                        <CustomEntry>
                          <CustomItem>
                            <Text>Processes:</Text>
                            <NewLine />
                            <NewLine />
                            <ExpressionBinding>
                              <CustomControl>
                                <CustomEntries>
                                  <CustomEntry>
                                    <EntrySelectedBy>
                                      <TypeName>MpPerformanceReport.NestedCollection</TypeName>
                                      <TypeName>Deserialized.MpPerformanceReport.NestedCollection</TypeName>
                                    </EntrySelectedBy>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <EnumerateCollection/>
                                        <CustomControl>
                                          <CustomEntries>
                                            <CustomEntry>
                                              <CustomItem>
                                                <ExpressionBinding>
                                                  <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                                </ExpressionBinding>
                                                <NewLine />
                                                <NewLine />
                                                <Frame>
                                                  <LeftIndent>4</LeftIndent>
                                                  <CustomItem>
                                                    <ExpressionBinding>
                                                      <PropertyName>Scans</PropertyName>
                                                      <ItemSelectionCondition>
                                                        <ScriptBlock>($_ | gm -Name:'Scans' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                                                      </ItemSelectionCondition>
                                                      <CustomControl>
                                                        <CustomEntries>
                                                          <CustomEntry>
                                                            <CustomItem>
                                                              <Text>Scans:</Text>
                                                              <NewLine />
                                                              <NewLine />
                                                              <ExpressionBinding>
                                                                <ScriptBlock>
                                                                  $properties = 'ScanType',@{Name='Duration'; Expression={$_.Duration}; Alignment='Right'},'Reason','SkipReason','ProcessName','Path'
                                                                  ($_ | ft -Property:$properties | Out-String).Trim("`r`n".ToCharArray())
                                                                </ScriptBlock>
                                                              </ExpressionBinding>
                                                              <NewLine />
                                                              <NewLine />
                                                            </CustomItem>
                                                          </CustomEntry>
                                                        </CustomEntries>
                                                      </CustomControl>
                                                    </ExpressionBinding>
                                                  </CustomItem>
                                                </Frame>
                                              </CustomItem>
                                            </CustomEntry>
                                          </CustomEntries>
                                        </CustomControl>
                                      </ExpressionBinding>
                                    </CustomItem>
                                  </CustomEntry>
                                  <CustomEntry>
                                    <CustomItem>
                                      <ExpressionBinding>
                                        <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
                                      </ExpressionBinding>
                                      <NewLine />
                                      <NewLine />
                                    </CustomItem>
                                  </CustomEntry>
                                </CustomEntries>
                              </CustomControl>
                            </ExpressionBinding>
                          </CustomItem>
                        </CustomEntry>
                      </CustomEntries>
                    </CustomControl>
                  </ExpressionBinding>
                  <ExpressionBinding>
                    <PropertyName>Scans</PropertyName>
                    <ItemSelectionCondition>
                      <ScriptBlock>($_ | gm -Name:'Scans' -MemberType:NoteProperty).Count -eq 1</ScriptBlock>
                    </ItemSelectionCondition>
                    <CustomControl>
                      <CustomEntries>
                        <CustomEntry>
                          <CustomItem>
                            <Text>Scans:</Text>
                            <NewLine />
                            <NewLine />
                            <ExpressionBinding>
                              <ScriptBlock>
                                $properties = 'ScanType',@{Name='Duration'; Expression={$_.Duration}; Alignment='Right'},'Reason','SkipReason'

                                if (($_ | gm -Name:'Comments' -MemberType:NoteProperty).Count -eq 1) {
                                  $properties += @{Name='Comments'; Expression={$_.Comments.Count}; Alignment='Right'}
                                }

                                if (($_ | gm -Name:'ProcessName' -MemberType:NoteProperty).Count -eq 1) {
                                  $properties += 'ProcessName'
                                }
                                
                                if (($_ | gm -Name:'Path' -MemberType:NoteProperty).Count -eq 1) {
                                  $properties += 'Path'
                                }

                                ($_ | ft -Property:$properties | Out-String).Trim("`r`n".ToCharArray())
                              </ScriptBlock>
                            </ExpressionBinding>
                            <NewLine />
                            <NewLine />
                          </CustomItem>
                        </CustomEntry>
                      </CustomEntries>
                    </CustomControl>
                  </ExpressionBinding>
                </CustomItem>
              </Frame>
            </CustomItem>
          </CustomEntry>
        </CustomEntries>
      </CustomControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScanStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScanStats</TypeName>
      </ViewSelectedBy>
      <ListControl>
        <ListEntries>
          <ListEntry>
            <ListItems>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.ProcessPath</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>ProcessPath</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Path</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>Path</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Extension</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>Extension</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>Count</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>TotalDuration</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>MinDuration</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>AverageDuration</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>MaxDuration</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>MedianDuration</PropertyName>
              </ListItem>
              <ListItem>
                <Label>Extensions</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Extensions</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.Extensions | fl | Out-String).Trim()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>Files</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Files</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.Files | fl | Out-String).Trim()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>Processes</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Processes</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.Processes | fl | Out-String).Trim()</ScriptBlock>
              </ListItem>
              <ListItem>
                <Label>Scans</Label>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Scans</ScriptBlock>
                </ItemSelectionCondition>
                <ScriptBlock>($_.Scans | fl | Out-String).Trim()</ScriptBlock>
              </ListItem>
            </ListItems>
          </ListEntry>
        </ListEntries>
      </ListControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedFilePathStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedFilePathStats</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader />
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <Wrap />
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Count</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TotalDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MinDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AverageDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MaxDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MedianDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Path</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedFileExtensionStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedFileExtensionStats</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader />
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <Wrap />
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Count</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TotalDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MinDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AverageDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MaxDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MedianDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Extension</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedPathStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedPathStats</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader />
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <Wrap />
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Count</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TotalDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MinDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AverageDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MaxDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MedianDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Path</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedProcessStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedProcessStats</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader />
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <Wrap />
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Count</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TotalDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MinDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AverageDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MaxDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MedianDuration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ProcessPath</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedFilePathStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedFilePathStats</TypeName>
      </ViewSelectedBy>
      <WideControl>
        <WideEntries>
          <WideEntry>
            <WideItem>
              <PropertyName>Path</PropertyName>
            </WideItem>
          </WideEntry>
        </WideEntries>
      </WideControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedFileExtensionStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedFileExtensionStats</TypeName>
      </ViewSelectedBy>
      <WideControl>
        <WideEntries>
          <WideEntry>
            <WideItem>
              <PropertyName>Extension</PropertyName>
            </WideItem>
          </WideEntry>
        </WideEntries>
      </WideControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedPathStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedPathStats</TypeName>
      </ViewSelectedBy>
      <WideControl>
        <WideEntries>
          <WideEntry>
            <WideItem>
              <PropertyName>Path</PropertyName>
            </WideItem>
          </WideEntry>
        </WideEntries>
      </WideControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScannedProcessStats</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScannedProcessStats</TypeName>
      </ViewSelectedBy>
      <WideControl>
        <WideEntries>
          <WideEntry>
            <WideItem>
              <PropertyName>ProcessPath</PropertyName>
            </WideItem>
          </WideEntry>
        </WideEntries>
      </WideControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScanInfo</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScanInfo</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader />
          <TableColumnHeader>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader />
          <TableColumnHeader />
          <TableColumnHeader>
            <Label>Comments</Label>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Process</Label>
          </TableColumnHeader>
          <TableColumnHeader />
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <Wrap />
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>ScanType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Duration</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Reason</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SkipReason</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <ScriptBlock>$_.Comments.Count</ScriptBlock>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ProcessName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Path</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScanInfo</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScanInfo</TypeName>
      </ViewSelectedBy>
      <ListControl>
        <ListEntries>
          <ListEntry>
            <ListItems>
              <ListItem>
                <PropertyName>ScanType</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>StartTime</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>EndTime</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>Duration</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.Reason) -and ($_.Reason.Length -gt 0)</ScriptBlock>
                </ItemSelectionCondition> 
                <PropertyName>Reason</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>SkipReason</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>Path</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.ProcessPath</ScriptBlock>
                </ItemSelectionCondition>              
                <PropertyName>ProcessPath</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.ProcessId</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>ProcessId</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Image</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>Image</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>$null -ne $_.Comments</ScriptBlock>
                </ItemSelectionCondition>
                <Label>Comments</Label>
                <ScriptBlock>($_.Comments | fl | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
              </ListItem>              
            </ListItems>
          </ListEntry>
        </ListEntries>
      </ListControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScanInfo</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScanInfo</TypeName>
      </ViewSelectedBy>
      <WideControl>
        <WideEntries>
          <WideEntry>
            <WideItem>
              <PropertyName>Path</PropertyName>
            </WideItem>
          </WideEntry>
        </WideEntries>
      </WideControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.ScanInfo</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.ScanInfo</TypeName>
      </ViewSelectedBy>
      <CustomControl>
        <CustomEntries>
          <CustomEntry>
            <CustomItem>
              <ExpressionBinding>
                <ScriptBlock>($_ | ft | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
              </ExpressionBinding>
            </CustomItem>
          </CustomEntry>
        </CustomEntries>
      </CustomControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.Overview</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.Overview</TypeName>
      </ViewSelectedBy>
      <CustomControl>
        <CustomEntries>
          <CustomEntry>
            <CustomItem>
              <ExpressionBinding>
                <ScriptBlock>($_ | fl | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
              </ExpressionBinding>
            </CustomItem>
          </CustomEntry>
        </CustomEntries>
      </CustomControl>
    </View>

    <View>
      <Name>default</Name>
      <ViewSelectedBy>
        <TypeName>MpPerformanceReport.Overview</TypeName>
        <TypeName>Deserialized.MpPerformanceReport.Overview</TypeName>
      </ViewSelectedBy>
      <ListControl>
        <ListEntries>
          <ListEntry>
            <ListItems>
              <ListItem>
                <PropertyName>StartTime</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>EndTime</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.RealTimeScans) -and ($_.RealTimeScans -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>RealTimeScans</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.OnDemandScans) -and ($_.OnDemandScans -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>OnDemandScans</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.FileScans) -and ($_.FileScans -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>FileScans</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.ProcessScans) -and ($_.ProcessScans -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>ProcessScans</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.RealTimeScansDuration) -and ($_.RealTimeScansDuration -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>RealTimeScansDuration</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.OnDemandScansDuration) -and ($_.OnDemandScansDuration -gt 0)</ScriptBlock>
                </ItemSelectionCondition>   
                <PropertyName>OnDemandScansDuration</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.FileScansDuration) -and ($_.FileScansDuration -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>FileScansDuration</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.ProcessScansDuration) -and ($_.ProcessScansDuration -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>ProcessScansDuration</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>MaxScanDuration</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>MedianScanDuration</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.SkipScanCount) -and ($_.SkipScanCount -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <PropertyName>SkipScanCount</PropertyName>
              </ListItem>
              <ListItem>
                <ItemSelectionCondition>
                  <ScriptBlock>($null -ne $_.PerfHints) -and ($_.PerfHints.Count -gt 0)</ScriptBlock>
                </ItemSelectionCondition>
                <Label>PerfHints</Label>
                <ScriptBlock>($_.PerfHints | fl | Out-String).Trim("`r`n".ToCharArray())</ScriptBlock>
              </ListItem>              
            </ListItems>
          </ListEntry>
        </ListEntries>
      </ListControl>
    </View>
  </ViewDefinitions>
</Configuration>

<!-- SIG # Begin signature block -->
<!-- MIIl4gYJKoZIhvcNAQcCoIIl0zCCJc8CAQExDzANBglghkgBZQMEAgEFADB5Bgor -->
<!-- BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG -->
<!-- KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCCdmU1kCh9yCsme -->
<!-- 5mvlPutaTNvCmHfwywxvXGFw3UjBIaCCC1MwggTgMIIDyKADAgECAhMzAAALjqZI -->
<!-- W414yEj+AAAAAAuOMA0GCSqGSIb3DQEBCwUAMHkxCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xIzAhBgNVBAMTGk1pY3Jvc29mdCBXaW5kb3dzIFBD -->
<!-- QSAyMDEwMB4XDTI0MDgyMjIxMzIwNVoXDTI1MDcwNDIxMzIwNVowcDELMAkGA1UE -->
<!-- BhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAc -->
<!-- BgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEaMBgGA1UEAxMRTWljcm9zb2Z0 -->
<!-- IFdpbmRvd3MwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCrxl8EJfzA -->
<!-- QclDL4nqroqJh8zn/bvwptnZ6a99GFLJ5GuBZxQloIPF+ZM+9D1QnH/xeQsnuh4e -->
<!-- CtIztigcnWawfvQAgpsgS47ROqxM5IsMhoGdbmahsAO9ld46iG/7RsKvfDvFCyip -->
<!-- SnaKR/Lz4YJl4diHhpSIAviRMkWBPoAXDZlcwIuGk0AeAuL8mxVYgBPuWUsq+MH5 -->
<!-- HL6ut/M+uzFpfv3atwbdECzGoXERLeHzqtOnpKLkqq+43TJLLWFnz5arpMs0MbjJ -->
<!-- o+40U3iarvp3hMIiM5O06++zz+PMoBO67yKqu9AcdQwJZ+jZ640qkYGDZeVJHNC4 -->
<!-- 55PXywdmGqOtAgMBAAGjggFoMIIBZDAfBgNVHSUEGDAWBgorBgEEAYI3CgMGBggr -->
<!-- BgEFBQcDAzAdBgNVHQ4EFgQU+rpJaEO0Uj4j7pcTCs301fdFu/gwRQYDVR0RBD4w -->
<!-- PKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEWMBQGA1UEBRMN -->
<!-- MjMwMDI4KzUwMjc5NzAfBgNVHSMEGDAWgBTRT6mKBwjO9CQYmOUA//PWeR03vDBT -->
<!-- BgNVHR8ETDBKMEigRqBEhkJodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny -->
<!-- bC9wcm9kdWN0cy9NaWNXaW5QQ0FfMjAxMC0wNy0wNi5jcmwwVwYIKwYBBQUHAQEE -->
<!-- SzBJMEcGCCsGAQUFBzAChjtodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpL2Nl -->
<!-- cnRzL01pY1dpblBDQV8yMDEwLTA3LTA2LmNydDAMBgNVHRMBAf8EAjAAMA0GCSqG -->
<!-- SIb3DQEBCwUAA4IBAQBsZbDNo5NyFwckNKY3ZusiPl4vHyU0CzJKbaV1lLdwxU// -->
<!-- ePCQRH0efL+g1HMnT0+3tYo3S+jcYYjbtVPhLT0V1jSIeYK5Raxwqm1Csb7B+zta -->
<!-- Qt933J/50c+3IiSeWXrNhi4r9Q7/h6f8Fu+TpG0pG9XGF5LmMSQKL16GZTbdOT5R -->
<!-- mqK5J/GW9+S20QDo5cMULRzoWvxRgYKbXIyQWQeUjIpXG4FdzLpPepPiybYpLztT -->
<!-- X8Vpcu62J9k2nG6nth0ExLrX3YdfRIpaP+ALXD7qa7fEAuy8hV1mX5eYVJAVmvTu -->
<!-- yyK779mXw0pqlIbCjbgRd7e0ohoIVzbEap3THr1RMIIGazCCBFOgAwIBAgIKYQxq -->
<!-- GQAAAAAABDANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgT -->
<!-- Cldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29m -->
<!-- dCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNh -->
<!-- dGUgQXV0aG9yaXR5IDIwMTAwHhcNMTAwNzA2MjA0MDIzWhcNMjUwNzA2MjA1MDIz -->
<!-- WjB5MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH -->
<!-- UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSMwIQYDVQQD -->
<!-- ExpNaWNyb3NvZnQgV2luZG93cyBQQ0EgMjAxMDCCASIwDQYJKoZIhvcNAQEBBQAD -->
<!-- ggEPADCCAQoCggEBAMB5uzqx8A+EuK1kKnUWc9C7B/Y+DZ0U5LGfwciUsDh8H9Az -->
<!-- VfW6I2b1LihIU8cWg7r1Uax+rOAmfw90/FmV3MnGovdScFosHZSrGb+vlX2vZqFv -->
<!-- m2JubUu8LzVs3qRqY1pf+/MNTWHMCn4x62wK0E2XD/1/OEbmisdzaXZVaZZM5Njw -->
<!-- NOu6sR/OKX7ET50TFasTG3JYYlZsioGjZHeYRmUpnYMUpUwIoIPXIx/zX99vLM/a -->
<!-- FtgOcgQo2Gs++BOxfKIXeU9+3DrknXAna7/b/B7HB9jAvguTHijgc23SVOkoTL9r -->
<!-- XZ//XTMSN5UlYTRqQst8nTq7iFnho0JtOlBbSNECAwEAAaOCAeMwggHfMBAGCSsG -->
<!-- AQQBgjcVAQQDAgEAMB0GA1UdDgQWBBTRT6mKBwjO9CQYmOUA//PWeR03vDAZBgkr -->
<!-- BgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUw -->
<!-- AwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvXzpoYxDBWBgNVHR8ETzBN -->
<!-- MEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2NybC9wcm9kdWN0 -->
<!-- cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYIKwYBBQUHAQEETjBMMEoG -->
<!-- CCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpL2NlcnRzL01p -->
<!-- Y1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDCBnQYDVR0gBIGVMIGSMIGPBgkrBgEE -->
<!-- AYI3LgMwgYEwPQYIKwYBBQUHAgEWMWh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9Q -->
<!-- S0kvZG9jcy9DUFMvZGVmYXVsdC5odG0wQAYIKwYBBQUHAgIwNB4yIB0ATABlAGcA -->
<!-- YQBsAF8AUABvAGwAaQBjAHkAXwBTAHQAYQB0AGUAbQBlAG4AdAAuIB0wDQYJKoZI -->
<!-- hvcNAQELBQADggIBAC5Bpoa1Bm/wgIX6O8oX6cn65DnClHDDZJTD2FamkI7+5Jr0 -->
<!-- bfVvjlONWqjzrttGbL5/HVRWGzwdccRRFVR+v+6llUIz/Q2QJCTj+dyWyvy4rL/0 -->
<!-- wjlWuLvtc7MX3X6GUCOLViTKu6YdmocvJ4XnobYKnA0bjPMAYkG6SHSHgv1QyfSH -->
<!-- KcMDqivfGil56BIkmobt0C7TQIH1B18zBlRdQLX3sWL9TUj3bkFHUhy7G8JXOqiZ -->
<!-- VpPUxt4mqGB1hrvsYqbwHQRF3z6nhNFbRCNjJTZ3b65b3CLVFCNqQX/QQqbb7yV7 -->
<!-- BOPSljdiBq/4Gw+Oszmau4n1NQblpFvDjJ43X1PRozf9pE/oGw5rduS4j7DC6v11 -->
<!-- 9yxBt5yj4R4F/peSy39ZA22oTo1OgBfU1XL2VuRIn6MjugagwI7RiE+TIPJwX9hr -->
<!-- cqMgSfx3DF3Fx+ECDzhCEA7bAq6aNx1QgCkepKfZxpolVf1Ayq1kEOgx+RJUeRry -->
<!-- DtjWqx4z/gLnJm1hSY/xJcKLdJnf+ZMakBzu3ZQzDkJQ239Q+J9iguymghZ8Zrzs -->
<!-- mbDBWF2osJphFJHRmS9J5D6Bmdbm78rj/T7u7AmGAwcNGw186/RayZXPhxIKXezF -->
<!-- ApLNBZlyyn3xKhAYOOQxoyi05kzFUqOcasd9wHEJBA1w3gI/h+5WoezrtUyFMYIZ -->
<!-- 5TCCGeECAQEwgZAweTELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x -->
<!-- EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv -->
<!-- bjEjMCEGA1UEAxMaTWljcm9zb2Z0IFdpbmRvd3MgUENBIDIwMTACEzMAAAuOpkhb -->
<!-- jXjISP4AAAAAC44wDQYJYIZIAWUDBAIBBQCggZAwGQYJKoZIhvcNAQkDMQwGCisG -->
<!-- AQQBgjcCAQQwLwYJKoZIhvcNAQkEMSIEIEasc+wEUmYUVf/MVlg8X7ODjKNlJCyi -->
<!-- SO20QWMyQyNLMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8AcwBvAGYA -->
<!-- dKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEBBQAEggEA -->
<!-- dT5gq36WZmVA0ZGOp97ajni2YlMbnILhy6NobWHmTBlqQUZXdH9cP6UtmZ8JExUJ -->
<!-- v7aDNgYGzptE9QuW4ju5kMEleGx4hgV/zri/ehKtQQ8KWrYtoPHWx2WJFTy40m0z -->
<!-- rREIcm8G1nuFGcX4HW4WotuaH09qf6l0pWAFetKClhQZgyIS/Rog8h5vhf897M7G -->
<!-- Va8niN3FBWGyOkaZ8vZiE7Leq7Ps/2r44rRD0jI9CO1EnDED70S4snryen+HVMhO -->
<!-- 1Bcl03iyyGJYFG7j+5UqdvPQuJ1RiFaqwW/WUqtR20vMzAEvIXFwJX+tMfhedUUr -->
<!-- JVhCMtLGHvJAkkP8rN1Q2qGCF5IwgheOBgorBgEEAYI3AwMBMYIXfjCCF3oGCSqG -->
<!-- SIb3DQEHAqCCF2swghdnAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFQBgsqhkiG9w0B -->
<!-- CRABBKCCAT8EggE7MIIBNwIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFlAwQCAQUA -->
<!-- BCDNLtaf+Qbf5stJ0+o3UEnNK8lDCjdp5xJBpsWB7Yp+kwIGZ/fhDdDsGBEyMDI1 -->
<!-- MDQxNjIzMTY0My4yWjAEgAIB9KCB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNV -->
<!-- BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv -->
<!-- c29mdCBDb3Jwb3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3Bl -->
<!-- cmF0aW9uczEnMCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjhEMDAtMDVFMC1EOTQ3 -->
<!-- MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloIIR6jCCByAw -->
<!-- ggUIoAMCAQICEzMAAAINDXe+ezaPf+MAAQAAAg0wDQYJKoZIhvcNAQELBQAwfDEL -->
<!-- MAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1v -->
<!-- bmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWlj -->
<!-- cm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAwHhcNMjUwMTMwMTk0MzAxWhcNMjYw -->
<!-- NDIyMTk0MzAxWjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x -->
<!-- EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv -->
<!-- bjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEnMCUGA1UE -->
<!-- CxMeblNoaWVsZCBUU1MgRVNOOjhEMDAtMDVFMC1EOTQ3MSUwIwYDVQQDExxNaWNy -->
<!-- b3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNlMIICIjANBgkqhkiG9w0BAQEFAAOCAg8A -->
<!-- MIICCgKCAgEAsX+oB93x98IJhTeU1fM0RfzdEyYLllGAq0GT1JsAPkIUskjae5Ih -->
<!-- phWXeG+c7AHzdFofj8yCb2LFTIFQmmXG1a/wyQX3scJ0H3Pn4CIzogM2Eo4OxLUj -->
<!-- nVqC5PNW/5hohiAQvvny3EgM22TtyrVuD0SjkrHQ/+XkXbTXGvxHeBAHFE99swBk -->
<!-- vUqv/+JA0/dihM0TVyY/M9zQy+KBhswkDLwPN7UBuZq53qcRdDrDXyih2BzKo8jh -->
<!-- D+aU4Cn0+VzH89LBBugEQI5Vye/Uhv46Hrh4fE6gvnCTK8vcAe2S63TABcvKxzd1 -->
<!-- tfpJKcOOyqPvYs3tm5Xvj+YzGqrnytN9wbvthV1UiqYYpdo9KgVFqZ+bwbX9lvoL -->
<!-- FasnQPnrHgR32MXA2ePuaEaMgwINLVvX+/bzgL/dT+G08xvgWTwWwzM0g8Jo/AEi -->
<!-- g5VcK7k3hdXpuzVonnVgAftvYBR/3PzTdbk0C78BgyDsNQ3C5wg9FVzS+5jr4n2L -->
<!-- zVroEwB3DBS1ZQNY3jh9+RPyFvanCiqq2oey8iWsf+jOVB7MELoBoC64ojDRL89y -->
<!-- KlvgIiCdPe4mMzBs7A6my0lM7SIwxLfcXyBx1LP6d4/QZK9mzz9b+M/L6f+OQpN5 -->
<!-- nxW1thdBLYzgpxjzKyTY4xoRBvcwCtddOQYNpCe0Z/Fk1juSGw/6w7UCAwEAAaOC -->
<!-- AUkwggFFMB0GA1UdDgQWBBQVl46Q5g4Da07CEo9uko1Nf2DDgDAfBgNVHSMEGDAW -->
<!-- gBSfpxVdAF5iXYP05dJlpxtTNRnpcjBfBgNVHR8EWDBWMFSgUqBQhk5odHRwOi8v -->
<!-- d3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNyb3NvZnQlMjBUaW1lLVN0 -->
<!-- YW1wJTIwUENBJTIwMjAxMCgxKS5jcmwwbAYIKwYBBQUHAQEEYDBeMFwGCCsGAQUF -->
<!-- BzAChlBodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NlcnRzL01pY3Jv -->
<!-- c29mdCUyMFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNydDAMBgNVHRMBAf8E -->
<!-- AjAAMBYGA1UdJQEB/wQMMAoGCCsGAQUFBwMIMA4GA1UdDwEB/wQEAwIHgDANBgkq -->
<!-- hkiG9w0BAQsFAAOCAgEAHB/IE+NsFUPBkGfjByFzSfneHUJ9PrzxvwHRb08ZP7bz -->
<!-- jpANou4Z6BDhBXEm80uRHSFQjIuWORIlk/IRCZM7XXquYV7VFrD2PtpWElPR3YSg -->
<!-- 30Q348FWfZHwrYay0Qt48PRWoeTABiPTB0hE6Pa+/nLsfAFKkN4ZmjNo5kzVr6xT -->
<!-- i2ztNQdXIjRm7xNsSvq28wQYf7lnd5N9KWrui6dGs4O6w2BjBr/I1qLSC36SjAXg -->
<!-- kKLnzLCP4M8aOPpPXamAZAMDJO4dYX9t8nG+Vp4EjEFf1MZd0sW4PuuCiuhgrE0h -->
<!-- us+8zU4Gy+vTzBn8FdnJ2sGPeSMtnICenGzDkUVHGM77LlHfKBEPO3FuF6qgS64X -->
<!-- nsCY0Tk+mMlq0NqEPo8xitliaBjZ32p5jX6kJ1SqYt9tKJ2oPeSzBY4/AcK35WLD -->
<!-- vZBDosuyOVJ4DwZhfkR0gr9oRUq/Jyu4LbosvUlDij+hW0Ds8jEbyd1MbJqx9/OO -->
<!-- +18xE2/7WSwI9dE3WBX5I5BXhY5W94jE0rP1ayUhdK9Lzjf6MWaigmPFmXs/rkD9 -->
<!-- E68tEEPcEaeQ9hWCfBNbiF/y78LhZAXVn+1+6pW/qc3bz6UgHGgoASFk5TJ9m1oJ -->
<!-- FObj88UTx4f9LvhSO/3kQmRmyzNqp598l0Lzv4fcEJvKSAAUQL6HWoldG1Zh2xEw -->
<!-- ggdxMIIFWaADAgECAhMzAAAAFcXna54Cm0mZAAAAAAAVMA0GCSqGSIb3DQEBCwUA -->
<!-- MIGIMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH -->
<!-- UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMTIwMAYDVQQD -->
<!-- EylNaWNyb3NvZnQgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0y -->
<!-- MTA5MzAxODIyMjVaFw0zMDA5MzAxODMyMjVaMHwxCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w -->
<!-- IFBDQSAyMDEwMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5OGmTOe0 -->
<!-- ciELeaLL1yR5vQ7VgtP97pwHB9KpbE51yMo1V/YBf2xK4OK9uT4XYDP/XE/HZveV -->
<!-- U3Fa4n5KWv64NmeFRiMMtY0Tz3cywBAY6GB9alKDRLemjkZrBxTzxXb1hlDcwUTI -->
<!-- cVxRMTegCjhuje3XD9gmU3w5YQJ6xKr9cmmvHaus9ja+NSZk2pg7uhp7M62AW36M -->
<!-- EBydUv626GIl3GoPz130/o5Tz9bshVZN7928jaTjkY+yOSxRnOlwaQ3KNi1wjjHI -->
<!-- NSi947SHJMPgyY9+tVSP3PoFVZhtaDuaRr3tpK56KTesy+uDRedGbsoy1cCGMFxP -->
<!-- LOJiss254o2I5JasAUq7vnGpF1tnYN74kpEeHT39IM9zfUGaRnXNxF803RKJ1v2l -->
<!-- IH1+/NmeRd+2ci/bfV+AutuqfjbsNkz2K26oElHovwUDo9Fzpk03dJQcNIIP8BDy -->
<!-- t0cY7afomXw/TNuvXsLz1dhzPUNOwTM5TI4CvEJoLhDqhFFG4tG9ahhaYQFzymei -->
<!-- XtcodgLiMxhy16cg8ML6EgrXY28MyTZki1ugpoMhXV8wdJGUlNi5UPkLiWHzNgY1 -->
<!-- GIRH29wb0f2y1BzFa/ZcUlFdEtsluq9QBXpsxREdcu+N+VLEhReTwDwV2xo3xwgV -->
<!-- GD94q0W29R6HXtqPnhZyacaue7e3PmriLq0CAwEAAaOCAd0wggHZMBIGCSsGAQQB -->
<!-- gjcVAQQFAgMBAAEwIwYJKwYBBAGCNxUCBBYEFCqnUv5kxJq+gpE8RjUpzxD/LwTu -->
<!-- MB0GA1UdDgQWBBSfpxVdAF5iXYP05dJlpxtTNRnpcjBcBgNVHSAEVTBTMFEGDCsG -->
<!-- AQQBgjdMg30BATBBMD8GCCsGAQUFBwIBFjNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j -->
<!-- b20vcGtpb3BzL0RvY3MvUmVwb3NpdG9yeS5odG0wEwYDVR0lBAwwCgYIKwYBBQUH -->
<!-- AwgwGQYJKwYBBAGCNxQCBAweCgBTAHUAYgBDAEEwCwYDVR0PBAQDAgGGMA8GA1Ud -->
<!-- EwEB/wQFMAMBAf8wHwYDVR0jBBgwFoAU1fZWy4/oolxiaNE9lJBb186aGMQwVgYD -->
<!-- VR0fBE8wTTBLoEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3BraS9jcmwv -->
<!-- cHJvZHVjdHMvTWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3JsMFoGCCsGAQUFBwEB -->
<!-- BE4wTDBKBggrBgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraS9j -->
<!-- ZXJ0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcnQwDQYJKoZIhvcNAQELBQAD -->
<!-- ggIBAJ1VffwqreEsH2cBMSRb4Z5yS/ypb+pcFLY+TkdkeLEGk5c9MTO1OdfCcTY/ -->
<!-- 2mRsfNB1OW27DzHkwo/7bNGhlBgi7ulmZzpTTd2YurYeeNg2LpypglYAA7AFvono -->
<!-- aeC6Ce5732pvvinLbtg/SHUB2RjebYIM9W0jVOR4U3UkV7ndn/OOPcbzaN9l9qRW -->
<!-- qveVtihVJ9AkvUCgvxm2EhIRXT0n4ECWOKz3+SmJw7wXsFSFQrP8DJ6LGYnn8Atq -->
<!-- gcKBGUIZUnWKNsIdw2FzLixre24/LAl4FOmRsqlb30mjdAy87JGA0j3mSj5mO0+7 -->
<!-- hvoyGtmW9I/2kQH2zsZ0/fZMcm8Qq3UwxTSwethQ/gpY3UA8x1RtnWN0SCyxTkct -->
<!-- wRQEcb9k+SS+c23Kjgm9swFXSVRk2XPXfx5bRAGOWhmRaw2fpCjcZxkoJLo4S5pu -->
<!-- +yFUa2pFEUep8beuyOiJXk+d0tBMdrVXVAmxaQFEfnyhYWxz/gq77EFmPWn9y8FB -->
<!-- SX5+k77L+DvktxW/tM4+pTFRhLy/AsGConsXHRWJjXD+57XQKBqJC4822rpM+Zv/ -->
<!-- Cuk0+CQ1ZyvgDbjmjJnW4SLq8CdCPSWU5nR0W2rRnj7tfqAxM328y+l7vzhwRNGQ -->
<!-- 8cirOoo6CGJ/2XBjU02N7oJtpQUQwXEGahC0HVUzWLOhcGbyoYIDTTCCAjUCAQEw -->
<!-- gfmhgdGkgc4wgcsxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw -->
<!-- DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x -->
<!-- JTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVyaWNhIE9wZXJhdGlvbnMxJzAlBgNVBAsT -->
<!-- Hm5TaGllbGQgVFNTIEVTTjo4RDAwLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z -->
<!-- b2Z0IFRpbWUtU3RhbXAgU2VydmljZaIjCgEBMAcGBSsOAwIaAxUAeywsKI+ht50O -->
<!-- zsYZf6X53uMgEz2ggYMwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz -->
<!-- aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv -->
<!-- cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx -->
<!-- MDANBgkqhkiG9w0BAQsFAAIFAOuqSAIwIhgPMjAyNTA0MTYxNTE1MTRaGA8yMDI1 -->
<!-- MDQxNzE1MTUxNFowdDA6BgorBgEEAYRZCgQBMSwwKjAKAgUA66pIAgIBADAHAgEA -->
<!-- AgIULzAHAgEAAgITbzAKAgUA66uZggIBADA2BgorBgEEAYRZCgQCMSgwJjAMBgor -->
<!-- BgEEAYRZCgMCoAowCAIBAAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3DQEBCwUA -->
<!-- A4IBAQBGKc1SLXifnM0kNzwBS7IHjgFXZ5AP6ER0wtcLwhOzsXKaThVahQDAU4Re -->
<!-- VwJerw4ymWMRsmFtg/C8YYLAkU2ny0H6oO9VkGmUW7o81d2hjaxDuTStF+xW8zkD -->
<!-- A1JpvSyTc39wzv9unETREDY6tTRHxJDJiolz3AqmmLnJf3r3CdOl3EhnxGACU7ih -->
<!-- y5Go+h73VFxzmqdwm1bxrM5pfYBWPlahQAUXo7jmMVB/5BqfAjMmMIBllHHGD4Fx -->
<!-- TspHqj0Ch+KgULDh/4Sq0WsZoi7Pcy909XvSiXOu+Nc9a5rRtTsfIFVreVPoHfhv -->
<!-- 3wEC59LVMGvQ+IShYQaH7PrW4n8sMYIEDTCCBAkCAQEwgZMwfDELMAkGA1UEBhMC -->
<!-- VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV -->
<!-- BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp -->
<!-- bWUtU3RhbXAgUENBIDIwMTACEzMAAAINDXe+ezaPf+MAAQAAAg0wDQYJYIZIAWUD -->
<!-- BAIBBQCgggFKMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAvBgkqhkiG9w0B -->
<!-- CQQxIgQgnXvEqCqt/yworHFcPLGn4zSXESUP4OzkFnoa14VlGEgwgfoGCyqGSIb3 -->
<!-- DQEJEAIvMYHqMIHnMIHkMIG9BCBj6geU7CRqiPDrpJwSrUr29BX6usOTUrOb3k1c -->
<!-- JGv4QDCBmDCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9u -->
<!-- MRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRp -->
<!-- b24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMzAAAC -->
<!-- DQ13vns2j3/jAAEAAAINMCIEIJia1lmUHUrf7vzVNYmncNLPiZE26q9dD1LEd0iq -->
<!-- cmYEMA0GCSqGSIb3DQEBCwUABIICAANvj7P2AFMkAMdMl5E7KlcNsBmCf+A/7D7c -->
<!-- zMWlSQqlXS6pUuubdswn3JmFPEIFoPjbfVjQ+WRLsEStJOxGUWa9NDyX6iVd+Hxm -->
<!-- DI943KzaCMCU3M/AzKagql0qFUCYtGDDLuiBciTc0XmMQnafBsi40RdfHEZc7iAn -->
<!-- 2DIgydB0PsPtsFZ4XQpvIzUcbDG6eRRR/LHb3h67xHDE8xzphS39Q6tekLBcoA+X -->
<!-- DFcs0FsrMnGx8nIXRvX+2jQqqaIoCYEsHhIa122MZ/uuaoC55L3lA77yEyEsG2db -->
<!-- w4irwOEwPsIfRs+2ZGcYUF0hXgSEW4lMgzCVmJwWqJIlJ39VmPCIogAs+V99dtXe -->
<!-- X1/9UntgWQxekbxSNAM0/3gv39Q1lDER77WRHWNjSxuTkjJOLTZsUIa09ycaK3Hb -->
<!-- CL6eRR0fCB/DdKA9FL4g8JyWaM4VImfQkI5emtka5vQ2cDelS+fWYRnq1AdrXo9E -->
<!-- DmLk9FV7/qg2h0bh9YLl3PEQFpvt55aJVHVBCJkHRvdml/xM46EhUJVrQp3dE4Ko -->
<!-- Q2xzV5Aq6dIqWfcEvq/vDYYhXaf8fW7cbuVLFVE3i2Uiqhml7J2AGC1cjgLyQ4HB -->
<!-- ChPPqeAyXCpQN3phoSJ0RZQFT3qIf5J7+1pyvMxik/BgGKuXVVNqElGOGa1DcqiO -->
<!-- UVZ1kB9q -->
<!-- SIG # End signature block -->
