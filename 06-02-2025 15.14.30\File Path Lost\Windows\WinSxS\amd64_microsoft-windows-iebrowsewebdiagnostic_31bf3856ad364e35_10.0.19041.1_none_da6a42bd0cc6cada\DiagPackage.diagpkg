<dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>IEDiagnostic</ID>
    <Version>1.2</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>http://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
  <PowerShellVersion>1.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>false</RequiresElevation>
      <RequiresInteractivity>false</RequiresInteractivity>
      <FileName>IEBrowseWeb_TroubleShooter.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_DefectiveIEaddon</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-3</Name>
        <Description>@diagpackage.dll,-4</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Disableaddon</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-5</Name>
            <Description>@diagpackage.dll,-6</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_Disableaddon.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_IEDefectiveAddon.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_cachesyncsettings</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-11</Name>
        <Description>@diagpackage.dll,-12</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_resetpagesyncpolicy</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-13</Name>
            <Description>@diagpackage.dll,-14</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_Resetpagesyncpolicy.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_pagesyncpolicy.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_Tempfilescachesize</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-15</Name>
        <Description>@diagpackage.dll,-16</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Resetcachesize</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-17</Name>
            <Description>@diagpackage.dll,-18</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_ResetCacheSize.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_tempfilecachesize.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_serverconnectionsnumber</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-19</Name>
        <Description>@diagpackage.dll,-20</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_RestoreIEconnection</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-21</Name>
            <Description>@diagpackage.dll,-22</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_RestoreIEconnection.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_IEconnection.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_IEaddonLoadingTime</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-23</Name>
        <Description>@diagpackage.dll,-24</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_IEaddonLoadingTime</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-25</Name>
            <Description>@diagpackage.dll,-26</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_DisableaddonLoadingTime.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_IEAddonLoadingTime.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
      <SingleResponseInteractions/>
      <MultipleResponseInteractions>
	    <MultipleResponseInteraction>
	      <AllowDynamicResponses>true</AllowDynamicResponses>
	      <Choices/>
	      <ID>IT_Disableaddon</ID>
	      <DisplayInformation>
	        <Parameters/>
	        <Name>@diagpackage.dll,-110</Name>
	        <Description>@diagpackage.dll,-111</Description>
	      </DisplayInformation>
	      <ContextParameters/>
	      <ExtensionPoint>
	        <RTFDescription>@diagpackage.dll,-98</RTFDescription>
	      </ExtensionPoint>
	    </MultipleResponseInteraction>
	    <MultipleResponseInteraction>
	      <AllowDynamicResponses>true</AllowDynamicResponses>
	      <Choices/>
	      <ID>IT_DisableLoadingTimeaddon</ID>
	      <DisplayInformation>
	        <Parameters/>
	        <Name>@diagpackage.dll,-112</Name>
	        <Description>@diagpackage.dll,-113</Description>
	      </DisplayInformation>
	      <ContextParameters/>
	      <ExtensionPoint>
	        <RTFDescription>@diagpackage.dll,-114</RTFDescription>
	      </ExtensionPoint>
	    </MultipleResponseInteraction>
      </MultipleResponseInteractions>
      <TextInteractions/>
      <PauseInteractions/>
      <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-109</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-101</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-126</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-127</HelpKeywords>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>