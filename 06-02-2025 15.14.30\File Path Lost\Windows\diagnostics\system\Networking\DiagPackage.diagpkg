<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007" xmlns:wdem="http://diagnostics.microsoft.com/2007/08/WindowsDiagnosticExtendedMetadata">
  <DiagnosticIdentification>
    <ID>NetworkDiagnostics</ID>
    <Version>4.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>http://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>1.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>false</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>NetworkDiagnosticsTroubleshoot.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <!-- The root causes section includes all manifested root causes, and the placeholder for unmanifested ones -->
  <Rootcauses>
    <!-- Manifested root causes -->
    <Rootcause>
      <!-- RCG_AA_UNREACHABLE_GATEWAY_DHCP -->
      <ID>{BAA4467D-8F57-4739-879B-2CF78298177B}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2206</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2207</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{BAA4467D-8F57-4739-879B-2CF78298177B}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_UNREACHABLE_GATEWAY_STATIC -->
      <ID>{271FFA7D-FD14-4CD5-8C8F-974956ED2D92}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2206</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2207</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{271FFA7D-FD14-4CD5-8C8F-974956ED2D92}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADADAPTERADDRESS_DHCP -->
      <ID>{245A9D66-AE9C-4518-A5B4-655752B0A5BD}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{245A9D66-AE9C-4518-A5B4-655752B0A5BD}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_DUPLICATEADAPTERADDRESS_DHCP -->
      <ID>{D3C919F0-92A0-4a31-B07D-A0D189AF7DCD}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2204</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2205</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RELEASERENEW_IP -->
          <ID>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2229</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2230</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{D3C919F0-92A0-4a31-B07D-A0D189AF7DCD}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADDNSADDRESS_DHCP -->
      <ID>{2D1607C2-C3D5-46bd-800D-48BE6165CE69}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RELEASERENEW_IP -->
          <ID>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2229</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2230</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{2D1607C2-C3D5-46bd-800D-48BE6165CE69}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADGATEWAYADDRESS_DHCP -->
      <ID>{0F6757BD-FABF-4b88-8FD6-04713D672414}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RELEASERENEW_IP -->
          <ID>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2229</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2230</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{0F6757BD-FABF-4b88-8FD6-04713D672414}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADGATEWAYSUBNET_DHCP -->
      <ID>{7CB28410-D5B2-4840-BDE0-A27A4E8CB36E}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RELEASERENEW_IP -->
          <ID>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2229</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2230</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{1A090044-E184-4991-B359-8FEA0B0EF7EA}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{7CB28410-D5B2-4840-BDE0-A27A4E8CB36E}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_NEIGHBORUNREACHABLE_DHCP -->
      <ID>{AA537141-C1AF-4bf0-B8A3-FCF94C877AEF}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2206</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2207</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{AA537141-C1AF-4bf0-B8A3-FCF94C877AEF}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADADAPTERADDRESS_STATIC -->
      <ID>{45449525-3F1C-4779-BF99-ACAFBAA99D63}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_CHANGE_IP -->
          <ID>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2231</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2232</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{45449525-3F1C-4779-BF99-ACAFBAA99D63}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADDNSADDRESS_STATIC -->
      <ID>{0258BFF1-4161-41e5-9F8B-CD8C8D581309}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_CHANGE_IP -->
          <ID>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2231</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2232</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{0258BFF1-4161-41e5-9F8B-CD8C8D581309}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADGATEWAYADDRESS_STATIC -->
      <ID>{D5FA613D-E1D7-427c-BE0B-D4AC47FF1814}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_CHANGE_IP -->
          <ID>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2231</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2232</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{D5FA613D-E1D7-427c-BE0B-D4AC47FF1814}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_BADGATEWAYSUBNET_STATIC -->
      <ID>{6F13C5FB-11B4-4417-AFAE-B154B9248A1B}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2200</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2201</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_CHANGE_IP -->
          <ID>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2231</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2232</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{6F13C5FB-11B4-4417-AFAE-B154B9248A1B}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_DUPLICATEADAPTERADDRESS_STATIC -->
      <ID>{8BF857D1-0441-4f4a-8964-5D64FA736EFA}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2204</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2205</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_CHANGE_IP -->
          <ID>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2231</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2232</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{8BF857D1-0441-4f4a-8964-5D64FA736EFA}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_NEIGHBORUNREACHABLE_STATIC -->
      <ID>{F2F9C581-A2FB-416f-A592-567B97BFC040}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2206</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2207</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_CHANGE_IP -->
          <ID>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2231</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2232</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{e23b33fd-da95-4ab8-b34c-c6598b526b10}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{F2F9C581-A2FB-416f-A592-567B97BFC040}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_AA_DHCPSERVERNOTAVAILABLE -->
      <ID>{55442987-1231-4796-BAE6-A27A4E899857}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\netcorehc.dll,-2202</Name>
        <Description>@%windir%\system32\netcorehc.dll,-2203</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_RESET_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\netcorehc.dll,-2223</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2224</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
        <Resolver>
           <!--ID_ADDRESS_ACQUISITION_LOWH_REPAIR_TURN_ON -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\netcorehc.dll,-2225</Name>
            <Description>@%windir%\system32\netcorehc.dll,-2226</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{55442987-1231-4796-BAE6-A27A4E899857}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCGUID_WLAN_COMMON_CATCHALL -->
      <ID>{1C0B07EF-01C8-4d1f-B137-1C078BB4383C}</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@%windir%\system32\wlanhc.dll,-4501</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--RID_WLAN_COMMON_CATCHALL_REPAIR_RESET_ADAPTER -->
          <ID>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\wlanhc.dll,-4503</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-ab107b29afb9}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
              <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
        <Resolver>
           <!--RID_WLAN_COMMON_CATCHALL_REPAIR_CHECK_AP -->
          <ID>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\wlanhc.dll,-4505</Name>
            <Description>@%windir%\system32\wlanhc.dll,-4506</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{9513cc1c-4a26-4cb8-bf89-0a82129bd105}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{1C0B07EF-01C8-4d1f-B137-1C078BB4383C}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_NDISHC_IPNOTBOUND -->
      <ID>{46EC1E49-CA70-4561-9AB7-009F6B1B3709}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\ndishc.dll,-1318</Name>
        <Description>@%windir%\system32\ndishc.dll,-1319</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
           <!--ID_NDISHC_LOWH_REPAIR_BIND_IP -->
          <ID>{4406F2CC-9CBD-4098-A03A-E5C9810E4895}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>FriendlyInterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\ndishc.dll,-7002</Name>
            <Description>@%windir%\system32\ndishc.dll,-7003</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{4406F2CC-9CBD-4098-A03A-E5C9810E4895}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
           <!--ID_NDISHC_LOWH_REPAIR_DRIVER_HELPERTOPIC -->
          <ID>{713CFC83-BB98-4c4d-93F6-D576BC8D9A0A}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>FriendlyInterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\ndishc.dll,-7000</Name>
            <Description>@%windir%\system32\ndishc.dll,-7001</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{713CFC83-BB98-4c4d-93F6-D576BC8D9A0A}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{46EC1E49-CA70-4561-9AB7-009F6B1B3709}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCGUID_AUTOCONF_LOWH_SERVICE_NOT_RUNNING -->
      <ID>{00E060F8-60C9-4273-B140-3A990C1616DA}</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@%windir%\system32\wlanhc.dll,-1108</Name>
        <Description>@%windir%\system32\wlanhc.dll,-1109</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
           <!--RID_AUTOCONF_LOWH_REPAIR_START_AUTOCONF_SERVICE -->
          <ID>{f2bf7e44-9e8b-4bb2-aead-879fe983f222}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\wlanhc.dll,-1660</Name>
            <Description>@%windir%\system32\wlanhc.dll,-1661</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{f2bf7e44-9e8b-4bb2-aead-879fe983f222}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{00E060F8-60C9-4273-B140-3A990C1616DA}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- DirectAccess not provisioned. This is a root cause outside of NDF diagnosis. -->
      <ID>{E42E5B5A-16E0-43f1-AB32-C94C608D269D}</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-300</Name>
        <Description>@diagpackage.dll,-317</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--Learn about DirectAccess  -->
          <ID>{771D57A9-5E2C-44e0-B490-CEB24EEDA882}</ID>
          <DisplayInformation>
            <Parameters/>
                <Name>@diagpackage.dll,-301</Name>
                <Description>@diagpackage.dll,-302</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
             <Parameters>
                   <Parameter>
                      <Name>RepairName</Name>
                      <DefaultValue>@DiagPackage.dll,-301</DefaultValue>
                   </Parameter>
                   <Parameter>
                      <Name>RepairText</Name>
                      <DefaultValue>@DiagPackage.dll,-302</DefaultValue>
                   </Parameter>
                   <Parameter>
                      <Name>FailResolution</Name>
                      <DefaultValue>FALSE</DefaultValue>
                   </Parameter>
             </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>InteractiveRes.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <!-- DPS Service Not Running, service is not disabled. This is a root cause outside of NDF diagnosis.  -->
      <ID>{F4148D83-8526-48e2-B21A-01894ECCE98C}</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-307</Name>
        <Description>@diagpackage.dll,-308</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--Start DPS Service  -->
          <ID>{EFA6117E-D1D2-4ab2-B4AA-9582A36AC30C}</ID>
          <DisplayInformation>
            <Parameters/>
                <Name>@diagpackage.dll,-309</Name>
                <Description>@diagpackage.dll,-310</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
             <Parameters>
                <Parameter>
                    <Name>SetAuto</Name>
                    <DefaultValue>0</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>StartDPSService.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <!-- DPS Service Not Running, service is disabled. This is a root cause outside of NDF diagnosis.  -->
      <ID>{A693C86C-3907-4467-8A92-F360C3482C01}</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-311</Name>
        <Description>@DiagPackage.dll,-308</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--Start DPS Service  -->
          <ID>{61F49208-2D8F-4dfb-B9F2-9CC74708C7BF}</ID>
          <DisplayInformation>
            <Parameters/>
                <Name>@diagpackage.dll,-312</Name>
                <Description>@DiagPackage.dll,-310</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
             <Parameters>
                <Parameter>
                    <Name>SetAuto</Name>
                    <DefaultValue>1</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>StartDPSService.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <!-- Computer is in safe mode. This is a root cause outside of NDF diagnosis. -->
      <ID>{6880DE42-9ED5-454a-8490-BA407BEABC22}</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-313</Name>
        <Description>@DiagPackage.dll,-2000</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--Computer is in safe mode  -->
          <ID>{9A4CFB56-4787-42a1-98D5-14D34F1EA839}</ID>
          <DisplayInformation>
            <Parameters/>
                <Name>@diagpackage.dll,-314</Name>
                <Description>@diagpackage.dll,-315</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
             <Parameters>
                   <Parameter>
                      <Name>RepairName</Name>
                      <DefaultValue>@DiagPackage.dll,-314</DefaultValue>
                   </Parameter>
                   <Parameter>
                      <Name>RepairText</Name>
                      <DefaultValue>@DiagPackage.dll,-315</DefaultValue>
                   </Parameter>
                   <Parameter>
                      <Name>HelpTopicLink</Name>
                      <DefaultValue>mshelp://Windows/?id=d063548a-3fc9-4723-99f3-b12a0c4354a8</DefaultValue>
                   </Parameter>
                   <Parameter>
                      <Name>HelpTopicLinkText</Name>
                      <DefaultValue>@DiagPackage.dll,-10101</DefaultValue>
                   </Parameter>
                   <Parameter>
                      <Name>FailResolution</Name>
                      <DefaultValue>TRUE</DefaultValue>
                   </Parameter>
             </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>HTInteractiveRes.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <!-- Unmanifested RootCause, single LUA repair-->
    <Rootcause>
      <ID>{000000000-0000-0000-0000-000000000001}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>RootCauseName</Name>
                <DefaultValue/>
           </Parameter>
          <Parameter>
                <Name>RootCauseDescription</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-1000</Name>
        <Description>@diagpackage.dll,-1001</Description>
      </DisplayInformation>
      <Resolvers>
        <!-- LUA -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000001}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-1002</Name>
            <Description>@diagpackage.dll,-1003</Description>
             </DisplayInformation>
             <RequiresConsent>false</RequiresConsent>
             <Script>
             <Parameters>
                   <Parameter>
                      <Name>InstanceID</Name>
                      <DefaultValue/>
                   </Parameter>
                   <Parameter>
                      <Name>RepairID</Name>
                      <DefaultValue/>
                   </Parameter>
             </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{000000000-0000-0000-0000-000000000001}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <!-- Unmanifested RootCause, single Admin repair-->
    <Rootcause>
      <ID>{000000000-0000-0000-0000-000000000002}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>RootCauseName</Name>
                <DefaultValue/>
           </Parameter>
          <Parameter>
                <Name>RootCauseDescription</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@DiagPackage.dll,-1000</Name>
        <Description>@DiagPackage.dll,-1001</Description>
      </DisplayInformation>
      <Resolvers>
        <!-- Admin -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000001}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1002</Name>
            <Description>@DiagPackage.dll,-1003</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{000000000-0000-0000-0000-000000000002}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <!-- Unmanifested RootCause, two LUA repairs-->
    <Rootcause>
      <ID>{000000000-0000-0000-0000-000000000003}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>RootCauseName</Name>
                <DefaultValue/>
           </Parameter>
          <Parameter>
                <Name>RootCauseDescription</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@DiagPackage.dll,-1000</Name>
        <Description>@DiagPackage.dll,-1001</Description>
      </DisplayInformation>
      <Resolvers>
        <!-- LUA -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000001}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1002</Name>
            <Description>@DiagPackage.dll,-1003</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <!-- LUA -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000002}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName1</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription1</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-1010</Name>
            <Description>@diagpackage.dll,-1011</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID1</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{000000000-0000-0000-0000-000000000003}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <!-- Unmanifested RootCause, two Admin repairs-->
    <Rootcause>
      <ID>{000000000-0000-0000-0000-000000000004}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>RootCauseName</Name>
                <DefaultValue/>
           </Parameter>
          <Parameter>
                <Name>RootCauseDescription</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@DiagPackage.dll,-1000</Name>
        <Description>@DiagPackage.dll,-1001</Description>
      </DisplayInformation>
      <Resolvers>
        <!-- Admin -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000001}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1002</Name>
            <Description>@DiagPackage.dll,-1003</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <!-- Admin -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000002}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName1</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription1</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1010</Name>
            <Description>@DiagPackage.dll,-1011</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID1</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{000000000-0000-0000-0000-000000000004}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <!-- Unmanifested RootCause, mix of LUA and Admin repairs-->
    <Rootcause>
      <ID>{000000000-0000-0000-0000-000000000005}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>RootCauseName</Name>
                <DefaultValue/>
           </Parameter>
          <Parameter>
                <Name>RootCauseDescription</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@DiagPackage.dll,-1000</Name>
        <Description>@DiagPackage.dll,-1001</Description>
      </DisplayInformation>
      <Resolvers>
        <!-- LUA -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000001}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1002</Name>
            <Description>@DiagPackage.dll,-1003</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <!-- Admin -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000002}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName1</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription1</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1010</Name>
            <Description>@DiagPackage.dll,-1011</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID1</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{000000000-0000-0000-0000-000000000005}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <!-- Unmanifested RootCause, single LUA Info Only or Help Topic repair (this guarantees it's at the end of the unmanifested list) -->
    <Rootcause>
      <ID>{000000000-0000-0000-0000-000000000006}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>RootCauseName</Name>
                <DefaultValue/>
           </Parameter>
          <Parameter>
                <Name>RootCauseDescription</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@DiagPackage.dll,-1000</Name>
        <Description>@DiagPackage.dll,-1001</Description>
      </DisplayInformation>
      <Resolvers>
        <!-- LUA -->
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000001}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1002</Name>
            <Description>@DiagPackage.dll,-1003</Description>
             </DisplayInformation>
             <RequiresConsent>false</RequiresConsent>
             <Script>
             <Parameters>
                   <Parameter>
                      <Name>InstanceID</Name>
                      <DefaultValue/>
                   </Parameter>
                   <Parameter>
                      <Name>RepairID</Name>
                      <DefaultValue/>
                   </Parameter>
             </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{000000000-0000-0000-0000-000000000006}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <!-- Unmanifested RootCause, single Local logon repair-->
    <Rootcause>
      <ID>{000000000-0000-0000-0000-000000000007}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>RootCauseName</Name>
                <DefaultValue/>
           </Parameter>
          <Parameter>
                <Name>RootCauseDescription</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@DiagPackage.dll,-1000</Name>
        <Description>@DiagPackage.dll,-1001</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>{000000000-0000-0000-0000-000000000001}</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                    <Name>RepairName</Name>
                    <DefaultValue/>
               </Parameter>
              <Parameter>
                    <Name>RepairDescription</Name>
                    <DefaultValue/>
               </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-1002</Name>
            <Description>@DiagPackage.dll,-1003</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
          <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue/>
                </Parameter>
          </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
                <PhysicallyLoggedOnUser/>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{000000000-0000-0000-0000-000000000007}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_DOT3HC_MEDIASTATE_DISCONNECTED -->
      <ID>{DFA35F24-DD2F-4c37-87AE-5D7A8B508786}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\dot3hc.dll,-200</Name>
        <Description>@DiagPackage.dll,-2000</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_DOT3HC_LOWH_REPAIR_CONNECT_MEDIA -->
          <ID>{4c1db172-9a9e-4b1a-bdab-24021291a158}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>InterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\dot3hc.dll,-2000</Name>
            <Description>@%windir%\system32\dot3hc.dll,-2001</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{4c1db172-9a9e-4b1a-bdab-24021291a158}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{DFA35F24-DD2F-4c37-87AE-5D7A8B508786}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
        <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_DOT3HC_MEDIASTATE_DISCONNECTED_MACHINE -->
      <ID>{4DA030B8-86E5-4b6a-A879-2FFF8443B527}</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@%windir%\system32\dot3hc.dll,-202</Name>
        <Description>@DiagPackage.dll,-2000</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_DOT3HC_LOWH_REPAIR_CONNECT_MEDIA_MACHINE -->
          <ID>{1296DFF0-D04E-4be1-A512-90F04DDFA3E6}</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@%windir%\system32\dot3hc.dll,-2002</Name>
            <Description>@%windir%\system32\dot3hc.dll,-2001</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{1296DFF0-D04E-4be1-A512-90F04DDFA3E6}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{4DA030B8-86E5-4b6a-A879-2FFF8443B527}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
        <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
    <Rootcause>
      <!-- RCG_NDISHC_DEVICEDISABLED -->
      <ID>{60372FD2-AD60-45c2-BD83-6B827FC438DF}</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
                <Name>InterfaceName</Name>
                <DefaultValue/>
           </Parameter>
        </Parameters>
        <Name>@%windir%\system32\ndishc.dll,-1300</Name>
        <Description>@diagpackage.dll,-2000</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <!--ID_NDISHC_LOWH_REPAIR_ENABLE_INTERFACE -->
          <ID>{07d37f7b-fa5e-4443-bda7-AB107B29AFB6}</ID>
          <DisplayInformation>
            <Parameters>
                <Parameter>
                    <Name>FriendlyInterfaceName</Name>
                    <DefaultValue/>
                </Parameter>
            </Parameters>
            <Name>@%windir%\system32\ndishc.dll,-1310</Name>
            <Description>@DiagPackage.dll,-2000</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>RepairID</Name>
                   <DefaultValue>{07d37f7b-fa5e-4443-bda7-AB107B29AFB6}</DefaultValue>
                </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>NetworkDiagnosticsResolve.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
            <Parameters>
                <Parameter>
                   <Name>RootCauseID</Name>
                   <DefaultValue>{60372FD2-AD60-45c2-BD83-6B827FC438DF}</DefaultValue>
                </Parameter>
                <Parameter>
                   <Name>InstanceID</Name>
                   <DefaultValue/>
                </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>NetworkDiagnosticsVerify.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
            <Parameter>
                <Name>SBSData</Name>
                <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Keywords</Name>
              <DefaultValue>Network</DefaultValue>
            </Parameter>
      </ContextParameters>
      <ExtensionPoint>
            <SecurityBoundarySafe>%SBSData%</SecurityBoundarySafe>
      </ExtensionPoint>
    </Rootcause>
  </Rootcauses>
  <Interactions>
      <SingleResponseInteractions>
        <SingleResponseInteraction>
          <AllowDynamicResponses>false</AllowDynamicResponses>
           <Choices>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-201</Name>
                  <Description>@diagpackage.dll,-202</Description>
                </DisplayInformation>
                <Value>HTTP</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-203</Name>
                      <Description>@diagpackage.dll,-204</Description>
                </DisplayInformation>
                <Value>SMB</Value>
                <ExtensionPoint/>
              </Choice>
             <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-205</Name>
                      <Description>@diagpackage.dll,-206</Description>
                </DisplayInformation>
                <Value>NetworkAdapter</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-217</Name>
                  <Description>@diagpackage.dll,-218</Description>
                 </DisplayInformation>
                 <Value>Winsock</Value>
                 <ExtensionPoint/>
               </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-221</Name>
                  <Description>@diagpackage.dll,-222</Description>
                 </DisplayInformation>
                 <Value>Grouping</Value>
                 <ExtensionPoint/>
               </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-223</Name>
                  <Description>@diagpackage.dll,-224</Description>
                 </DisplayInformation>
                 <Value>Inbound</Value>
                 <ExtensionPoint/>
               </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-226</Name>
                  <Description>@diagpackage.dll,-227</Description>
                 </DisplayInformation>
                 <Value>DirectAccess</Value>
                 <ExtensionPoint/>
               </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-232</Name>
                  <Description>@diagpackage.dll,-233</Description>
                 </DisplayInformation>
                 <Value>DefaultConnectivity</Value>
                 <ExtensionPoint/>
               </Choice>
               <Choice>
                 <DisplayInformation>
                   <Parameters/>
                   <Name>@DiagPackage.dll,-103</Name>
                  <Description>@DiagPackage.dll,-104</Description>
                </DisplayInformation>
                <Value>InContext</Value>
                <ExtensionPoint/>
              </Choice>
            </Choices>
          <ID>IT_EntryPoint</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-101</Name>
            <Description>@diagpackage.dll,-102</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <NoUI/>
          </ExtensionPoint>
        </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>true</AllowDynamicResponses>
          <Choices/>
          <ID>IT_NetworkAdapter</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-111</Name>
            <Description>@diagpackage.dll,-112</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint/>
       </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>false</AllowDynamicResponses>
          <Choices>
               <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-228</Name>
                      <Description>@diagpackage.dll,-229</Description>
                </DisplayInformation>
                <Value>Internet</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                 <DisplayInformation>
                     <Parameters/>
                     <Name>@diagpackage.dll,-230</Name>
                     <Description>@DiagPackage.dll,-2000</Description>
               </DisplayInformation>
               <Value>URL</Value>
               <ExtensionPoint/>
             </Choice>
          </Choices>
          <ID>IT_WebChoice</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-123</Name>
            <Description>@DiagPackage.dll,-2000</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
                <CommandLinks/>
          </ExtensionPoint>
        </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>true</AllowDynamicResponses>
          <Choices/>
          <ID>IT_ServiceName</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-121</Name>
                <Description>@diagpackage.dll,-127</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint/>
        </SingleResponseInteraction>
       <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
              <Choice>
                <DisplayInformation>
                  <Parameters>
                    <Parameter>
                       <Name>IT_P_ContinueButtonName</Name>
                       <DefaultValue/>
                    </Parameter>
                      <Parameter>
                       <Name>IT_P_ContinueButtonDescription</Name>
                       <DefaultValue/>
                    </Parameter>
                  </Parameters>
                  <Name>@DiagPackage.dll,-207</Name>
                  <Description>@DiagPackage.dll,-208</Description>
                </DisplayInformation>
                <Value>Validate</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@DiagPackage.dll,-209</Name>
                  <Description>@DiagPackage.dll,-210</Description>
                </DisplayInformation>
                <Value>Skip</Value>
                <ExtensionPoint/>
              </Choice>
        </Choices>
        <ID>IT_ValidateHelpTopicRepair</ID>
        <DisplayInformation>
          <Parameters>
              <Parameter>
               <Name>IT_P_Name</Name>
               <DefaultValue/>
            </Parameter>
              <Parameter>
               <Name>IT_P_Description</Name>
               <DefaultValue/>
            </Parameter>
          </Parameters>
          <Name>@DiagPackage.dll,-113</Name>
          <Description>@DiagPackage.dll,-114</Description>
        </DisplayInformation>
        <ContextParameters>
            <Parameter>
               <Name>IT_P_HelpTopicLink</Name>
               <DefaultValue/>
            </Parameter>
            <Parameter>
               <Name>IT_P_HelpTopicText</Name>
               <DefaultValue/>
            </Parameter>
        </ContextParameters>
        <ExtensionPoint>
            <NoCache/>
            <CommandLinks/>
            <Link>%IT_P_HelpTopicLink%</Link>
            <LinkText>%IT_P_HelpTopicText%</LinkText>
        <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
       </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>false</AllowDynamicResponses>
          <Choices>
               <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-219</Name>
                      <Description>@DiagPackage.dll,-2000</Description>
                </DisplayInformation>
                <Value>6</Value>
                <ExtensionPoint/>
              </Choice>
               <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-220</Name>
                      <Description>@DiagPackage.dll,-2000</Description>
                </DisplayInformation>
                <Value>17</Value>
                <ExtensionPoint/>
              </Choice>
          </Choices>
          <ID>IT_Protocol</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-117</Name>
            <Description>@DiagPackage.dll,-2000</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint/>
        </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>false</AllowDynamicResponses>
          <Choices>
               <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-234</Name>
                      <Description>@DiagPackage.dll,-2000</Description>
                </DisplayInformation>
                <Value>HTTPorUNC</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                 <DisplayInformation>
                     <Parameters/>
                     <Name>@diagpackage.dll,-235</Name>
                     <Description>@diagpackage.dll,-236</Description>
               </DisplayInformation>
               <Value>Other</Value>
               <ExtensionPoint/>
             </Choice>
          </Choices>
          <ID>IT_DefaultConnectivityInitialChoice</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-124</Name>
            <Description>@diagpackage.dll,-125</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
                <CommandLinks/>
          </ExtensionPoint>
        </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>false</AllowDynamicResponses>
          <Choices>
               <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-237</Name>
                      <Description>@DiagPackage.dll,-2000</Description>
                </DisplayInformation>
                <Value>Inbound</Value>
                <ExtensionPoint>
                    <Default/>
                </ExtensionPoint>
              </Choice>
               <Choice>
                  <DisplayInformation>
                      <Parameters/>
                      <Name>@diagpackage.dll,-238</Name>
                      <Description>@DiagPackage.dll,-2000</Description>
                </DisplayInformation>
                <Value>DirectAccess</Value>
                <ExtensionPoint/>
              </Choice>
          </Choices>
          <ID>IT_DefaultConnectivityOtherChoice</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-126</Name>
            <Description>@DiagPackage.dll,-2000</Description>
          </DisplayInformation>
          <ContextParameters/>
        <ExtensionPoint>
            <CommandLinks/>
        </ExtensionPoint>
        </SingleResponseInteraction>
       <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
            <Choice>
                <DisplayInformation>
                  <Parameters>
                    <Parameter>
                       <Name>IT_P_ContinueButtonName</Name>
                       <DefaultValue/>
                    </Parameter>
                      <Parameter>
                       <Name>IT_P_ContinueButtonDescription</Name>
                       <DefaultValue/>
                    </Parameter>
                  </Parameters>
                  <Name>@diagpackage.dll,-207</Name>
                  <Description>@diagpackage.dll,-208</Description>
                </DisplayInformation>
                <Value>Validate</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-209</Name>
                  <Description>@diagpackage.dll,-210</Description>
                </DisplayInformation>
                <Value>Skip</Value>
                <ExtensionPoint/>
              </Choice>
        </Choices>
        <ID>IT_UserActionRepair</ID>
        <DisplayInformation>
          <Parameters>
              <Parameter>
               <Name>IT_P_Name</Name>
               <DefaultValue/>
            </Parameter>
              <Parameter>
               <Name>IT_P_Description</Name>
               <DefaultValue/>
            </Parameter>
          </Parameters>
          <Name>@DiagPackage.dll,-113</Name>
          <Description>@DiagPackage.dll,-114</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
            <NoCache/>
            <CommandLinks/>
        <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
       </SingleResponseInteraction>
       <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
            <Choice>
                <DisplayInformation>
                    <Parameters>
                        <Parameter>
                           <Name>IT_P_LaunchButtonName</Name>
                           <DefaultValue/>
                        </Parameter>
                          <Parameter>
                           <Name>IT_P_LaunchButtonDescription</Name>
                           <DefaultValue/>
                        </Parameter>
                    </Parameters>
                  <Name>@diagpackage.dll,-211</Name>
                  <Description>@diagpackage.dll,-242</Description>
                </DisplayInformation>
                <Value>LaunchUI</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@DiagPackage.dll,-209</Name>
                  <Description>@DiagPackage.dll,-210</Description>
                </DisplayInformation>
                <Value>Skip</Value>
                <ExtensionPoint/>
              </Choice>
        </Choices>
        <ID>IT_UIRepair</ID>
        <DisplayInformation>
          <Parameters>
              <Parameter>
               <Name>IT_P_Name</Name>
               <DefaultValue/>
            </Parameter>
              <Parameter>
               <Name>IT_P_Description</Name>
               <DefaultValue/>
            </Parameter>
          </Parameters>
          <Name>@DiagPackage.dll,-113</Name>
          <Description>@DiagPackage.dll,-114</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
                <NoCache/>
            <CommandLinks/>
        <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
       </SingleResponseInteraction>
       <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
            <Choice>
                <DisplayInformation>
                  <Parameters>
                    <Parameter>
                       <Name>IT_P_ContinueButtonName</Name>
                       <DefaultValue/>
                    </Parameter>
                      <Parameter>
                       <Name>IT_P_ContinueButtonDescription</Name>
                       <DefaultValue/>
                    </Parameter>
                  </Parameters>
                  <Name>@DiagPackage.dll,-207</Name>
                  <Description>@DiagPackage.dll,-208</Description>
                </DisplayInformation>
                <Value>Validate</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@DiagPackage.dll,-209</Name>
                  <Description>@DiagPackage.dll,-210</Description>
                </DisplayInformation>
                <Value>Skip</Value>
                <ExtensionPoint/>
              </Choice>
        </Choices>
        <ID>IT_AsyncUIRepair</ID>
        <DisplayInformation>
          <Parameters>
              <Parameter>
               <Name>IT_P_Name</Name>
               <DefaultValue/>
            </Parameter>
              <Parameter>
               <Name>IT_P_Description</Name>
               <DefaultValue/>
            </Parameter>
          </Parameters>
          <Name>@DiagPackage.dll,-113</Name>
          <Description>@DiagPackage.dll,-114</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
            <CommandLinks/>
        <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
       </SingleResponseInteraction>
       <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
            <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-216</Name>
                  <Description/>
                </DisplayInformation>
                <Value>Validate</Value>
                <ExtensionPoint/>
              </Choice>
              <Choice>
                <DisplayInformation>
                  <Parameters/>
                  <Name>@DiagPackage.dll,-209</Name>
                  <Description>@DiagPackage.dll,-210</Description>
                </DisplayInformation>
                <Value>Skip</Value>
                <ExtensionPoint/>
              </Choice>
        </Choices>
        <ID>IT_UserConfirmation</ID>
        <DisplayInformation>
          <Parameters>
              <Parameter>
               <Name>IT_P_Name</Name>
               <DefaultValue/>
            </Parameter>
              <Parameter>
               <Name>IT_P_Description</Name>
               <DefaultValue/>
            </Parameter>
          </Parameters>
          <Name>@DiagPackage.dll,-113</Name>
          <Description>@DiagPackage.dll,-114</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
                <NoCache/>
            <CommandLinks/>
        <InteractionIcon>warning</InteractionIcon>
         </ExtensionPoint>
       </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>false</AllowDynamicResponses>
          <Choices>
            <Choice>
              <DisplayInformation>
                <Parameters/>
                <Name>@DiagPackage.dll,-216</Name>
                <Description>@diagpackage.dll,-250</Description>
              </DisplayInformation>
              <Value>Validate</Value>
              <ExtensionPoint/>
            </Choice>
            <Choice>
              <DisplayInformation>
                <Parameters/>
                <Name>@DiagPackage.dll,-209</Name>
                <Description>@DiagPackage.dll,-210</Description>
              </DisplayInformation>
              <Value>Skip</Value>
              <ExtensionPoint/>
            </Choice>
          </Choices>
          <ID>IT_UserConfirmationReboot</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>IT_P_Name</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>IT_P_Description</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@DiagPackage.dll,-113</Name>
            <Description/>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <NoCache/>
            <CommandLinks/>
            <RTFDescription>@diagpackage.dll,-33108</RTFDescription>
            <InteractionIcon>warning</InteractionIcon>
          </ExtensionPoint>
        </SingleResponseInteraction>
      </SingleResponseInteractions>
      <MultipleResponseInteractions>
      </MultipleResponseInteractions>
      <TextInteractions>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_HelperClassName</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-103</Name>
            <Description>@diagpackage.dll,-104</Description>
          </DisplayInformation>
          <ContextParameters/>
        <ExtensionPoint>
        <InteractionIcon>info</InteractionIcon>
          </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_HelperAttributes</ID>
          <DisplayInformation>
            <Parameters/>
              <Name>@DiagPackage.dll,-103</Name>
              <Description>@DiagPackage.dll,-104</Description>
          </DisplayInformation>
          <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>info</InteractionIcon>
          </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_URL</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-107</Name>
            <Description>@diagpackage.dll,-108</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <Default>http://</Default>
          <InteractionIcon>info</InteractionIcon>
          </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_UNC</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-109</Name>
            <Description>@diagpackage.dll,-110</Description>
          </DisplayInformation>
          <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>info</InteractionIcon>
        </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_Invalid_URL</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@DiagPackage.dll,-107</Name>
            <Description>@DiagPackage.dll,-108</Description>
          </DisplayInformation>
          <ContextParameters>
             <Parameter>
              <Name>URL</Name>
              <DefaultValue/>
             </Parameter>
            <Parameter>
              <Name>RTFText</Name>
              <DefaultValue/>
             </Parameter>
          </ContextParameters>
          <ExtensionPoint>
            <Default>%URL%</Default>
            <NoCache/>
            <RTFDescription>@diagpackage.dll,-316</RTFDescription>
          <InteractionIcon>info</InteractionIcon>
          </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_Invalid_UNC</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@DiagPackage.dll,-109</Name>
            <Description>@DiagPackage.dll,-110</Description>
          </DisplayInformation>
          <ContextParameters>
             <Parameter>
              <Name>UNC</Name>
              <DefaultValue/>
             </Parameter>
            <Parameter>
              <Name>RTFText</Name>
              <DefaultValue/>
             </Parameter>
          </ContextParameters>
          <ExtensionPoint>
            <Default>%UNC%</Default>
            <NoCache/>
            <RTFDescription>@DiagPackage.dll,-316</RTFDescription>
          <InteractionIcon>info</InteractionIcon>
          </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_RemoteAddress</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-115</Name>
            <Description>@diagpackage.dll,-116</Description>
          </DisplayInformation>
          <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>info</InteractionIcon>
        </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_ApplicationID</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-118</Name>
            <Description>@diagpackage.dll,-119</Description>
          </DisplayInformation>
          <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>info</InteractionIcon>
        </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_BrowseFile</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-122</Name>
            <Description>@diagpackage.dll,-128</Description>
          </DisplayInformation>
          <ContextParameters/>
              <ExtensionPoint>
                  <Browse FilterText="Executables" FilterExtension="*.exe">File</Browse>
                <InteractionIcon>info</InteractionIcon>
            </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_Invalid_BrowseFile</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@DiagPackage.dll,-122</Name>
            <Description>@DiagPackage.dll,-128</Description>
          </DisplayInformation>
              <ContextParameters>
               <Parameter>
                  <Name>File</Name>
                  <DefaultValue/>
               </Parameter>
               <Parameter>
                  <Name>RTFText</Name>
                  <DefaultValue/>
               </Parameter>
              </ContextParameters>
              <ExtensionPoint>
              <Default>%File%</Default>
              <NoCache/>
              <RTFDescription>@DiagPackage.dll,-316</RTFDescription>
                  <Browse FilterText="Executables" FilterExtension="*.exe">File</Browse>
                <InteractionIcon>info</InteractionIcon>
            </ExtensionPoint>
        </TextInteraction>
            <TextInteraction>
          <RegularExpression/>
              <ID>IT_Invalid_BrowseFile</ID>
              <DisplayInformation>
                <Parameters/>
                <Name>@DiagPackage.dll,-122</Name>
                <Description>@DiagPackage.dll,-128</Description>
              </DisplayInformation>
              <ContextParameters>
                <Parameter>
                    <Name>File</Name>
                    <DefaultValue/>
                </Parameter>
                <Parameter>
                    <Name>RTFText</Name>
                    <DefaultValue/>
                </Parameter>
              </ContextParameters>
              <ExtensionPoint>
                <Default>%File%</Default>
                <NoCache/>
                <RTFDescription>@DiagPackage.dll,-316</RTFDescription>
                <Browse FilterText="Executables" FilterExtension="*.exe">File</Browse>
                <InteractionIcon>info</InteractionIcon>
              </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_GroupName</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-120</Name>
            <Description>@DiagPackage.dll,-2000</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint/>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_URLOrUNC</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-240</Name>
            <Description>@diagpackage.dll,-241</Description>
          </DisplayInformation>
          <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>info</InteractionIcon>
        </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>IT_Invalid_URLOrUNC</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@DiagPackage.dll,-240</Name>
            <Description>@DiagPackage.dll,-241</Description>
          </DisplayInformation>
          <ContextParameters>
             <Parameter>
              <Name>URLOrUNC</Name>
              <DefaultValue/>
             </Parameter>
            <Parameter>
              <Name>RTFText</Name>
              <DefaultValue/>
             </Parameter>
          </ContextParameters>
          <ExtensionPoint>
            <Default>%URLOrUNC%</Default>
            <NoCache/>
            <RTFDescription>@DiagPackage.dll,-316</RTFDescription>
          <InteractionIcon>info</InteractionIcon>
          </ExtensionPoint>
        </TextInteraction>
        <TextInteraction>
          <RegularExpression/>
          <ID>SecurityBoundarySafe</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-198</Name>
            <Description>@diagpackage.dll,-199</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <NoUI/>
          </ExtensionPoint>
        </TextInteraction>
      </TextInteractions>
      <PauseInteractions>
          <PauseInteraction>
            <ID>IT_InfoOnlyRepair</ID>
            <DisplayInformation>
              <Parameters>
                  <Parameter>
                   <Name>IT_P_Name</Name>
                   <DefaultValue/>
                </Parameter>
                  <Parameter>
                   <Name>IT_P_Description</Name>
                   <DefaultValue/>
                </Parameter>
              </Parameters>
              <Name>@diagpackage.dll,-113</Name>
              <Description>@diagpackage.dll,-114</Description>
            </DisplayInformation>
            <ContextParameters/>
            <ExtensionPoint>
                        <NoCache/>
          <InteractionIcon>warning</InteractionIcon>
            </ExtensionPoint>
           </PauseInteraction>
           <PauseInteraction>
            <ID>IT_HelpTopicRepair</ID>
            <DisplayInformation>
              <Parameters>
                  <Parameter>
                   <Name>IT_P_Name</Name>
                   <DefaultValue/>
                </Parameter>
                  <Parameter>
                   <Name>IT_P_Description</Name>
                   <DefaultValue/>
                </Parameter>
              </Parameters>
              <Name>@DiagPackage.dll,-113</Name>
              <Description>@DiagPackage.dll,-114</Description>
            </DisplayInformation>
            <ContextParameters>
                <Parameter>
                   <Name>IT_P_HelpTopicLink</Name>
                   <DefaultValue/>
                </Parameter>
                <Parameter>
                   <Name>IT_P_HelpTopicText</Name>
                   <DefaultValue/>
                </Parameter>
            </ContextParameters>
            <ExtensionPoint>
                    <NoCache/>
                <Link>%IT_P_HelpTopicLink%</Link>
                <LinkText>%IT_P_HelpTopicText%</LinkText>
          <InteractionIcon>warning</InteractionIcon>
            </ExtensionPoint>
           </PauseInteraction>
      </PauseInteractions>
      <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
        <Icon>@%windir%\diagnostics\system\Networking\DiagPackage.dll,-20006</Icon>
        <HelpKeywords>@DiagPackage.dll,-20001</HelpKeywords>
        <HelpKeywords>@DiagPackage.dll,-20010</HelpKeywords>
        <HelpKeywords>@DiagPackage.dll,-20011</HelpKeywords>
        <HelpKeywords>@DiagPackage.dll,-20013</HelpKeywords>
        <HelpKeywords>@DiagPackage.dll,-20014</HelpKeywords>
        <HelpKeywords>@DiagPackage.dll,-20016</HelpKeywords>
        <Feedback>
          <ContextId>862</ContextId>
        </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>