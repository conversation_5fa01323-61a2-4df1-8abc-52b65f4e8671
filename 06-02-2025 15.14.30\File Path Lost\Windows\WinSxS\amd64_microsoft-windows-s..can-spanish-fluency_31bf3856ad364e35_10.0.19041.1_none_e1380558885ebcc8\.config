{"character-map": "charactermap.json", "ime": "ime.json", "models": [{"path": "es_MX_bus_c.lm1", "type": "term", "version": "3"}, {"path": "es_MX_bus_c.lm3", "type": "term", "version": "3"}, {"path": "emoji_bg_c.lm2", "tags": ["emoji"], "type": "term", "version": "1"}], "punctuation": "punctuation.json", "tags": ["spanish", "es", "MX", "id:es_MX", "id:es", "name:<PERSON><PERSON><PERSON><PERSON><PERSON> (Mexico)"], "timestamp": "2019/03/20 14:09", "version": "4"}