﻿2024-09-09 10:03:01, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2024-09-09 10:03:01, Error                        0x80070003 in PbrDeleteDirectory (base\reset\util\src\filesystem.cpp:2948): Failed to delete directory [\\?\C:\$WinREAgent\Scratch][gle=0x00000003]
2024-09-09 10:03:01, Error                        0x80070003 in PushButtonReset::Directory::Delete (base\reset\util\src\filesystem.cpp:2981): Failed to recursively delete [C:\$WinREAgent\Scratch][gle=0x00000003]
2024-09-09 10:03:01, Warning                      0x80070003 in WinREAgent::WorkDir::CleanupScratchDir (base\diagnosis\srt\winreagent\lib\operations\src\workdir.cpp:155): Failed to delete scratch dir
2024-09-09 10:03:01, Info                         [svchost.exe] Enter WinReGetConfig
2024-09-09 10:03:01, Info                         [svchost.exe] Parameters: configWinDir: NULL
2024-09-09 10:03:01, Info                         [svchost.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2024-09-09 10:03:01, Info                         [svchost.exe] Update enhanced config info is enabled.
2024-09-09 10:03:01, Info                         [svchost.exe] WinRE is installed
2024-09-09 10:03:01, Info                         [svchost.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2024-09-09 10:03:01, Info                         [svchost.exe] System is WimBoot: FALSE
2024-09-09 10:03:01, Info                         [svchost.exe] WinRE image validated
2024-09-09 10:03:01, Info                         [svchost.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll: API Version 10.0.19041.844 - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll: Parent process command line: C:\Windows\system32\svchost.exe -k netsvcs -p -s wuauserv - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Enter DismInitializeInternal - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Initialized GlobalConfig - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Initialized SessionTable - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Waiting for m_pInternalThread to start - CCommandThread::Start
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 m_pInternalThread started - CCommandThread::Start
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Created g_internalDismSession - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Leave DismInitializeInternal - DismInitializeInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:03:01, Info                  DISM   PID=5488 TID=6548 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2024-09-09 10:03:01, Info                  DISM   PID=5488 TID=6548 DismCore.dll version: 10.0.19041.746 - CDISMManager::FinalConstruct
2024-09-09 10:03:01, Info                  DISM   PID=5488 TID=6548 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2024-09-09 10:03:01, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:03:01, Info                  DISM   PID=5488 TID=6548 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2024-09-09 10:03:01, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:03:01, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:03:01, Info                  DISM   DISM Manager: PID=5488 TID=6548 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:03:01, Info                  DISM   DISM Imaging Provider: PID=5488 TID=6548 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Enter DismDeleteInternal - DismDeleteInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Leave DismDeleteInternal - DismDeleteInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Enter DismShutdownInternal - DismShutdownInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:03:01, Info                  DISM   DISM Provider Store: PID=5488 TID=6548 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=6548 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Deleted g_internalDismSession - DismShutdownInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Shutdown SessionTable - DismShutdownInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 Leave DismShutdownInternal - DismShutdownInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll:                                          - DismShutdownInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2024-09-09 10:03:01, Info                  DISM   API: PID=5488 TID=5236 DismApi.dll:                                          - DismShutdownInternal
2024-09-09 10:06:42, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2024-09-09 10:06:42, Info                         [wuauclt.exe] Enter WinReGetConfig
2024-09-09 10:06:42, Info                         [wuauclt.exe] Parameters: configWinDir: NULL
2024-09-09 10:06:42, Info                         [wuauclt.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2024-09-09 10:06:42, Info                         [wuauclt.exe] Update enhanced config info is enabled.
2024-09-09 10:06:42, Info                         [wuauclt.exe] WinRE is installed
2024-09-09 10:06:42, Info                         [wuauclt.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2024-09-09 10:06:42, Info                         [wuauclt.exe] System is WimBoot: FALSE
2024-09-09 10:06:42, Info                         [wuauclt.exe] WinRE image validated
2024-09-09 10:06:42, Info                         [wuauclt.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll: API Version 10.0.19041.844 - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll: Parent process command line: "C:\Windows\system32\wuauclt.exe" /UpdateDeploymentProvider UpdateDeploymentProvider.dll /ClassId caf1b9bc-b4b7-4e9f-a769-c67927e617e7 /RunHandlerComServer - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Enter DismInitializeInternal - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Initialized GlobalConfig - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Initialized SessionTable - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Waiting for m_pInternalThread to start - CCommandThread::Start
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 m_pInternalThread started - CCommandThread::Start
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Created g_internalDismSession - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Leave DismInitializeInternal - DismInitializeInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:06:42, Info                  DISM   PID=9068 TID=8712 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2024-09-09 10:06:42, Info                  DISM   PID=9068 TID=8712 DismCore.dll version: 10.0.19041.746 - CDISMManager::FinalConstruct
2024-09-09 10:06:42, Info                  DISM   PID=9068 TID=8712 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2024-09-09 10:06:42, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:06:42, Info                  DISM   PID=9068 TID=8712 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2024-09-09 10:06:42, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:06:42, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:06:42, Info                  DISM   DISM Manager: PID=9068 TID=8712 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:06:42, Info                  DISM   DISM Imaging Provider: PID=9068 TID=8712 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Enter DismDeleteInternal - DismDeleteInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Leave DismDeleteInternal - DismDeleteInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Enter DismShutdownInternal - DismShutdownInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:06:42, Info                  DISM   DISM Provider Store: PID=9068 TID=8712 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=8712 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Deleted g_internalDismSession - DismShutdownInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Shutdown SessionTable - DismShutdownInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 Leave DismShutdownInternal - DismShutdownInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll:                                          - DismShutdownInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2024-09-09 10:06:42, Info                  DISM   API: PID=9068 TID=9096 DismApi.dll:                                          - DismShutdownInternal
2024-09-09 10:18:21, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2024-09-09 10:18:21, Info                         [svchost.exe] Enter WinReGetConfig
2024-09-09 10:18:21, Info                         [svchost.exe] Parameters: configWinDir: NULL
2024-09-09 10:18:21, Info                         [svchost.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2024-09-09 10:18:21, Info                         [svchost.exe] Update enhanced config info is enabled.
2024-09-09 10:18:21, Info                         [svchost.exe] WinRE is installed
2024-09-09 10:18:21, Info                         [svchost.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2024-09-09 10:18:21, Info                         [svchost.exe] System is WimBoot: FALSE
2024-09-09 10:18:21, Info                         [svchost.exe] WinRE image validated
2024-09-09 10:18:21, Info                         [svchost.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll: API Version 10.0.19041.844 - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll: Parent process command line: C:\Windows\system32\svchost.exe -k netsvcs -p -s wuauserv - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Enter DismInitializeInternal - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Initialized GlobalConfig - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Initialized SessionTable - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Waiting for m_pInternalThread to start - CCommandThread::Start
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 m_pInternalThread started - CCommandThread::Start
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Created g_internalDismSession - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Leave DismInitializeInternal - DismInitializeInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:18:21, Info                  DISM   PID=5488 TID=8476 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2024-09-09 10:18:21, Info                  DISM   PID=5488 TID=8476 DismCore.dll version: 10.0.19041.746 - CDISMManager::FinalConstruct
2024-09-09 10:18:21, Info                  DISM   PID=5488 TID=8476 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2024-09-09 10:18:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:18:21, Info                  DISM   PID=5488 TID=8476 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2024-09-09 10:18:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:18:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:18:21, Info                  DISM   DISM Manager: PID=5488 TID=8476 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:18:21, Info                  DISM   DISM Imaging Provider: PID=5488 TID=8476 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Enter DismDeleteInternal - DismDeleteInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Leave DismDeleteInternal - DismDeleteInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Enter DismShutdownInternal - DismShutdownInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:18:21, Info                  DISM   DISM Provider Store: PID=5488 TID=8476 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8476 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Deleted g_internalDismSession - DismShutdownInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Shutdown SessionTable - DismShutdownInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 Leave DismShutdownInternal - DismShutdownInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll:                                          - DismShutdownInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2024-09-09 10:18:21, Info                  DISM   API: PID=5488 TID=8392 DismApi.dll:                                          - DismShutdownInternal
2024-09-09 10:48:17, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2024-09-09 10:48:17, Info                         [svchost.exe] Enter WinReGetConfig
2024-09-09 10:48:17, Info                         [svchost.exe] Parameters: configWinDir: NULL
2024-09-09 10:48:17, Info                         [svchost.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2024-09-09 10:48:17, Info                         [svchost.exe] Update enhanced config info is enabled.
2024-09-09 10:48:17, Info                         [svchost.exe] WinRE is installed
2024-09-09 10:48:17, Info                         [svchost.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2024-09-09 10:48:17, Info                         [svchost.exe] System is WimBoot: FALSE
2024-09-09 10:48:17, Info                         [svchost.exe] WinRE image validated
2024-09-09 10:48:17, Info                         [svchost.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll:                                            - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll: Parent process command line: C:\Windows\system32\svchost.exe -k netsvcs -p -s wuauserv - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Enter DismInitializeInternal - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Initialized GlobalConfig - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Initialized SessionTable - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Waiting for m_pInternalThread to start - CCommandThread::Start
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 m_pInternalThread started - CCommandThread::Start
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Created g_internalDismSession - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Leave DismInitializeInternal - DismInitializeInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:48:17, Info                  DISM   PID=6652 TID=5836 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2024-09-09 10:48:17, Info                  DISM   PID=6652 TID=5836 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2024-09-09 10:48:17, Info                  DISM   PID=6652 TID=5836 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2024-09-09 10:48:17, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:48:17, Info                  DISM   PID=6652 TID=5836 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2024-09-09 10:48:17, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2024-09-09 10:48:17, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2024-09-09 10:48:17, Info                  DISM   DISM Manager: PID=6652 TID=5836 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:48:17, Info                  DISM   DISM Imaging Provider: PID=6652 TID=5836 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Enter DismDeleteInternal - DismDeleteInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Leave DismDeleteInternal - DismDeleteInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Enter DismShutdownInternal - DismShutdownInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:48:17, Info                  DISM   DISM Provider Store: PID=6652 TID=5836 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=5836 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Deleted g_internalDismSession - DismShutdownInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Shutdown SessionTable - DismShutdownInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 Leave DismShutdownInternal - DismShutdownInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll:                                          - DismShutdownInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2024-09-09 10:48:17, Info                  DISM   API: PID=6652 TID=2096 DismApi.dll:                                          - DismShutdownInternal
2025-01-18 12:24:44, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-01-18 12:24:44, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-01-18 12:24:44, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-01-18 12:24:44, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-01-18 12:24:44, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                            - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                            - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 0f59ef988c9e5ffb885fa7c5cb773735 116+69IMxEu60pznqfk0gA.*******.0 - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Enter DismInitializeInternal - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Initialized GlobalConfig - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Initialized SessionTable - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 m_pInternalThread started - CCommandThread::Start
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Created g_internalDismSession - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Leave DismInitializeInternal - DismInitializeInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-01-18 12:24:46, Info                  DISM   PID=5220 TID=6720 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-01-18 12:24:46, Info                  DISM   PID=5220 TID=6720 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-01-18 12:24:46, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:46, Info                  DISM   PID=5220 TID=6720 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-01-18 12:24:46, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-01-18 12:24:46, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:46, Info                  DISM   DISM Manager: PID=5220 TID=6720 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-01-18 12:24:46, Info                  DISM   DISM Imaging Provider: PID=5220 TID=6720 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Enter DismDeleteInternal - DismDeleteInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Leave DismDeleteInternal - DismDeleteInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Enter DismShutdownInternal - DismShutdownInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-01-18 12:24:46, Info                  DISM   DISM Provider Store: PID=5220 TID=6720 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=6720 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Deleted g_internalDismSession - DismShutdownInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Shutdown SessionTable - DismShutdownInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 Leave DismShutdownInternal - DismShutdownInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                          - DismShutdownInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-01-18 12:24:46, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                          - DismShutdownInternal
2025-01-18 12:24:46, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-01-18 12:24:46, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-01-18 12:24:47, Info                         WinRE partition total space [943714304], free space [476094464]
2025-01-18 12:24:47, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-01-18 12:24:47, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                            - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                            - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 0f59ef988c9e5ffb885fa7c5cb773735 116+69IMxEu60pznqfk0gA.*******.0 - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Enter DismInitializeInternal - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Initialized GlobalConfig - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Initialized SessionTable - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 m_pInternalThread started - CCommandThread::Start
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Created g_internalDismSession - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Leave DismInitializeInternal - DismInitializeInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-01-18 12:24:48, Info                  DISM   PID=5220 TID=2628 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-01-18 12:24:48, Info                  DISM   PID=5220 TID=2628 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-01-18 12:24:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:48, Info                  DISM   PID=5220 TID=2628 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-01-18 12:24:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-01-18 12:24:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-01-18 12:24:48, Info                  DISM   DISM Manager: PID=5220 TID=2628 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-01-18 12:24:48, Info                  DISM   DISM Imaging Provider: PID=5220 TID=2628 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Enter DismDeleteInternal - DismDeleteInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Leave DismDeleteInternal - DismDeleteInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Enter DismShutdownInternal - DismShutdownInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-01-18 12:24:48, Info                  DISM   DISM Provider Store: PID=5220 TID=2628 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=2628 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Deleted g_internalDismSession - DismShutdownInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Shutdown SessionTable - DismShutdownInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 Leave DismShutdownInternal - DismShutdownInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                          - DismShutdownInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-01-18 12:24:48, Info                  DISM   API: PID=5220 TID=9080 DismApi.dll:                                          - DismShutdownInternal
2025-01-18 12:24:48, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-01-18 12:24:48, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-01-18 12:24:49, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-01-18 12:24:49, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-01-18 12:24:49, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-01-18 12:24:49, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-01-18 12:24:49, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-01-18 12:24:49, Info                         WinRE partition total space [943714304], free space [476094464]
2025-04-09 11:48:43, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 53d128d3d6c44fa37af5db4ce4e4b7ab pq+NF3Rtr06OwwQP9mMobQ.*******.0 - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Enter DismInitializeInternal - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Initialized GlobalConfig - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Initialized SessionTable - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 m_pInternalThread started - CCommandThread::Start
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Created g_internalDismSession - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Leave DismInitializeInternal - DismInitializeInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 11:48:43, Info                  DISM   PID=2012 TID=7240 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-09 11:48:43, Info                  DISM   PID=2012 TID=7240 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-09 11:48:43, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 11:48:43, Info                  DISM   PID=2012 TID=7240 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-09 11:48:43, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 11:48:43, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 11:48:43, Info                  DISM   DISM Manager: PID=2012 TID=7240 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 11:48:43, Info                  DISM   DISM Imaging Provider: PID=2012 TID=7240 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Enter DismDeleteInternal - DismDeleteInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Leave DismDeleteInternal - DismDeleteInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Enter DismShutdownInternal - DismShutdownInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 11:48:43, Info                  DISM   DISM Provider Store: PID=2012 TID=7240 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7240 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Deleted g_internalDismSession - DismShutdownInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Shutdown SessionTable - DismShutdownInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 Leave DismShutdownInternal - DismShutdownInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll:                                          - DismShutdownInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-09 11:48:43, Info                  DISM   API: PID=2012 TID=7220 DismApi.dll:                                          - DismShutdownInternal
2025-04-09 11:48:43, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-09 11:48:43, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-09 11:48:43, Info                         WinRE partition total space [943714304], free space [476094464]
2025-04-09 19:23:50, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-09 19:23:50, Info                         [svchost.exe] Enter WinReGetConfig
2025-04-09 19:23:50, Info                         [svchost.exe] Parameters: configWinDir: NULL
2025-04-09 19:23:50, Info                         [svchost.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-09 19:23:50, Info                         [svchost.exe] Update enhanced config info is enabled.
2025-04-09 19:23:50, Info                         [svchost.exe] WinRE is installed
2025-04-09 19:23:50, Info                         [svchost.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-09 19:23:50, Info                         [svchost.exe] System is WimBoot: FALSE
2025-04-09 19:23:50, Info                         [svchost.exe] WinRE image validated
2025-04-09 19:23:50, Info                         [svchost.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll: Parent process command line: C:\Windows\system32\svchost.exe -k netsvcs -p -s wuauserv - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Enter DismInitializeInternal - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Initialized GlobalConfig - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Initialized SessionTable - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 m_pInternalThread started - CCommandThread::Start
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Created g_internalDismSession - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Leave DismInitializeInternal - DismInitializeInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 19:23:50, Info                  DISM   PID=7604 TID=6004 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-09 19:23:50, Info                  DISM   PID=7604 TID=6004 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-09 19:23:50, Info                  DISM   PID=7604 TID=6004 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-04-09 19:23:50, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:23:50, Info                  DISM   PID=7604 TID=6004 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-09 19:23:50, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:23:50, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:23:50, Info                  DISM   DISM Manager: PID=7604 TID=6004 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:23:50, Info                  DISM   DISM Imaging Provider: PID=7604 TID=6004 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Enter DismDeleteInternal - DismDeleteInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Leave DismDeleteInternal - DismDeleteInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Enter DismShutdownInternal - DismShutdownInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:23:50, Info                  DISM   DISM Provider Store: PID=7604 TID=6004 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=6004 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Deleted g_internalDismSession - DismShutdownInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Shutdown SessionTable - DismShutdownInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 Leave DismShutdownInternal - DismShutdownInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll:                                          - DismShutdownInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-09 19:23:50, Info                  DISM   API: PID=7604 TID=13756 DismApi.dll:                                          - DismShutdownInternal
2025-04-09 19:32:07, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-09 19:32:07, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\DISM\dism.log
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:07, Info                  DISM   DISM Manager: PID=12320 TID=14772 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Getting the collection of providers from a local provider store type. - CDISMProviderStore::GetProviderCollection
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FolderProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:07, Warning               DISM   DISM Provider Store: PID=12320 TID=14772 Failed to load the provider: C:\Windows\SYSTEM32\Dism\SiloedPackageProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FfuProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\VHDProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:07, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:07, Warning               DISM   DISM Provider Store: PID=12320 TID=14772 Failed to load the provider: C:\Windows\SYSTEM32\Dism\MetaDeployProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:32:07, Info                  DISM   DISM FFU Provider: PID=12320 TID=14772 [C:\] is not recognized by the DISM FFU provider. - CFfuImage::Initialize
2025-04-09 19:32:07, Info                  DISM   DISM WIM Provider: PID=12320 TID=14772 [C:\] is not a WIM mount point. - CWimMountedImageInfo::Initialize
2025-04-09 19:32:07, Info                  DISM   DISM VHD Provider: PID=12320 TID=14772 [C:\] is not recognized by the DISM VHD provider. - CVhdImage::Initialize
2025-04-09 19:32:07, Info                  DISM   DISM FFU Provider: PID=12320 TID=14772 [C:\] is not recognized by the DISM FFU provider. - CFfuImage::Initialize
2025-04-09 19:32:07, Info                  DISM   DISM Imaging Provider: PID=12320 TID=14772 The provider FfuManager does not support CreateDismImage on C:\ - CGenericImagingManager::CreateDismImage
2025-04-09 19:32:07, Info                  DISM   DISM VHD Provider: PID=12320 TID=14772 [C:\] is not recognized by the DISM VHD provider. - CVhdImage::Initialize
2025-04-09 19:32:07, Info                  DISM   DISM Imaging Provider: PID=12320 TID=14772 The provider VHDManager does not support CreateDismImage on C:\ - CGenericImagingManager::CreateDismImage
2025-04-09 19:32:07, Info                  DISM   DISM WIM Provider: PID=12320 TID=14772 [C:\] is not a WIM mount point. - CWimMountedImageInfo::Initialize
2025-04-09 19:32:07, Info                  DISM   DISM Imaging Provider: PID=12320 TID=14772 The provider WimManager does not support CreateDismImage on C:\ - CGenericImagingManager::CreateDismImage
2025-04-09 19:32:07, Info                  DISM   DISM Imaging Provider: PID=12320 TID=14772 No imaging provider supported CreateDismImage for this path - CGenericImagingManager::CreateDismImage
2025-04-09 19:32:07, Info                  DISM   DISM Manager: PID=12320 TID=14772 physical location path: C:\ - CDISMManager::CreateImageSession
2025-04-09 19:32:07, Info                  DISM   DISM Manager: PID=12320 TID=14772 Event name for current DISM session is Global\{75F94037-50DE-4748-A433-9C006D655623} - CDISMManager::CheckSessionAndLock
2025-04-09 19:32:07, Info                  DISM   DISM Manager: PID=12320 TID=14772 Create session event 0x338 for current DISM session and event name is Global\{75F94037-50DE-4748-A433-9C006D655623}  - CDISMManager::CheckSessionAndLock
2025-04-09 19:32:07, Info                  DISM   DISM Manager: PID=12320 TID=14772 Copying DISM from "C:\Windows\System32\Dism" - CDISMManager::CreateImageSessionFromLocation
2025-04-09 19:32:08, Info                  DISM   DISM Manager: PID=12320 TID=14772 Successfully loaded the ImageSession at "C:\Windows\TEMP\0CF7A8CC-D3B2-4E66-95C3-D65DC9A0AC08" - CDISMManager::LoadRemoteImageSession
2025-04-09 19:32:08, Info                  DISM   DISM Image Session: PID=14728 TID=11904 Instantiating the Provider Store. - CDISMImageSession::get_ProviderStore
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=14728 TID=11904 Initializing a provider store for the IMAGE session type. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=14728 TID=11904 Connecting to the provider located at C:\Windows\TEMP\0CF7A8CC-D3B2-4E66-95C3-D65DC9A0AC08\OSProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:08, Info                  DISM   DISM OS Provider: PID=14728 TID=11904 Defaulting SystemPath to C:\ - CDISMOSServiceManager::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM OS Provider: PID=14728 TID=11904 Defaulting Windows folder to C:\Windows - CDISMOSServiceManager::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=14728 TID=11904 Attempting to initialize the logger from the Image Session. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=14728 TID=11904 Connecting to the provider located at C:\Windows\TEMP\0CF7A8CC-D3B2-4E66-95C3-D65DC9A0AC08\LogProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:08, Info                  DISM   DISM Manager: PID=12320 TID=14772 Image session successfully loaded from the temporary location: C:\Windows\TEMP\0CF7A8CC-D3B2-4E66-95C3-D65DC9A0AC08 - CDISMManager::CreateImageSession
2025-04-09 19:32:08, Info                  DISM   DISM OS Provider: PID=14728 TID=11904 Determined System directory to be C:\Windows\System32 - CDISMOSServiceManager::get_SystemDirectory
2025-04-09 19:32:08, Info                  DISM   DISM Manager: PID=12320 TID=14772 Closing session event handle 0x338 - CDISMManager::CleanupImageSessionEntry
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Disconnecting Provider: FolderManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Disconnecting Provider: FfuManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Disconnecting Provider: VHDManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=14772 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:08, Info                         Servicing of WinRE image using packages
2025-04-09 19:32:08, Info                         Schedule WinRE Servicing execution
2025-04-09 19:32:08, Info                         OS is Client
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] Enter WinReGetConfig
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] Parameters: configWinDir: NULL
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] Update enhanced config info is enabled.
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] WinRE is installed
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] System is WimBoot: FALSE
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] WinRE image validated
2025-04-09 19:32:08, Info                         [WinREUpdateInstaller.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-09 19:32:08, Info                         Initialize RollbackHelper at [C:\$WinREAgent]
2025-04-09 19:32:08, Info                         Free space on OS Volume is [960510107648]
2025-04-09 19:32:08, Info                         Estimated target OS disk space usage peak [1442490056]
2025-04-09 19:32:08, Info                         Checking servicing of WinRE image with scheduled execution
2025-04-09 19:32:08, Info                         Free space on OS Volume is [960510107648]
2025-04-09 19:32:08, Info                         Estimated target OS disk space usage peak [1442490056]
2025-04-09 19:32:08, Info                         Stage WinRE servicing
2025-04-09 19:32:08, Info                         Start executing stage operations
2025-04-09 19:32:08, Info                         Stage: Add checkpoint [0]
2025-04-09 19:32:08, Info                         GetWinREVersion: WinRE path [\\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim]
2025-04-09 19:32:08, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\DISM\dism.log
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=13060 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=13060 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=13060 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM Manager: PID=12320 TID=13060 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=13060 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:08, Info                  DISM   DISM Imaging Provider: PID=12320 TID=13060 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=13060 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:08, Info                         GetWinREVersion: Version [10.0.19041.1]
2025-04-09 19:32:08, Info                         Current WinRE version: [10.0.19041.1]
2025-04-09 19:32:08, Info                         Executing operation [0]: [CleanupScratch]
2025-04-09 19:32:08, Info                         The operation executed successfully
2025-04-09 19:32:08, Info                         Executing operation [1]: [CopyWinRE]
2025-04-09 19:32:08, Info                         Progress: Completed [112] Total [11028] 1%
2025-04-09 19:32:08, Info                         Progress: Completed [222] Total [11028] 2%
2025-04-09 19:32:08, Info                         Progress: Completed [333] Total [11028] 3%
2025-04-09 19:32:08, Info                         Progress: Completed [443] Total [11028] 4%
2025-04-09 19:32:08, Info                         Progress: Completed [554] Total [11028] 5%
2025-04-09 19:32:08, Info                         Progress: Completed [664] Total [11028] 6%
2025-04-09 19:32:08, Info                         Progress: Completed [775] Total [11028] 7%
2025-04-09 19:32:08, Info                         Progress: Completed [885] Total [11028] 8%
2025-04-09 19:32:08, Info                         Progress: Completed [996] Total [11028] 9%
2025-04-09 19:32:08, Info                         Progress: Completed [1107] Total [11028] 10%
2025-04-09 19:32:08, Info                         Progress: Completed [1217] Total [11028] 11%
2025-04-09 19:32:08, Info                         Progress: Completed [1324] Total [11028] 12%
2025-04-09 19:32:08, Info                         Progress: Completed [1434] Total [11028] 13%
2025-04-09 19:32:08, Info                         Progress: Completed [1545] Total [11028] 14%
2025-04-09 19:32:08, Info                         Progress: Completed [1655] Total [11028] 15%
2025-04-09 19:32:08, Info                         Progress: Completed [1766] Total [11028] 16%
2025-04-09 19:32:08, Info                         CopyWinRE: Save copied WinRE wim path to Rollback info
2025-04-09 19:32:08, Info                         The operation executed successfully
2025-04-09 19:32:08, Info                         Executing operation [2]: [PrepareWinRE]
2025-04-09 19:32:08, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\DISM\dism.log
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:08, Info                  DISM   DISM Manager: PID=12320 TID=8004 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:08, Info                  DISM   DISM Imaging Provider: PID=12320 TID=8004 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-09 19:32:08, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:10, Info                         Progress: Completed [1892] Total [11028] 17%
2025-04-09 19:32:11, Info                         Progress: Completed [1990] Total [11028] 18%
2025-04-09 19:32:11, Info                         Progress: Completed [2107] Total [11028] 19%
2025-04-09 19:32:11, Info                         Progress: Completed [2224] Total [11028] 20%
2025-04-09 19:32:11, Info                         Progress: Completed [2321] Total [11028] 21%
2025-04-09 19:32:11, Info                         Progress: Completed [2438] Total [11028] 22%
2025-04-09 19:32:11, Info                         Progress: Completed [2555] Total [11028] 23%
2025-04-09 19:32:11, Info                         Progress: Completed [2653] Total [11028] 24%
2025-04-09 19:32:12, Info                         Progress: Completed [2770] Total [11028] 25%
2025-04-09 19:32:12, Info                         Progress: Completed [2887] Total [11028] 26%
2025-04-09 19:32:12, Info                         Progress: Completed [2984] Total [11028] 27%
2025-04-09 19:32:14, Info                         Progress: Completed [3101] Total [11028] 28%
2025-04-09 19:32:14, Info                         Progress: Completed [3199] Total [11028] 29%
2025-04-09 19:32:15, Info                         Progress: Completed [3316] Total [11028] 30%
2025-04-09 19:32:15, Info                         Progress: Completed [3433] Total [11028] 31%
2025-04-09 19:32:15, Info                         Progress: Completed [3530] Total [11028] 32%
2025-04-09 19:32:15, Info                         Progress: Completed [3647] Total [11028] 33%
2025-04-09 19:32:15, Info                         Progress: Completed [3764] Total [11028] 34%
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=8004 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:17, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\DISM\dism.log
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:17, Info                  DISM   DISM Manager: PID=12320 TID=15304 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Getting the collection of providers from a local provider store type. - CDISMProviderStore::GetProviderCollection
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FolderProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:17, Warning               DISM   DISM Provider Store: PID=12320 TID=15304 Failed to load the provider: C:\Windows\SYSTEM32\Dism\SiloedPackageProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FfuProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\VHDProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:17, Warning               DISM   DISM Provider Store: PID=12320 TID=15304 Failed to load the provider: C:\Windows\SYSTEM32\Dism\MetaDeployProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:32:17, Info                  DISM   DISM FFU Provider: PID=12320 TID=15304 [C:\$WinREAgent\Scratch\Mount] is not recognized by the DISM FFU provider. - CFfuImage::Initialize
2025-04-09 19:32:17, Info                  DISM   DISM Manager: PID=12320 TID=15304 physical location path: C:\$WinREAgent\Scratch\Mount - CDISMManager::CreateImageSession
2025-04-09 19:32:17, Info                  DISM   DISM Manager: PID=12320 TID=15304 Event name for current DISM session is Global\__?_Volume{efaec0de-6242-4451-b502-894bbc580f88}__3798701321_196608_183919 - CDISMManager::CheckSessionAndLock
2025-04-09 19:32:17, Info                  DISM   DISM Manager: PID=12320 TID=15304 Create session event 0x4f4 for current DISM session and event name is Global\__?_Volume{efaec0de-6242-4451-b502-894bbc580f88}__3798701321_196608_183919  - CDISMManager::CheckSessionAndLock
2025-04-09 19:32:17, Info                  DISM   DISM Manager: PID=12320 TID=15304 Copying DISM from "C:\$WinREAgent\Scratch\Mount\Windows\System32\Dism" - CDISMManager::CreateImageSessionFromLocation
2025-04-09 19:32:17, Info                  DISM   DISM Manager: PID=12320 TID=15304 Successfully loaded the ImageSession at "C:\Windows\TEMP\59878087-E0E0-4D95-8B8E-9EF88B6CE241" - CDISMManager::LoadRemoteImageSession
2025-04-09 19:32:17, Info                  DISM   DISM Image Session: PID=15048 TID=15296 Instantiating the Provider Store. - CDISMImageSession::get_ProviderStore
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=15048 TID=15296 Initializing a provider store for the IMAGE session type. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=15048 TID=15296 Connecting to the provider located at C:\Windows\TEMP\59878087-E0E0-4D95-8B8E-9EF88B6CE241\OSProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:17, Info                  DISM   DISM OS Provider: PID=15048 TID=15296 Defaulting SystemPath to C:\$WinREAgent\Scratch\Mount - CDISMOSServiceManager::Final_OnConnect
2025-04-09 19:32:17, Info                  DISM   DISM OS Provider: PID=15048 TID=15296 Defaulting Windows folder to C:\$WinREAgent\Scratch\Mount\Windows - CDISMOSServiceManager::Final_OnConnect
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=15048 TID=15296 Attempting to initialize the logger from the Image Session. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:17, Info                  DISM   DISM Provider Store: PID=15048 TID=15296 Connecting to the provider located at C:\Windows\TEMP\59878087-E0E0-4D95-8B8E-9EF88B6CE241\LogProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:18, Info                  DISM   DISM Manager: PID=12320 TID=15304 Image session successfully loaded from the temporary location: C:\Windows\TEMP\59878087-E0E0-4D95-8B8E-9EF88B6CE241 - CDISMManager::CreateImageSession
2025-04-09 19:32:18, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive already mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/SOFTWARE. - CRegistryMapping::Init
2025-04-09 19:32:18, Info                  DISM   DISM OS Provider: PID=15048 TID=15296 Determined System directory to be C:\$WinREAgent\Scratch\Mount\Windows\System32 - CDISMOSServiceManager::get_SystemDirectory
2025-04-09 19:32:19, Info                         Progress: Completed [3911] Total [11028] 35%
2025-04-09 19:32:21, Info                         Progress: Completed [3998] Total [11028] 36%
2025-04-09 19:32:21, Info                         Progress: Completed [4096] Total [11028] 37%
2025-04-09 19:32:21, Info                         Progress: Completed [4193] Total [11028] 38%
2025-04-09 19:32:23, Info                         Progress: Completed [4310] Total [11028] 39%
2025-04-09 19:32:23, Info                         Progress: Completed [4427] Total [11028] 40%
2025-04-09 19:32:23, Info                         Progress: Completed [4525] Total [11028] 41%
2025-04-09 19:32:25, Info                         Progress: Completed [4632] Total [11028] 42%
2025-04-09 19:32:25, Info                         Progress: Completed [4778] Total [11028] 43%
2025-04-09 19:32:25, Info                         Progress: Completed [4885] Total [11028] 44%
2025-04-09 19:32:26, Info                         Progress: Completed [4973] Total [11028] 45%
2025-04-09 19:32:28, Info                         Progress: Completed [5080] Total [11028] 46%
2025-04-09 19:32:28, Info                         Progress: Completed [5188] Total [11028] 47%
2025-04-09 19:32:29, Info                         Progress: Completed [5295] Total [11028] 48%
2025-04-09 19:32:29, Info                         Progress: Completed [5412] Total [11028] 49%
2025-04-09 19:32:34, Info                         Progress: Completed [5519] Total [11028] 50%
2025-04-09 19:32:34, Info                         Progress: Completed [5626] Total [11028] 51%
2025-04-09 19:32:36, Info                         Progress: Completed [5753] Total [11028] 52%
2025-04-09 19:32:36, Info                         Servicing packages successfully added.
2025-04-09 19:32:36, Info                         Progress: Completed [5948] Total [11028] 53%
2025-04-09 19:32:36, Info                         Progress: Completed [6124] Total [11028] 55%
2025-04-09 19:32:37, Info                         Progress: Completed [7703] Total [11028] 69%
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/SOFTWARE. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/SYSTEM. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/SECURITY. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/SAM. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/DEFAULT. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Users/<USER>/NTUSER.DAT. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/COMPONENTS. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   Offline Registry: PID=15048 TID=15296 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}C:/$WinREAgent/Scratch/Mount/Windows/system32/config/DRIVERS. - CRegistryMapping::Init
2025-04-09 19:32:38, Info                  DISM   DISM OS Provider: PID=15048 TID=15296 Successfully unloaded all registry hives. - CDISMOSServiceManager::Final_OnDisconnect
2025-04-09 19:32:38, Info                  DISM   DISM Manager: PID=12320 TID=15304 Closing session event handle 0x4f4 - CDISMManager::CleanupImageSessionEntry
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Disconnecting Provider: FolderManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Disconnecting Provider: FfuManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Disconnecting Provider: VHDManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=15304 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:38, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\DISM\dism.log
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:32:38, Info                  DISM   DISM Manager: PID=12320 TID=7128 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Getting the collection of providers from a local provider store type. - CDISMProviderStore::GetProviderCollection
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FolderProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:38, Warning               DISM   DISM Provider Store: PID=12320 TID=7128 Failed to load the provider: C:\Windows\SYSTEM32\Dism\SiloedPackageProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FfuProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\VHDProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:38, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:32:38, Warning               DISM   DISM Provider Store: PID=12320 TID=7128 Failed to load the provider: C:\Windows\SYSTEM32\Dism\MetaDeployProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:32:38, Info                  DISM   DISM FFU Provider: PID=12320 TID=7128 [C:\$WinREAgent\Scratch\Mount] is not recognized by the DISM FFU provider. - CFfuImage::Initialize
2025-04-09 19:32:43, Info                         Progress: Completed [7722] Total [11028] 70%
2025-04-09 19:32:43, Info                         Progress: Completed [7830] Total [11028] 71%
2025-04-09 19:32:44, Info                         Progress: Completed [7947] Total [11028] 72%
2025-04-09 19:32:46, Info                         Progress: Completed [8054] Total [11028] 73%
2025-04-09 19:32:46, Info                         Progress: Completed [8161] Total [11028] 74%
2025-04-09 19:32:46, Info                         Progress: Completed [8278] Total [11028] 75%
2025-04-09 19:32:46, Info                         Progress: Completed [8385] Total [11028] 76%
2025-04-09 19:32:47, Info                         Progress: Completed [8492] Total [11028] 77%
2025-04-09 19:32:47, Info                         Progress: Completed [8609] Total [11028] 78%
2025-04-09 19:32:50, Info                         Progress: Completed [8717] Total [11028] 79%
2025-04-09 19:32:51, Info                         Progress: Completed [8824] Total [11028] 80%
2025-04-09 19:32:52, Info                         Progress: Completed [8941] Total [11028] 81%
2025-04-09 19:32:53, Info                         Progress: Completed [9048] Total [11028] 82%
2025-04-09 19:32:53, Info                         Progress: Completed [9155] Total [11028] 83%
2025-04-09 19:32:54, Info                         Progress: Completed [9272] Total [11028] 84%
2025-04-09 19:32:55, Info                         Progress: Completed [9380] Total [11028] 85%
2025-04-09 19:32:56, Info                         Progress: Completed [9487] Total [11028] 86%
2025-04-09 19:32:56, Info                         Progress: Completed [9604] Total [11028] 87%
2025-04-09 19:32:58, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 19:32:58, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Disconnecting Provider: FolderManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:58, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Disconnecting Provider: FfuManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:58, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:58, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Disconnecting Provider: VHDManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:58, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:58, Info                  DISM   DISM Provider Store: PID=12320 TID=7128 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:32:58, Info                         PrepareWinRE: Save exported WinRE wim path to Rollback info
2025-04-09 19:32:58, Info                         Started tracing WIMGAPI logs to [C:\Windows\Logs\WinREAgent\SetupAct.log]
2025-04-09 19:32:58, Info                         Progress: Completed [9711] Total [11028] 88%
2025-04-09 19:32:58, Info                         Progress: Completed [9818] Total [11028] 89%
2025-04-09 19:32:58, Info                         Progress: Completed [9926] Total [11028] 90%
2025-04-09 19:32:58, Info                         Progress: Completed [10043] Total [11028] 91%
2025-04-09 19:32:58, Info                         Progress: Completed [10150] Total [11028] 92%
2025-04-09 19:32:58, Info                         Progress: Completed [10257] Total [11028] 93%
2025-04-09 19:32:58, Info                         Progress: Completed [10374] Total [11028] 94%
2025-04-09 19:32:58, Info                         Progress: Completed [10481] Total [11028] 95%
2025-04-09 19:32:58, Info                         Progress: Completed [10589] Total [11028] 96%
2025-04-09 19:32:59, Info                         Stopped tracing WIMGAPI logs to [C:\Windows\Logs\WinREAgent\SetupAct.log]
2025-04-09 19:32:59, Info                         Size of update wim [497537285]
2025-04-09 19:32:59, Info                         The operation executed successfully
2025-04-09 19:32:59, Info                         Executing operation [3]: [SaveWinREHash]
2025-04-09 19:32:59, Info                         [WinREUpdateInstaller.exe] Enter WinReHashWimFile
2025-04-09 19:32:59, Info                         [WinREUpdateInstaller.exe] Parameters: WimFile: C:\$WinREAgent\Scratch\update.wim
2025-04-09 19:33:02, Info                         [WinREUpdateInstaller.exe] Exit WinReHashWimFile return value: 1, last error: 0x0
2025-04-09 19:33:02, Info                         SaveWinREHash: Save new WimRE Hash to Rollback info
2025-04-09 19:33:02, Info                         [WinREUpdateInstaller.exe] Enter WinReHashWimFile
2025-04-09 19:33:02, Info                         [WinREUpdateInstaller.exe] Parameters: WimFile: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] Exit WinReHashWimFile return value: 1, last error: 0x0
2025-04-09 19:33:04, Info                         Wim file [\\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim] isn't trusted by BitLocker on target volume
2025-04-09 19:33:04, Info                         SaveWinREHash: Old wim is not trusted
2025-04-09 19:33:04, Info                         The operation executed successfully
2025-04-09 19:33:04, Info                         Progress: Completed [11028] Total [11028] 100%
2025-04-09 19:33:04, Info                         Executing operation [4]: [BackupWinRE]
2025-04-09 19:33:04, Info                         BackupWinRE: Backup WinRE to [C:\$WinREAgent\Backup]
2025-04-09 19:33:04, Info                         BackupWinRE: Create backup directory
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] Enter WinReSetupBackupWinRE
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] backup directory: C:\$WinREAgent\Backup
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] targetOsGuid: : NULL 
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] Update enhanced config info is enabled.
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] WinRE is installed
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] pFixImageLocation returning TRUE
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] pGetExistingWinREPath returning TRUE
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] WinRE image version: 10.0
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe]  (WinRE)winRECopyDirectory() returning TRUE
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe] Marker file \\?\GLOBALROOT\device\harddisk0\partition4\$WINRE_BACKUP_PARTITION.MARKER created
2025-04-09 19:33:04, Info                         [WinREUpdateInstaller.exe]  (WinRE)WinReSetupBackupWinRE() returning TRUE
2025-04-09 19:33:04, Info                         BackupWinRE: WinReSetupBackupWinRE succeed. WinRE location: [\\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE]
2025-04-09 19:33:04, Info                         BackupWinRE: Save Backup WinRE wim path to Rollback info
2025-04-09 19:33:04, Info                         The operation executed successfully
2025-04-09 19:33:04, Info                         Successfully executed stage operations
2025-04-09 19:33:04, Info                         GetWinREVersion: WinRE path [C:\$WinREAgent\Scratch\update.wim]
2025-04-09 19:33:04, Info                  DISM   DISM Imaging Provider: PID=12320 TID=13060 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-09 19:33:04, Info                         GetWinREVersion: Version [10.0.19041.5728]
2025-04-09 19:33:04, Info                         Update WinRE version: [10.0.19041.5728]
2025-04-09 19:33:04, Info                         Stage completes, schedule Commit execution
2025-04-09 19:33:04, Info                         Schedule Commit execution
2025-04-09 19:33:04, Info                         OS is Client
2025-04-09 19:33:04, Info                         WinRE partition total space [943714304], free space [476094464], winre size [449274568], target WinRE size [497537285]
2025-04-09 19:33:04, Info                         Free space requirement: [54525952]
2025-04-09 19:33:04, Info                         Selected [2] scenario to execute
2025-04-09 19:33:04, Info                         Check whether can execute Commit
2025-04-09 19:33:04, Info                         Free space on Target Volume is [959529029632]
2025-04-09 19:33:04, Info                         Estimated target OS disk space usage peak [0]
2025-04-09 19:33:04, Info                         WinRE servicing staged
2025-04-09 19:33:06, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\DISM\dism.log
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:33:06, Info                  DISM   DISM Manager: PID=12320 TID=7296 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Getting the collection of providers from a local provider store type. - CDISMProviderStore::GetProviderCollection
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FolderProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:06, Warning               DISM   DISM Provider Store: PID=12320 TID=7296 Failed to load the provider: C:\Windows\SYSTEM32\Dism\SiloedPackageProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\FfuProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\VHDProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:06, Warning               DISM   DISM Provider Store: PID=12320 TID=7296 Failed to load the provider: C:\Windows\SYSTEM32\Dism\MetaDeployProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2025-04-09 19:33:06, Info                  DISM   DISM FFU Provider: PID=12320 TID=7296 [C:\] is not recognized by the DISM FFU provider. - CFfuImage::Initialize
2025-04-09 19:33:06, Info                  DISM   DISM WIM Provider: PID=12320 TID=7296 [C:\] is not a WIM mount point. - CWimMountedImageInfo::Initialize
2025-04-09 19:33:06, Info                  DISM   DISM VHD Provider: PID=12320 TID=7296 [C:\] is not recognized by the DISM VHD provider. - CVhdImage::Initialize
2025-04-09 19:33:06, Info                  DISM   DISM FFU Provider: PID=12320 TID=7296 [C:\] is not recognized by the DISM FFU provider. - CFfuImage::Initialize
2025-04-09 19:33:06, Info                  DISM   DISM Imaging Provider: PID=12320 TID=7296 The provider FfuManager does not support CreateDismImage on C:\ - CGenericImagingManager::CreateDismImage
2025-04-09 19:33:06, Info                  DISM   DISM VHD Provider: PID=12320 TID=7296 [C:\] is not recognized by the DISM VHD provider. - CVhdImage::Initialize
2025-04-09 19:33:06, Info                  DISM   DISM Imaging Provider: PID=12320 TID=7296 The provider VHDManager does not support CreateDismImage on C:\ - CGenericImagingManager::CreateDismImage
2025-04-09 19:33:06, Info                  DISM   DISM WIM Provider: PID=12320 TID=7296 [C:\] is not a WIM mount point. - CWimMountedImageInfo::Initialize
2025-04-09 19:33:06, Info                  DISM   DISM Imaging Provider: PID=12320 TID=7296 The provider WimManager does not support CreateDismImage on C:\ - CGenericImagingManager::CreateDismImage
2025-04-09 19:33:06, Info                  DISM   DISM Imaging Provider: PID=12320 TID=7296 No imaging provider supported CreateDismImage for this path - CGenericImagingManager::CreateDismImage
2025-04-09 19:33:06, Info                  DISM   DISM Manager: PID=12320 TID=7296 physical location path: C:\ - CDISMManager::CreateImageSession
2025-04-09 19:33:06, Info                  DISM   DISM Manager: PID=12320 TID=7296 Event name for current DISM session is Global\{75CC14D5-465B-4804-A13A-5A7AA97C9D8E} - CDISMManager::CheckSessionAndLock
2025-04-09 19:33:06, Info                  DISM   DISM Manager: PID=12320 TID=7296 Create session event 0x93c for current DISM session and event name is Global\{75CC14D5-465B-4804-A13A-5A7AA97C9D8E}  - CDISMManager::CheckSessionAndLock
2025-04-09 19:33:06, Info                  DISM   DISM Manager: PID=12320 TID=7296 Copying DISM from "C:\Windows\System32\Dism" - CDISMManager::CreateImageSessionFromLocation
2025-04-09 19:33:06, Info                  DISM   DISM Manager: PID=12320 TID=7296 Successfully loaded the ImageSession at "C:\Windows\TEMP\F7797FF2-0355-48D1-AD2F-19AAF6F78812" - CDISMManager::LoadRemoteImageSession
2025-04-09 19:33:06, Info                  DISM   DISM Image Session: PID=1896 TID=2896 Instantiating the Provider Store. - CDISMImageSession::get_ProviderStore
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=1896 TID=2896 Initializing a provider store for the IMAGE session type. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=1896 TID=2896 Connecting to the provider located at C:\Windows\TEMP\F7797FF2-0355-48D1-AD2F-19AAF6F78812\OSProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:06, Info                  DISM   DISM OS Provider: PID=1896 TID=2896 Defaulting SystemPath to C:\ - CDISMOSServiceManager::Final_OnConnect
2025-04-09 19:33:06, Info                  DISM   DISM OS Provider: PID=1896 TID=2896 Defaulting Windows folder to C:\Windows - CDISMOSServiceManager::Final_OnConnect
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=1896 TID=2896 Attempting to initialize the logger from the Image Session. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:33:06, Info                  DISM   DISM Provider Store: PID=1896 TID=2896 Connecting to the provider located at C:\Windows\TEMP\F7797FF2-0355-48D1-AD2F-19AAF6F78812\LogProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:06, Info                  DISM   DISM Manager: PID=12320 TID=7296 Image session successfully loaded from the temporary location: C:\Windows\TEMP\F7797FF2-0355-48D1-AD2F-19AAF6F78812 - CDISMManager::CreateImageSession
2025-04-09 19:33:06, Info                  DISM   DISM OS Provider: PID=1896 TID=2896 Determined System directory to be C:\Windows\System32 - CDISMOSServiceManager::get_SystemDirectory
2025-04-09 19:33:08, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-09 19:33:08, Info                         Load RollbackHelper from [C:\$WinREAgent]
2025-04-09 19:33:08, Info                         Loading Rollback checkpoints from [C:\$WinREAgent\Rollback.xml]
2025-04-09 19:33:08, Info                         Commit WinRE servicing
2025-04-09 19:33:08, Info                         Start executing commit operations
2025-04-09 19:33:08, Info                         WinRE partition total space [943714304], free space [476094464], winre size [449274568]
2025-04-09 19:33:08, Info                         Executing operation [0]: [VerifyNewWinREHash]
2025-04-09 19:33:08, Info                         [TiWorker.exe] Enter WinReHashWimFile
2025-04-09 19:33:08, Info                         [TiWorker.exe] Parameters: WimFile: C:\$WinREAgent\Scratch\update.wim
2025-04-09 19:33:10, Info                         [TiWorker.exe] Exit WinReHashWimFile return value: 1, last error: 0x0
2025-04-09 19:33:10, Info                         VerifyNewWinREHash: Current update Wim hash [Jw4snBziOKQysQbjAX5nh93YGABoaLzUi+z1I+VRpXA=]
2025-04-09 19:33:10, Info                         VerifyNewWinREHash: Saved update Wim hash   [Jw4snBziOKQysQbjAX5nh93YGABoaLzUi+z1I+VRpXA=]
2025-04-09 19:33:10, Info                         The operation executed successfully
2025-04-09 19:33:10, Info                         Progress: Completed [100] Total [400] 25%
2025-04-09 19:33:10, Info                         Executing operation [1]: [AddNewWinREHash]
2025-04-09 19:33:10, Info                         The operation executed successfully
2025-04-09 19:33:10, Info                         Progress: Completed [200] Total [400] 50%
2025-04-09 19:33:10, Info                         Executing operation [2]: [UpdateInPartition]
2025-04-09 19:33:10, Info                         UpdateInPartition: Update Wim size [497537285], Current Wim size [449274568]
2025-04-09 19:33:10, Info                         Free space requirement: [54525952]
2025-04-09 19:33:10, Info                         UpdateInPartition: WinRE partition free space [476094464]
2025-04-09 19:33:10, Info                         UpdateInPartition: Required free space on recovery partition [113274429]
2025-04-09 19:33:10, Info                         UpdateInPartition: Add checkpoint [4]
2025-04-09 19:33:10, Info                         UpdateInPartition: Delete Current WinRE
2025-04-09 19:33:10, Info                         UpdateInPartition: Copy the updated WinRE
2025-04-09 19:33:10, Info                         UpdateInPartition: Add checkpoint [5]
2025-04-09 19:33:10, Info                         UpdateInPartition: Delete the updated WinRE
2025-04-09 19:33:10, Info                         The operation executed successfully
2025-04-09 19:33:10, Info                         Executing operation [3]: [DeleteOldWinREHash]
2025-04-09 19:33:10, Info                         The operation executed successfully
2025-04-09 19:33:10, Info                         Progress: Completed [300] Total [400] 75%
2025-04-09 19:33:10, Info                         Executing operation [4]: [CleanupScratch]
2025-04-09 19:33:10, Info                         The operation executed successfully
2025-04-09 19:33:10, Info                         Progress: Completed [400] Total [400] 100%
2025-04-09 19:33:10, Info                         Successfully executed commit operations
2025-04-09 19:33:10, Info                         WinRE partition total space [943714304]
2025-04-09 19:33:10, Info                         WinRE partition free space [427831296]
2025-04-09 19:33:10, Info                         WinRE size [497537285]
2025-04-09 19:33:10, Info                         WinRE servicing commited
2025-04-09 19:33:10, Info                         Let Executor cleanup state
2025-04-09 19:33:10, Info                         Start cleanup of execution
2025-04-09 19:33:10, Info                         External backup directory [NULL]
2025-04-09 19:33:10, Info                         Cleanup Backup directory
2025-04-09 19:33:10, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-09 19:33:10, Info                         [TiWorker.exe] Enter WinReGetConfig
2025-04-09 19:33:10, Info                         [TiWorker.exe] Parameters: configWinDir: NULL
2025-04-09 19:33:10, Info                         [TiWorker.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-09 19:33:10, Info                         [TiWorker.exe] Update enhanced config info is enabled.
2025-04-09 19:33:10, Info                         [TiWorker.exe] WinRE is installed
2025-04-09 19:33:10, Info                         [TiWorker.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-09 19:33:10, Info                         [TiWorker.exe] System is WimBoot: FALSE
2025-04-09 19:33:10, Info                         [TiWorker.exe] WinRE image validated
2025-04-09 19:33:10, Info                         [TiWorker.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll: Parent process command line: C:\Windows\winsxs\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334\TiWorker.exe -Embedding - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Enter DismInitializeInternal - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Initialized GlobalConfig - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Initialized SessionTable - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 m_pInternalThread started - CCommandThread::Start
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Created g_internalDismSession - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Leave DismInitializeInternal - DismInitializeInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 19:33:10, Info                  DISM   PID=6944 TID=10924 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-09 19:33:10, Info                  DISM   PID=6944 TID=10924 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-09 19:33:10, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:33:10, Info                  DISM   PID=6944 TID=10924 Successfully loaded the ImageSession at "C:\Windows\System32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-09 19:33:10, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 19:33:10, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 19:33:10, Info                  DISM   DISM Manager: PID=6944 TID=10924 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Connecting to the provider located at C:\Windows\System32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:10, Info                  DISM   DISM Imaging Provider: PID=6944 TID=10924 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Connecting to the provider located at C:\Windows\System32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Enter DismDeleteInternal - DismDeleteInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Leave DismDeleteInternal - DismDeleteInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Enter DismShutdownInternal - DismShutdownInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:10, Info                  DISM   DISM Provider Store: PID=6944 TID=10924 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=10924 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Deleted g_internalDismSession - DismShutdownInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Shutdown SessionTable - DismShutdownInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 Leave DismShutdownInternal - DismShutdownInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll:                                          - DismShutdownInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-09 19:33:10, Info                  DISM   API: PID=6944 TID=14512 DismApi.dll:                                          - DismShutdownInternal
2025-04-09 19:33:10, Info                  CSI    00000046@2025/4/10:00:33:10.966 Completed WinRE CommitChanges. New Version: 10.0.19041.5728
2025-04-09 19:33:10, Info                  CSI    00000047@2025/4/10:00:33:10.967 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{29194b27-704c-49cc-b251-094354712ebd};(null);2843043us
2025-04-09 19:33:10, Info                  CSI    00000048 End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:33:10, Info                  CSI    00000049 ExecutionComplete online ticklers
2025-04-09 19:33:10, Info                  CSI    0000004a Loading installer DLL from explicit path: C:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334\wcp.dll
2025-04-09 19:33:10, Info                  CSI    0000004b Begin executing advanced installer phase 54 index 0 (sequence 0)
    Old component: [l:0]''
    New component: [l:0]''
    Install mode: delta
    Smart installer: true
    Installer ID: {d4486727-4d36-45a5-bc81-b8935aed1429}
    Installer name: 'Ngen Task Scheduler Tickler'
2025-04-09 19:33:10, Info                  CSI    0000004c@2025/4/10:00:33:10.985 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{d4486727-4d36-45a5-bc81-b8935aed1429};(null);130us
2025-04-09 19:33:10, Info                  CSI    0000004d End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:33:11, Info                  CSI    0000004e Loading installer DLL from explicit path: C:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334\wcp.dll
2025-04-09 19:33:11, Info                  CSI    0000004f Begin executing advanced installer phase 54 index 0 (sequence 0)
    Old component: [l:0]''
    New component: [l:0]''
    Install mode: delta
    Smart installer: true
    Installer ID: {4006c604-9280-4d68-b731-2fe3d02e84bf}
    Installer name: 'ShortcutTickler'
2025-04-09 19:33:11, Info                  CSI    00000050@2025/4/10:00:33:11.04 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{4006c604-9280-4d68-b731-2fe3d02e84bf};(null);123us
2025-04-09 19:33:11, Info                  CSI    00000051 End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:33:11, Info                  CSI    00000052 Loading installer DLL from explicit path: C:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334\wcp.dll
2025-04-09 19:33:11, Info                  CSI    00000053 Begin executing advanced installer phase 54 index 0 (sequence 0)
    Old component: [l:0]''
    New component: [l:0]''
    Install mode: delta
    Smart installer: true
    Installer ID: {863fbd5f-faba-4730-9858-ceca474b2195}
    Installer name: 'BcdMirror'
2025-04-09 19:33:11, Info                  CSI    00000054 BCD Mirror tickler always runs
2025-04-09 19:33:11, Info                  CSI    00000055@2025/4/10:00:33:11.023 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{863fbd5f-faba-4730-9858-ceca474b2195};(null);478us
2025-04-09 19:33:11, Info                  CSI    00000056 End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:33:11, Info                  CSI    00000057 Online ticklers finished with status S_OK
2025-04-09 19:33:11, Info                  CSI    00000058@2025/4/10:00:33:11.023 Finished running all AIs.

2025-04-09 19:33:11, Info                  CSI    00000059 Creating NT transaction (seq 6)
2025-04-09 19:33:11, Info                  CSI    0000005a Created NT transaction (seq 6) result 0x00000000, handle @0x464
2025-04-09 19:33:11, Info                  CSI    0000005b Transaction UoW: {39501e91-1573-11f0-a589-64006a8439b4} for BeginTransaction
2025-04-09 19:33:11, Info                  CSI    0000005c Poqexec successfully registered in [l:12 ml:13]'SetupExecute'
2025-04-09 19:33:11, Info                  CSI    0000005d@2025/4/10:00:33:11.024 Beginning NT transaction commit...
2025-04-09 19:33:11, Info                  CSI    0000005e Transaction UoW: {39501e91-1573-11f0-a589-64006a8439b4} for CommitTransaction
2025-04-09 19:33:11, Info                  CSI    0000005f@2025/4/10:00:33:11.028 CSI perf trace:
CSIPERF:TXCOMMIT;3672
2025-04-09 19:33:11, Info                  CBS    Setting ExecuteState key to: ExecuteStateNone
2025-04-09 19:33:11, Info                  CBS    Clearing HangDetect value
2025-04-09 19:33:11, Info                  CBS    Saved last global progress. Current: 0, Limit: 1, ExecuteState: ExecuteStateNone
2025-04-09 19:33:11, Info                  CSI    00000060@2025/4/10:00:33:11.028 CSI Transaction @0x1d997a71980 destroyed
2025-04-09 19:33:11, Info                  CBS    Perf: InstallUninstallChain complete.
2025-04-09 19:33:11, Info                  CBS    FinalCommitPackagesState: Started persisting state of packages
2025-04-09 19:33:11, Info                  CBS    Reporting package change for package: Package_for_WinREServicing~31bf3856ad364e35~amd64~~19041.5728.1.1, current: Absent, pending: Default, start: Absent, applicable: Installed, target: Installed, limit: Installed, status: 0x0, failure source: Execute, reboot required: False, client id: DISM Package Manager Provider, initiated offline: False, execution sequence: 60, first merged sequence: 60, reboot reason: REBOOT_NOT_REQUIRED, RM App session: -1, RM App name: N/A, FileName in use: N/A, release type: Security Update, OC operation: False, download source: 0, download time (secs): **********, download status: 0x0 (S_OK), Express download: False, Download Size: 0
2025-04-09 19:33:11, Info                  CBS    Reporting package change completion for package: Package_for_WinREServicing~31bf3856ad364e35~amd64~~19041.5728.1.1, current: Installed, original: Absent, target: Installed, status: 0x0, failure source: Execute, failure details: "(null)", client id: DISM Package Manager Provider, initiated offline: False, execution sequence: 60, first merged sequence: 60, pending decision: InteractiveInstallSucceeded, primitive execution context: Interactive 
2025-04-09 19:33:11, Info                  CBS    FinalCommitPackagesState: Completed persisting state of packages
2025-04-09 19:33:11, Info                  CBS    Enabling LKG boot option
2025-04-09 19:33:11, Info                  CBS    Exec: Processing complete.  Session: 31173040_547708811, Package: Package_for_WinREServicing~31bf3856ad364e35~amd64~~19041.5728.1.1, Identifier: KB5057589 [HRESULT = 0x00000000 - S_OK]
2025-04-09 19:33:11, Info                  CBS    Session: 31173040_547708811 finalized. Reboot required: no [HRESULT = 0x00000000 - S_OK]
2025-04-09 19:33:11, Info                  CBS    Deleting directory: \\?\C:\Windows\CbsTemp\31173040_547708811\
2025-04-09 19:33:11, Info                  CBS    Moving directory from \\?\C:\Windows\CbsTemp\31173040_547708811\ to \\?\C:\Windows\CbsTemp\31173040_547708811\{D64E7BE6-831E-4221-B56F-812F0CC6FE36}
2025-04-09 19:33:11, Info                  CBS    Failed to move \\?\C:\Windows\CbsTemp\31173040_547708811\ to temp directory \\?\C:\Windows\CbsTemp\31173040_547708811\{D64E7BE6-831E-4221-B56F-812F0CC6FE36} [HRESULT = 0x80070020 - ERROR_SHARING_VIOLATION]
2025-04-09 19:33:11, Info                  CBS    Failed moving directory: \\?\C:\Windows\CbsTemp\31173040_547708811\ to temp, will delete in-place instead [HRESULT = 0x80070020 - ERROR_SHARING_VIOLATION]
2025-04-09 19:33:11, Info                  CBS    Deletion of: \\?\C:\Windows\CbsTemp\31173040_547708811\ successful
2025-04-09 19:33:11, Info                  DISM   DISM Manager: PID=12320 TID=7296 Closing session event handle 0x93c - CDISMManager::CleanupImageSessionEntry
2025-04-09 19:33:11, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 19:33:11, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Disconnecting Provider: FolderManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:11, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Disconnecting Provider: FfuManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:11, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:11, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Disconnecting Provider: VHDManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:11, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:11, Info                  DISM   DISM Provider Store: PID=12320 TID=7296 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 19:33:11, Info                         [WinREUpdInst] Servicing complete. Successfully staged operations and added AI package
2025-04-09 19:34:37, Info                  CBS    Session: 31173040_1458537998 initialized by client DISM Package Manager Provider, external staging directory: (null), external registry directory: (null)
2025-04-09 19:34:37, Info                  CBS    Enumerating Foundation package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, this could be slow
2025-04-09 19:34:37, Info                  CBS    Appl:Feature On Demand package without explicit comparator, using GE on build version
2025-04-09 19:34:37, Info                  CBS    Appl:Feature On Demand package without explicit comparator, using GE on build version
2025-04-09 19:34:37, Info                  CBS    Enumerating Foundation package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, this could be slow
2025-04-09 19:34:37, Info                  CBS    Enumerating Foundation package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, this could be slow
2025-04-09 19:34:37, Info                  CBS    Update: Setting Install State, Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: Windows-Defender-Default-Definitions, new state: Off
2025-04-09 19:34:37, Info                  CBS    Update: Setting Install State, Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: Windows-Defender-Default-Definitions, current State: Default, new state: Off, RemovePayload: 0
2025-04-09 19:34:37, Info                  CBS    Real pend will be used if required
2025-04-09 19:34:37, Info                  CBS    Exec: Processing started.  Client: DISM Package Manager Provider, Session: 31173040_1458537998, Package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Identifier: Windows Foundation
2025-04-09 19:34:37, Info                  CBS    Exec: Using execution sequence: 61
2025-04-09 19:34:37, Info                  CBS    Disabling LKG boot option
2025-04-09 19:34:37, Info                  CBS    Exec: Creating restore point: Package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, current: Installed, targeted: Installed
2025-04-09 19:34:37, Info                  CBS    Restore point type: Install
2025-04-09 19:34:37, Info                  CBS    Perf: Begin: nested restore point - begin
2025-04-09 19:34:37, Info                  CBS    Failed setting restore point [HRESULT = 0x80070422 - ERROR_SERVICE_DISABLED]
2025-04-09 19:34:37, Info                  CBS    Exec: Begin: nested restore point - failed, ignoring and continuing. [HRESULT = 0x80070422 - ERROR_SERVICE_DISABLED]
2025-04-09 19:34:37, Info                  CBS    Perf: Entering stage: Planning
2025-04-09 19:34:37, Info                  CBS    FLOW: Entering stage: Planning 
2025-04-09 19:34:37, Info                  CBS    Appl: detect Parent, Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Parent: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Disposition = Detect, VersionComp: EQ, BuildComp: EQ, RevisionComp: GE, Exist: present
2025-04-09 19:34:37, Info                  CBS    Appl: detectParent: parent found: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, state: Installed
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: Windows-Defender-Default-Definitions, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Installed, selected: Off
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: db80cfb53663aa7f54b1cb4eddc17f14, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: c65b6e503caab612e3ee775b55d0a006, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 6b0b695f5a927e715ed2db0c62da3ccb, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 96e09267318e4a145b870026075f1f90, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 0e23ecf9fe2cf0b171ec8770185d4520, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 90efeb8a647ce57b7858a1ef7bf2222a, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Plan: Force applicability state to absent due to absent limit state on package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Absent
2025-04-09 19:34:37, Info                  CSI    00000061@2025/4/10:00:34:37.624 CSI Transaction @0x1d997a70590 initialized for deployment engine {d16d444c-56d8-11d5-882d-0080c847b195} with flags 00000000 and client id 'TI5.31173040_1458537998:1/'

2025-04-09 19:34:37, Info                  CSI    00000062@2025/4/10:00:34:37.626 CSI Transaction @0x1d997a70590 destroyed
2025-04-09 19:34:37, Info                  CBS    Plan: Force package:Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, update: 705d28449bc04bbee2e2d693cb759650 into absent state since package state is absent
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 705d28449bc04bbee2e2d693cb759650, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Absent, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-AppLayer-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-AppLayer-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-AppLayer-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: c7c0457e969c29080960026388425083, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-Core-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Core-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Core-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 95d265f181123e72260986f2c9ed344a, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-Management-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Management-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Management-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: ad5b80b4be1969397739fca849eea67d, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-Management-MDM-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Management-MDM-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Management-MDM-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 504a8f6b8c98801a6472360c45479955, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-Management-Powershell-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Management-Powershell-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Management-Powershell-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 19d7e6e595ade1abc2bd0ca651b4908a, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-Nis-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Nis-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Windows-Defender-Nis-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 8a0d1c12d69dcbbfd8b7683eb6b852f0, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: CommonFoundation, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:37, Info                  CBS    Appl: Evaluating package applicability for package Microsoft-Windows-Common-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, applicable state: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Microsoft-Windows-Common-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed
2025-04-09 19:34:37, Info                  CBS    Plan: Package: Microsoft-Windows-Common-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: WindowsFoundationDelivery, current: Installed, pending: Default, start: Installed, applicable: Installed, targeted: Installed, limit: Installed, selected: Default
2025-04-09 19:34:38, Info                  CBS    Package Format: PSFX
2025-04-09 19:34:38, Info                  CBS    Package Format: PSFX
2025-04-09 19:34:38, Info                  CBS    Update.mum in package has 'PackageSupportedFeatures = 0x1
2025-04-09 19:34:38, Info                  CBS    Appl:LCU package and revision compare set to explicit
2025-04-09 19:34:38, Info                  CBS    Package Format: PSFX
2025-04-09 19:34:38, Info                  CBS    Delta Format: ForwardOnly
2025-04-09 19:34:38, Info                  CBS    Generate playback status for m_fGeneratePlaybackDeltaWhenPackageHasReverseDeltas: 0
2025-04-09 19:34:38, Info                  CBS    Generate playback status for m_fShouldGeneratePlaybackDeltas: 1
2025-04-09 19:34:38, Info                  CBS    KIR Feature_Servicing_PlaybackDeltaGenerationV2 is enabled
2025-04-09 19:34:38, Info                  CBS    Package Format: PSFX
2025-04-09 19:34:38, Info                  CBS    Plan: Start to process package watchlist.
2025-04-09 19:34:38, Info                  CBS    Appl: detect Parent, Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, Parent: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Disposition = Detect, VersionComp: EQ, BuildComp: EQ, RevisionComp: GE, Exist: present
2025-04-09 19:34:38, Info                  CBS    Appl: detectParent: parent found: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, state: Absent
2025-04-09 19:34:38, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, applicable state: Absent
2025-04-09 19:34:38, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Installed
2025-04-09 19:34:38, Info                  CBS    Plan: Force package:Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, update: 43b88c2e5c84f60b174db17bd846f771 into absent state since package state is absent
2025-04-09 19:34:38, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Installed, selected: Default
2025-04-09 19:34:38, Info                  CBS    Appl: detect Parent, Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, Parent: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Disposition = Detect, VersionComp: EQ, BuildComp: EQ, RevisionComp: GE, Exist: present
2025-04-09 19:34:38, Info                  CBS    Appl: detectParent: parent found: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, state: Absent
2025-04-09 19:34:38, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, applicable state: Absent
2025-04-09 19:34:38, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Installed
2025-04-09 19:34:38, Info                  CBS    Plan: Force package:Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, update: 43b88c2e5c84f60b174db17bd846f771 into absent state since package state is absent
2025-04-09 19:34:38, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Installed, selected: Default
2025-04-09 19:34:38, Info                  CBS    Appl: detect Parent, Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, Parent: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Disposition = Detect, VersionComp: EQ, BuildComp: EQ, RevisionComp: GE, Exist: present
2025-04-09 19:34:38, Info                  CBS    Appl: detectParent: parent found: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, state: Absent
2025-04-09 19:34:38, Info                  CBS    Appl: Evaluating package applicability for package Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, applicable state: Absent
2025-04-09 19:34:38, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Installed
2025-04-09 19:34:38, Info                  CBS    Plan: Force package:Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, update: 43b88c2e5c84f60b174db17bd846f771 into absent state since package state is absent
2025-04-09 19:34:38, Info                  CBS    Plan: Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, current: Installed, pending: Default, start: Installed, applicable: Absent, targeted: Absent, limit: Installed, selected: Default
2025-04-09 19:34:38, Info                  CBS    Computed hard reserve increase of 0 bytes for Client: DISM Package Manager Provider, Session: 31173040_1458537998
2025-04-09 19:34:38, Info                  CBS    Perf: Resolve chain started.
2025-04-09 19:34:38, Info                  CBS    FLOW: Entering stage: Resolving 
2025-04-09 19:34:38, Info                  CSI    00000063@2025/4/10:00:34:38.827 CSI Transaction @0x1d997a71070 initialized for deployment engine {d16d444c-56d8-11d5-882d-0080c847b195} with flags 00000000 and client id 'TI1.31173040_1458537998:2/Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1'

2025-04-09 19:34:38, Info                  CSI    00000064@2025/4/10:00:34:38.829 CSI Transaction @0x1d997a71070 destroyed
2025-04-09 19:34:38, Info                  CBS    Perf: Resolve chain complete.
2025-04-09 19:34:38, Info                  CBS    Perf: Stage chain started.
2025-04-09 19:34:38, Info                  CBS    FLOW: Entering stage: Staging 
2025-04-09 19:34:38, Info                  CSI    00000065@2025/4/10:00:34:38.829 CSI Transaction @0x1d997a71070 initialized for deployment engine {d16d444c-56d8-11d5-882d-0080c847b195} with flags 00000000 and client id 'TI3.31173040_1458537998:3/Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1'

2025-04-09 19:34:38, Info                  CSI    00000066@2025/4/10:00:34:38.831 CSI Transaction @0x1d997a71070 destroyed
2025-04-09 19:34:38, Info                  CBS    Perf: Stage chain complete.
2025-04-09 19:34:38, Info                  CBS    Perf: Execute chain started.
2025-04-09 19:34:38, Info                  CBS    Perf: Entering stage: Install/Uninstalling
2025-04-09 19:34:38, Info                  CBS    FLOW: Entering stage: Installing 
2025-04-09 19:34:38, Info                  CBS    FLOW: Enter Installation Stage: InstallDeployment, Current Operation Stage: Installing
2025-04-09 19:34:38, Info                  CSI    00000067@2025/4/10:00:34:38.831 CSI Transaction @0x1d997a717b0 initialized for deployment engine {d16d444c-56d8-11d5-882d-0080c847b195} with flags 00000000 and client id 'TI4.31173040_1458537998:4/Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1'

2025-04-09 19:34:38, Info                  CBS    Exec: Package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 705d28449bc04bbee2e2d693cb759650
2025-04-09 19:34:38, Info                  CBS    Exec: Unprojecting Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 705d28449bc04bbee2e2d693cb759650, UninstallDeployment: amd64_windows-defender-am..initions-deployment_31bf3856ad364e35_10.0.19041.1_none_8fd08cf5072d45cd
2025-04-09 19:34:38, Info                  CBS    Exec: Unpinning Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 705d28449bc04bbee2e2d693cb759650, UnpinDeployment: amd64_windows-defender-am..initions-deployment_31bf3856ad364e35_10.0.19041.1_none_8fd08cf5072d45cd
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Windows-Defender-AppLayer-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Skipping Package: Windows-Defender-AppLayer-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: c7c0457e969c29080960026388425083 because it is already in the correct state.
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Windows-Defender-Core-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Skipping Package: Windows-Defender-Core-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 95d265f181123e72260986f2c9ed344a because it is already in the correct state.
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Windows-Defender-Management-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Skipping Package: Windows-Defender-Management-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: ad5b80b4be1969397739fca849eea67d because it is already in the correct state.
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Windows-Defender-Management-MDM-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Skipping Package: Windows-Defender-Management-MDM-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, Update: 504a8f6b8c98801a6472360c45479955 because it is already in the correct state.
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Windows-Defender-Management-Powershell-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Skipping Package: Windows-Defender-Management-Powershell-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 19d7e6e595ade1abc2bd0ca651b4908a because it is already in the correct state.
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Windows-Defender-Nis-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Skipping Package: Windows-Defender-Nis-Group-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: 8a0d1c12d69dcbbfd8b7683eb6b852f0 because it is already in the correct state.
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Package: Microsoft-Windows-Common-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1 is already in the correct state, current: Installed, targeted: Installed
2025-04-09 19:34:38, Info                  CBS    Exec: Skipping Package: Microsoft-Windows-Common-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Update: WindowsFoundationDelivery because it is already in the correct state.
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771
2025-04-09 19:34:38, Info                  CBS    Exec: Unprojecting Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, UninstallDeployment: amd64_windows-defender-am..oyment-languagepack_31bf3856ad364e35_10.0.19041.1_en-us_9e8b7e7a59ee7cb3
2025-04-09 19:34:38, Info                  CBS    Exec: Unpinning Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, UnpinDeployment: amd64_windows-defender-am..oyment-languagepack_31bf3856ad364e35_10.0.19041.1_en-us_9e8b7e7a59ee7cb3
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771
2025-04-09 19:34:38, Info                  CBS    Exec: Unprojecting Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, UninstallDeployment: amd64_windows-defender-am..oyment-languagepack_31bf3856ad364e35_10.0.19041.1_fr-ca_39849ba751bfeae0
2025-04-09 19:34:38, Info                  CBS    Exec: Unpinning Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, UnpinDeployment: amd64_windows-defender-am..oyment-languagepack_31bf3856ad364e35_10.0.19041.1_fr-ca_39849ba751bfeae0
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1
2025-04-09 19:34:38, Info                  CBS    Exec: Uninstall package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771
2025-04-09 19:34:38, Info                  CBS    Exec: Unprojecting Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, UninstallDeployment: amd64_windows-defender-am..oyment-languagepack_31bf3856ad364e35_10.0.19041.1_es-mx_a08dc90458a95203
2025-04-09 19:34:38, Info                  CBS    Exec: Unpinning Package: Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1, Update: 43b88c2e5c84f60b174db17bd846f771, UnpinDeployment: amd64_windows-defender-am..oyment-languagepack_31bf3856ad364e35_10.0.19041.1_es-mx_a08dc90458a95203
2025-04-09 19:34:38, Info                  CBS    Plan: Start to process component watchlist
2025-04-09 19:34:38, Info                  CBS    Setting ExecuteState key to: CbsExecuteStateFailed
2025-04-09 19:34:38, Info                  CSI    00000068 Performing 12 operations as follows:
  (0)  Uninstall: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment, version 10.0.19041.1, arch amd64, nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:117]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1.705d28449bc04bbee2e2d693cb759650' ncdata: [l:0]'')
  (1)  MarkUnstaged: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment, version 10.0.19041.1, arch amd64, nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:117]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1.705d28449bc04bbee2e2d693cb759650' ncdata: [l:0]'')
  (2)  Unpin: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment, version 10.0.19041.1, arch amd64, nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:117]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~~10.0.19041.1.705d28449bc04bbee2e2d693cb759650' ncdata: [l:0]'')
  (3)  Uninstall: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'en-US', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (4)  MarkUnstaged: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'en-US', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (5)  Unpin: flags: 0 tlc: [Windows-Defender
2025-04-09 19:34:38, Info                  CSI    -AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'en-US', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~en-US~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (6)  Uninstall: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'fr-CA', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (7)  MarkUnstaged: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'fr-CA', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (8)  Unpin: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'fr-CA', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~fr-CA~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (9)  Uninstall: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'es-MX', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (10)  MarkUnstaged: f
2025-04-09 19:34:38, Info                  CSI    lags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'es-MX', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
  (11)  Unpin: flags: 0 tlc: [Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, version 10.0.19041.1, arch amd64, culture [l:5]'es-MX', nonSxS, pkt {l:8 b:31bf3856ad364e35}]) ref: ( flgs: 00000000 guid: {d16d444c-56d8-11d5-882d-0080c847b195} name: [l:122]'Windows-Defender-AM-Default-Definitions-Package~31bf3856ad364e35~amd64~es-MX~10.0.19041.1.43b88c2e5c84f60b174db17bd846f771' ncdata: [l:0]'')
2025-04-09 19:34:38, Info                  CBS    FLOW: Enter Installation Stage: Closure Analysis, Current Operation Stage: Installing
2025-04-09 19:34:38, Info                  CSI    00000069 Component change list:   { 10.0.19041.1 -> (null) Windows-Defender-AM-Sigs, arch amd64, nonSxS, pkt {l:8 b:31bf3856ad364e35} }
  { 10.0.19041.1 -> (null) Windows-Defender-AM-Engine, arch amd64, nonSxS, pkt {l:8 b:31bf3856ad364e35} }
  { 10.0.19041.1 -> (null) Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, arch amd64, culture [l:5]'fr-CA', nonSxS, pkt {l:8 b:31bf3856ad364e35} }
  { 10.0.19041.1 -> (null) Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, arch amd64, culture [l:5]'en-US', nonSxS, pkt {l:8 b:31bf3856ad364e35} }
  { 10.0.19041.1 -> (null) Windows-Defender-AM-Default-Definitions-Deployment, arch amd64, nonSxS, pkt {l:8 b:31bf3856ad364e35} }
  { 10.0.19041.1 -> (null) Windows-Defender-AM-Default-Definitions-Deployment-LanguagePack, arch amd64, culture [l:5]'es-MX', nonSxS, pkt {l:8 b:31bf3856ad364e35} }
2025-04-09 19:34:38, Info                  CBS    FLOW: Enter Installation Stage: Primitive Installer Analysis, Current Operation Stage: Installing
2025-04-09 19:34:38, Info                  CSI    0000006a Registry installer wrote 0 values

2025-04-09 19:34:38, Info                  CSI    0000006b SMI Primitive Installer [done]

2025-04-09 19:34:38, Info                  CSI    0000006c@2025/4/10:00:34:38.888 Primitive installers committed
2025-04-09 19:34:38, Info                  CSI    0000006d ICSITransaction::Commit calling IStorePendingTransaction::Apply - applyflags=13
2025-04-09 19:34:38, Info                  CSI    0000006e Creating NT transaction (seq 7)
2025-04-09 19:34:38, Info                  CSI    0000006f Created NT transaction (seq 7) result 0x00000000, handle @0x5a4
2025-04-09 19:34:38, Info                  CSI    00000070 Transaction UoW: {39501ec1-1573-11f0-a589-64006a8439b4} for BeginTransaction
2025-04-09 19:34:38, Info                  CBS    FLOW: Enter Installation Stage: Commit Primitive, Current Operation Stage: Installing
2025-04-09 19:34:38, Info                  CSI    00000071@2025/4/10:00:34:38.917 Beginning NT transaction commit...
2025-04-09 19:34:38, Info                  CSI    00000072 Transaction UoW: {39501ec1-1573-11f0-a589-64006a8439b4} for CommitTransaction
2025-04-09 19:34:38, Info                  CSI    00000073@2025/4/10:00:34:38.927 CSI perf trace:
CSIPERF:TXCOMMIT;11411
2025-04-09 19:34:38, Info                  CBS    FLOW: Enter Installation Stage: Advanced Installer Execution, Current Operation Stage: Installing
2025-04-09 19:34:38, Info                  CSI    00000074 Begin executing advanced installer phase 31 index 1 (sequence 66)
    Old component: [l:148 ml:149]'Windows-Defender-AM-Engine, Culture=neutral, Version=10.0.19041.1, PublicKeyToken=31bf3856ad364e35, ProcessorArchitecture=amd64, versionScope=NonSxS'
    New component: [l:0]''
    Install mode: delta
    Smart installer: false
    Installer ID: {3bb9fd2b-351e-4b9c-b1fc-ed0758805998}
    Installer name: 'Events'
2025-04-09 19:34:38, Info                  CSI    00000075 Performing 1 operations as follows:
  (0)  LockComponentPath: flags: 0 comp: {l:16 b:2280c357b0a9db0130000000201bb038} pathid: {l:16 b:2280c357b0a9db0131000000201bb038} path: [l:120]'\SystemRoot\WinSxS\amd64_microsoft-windows-s..-installers-onecore_31bf3856ad364e35_10.0.19041.4840_none_603ff25f5ac99314' pid: 1b20 starttime: 133887187055576063
2025-04-09 19:34:38, Info                  CSI    00000076 Loading installer DLL from explicit path: C:\Windows\WinSxS\amd64_microsoft-windows-s..-installers-onecore_31bf3856ad364e35_10.0.19041.4840_none_603ff25f5ac99314\eventsinstaller.dll
2025-04-09 19:34:38, Info                  CSI    00000077 Performing 1 operations as follows:
  (0)  LockComponentPath: flags: 0 comp: {l:16 b:686ac457b0a9db0132000000201bb038} pathid: {l:16 b:686ac457b0a9db0133000000201bb038} path: [l:103]'\SystemRoot\WinSxS\amd64_windows-defender-am-engine_31bf3856ad364e35_10.0.19041.1_none_b51c691f559e8961' pid: 1b20 starttime: 133887187055576063
2025-04-09 19:34:38, Info                  CSI    00000078@2025/4/10:00:34:38.940 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{3bb9fd2b-351e-4b9c-b1fc-ed0758805998};(null);11885us
2025-04-09 19:34:38, Info                  CSI    00000079 End executing advanced installer (sequence 66)
    Completion status: S_OK 

2025-04-09 19:34:38, Info                  CSI    0000007a Begin executing advanced installer phase 31 index 0 (sequence 0)
    Old component: [l:0]''
    New component: [l:0]''
    Install mode: delta
    Smart installer: true
    Installer ID: {3bb9fd2b-351e-4b9c-b1fc-ed0758805998}
    Installer name: 'Events'
2025-04-09 19:34:38, Info                  CSI    0000007b Registry root HKLM\Software: HKLM\Software

2025-04-09 19:34:38, Info                  CSI    0000007c Registry root HKLM\System: HKLM\System

2025-04-09 19:34:38, Info                  CSI    0000007d@2025/4/10:00:34:38.964 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{3bb9fd2b-351e-4b9c-b1fc-ed0758805998};(null);24165us
2025-04-09 19:34:38, Info                  CSI    0000007e End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:34:38, Info                  CSI    0000007f ExecutionComplete online ticklers
2025-04-09 19:34:38, Info                  CSI    00000080 Performing 1 operations as follows:
  (0)  LockComponentPath: flags: 0 comp: {l:16 b:aa24c957b0a9db0134000000201bb038} pathid: {l:16 b:aa24c957b0a9db0135000000201bb038} path: [l:112]'\SystemRoot\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334' pid: 1b20 starttime: 133887187055576063
2025-04-09 19:34:38, Info                  CSI    00000081 Loading installer DLL from explicit path: C:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334\wcp.dll
2025-04-09 19:34:38, Info                  CSI    00000082 Begin executing advanced installer phase 54 index 0 (sequence 0)
    Old component: [l:0]''
    New component: [l:0]''
    Install mode: delta
    Smart installer: true
    Installer ID: {d4486727-4d36-45a5-bc81-b8935aed1429}
    Installer name: 'Ngen Task Scheduler Tickler'
2025-04-09 19:34:38, Info                  CSI    00000083@2025/4/10:00:34:38.989 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{d4486727-4d36-45a5-bc81-b8935aed1429};(null);116us
2025-04-09 19:34:38, Info                  CSI    00000084 End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:34:39, Info                  CSI    00000085 Loading installer DLL from explicit path: C:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334\wcp.dll
2025-04-09 19:34:39, Info                  CSI    00000086 Begin executing advanced installer phase 54 index 0 (sequence 0)
    Old component: [l:0]''
    New component: [l:0]''
    Install mode: delta
    Smart installer: true
    Installer ID: {4006c604-9280-4d68-b731-2fe3d02e84bf}
    Installer name: 'ShortcutTickler'
2025-04-09 19:34:39, Info                  CSI    00000087@2025/4/10:00:34:39.08 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{4006c604-9280-4d68-b731-2fe3d02e84bf};(null);97us
2025-04-09 19:34:39, Info                  CSI    00000088 End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:34:39, Info                  CSI    00000089 Loading installer DLL from explicit path: C:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.4840_none_7de2e3147cada334\wcp.dll
2025-04-09 19:34:39, Info                  CSI    0000008a Begin executing advanced installer phase 54 index 0 (sequence 0)
    Old component: [l:0]''
    New component: [l:0]''
    Install mode: delta
    Smart installer: true
    Installer ID: {863fbd5f-faba-4730-9858-ceca474b2195}
    Installer name: 'BcdMirror'
2025-04-09 19:34:39, Info                  CSI    0000008b BCD Mirror tickler always runs
2025-04-09 19:34:39, Info                  CSI    0000008c@2025/4/10:00:34:39.027 CSI Advanced installer perf trace:
CSIPERF:AIDONE;{863fbd5f-faba-4730-9858-ceca474b2195};(null);489us
2025-04-09 19:34:39, Info                  CSI    0000008d End executing advanced installer (sequence 0)
    Completion status: S_OK 

2025-04-09 19:34:39, Info                  CSI    0000008e Online ticklers finished with status S_OK
2025-04-09 19:34:39, Info                  CSI    0000008f@2025/4/10:00:34:39.028 Finished running all AIs.

2025-04-09 19:34:39, Info                  CSI    00000090 Creating NT transaction (seq 8)
2025-04-09 19:34:39, Info                  CSI    00000091 Created NT transaction (seq 8) result 0x00000000, handle @0x598
2025-04-09 19:34:39, Info                  CSI    00000092 Transaction UoW: {39501ecd-1573-11f0-a589-64006a8439b4} for BeginTransaction
2025-04-09 19:34:39, Info                  CSI    00000093 Poqexec successfully registered in [l:12 ml:13]'SetupExecute'
2025-04-09 19:34:39, Info                  CSI    00000094@2025/4/10:00:34:39.029 Beginning NT transaction commit...
2025-04-09 19:34:39, Info                  CSI    00000095 Transaction UoW: {39501ecd-1573-11f0-a589-64006a8439b4} for CommitTransaction
2025-04-09 19:34:39, Info                  CSI    00000096@2025/4/10:00:34:39.033 CSI perf trace:
CSIPERF:TXCOMMIT;3831
2025-04-09 19:34:39, Info                  CBS    Setting ExecuteState key to: ExecuteStateNone
2025-04-09 19:34:39, Info                  CBS    Clearing HangDetect value
2025-04-09 19:34:39, Info                  CBS    Saved last global progress. Current: 0, Limit: 1, ExecuteState: ExecuteStateNone
2025-04-09 19:34:39, Info                  CSI    00000097@2025/4/10:00:34:39.033 CSI Transaction @0x1d997a717b0 destroyed
2025-04-09 19:34:39, Info                  CBS    Perf: InstallUninstallChain complete.
2025-04-09 19:34:39, Info                  CBS    FinalCommitPackagesState: Started persisting state of packages
2025-04-09 19:34:39, Info                  CBS    Reporting selectable update change for package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, update: Windows-Defender-Default-Definitions, start: Installed, applicable: Absent, target: Absent, client id: DISM Package Manager Provider, initiated offline: False, execution sequence: 61, first merged sequence: 61, download source: 0, download time (secs): **********, download status: 0x0 (S_OK),reboot required: False, edition based selectable: False, overall result:0x0 (S_OK)
2025-04-09 19:34:39, Info                  CBS    Reporting package change for package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, current: Installed, pending: Default, start: Installed, applicable: Installed, target: Installed, limit: Installed, status: 0x0, failure source: Execute, reboot required: False, client id: DISM Package Manager Provider, initiated offline: False, execution sequence: 61, first merged sequence: 61, reboot reason: REBOOT_NOT_REQUIRED, RM App session: -1, RM App name: N/A, FileName in use: N/A, release type: Feature Pack, OC operation: True, download source: 0, download time (secs): **********, download status: 0x0 (S_OK), Express download: False, Download Size: 0
2025-04-09 19:34:39, Info                  CBS    Reporting package change completion for package: Windows-Defender-Client-Package~31bf3856ad364e35~amd64~~10.0.19041.3636, current: Installed, original: Installed, target: Installed, status: 0x0, failure source: Execute, failure details: "(null)", client id: DISM Package Manager Provider, initiated offline: False, execution sequence: 61, first merged sequence: 61, pending decision: InteractiveInstallSucceeded, primitive execution context: Interactive 
2025-04-09 19:34:39, Info                  CBS    FinalCommitPackagesState: Completed persisting state of packages
2025-04-09 19:34:39, Info                  CBS    Enabling LKG boot option
2025-04-09 19:34:39, Info                  CBS    Exec: Processing complete.  Session: 31173040_1458537998, Package: Microsoft-Windows-Foundation-Package~31bf3856ad364e35~amd64~~10.0.19041.1, Identifier: Windows Foundation [HRESULT = 0x00000000 - S_OK]
2025-04-09 19:34:39, Info                  CBS    Session: 31173040_1458537998 finalized. Reboot required: no [HRESULT = 0x00000000 - S_OK]
2025-04-09 19:34:39, Info                  CBS    Deleting directory: \\?\C:\Windows\CbsTemp\31173040_1458537998\
2025-04-09 19:34:39, Info                  CBS    Moving directory from \\?\C:\Windows\CbsTemp\31173040_1458537998\ to \\?\C:\Windows\CbsTemp\31173040_1458537998\{BCBD6F42-3F91-4EC2-817F-43B4E4169620}
2025-04-09 19:34:39, Info                  CBS    Failed to move \\?\C:\Windows\CbsTemp\31173040_1458537998\ to temp directory \\?\C:\Windows\CbsTemp\31173040_1458537998\{BCBD6F42-3F91-4EC2-817F-43B4E4169620} [HRESULT = 0x80070020 - ERROR_SHARING_VIOLATION]
2025-04-09 19:34:39, Info                  CBS    Failed moving directory: \\?\C:\Windows\CbsTemp\31173040_1458537998\ to temp, will delete in-place instead [HRESULT = 0x80070020 - ERROR_SHARING_VIOLATION]
2025-04-09 19:34:39, Info                  CBS    Deletion of: \\?\C:\Windows\CbsTemp\31173040_1458537998\ successful
2025-04-09 19:36:40, Info                  CBS    TiWorker signaled for shutdown, going to exit.
2025-04-09 19:36:40, Info                  CBS    Deleting the contents of directory: \\?\C:\Windows\CbsTemp
2025-04-09 19:36:40, Info                  CBS    Deletion of: \\?\C:\Windows\CbsTemp successful
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: ExecutionEngineFinalize
2025-04-09 19:36:40, Info                  CBS    Execution Engine Finalize
2025-04-09 19:36:40, Info                  CBS    Execution Engine Finalize
2025-04-09 19:36:40, Info                  CBS    Lock: Lock removed: TiWorkerClassFactory, level: 30, total lock:3
2025-04-09 19:36:40, Info                  CBS    Lock: Lock removed: CCbsWorker, level: 5, total lock:2
2025-04-09 19:36:40, Info                  CBS    Ending the TiWorker main loop.
2025-04-09 19:36:40, Info                  CBS    Starting TiWorker finalization.
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: ExecutionEngineFinalize
2025-04-09 19:36:40, Info                  CBS    CBS Engine already deactivated
2025-04-09 19:36:40, Info                  CBS    CBS Engine already deactivated
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: ComponentAnalyzerFinalize
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: PackageTrackerFinalize
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: CoreResourcesUnload
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: SessionManagerFinalize
2025-04-09 19:36:40, Info                  CBS    Lock: Lock removed: CSIInventoryCriticalSection, level: 64, total lock:10
2025-04-09 19:36:40, Info                  CBS    Lock: Lock removed: CCbsSessionManager, level: 11, total lock:9
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: CapabilityManagerFinalize
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: PublicObjectMonitorFinalize
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: Enter vCoreInitializeLock
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: WcpUnload
2025-04-09 19:36:40, Info                  CSI    00000098 Direct SIL provider: Number of files opened: 28.
2025-04-09 19:36:40, Info                  CSI    00000099 Direct SIL provider: Number of files opened: 478.
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: DrupUnload
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: CfgMgr32Unload
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: DpxUnload
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: SrUnload
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: CbsEsdUnload
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: CbsTraceInfoUninitialize
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: CbsEventUnregister
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: AppContainerUnload
2025-04-09 19:36:40, Info                  CBS    CbsCoreFinalize: WdsUnload, logging from cbscore will end.
2025-04-09 19:36:40, Info                  CBS    Ending TiWorker finalization.
2025-04-09 21:08:05, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-09 21:08:05, Error                        0x80070003 in PbrDeleteDirectory (base\reset\util\src\filesystem.cpp:2948): Failed to delete directory [\\?\C:\$WinREAgent\Scratch][gle=0x00000003]
2025-04-09 21:08:05, Error                        0x80070003 in PushButtonReset::Directory::Delete (base\reset\util\src\filesystem.cpp:2981): Failed to recursively delete [C:\$WinREAgent\Scratch][gle=0x00000003]
2025-04-09 21:08:05, Warning                      0x80070003 in WinREAgent::WorkDir::CleanupScratchDir (base\diagnosis\srt\winreagent\lib\operations\src\workdir.cpp:155): Failed to delete scratch dir
2025-04-09 21:08:05, Info                         [wuauclt.exe] Enter WinReGetConfig
2025-04-09 21:08:05, Info                         [wuauclt.exe] Parameters: configWinDir: NULL
2025-04-09 21:08:05, Info                         [wuauclt.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-09 21:08:05, Info                         [wuauclt.exe] Update enhanced config info is enabled.
2025-04-09 21:08:05, Info                         [wuauclt.exe] WinRE is installed
2025-04-09 21:08:05, Info                         [wuauclt.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-09 21:08:05, Info                         [wuauclt.exe] System is WimBoot: FALSE
2025-04-09 21:08:05, Info                         [wuauclt.exe] WinRE image validated
2025-04-09 21:08:05, Info                         [wuauclt.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll:                                            - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll: Parent process command line: "C:\Windows\system32\wuauclt.exe" /UpdateDeploymentProvider UpdateDeploymentProvider.dll /ClassId 83c8e826-c908-4ee5-866f-8850300bda73 /RunHandlerComServer - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Enter DismInitializeInternal - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Initialized GlobalConfig - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Initialized SessionTable - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 m_pInternalThread started - CCommandThread::Start
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Created g_internalDismSession - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Leave DismInitializeInternal - DismInitializeInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 21:08:05, Info                  DISM   PID=2764 TID=8536 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-09 21:08:05, Info                  DISM   PID=2764 TID=8536 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-09 21:08:05, Info                  DISM   PID=2764 TID=8536 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-04-09 21:08:05, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 21:08:05, Info                  DISM   PID=2764 TID=8536 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-09 21:08:05, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-09 21:08:05, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-09 21:08:05, Info                  DISM   DISM Manager: PID=2764 TID=8536 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 21:08:05, Info                  DISM   DISM Imaging Provider: PID=2764 TID=8536 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Enter DismDeleteInternal - DismDeleteInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Leave DismDeleteInternal - DismDeleteInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Enter DismShutdownInternal - DismShutdownInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 21:08:05, Info                  DISM   DISM Provider Store: PID=2764 TID=8536 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=8536 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Deleted g_internalDismSession - DismShutdownInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Shutdown SessionTable - DismShutdownInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 Leave DismShutdownInternal - DismShutdownInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll:                                          - DismShutdownInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-09 21:08:05, Info                  DISM   API: PID=2764 TID=13752 DismApi.dll:                                          - DismShutdownInternal
2025-04-10 14:04:37, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                            - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                            - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 7a53c1d7e94baacd0b1a72a716e61344 MNMGruT/3EeVsKpezhDREQ.*******.0 - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismInitializeInternal - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Initialized GlobalConfig - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Initialized SessionTable - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 m_pInternalThread started - CCommandThread::Start
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Created g_internalDismSession - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismInitializeInternal - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-10 14:04:37, Info                  DISM   PID=8884 TID=8328 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-10 14:04:37, Info                  DISM   PID=8884 TID=8328 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   PID=8884 TID=8328 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   DISM Manager: PID=8884 TID=8328 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-10 14:04:37, Info                  DISM   DISM Imaging Provider: PID=8884 TID=8328 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismDeleteInternal - DismDeleteInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismDeleteInternal - DismDeleteInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismShutdownInternal - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8328 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8328 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Deleted g_internalDismSession - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Shutdown SessionTable - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismShutdownInternal - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                          - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                          - DismShutdownInternal
2025-04-10 14:04:37, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-10 14:04:37, Info                         WinRE partition total space [943714304], free space [427831296]
2025-04-10 14:04:37, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                            - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                            - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 7a53c1d7e94baacd0b1a72a716e61344 MNMGruT/3EeVsKpezhDREQ.*******.0 - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismInitializeInternal - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Initialized GlobalConfig - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Initialized SessionTable - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 m_pInternalThread started - CCommandThread::Start
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Created g_internalDismSession - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismInitializeInternal - DismInitializeInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-10 14:04:37, Info                  DISM   PID=8884 TID=8352 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-10 14:04:37, Info                  DISM   PID=8884 TID=8352 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   PID=8884 TID=8352 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-10 14:04:37, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-10 14:04:37, Info                  DISM   DISM Manager: PID=8884 TID=8352 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-10 14:04:37, Info                  DISM   DISM Imaging Provider: PID=8884 TID=8352 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismDeleteInternal - DismDeleteInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismDeleteInternal - DismDeleteInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Enter DismShutdownInternal - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-10 14:04:37, Info                  DISM   DISM Provider Store: PID=8884 TID=8352 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=8352 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Deleted g_internalDismSession - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Shutdown SessionTable - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 Leave DismShutdownInternal - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                          - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-10 14:04:37, Info                  DISM   API: PID=8884 TID=9208 DismApi.dll:                                          - DismShutdownInternal
2025-04-10 14:04:37, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-10 14:04:37, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-10 14:04:37, Info                         WinRE partition total space [943714304], free space [427831296]
2025-04-12 10:57:04, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll:                                            - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll:                                            - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 511648d9e61d33ff9c5abdf25c28531e A6A2JhubWkiWf8xGlaNubQ.*******.0 - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Enter DismInitializeInternal - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Initialized GlobalConfig - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Initialized SessionTable - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 m_pInternalThread started - CCommandThread::Start
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Created g_internalDismSession - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Leave DismInitializeInternal - DismInitializeInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-12 10:57:04, Info                  DISM   PID=18956 TID=5968 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-12 10:57:04, Info                  DISM   PID=18956 TID=5968 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-12 10:57:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-12 10:57:04, Info                  DISM   PID=18956 TID=5968 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-12 10:57:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-12 10:57:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-12 10:57:04, Info                  DISM   DISM Manager: PID=18956 TID=5968 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-12 10:57:04, Info                  DISM   DISM Imaging Provider: PID=18956 TID=5968 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Enter DismDeleteInternal - DismDeleteInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Leave DismDeleteInternal - DismDeleteInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Enter DismShutdownInternal - DismShutdownInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-12 10:57:04, Info                  DISM   DISM Provider Store: PID=18956 TID=5968 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=5968 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Deleted g_internalDismSession - DismShutdownInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Shutdown SessionTable - DismShutdownInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 Leave DismShutdownInternal - DismShutdownInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll:                                          - DismShutdownInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-12 10:57:04, Info                  DISM   API: PID=18956 TID=7040 DismApi.dll:                                          - DismShutdownInternal
2025-04-12 10:57:04, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-12 10:57:04, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-12 10:57:04, Info                         WinRE partition total space [943714304], free space [427831296]
2025-04-13 12:25:26, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll:                                            - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll:                                            - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 862e610762cd054076105adc8a9dfed2 Gamt7QMw50GTV7z5.0.0.0 - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Enter DismInitializeInternal - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Initialized GlobalConfig - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Initialized SessionTable - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 m_pInternalThread started - CCommandThread::Start
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Created g_internalDismSession - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Leave DismInitializeInternal - DismInitializeInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-13 12:25:26, Info                  DISM   PID=2768 TID=12688 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-13 12:25:26, Info                  DISM   PID=2768 TID=12688 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-13 12:25:26, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-13 12:25:26, Info                  DISM   PID=2768 TID=12688 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-13 12:25:26, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-13 12:25:26, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-13 12:25:26, Info                  DISM   DISM Manager: PID=2768 TID=12688 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-13 12:25:26, Info                  DISM   DISM Imaging Provider: PID=2768 TID=12688 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Enter DismDeleteInternal - DismDeleteInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Leave DismDeleteInternal - DismDeleteInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Enter DismShutdownInternal - DismShutdownInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-13 12:25:26, Info                  DISM   DISM Provider Store: PID=2768 TID=12688 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=12688 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Deleted g_internalDismSession - DismShutdownInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Shutdown SessionTable - DismShutdownInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 Leave DismShutdownInternal - DismShutdownInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll:                                          - DismShutdownInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-13 12:25:26, Info                  DISM   API: PID=2768 TID=21184 DismApi.dll:                                          - DismShutdownInternal
2025-04-13 12:25:26, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-13 12:25:26, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-13 12:25:26, Info                         WinRE partition total space [943714304], free space [427831296]
2025-04-20 10:19:18, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll:                                            - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll:                                            - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 8f1736b1d4f77c91677d7bbdfc73186c ZqbnfO+zfkq78cBUdTPEmQ.*******.0 - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Enter DismInitializeInternal - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Initialized GlobalConfig - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Initialized SessionTable - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 m_pInternalThread started - CCommandThread::Start
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Created g_internalDismSession - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Leave DismInitializeInternal - DismInitializeInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-20 10:19:18, Info                  DISM   PID=29552 TID=7756 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-20 10:19:18, Info                  DISM   PID=29552 TID=7756 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-20 10:19:18, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-20 10:19:18, Info                  DISM   PID=29552 TID=7756 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-20 10:19:18, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-20 10:19:18, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-20 10:19:18, Info                  DISM   DISM Manager: PID=29552 TID=7756 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-20 10:19:18, Info                  DISM   DISM Imaging Provider: PID=29552 TID=7756 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Enter DismDeleteInternal - DismDeleteInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Leave DismDeleteInternal - DismDeleteInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Enter DismShutdownInternal - DismShutdownInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-20 10:19:18, Info                  DISM   DISM Provider Store: PID=29552 TID=7756 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=7756 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Deleted g_internalDismSession - DismShutdownInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Shutdown SessionTable - DismShutdownInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 Leave DismShutdownInternal - DismShutdownInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll:                                          - DismShutdownInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-20 10:19:18, Info                  DISM   API: PID=29552 TID=26564 DismApi.dll:                                          - DismShutdownInternal
2025-04-20 10:19:18, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-20 10:19:18, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-20 10:19:18, Info                         WinRE partition total space [943714304], free space [427831296]
2025-04-23 15:03:59, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-23 15:03:59, Info                         [svchost.exe] Enter WinReGetConfig
2025-04-23 15:03:59, Info                         [svchost.exe] Parameters: configWinDir: NULL
2025-04-23 15:03:59, Info                         [svchost.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-23 15:03:59, Info                         [svchost.exe] Update enhanced config info is enabled.
2025-04-23 15:03:59, Info                         [svchost.exe] WinRE is installed
2025-04-23 15:03:59, Info                         [svchost.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-23 15:03:59, Info                         [svchost.exe] System is WimBoot: FALSE
2025-04-23 15:03:59, Info                         [svchost.exe] WinRE image validated
2025-04-23 15:03:59, Info                         [svchost.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll:                                            - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll:                                            - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll: Parent process command line: C:\Windows\system32\svchost.exe -k netsvcs -p -s wuauserv - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Enter DismInitializeInternal - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Initialized GlobalConfig - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Initialized SessionTable - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 m_pInternalThread started - CCommandThread::Start
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Created g_internalDismSession - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Leave DismInitializeInternal - DismInitializeInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-23 15:03:59, Info                  DISM   PID=26780 TID=16832 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-23 15:03:59, Info                  DISM   PID=26780 TID=16832 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-23 15:03:59, Info                  DISM   PID=26780 TID=16832 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-04-23 15:03:59, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:03:59, Info                  DISM   PID=26780 TID=16832 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-23 15:03:59, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-23 15:03:59, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:03:59, Info                  DISM   DISM Manager: PID=26780 TID=16832 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-23 15:03:59, Info                  DISM   DISM Imaging Provider: PID=26780 TID=16832 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Enter DismDeleteInternal - DismDeleteInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Leave DismDeleteInternal - DismDeleteInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Enter DismShutdownInternal - DismShutdownInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-23 15:03:59, Info                  DISM   DISM Provider Store: PID=26780 TID=16832 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=16832 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Deleted g_internalDismSession - DismShutdownInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Shutdown SessionTable - DismShutdownInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 Leave DismShutdownInternal - DismShutdownInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll:                                          - DismShutdownInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-23 15:03:59, Info                  DISM   API: PID=26780 TID=12512 DismApi.dll:                                          - DismShutdownInternal
2025-04-23 15:26:06, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-23 15:26:06, Info                         [wuauclt.exe] Enter WinReGetConfig
2025-04-23 15:26:06, Info                         [wuauclt.exe] Parameters: configWinDir: NULL
2025-04-23 15:26:06, Info                         [wuauclt.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-23 15:26:06, Info                         [wuauclt.exe] Update enhanced config info is enabled.
2025-04-23 15:26:06, Info                         [wuauclt.exe] WinRE is installed
2025-04-23 15:26:06, Info                         [wuauclt.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-23 15:26:06, Info                         [wuauclt.exe] System is WimBoot: FALSE
2025-04-23 15:26:06, Info                         [wuauclt.exe] WinRE image validated
2025-04-23 15:26:06, Info                         [wuauclt.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll:                                            - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll:                                            - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll: API Version 10.0.19041.4597 - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll: Parent process command line: "C:\Windows\system32\wuauclt.exe" /UpdateDeploymentProvider UpdateDeploymentProvider.dll /ClassId af8a40c5-4197-43e9-b858-ced9980ac8b1 /RunHandlerComServer - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Enter DismInitializeInternal - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Initialized GlobalConfig - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Initialized SessionTable - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 m_pInternalThread started - CCommandThread::Start
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Created g_internalDismSession - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Leave DismInitializeInternal - DismInitializeInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-23 15:26:06, Info                  DISM   PID=21620 TID=22916 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-23 15:26:06, Info                  DISM   PID=21620 TID=22916 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-23 15:26:06, Info                  DISM   PID=21620 TID=22916 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-04-23 15:26:06, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:26:06, Info                  DISM   PID=21620 TID=22916 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-23 15:26:06, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-23 15:26:06, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-23 15:26:06, Info                  DISM   DISM Manager: PID=21620 TID=22916 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-23 15:26:06, Info                  DISM   DISM Imaging Provider: PID=21620 TID=22916 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Enter DismDeleteInternal - DismDeleteInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Leave DismDeleteInternal - DismDeleteInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Enter DismShutdownInternal - DismShutdownInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-23 15:26:06, Info                  DISM   DISM Provider Store: PID=21620 TID=22916 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=22916 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Deleted g_internalDismSession - DismShutdownInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Shutdown SessionTable - DismShutdownInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 Leave DismShutdownInternal - DismShutdownInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll:                                          - DismShutdownInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-23 15:26:06, Info                  DISM   API: PID=21620 TID=27056 DismApi.dll:                                          - DismShutdownInternal
2025-04-27 10:43:36, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll:                                            - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll:                                            - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 0301960a85352dd009ff786773a56ebf 6G8cEfnZk0uU11AgGLYfQA.*******.0 - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Enter DismInitializeInternal - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Initialized GlobalConfig - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Initialized SessionTable - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 m_pInternalThread started - CCommandThread::Start
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Created g_internalDismSession - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Leave DismInitializeInternal - DismInitializeInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-27 10:43:36, Info                  DISM   PID=9176 TID=1432 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-04-27 10:43:36, Info                  DISM   PID=9176 TID=1432 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-04-27 10:43:36, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-27 10:43:36, Info                  DISM   PID=9176 TID=1432 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-04-27 10:43:36, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-04-27 10:43:36, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-04-27 10:43:36, Info                  DISM   DISM Manager: PID=9176 TID=1432 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-27 10:43:36, Info                  DISM   DISM Imaging Provider: PID=9176 TID=1432 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Enter DismDeleteInternal - DismDeleteInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Leave DismDeleteInternal - DismDeleteInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Enter DismShutdownInternal - DismShutdownInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-04-27 10:43:36, Info                  DISM   DISM Provider Store: PID=9176 TID=1432 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=1432 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Deleted g_internalDismSession - DismShutdownInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Shutdown SessionTable - DismShutdownInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 Leave DismShutdownInternal - DismShutdownInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll:                                          - DismShutdownInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-04-27 10:43:36, Info                  DISM   API: PID=9176 TID=15748 DismApi.dll:                                          - DismShutdownInternal
2025-04-27 10:43:36, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-04-27 10:43:36, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-04-27 10:43:36, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-04 17:34:04, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll:                                            - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll:                                            - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe a19eab62416bab18452addc8cf98dd5f VfINNbDSikiI51Pg66SijQ.*******.0 - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Enter DismInitializeInternal - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Initialized GlobalConfig - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Initialized SessionTable - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 m_pInternalThread started - CCommandThread::Start
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Created g_internalDismSession - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Leave DismInitializeInternal - DismInitializeInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-04 17:34:04, Info                  DISM   PID=13220 TID=15344 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-04 17:34:04, Info                  DISM   PID=13220 TID=15344 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-04 17:34:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-04 17:34:04, Info                  DISM   PID=13220 TID=15344 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-04 17:34:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-04 17:34:04, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-04 17:34:04, Info                  DISM   DISM Manager: PID=13220 TID=15344 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-04 17:34:04, Info                  DISM   DISM Imaging Provider: PID=13220 TID=15344 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Enter DismDeleteInternal - DismDeleteInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Leave DismDeleteInternal - DismDeleteInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Enter DismShutdownInternal - DismShutdownInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-04 17:34:04, Info                  DISM   DISM Provider Store: PID=13220 TID=15344 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15344 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Deleted g_internalDismSession - DismShutdownInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Shutdown SessionTable - DismShutdownInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 Leave DismShutdownInternal - DismShutdownInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll:                                          - DismShutdownInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-04 17:34:04, Info                  DISM   API: PID=13220 TID=15100 DismApi.dll:                                          - DismShutdownInternal
2025-05-04 17:34:04, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-04 17:34:04, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-04 17:34:04, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-11 04:47:21, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll:                                            - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll:                                            - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 8421e98f5542f0b7ef01c1cf8e6b563d aUCFw1Lw1USnm2k7.0.0.0 - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Enter DismInitializeInternal - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Initialized GlobalConfig - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Initialized SessionTable - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 m_pInternalThread started - CCommandThread::Start
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Created g_internalDismSession - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Leave DismInitializeInternal - DismInitializeInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-11 04:47:21, Info                  DISM   PID=11856 TID=13056 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-11 04:47:21, Info                  DISM   PID=11856 TID=13056 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-11 04:47:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-11 04:47:21, Info                  DISM   PID=11856 TID=13056 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-11 04:47:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-11 04:47:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-11 04:47:21, Info                  DISM   DISM Manager: PID=11856 TID=13056 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-11 04:47:21, Info                  DISM   DISM Imaging Provider: PID=11856 TID=13056 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Enter DismDeleteInternal - DismDeleteInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Leave DismDeleteInternal - DismDeleteInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Enter DismShutdownInternal - DismShutdownInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-11 04:47:21, Info                  DISM   DISM Provider Store: PID=11856 TID=13056 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=13056 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Deleted g_internalDismSession - DismShutdownInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Shutdown SessionTable - DismShutdownInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 Leave DismShutdownInternal - DismShutdownInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll:                                          - DismShutdownInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-11 04:47:21, Info                  DISM   API: PID=11856 TID=7628 DismApi.dll:                                          - DismShutdownInternal
2025-05-11 04:47:21, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-11 04:47:21, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-11 04:47:21, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-12 15:58:23, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll:                                            - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll:                                            - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 9f45cd476a021f5513949b40c93584ae aPBJxJRqtEKWwzyGP+dFtw.*******.0 - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Enter DismInitializeInternal - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Initialized GlobalConfig - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Initialized SessionTable - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 m_pInternalThread started - CCommandThread::Start
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Created g_internalDismSession - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Leave DismInitializeInternal - DismInitializeInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-12 15:58:23, Info                  DISM   PID=10420 TID=18112 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-12 15:58:23, Info                  DISM   PID=10420 TID=18112 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-12 15:58:23, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-12 15:58:23, Info                  DISM   PID=10420 TID=18112 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-12 15:58:23, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-12 15:58:23, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-12 15:58:23, Info                  DISM   DISM Manager: PID=10420 TID=18112 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-12 15:58:23, Info                  DISM   DISM Imaging Provider: PID=10420 TID=18112 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Enter DismDeleteInternal - DismDeleteInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Leave DismDeleteInternal - DismDeleteInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Enter DismShutdownInternal - DismShutdownInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-12 15:58:23, Info                  DISM   DISM Provider Store: PID=10420 TID=18112 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=18112 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Deleted g_internalDismSession - DismShutdownInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Shutdown SessionTable - DismShutdownInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 Leave DismShutdownInternal - DismShutdownInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll:                                          - DismShutdownInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-12 15:58:23, Info                  DISM   API: PID=10420 TID=17676 DismApi.dll:                                          - DismShutdownInternal
2025-05-12 15:58:23, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-12 15:58:23, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-12 15:58:23, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-14 11:20:48, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-14 11:20:48, Info                         [svchost.exe] Enter WinReGetConfig
2025-05-14 11:20:48, Info                         [svchost.exe] Parameters: configWinDir: NULL
2025-05-14 11:20:48, Info                         [svchost.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-14 11:20:48, Info                         [svchost.exe] Update enhanced config info is enabled.
2025-05-14 11:20:48, Info                         [svchost.exe] WinRE is installed
2025-05-14 11:20:48, Info                         [svchost.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-14 11:20:48, Info                         [svchost.exe] System is WimBoot: FALSE
2025-05-14 11:20:48, Info                         [svchost.exe] WinRE image validated
2025-05-14 11:20:48, Info                         [svchost.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll:                                            - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll:                                            - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll: Parent process command line: C:\Windows\system32\svchost.exe -k netsvcs -p -s wuauserv - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Enter DismInitializeInternal - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Initialized GlobalConfig - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Initialized SessionTable - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 m_pInternalThread started - CCommandThread::Start
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Created g_internalDismSession - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Leave DismInitializeInternal - DismInitializeInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-14 11:20:48, Info                  DISM   PID=3028 TID=8708 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-14 11:20:48, Info                  DISM   PID=3028 TID=8708 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-14 11:20:48, Info                  DISM   PID=3028 TID=8708 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-05-14 11:20:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 11:20:48, Info                  DISM   PID=3028 TID=8708 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-14 11:20:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-14 11:20:48, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 11:20:48, Info                  DISM   DISM Manager: PID=3028 TID=8708 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-14 11:20:48, Info                  DISM   DISM Imaging Provider: PID=3028 TID=8708 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Enter DismDeleteInternal - DismDeleteInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Leave DismDeleteInternal - DismDeleteInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Enter DismShutdownInternal - DismShutdownInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-14 11:20:48, Info                  DISM   DISM Provider Store: PID=3028 TID=8708 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8708 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Deleted g_internalDismSession - DismShutdownInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Shutdown SessionTable - DismShutdownInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 Leave DismShutdownInternal - DismShutdownInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll:                                          - DismShutdownInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-14 11:20:48, Info                  DISM   API: PID=3028 TID=8244 DismApi.dll:                                          - DismShutdownInternal
2025-05-14 12:52:53, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-14 12:52:53, Info                         [wuauclt.exe] Enter WinReGetConfig
2025-05-14 12:52:53, Info                         [wuauclt.exe] Parameters: configWinDir: NULL
2025-05-14 12:52:53, Info                         [wuauclt.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-14 12:52:53, Info                         [wuauclt.exe] Update enhanced config info is enabled.
2025-05-14 12:52:53, Info                         [wuauclt.exe] WinRE is installed
2025-05-14 12:52:53, Info                         [wuauclt.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-14 12:52:53, Info                         [wuauclt.exe] System is WimBoot: FALSE
2025-05-14 12:52:53, Info                         [wuauclt.exe] WinRE image validated
2025-05-14 12:52:53, Info                         [wuauclt.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll:                                            - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll:                                            - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll: Parent process command line: "C:\Windows\system32\wuauclt.exe" /UpdateDeploymentProvider UpdateDeploymentProvider.dll /ClassId 0238d958-0d93-439d-8714-d15161cc34cb /RunHandlerComServer - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Enter DismInitializeInternal - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Initialized GlobalConfig - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Initialized SessionTable - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 m_pInternalThread started - CCommandThread::Start
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Created g_internalDismSession - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Leave DismInitializeInternal - DismInitializeInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-14 12:52:53, Info                  DISM   PID=11488 TID=7504 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-14 12:52:53, Info                  DISM   PID=11488 TID=7504 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-14 12:52:53, Info                  DISM   PID=11488 TID=7504 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-05-14 12:52:53, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 12:52:53, Info                  DISM   PID=11488 TID=7504 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-14 12:52:53, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-14 12:52:53, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-14 12:52:53, Info                  DISM   DISM Manager: PID=11488 TID=7504 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-14 12:52:53, Info                  DISM   DISM Imaging Provider: PID=11488 TID=7504 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Enter DismDeleteInternal - DismDeleteInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Leave DismDeleteInternal - DismDeleteInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Enter DismShutdownInternal - DismShutdownInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-14 12:52:53, Info                  DISM   DISM Provider Store: PID=11488 TID=7504 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=7504 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Deleted g_internalDismSession - DismShutdownInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Shutdown SessionTable - DismShutdownInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 Leave DismShutdownInternal - DismShutdownInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll:                                          - DismShutdownInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-14 12:52:53, Info                  DISM   API: PID=11488 TID=16420 DismApi.dll:                                          - DismShutdownInternal
2025-05-18 12:19:45, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-18 12:19:45, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll:                                            - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll:                                            - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe b0b466bc2325a91b3ed20abd983f047c lx7qCsz8j0SRveLV.0.0.0 - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 Enter DismInitializeInternal - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 Initialized GlobalConfig - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 Initialized SessionTable - DismInitializeInternal
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=18336 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-18 12:19:45, Info                  DISM   API: PID=2232 TID=4880 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 m_pInternalThread started - CCommandThread::Start
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Created g_internalDismSession - DismInitializeInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Leave DismInitializeInternal - DismInitializeInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-18 12:19:46, Info                  DISM   PID=2232 TID=4880 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-18 12:19:46, Info                  DISM   PID=2232 TID=4880 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-18 12:19:46, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-18 12:19:46, Info                  DISM   PID=2232 TID=4880 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-18 12:19:46, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-18 12:19:46, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-18 12:19:46, Info                  DISM   DISM Manager: PID=2232 TID=4880 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-18 12:19:46, Info                  DISM   DISM Imaging Provider: PID=2232 TID=4880 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Enter DismDeleteInternal - DismDeleteInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Leave DismDeleteInternal - DismDeleteInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Enter DismShutdownInternal - DismShutdownInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-18 12:19:46, Info                  DISM   DISM Provider Store: PID=2232 TID=4880 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=4880 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Deleted g_internalDismSession - DismShutdownInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Shutdown SessionTable - DismShutdownInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 Leave DismShutdownInternal - DismShutdownInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll:                                          - DismShutdownInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-18 12:19:46, Info                  DISM   API: PID=2232 TID=18336 DismApi.dll:                                          - DismShutdownInternal
2025-05-18 12:19:46, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-18 12:19:46, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-18 12:19:46, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-20 06:24:51, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll:                                            - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll:                                            - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 57f5dbd89699321532b198274e98d660 rn8Yjhaaika2uFI9fPwt/g.*******.0 - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Enter DismInitializeInternal - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Initialized GlobalConfig - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Initialized SessionTable - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 m_pInternalThread started - CCommandThread::Start
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Created g_internalDismSession - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Leave DismInitializeInternal - DismInitializeInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-20 06:24:51, Info                  DISM   PID=21848 TID=6712 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-20 06:24:51, Info                  DISM   PID=21848 TID=6712 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-20 06:24:51, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-20 06:24:51, Info                  DISM   PID=21848 TID=6712 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-20 06:24:51, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-20 06:24:51, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-20 06:24:51, Info                  DISM   DISM Manager: PID=21848 TID=6712 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-20 06:24:51, Info                  DISM   DISM Imaging Provider: PID=21848 TID=6712 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Enter DismDeleteInternal - DismDeleteInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Leave DismDeleteInternal - DismDeleteInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Enter DismShutdownInternal - DismShutdownInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-20 06:24:51, Info                  DISM   DISM Provider Store: PID=21848 TID=6712 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=6712 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Deleted g_internalDismSession - DismShutdownInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Shutdown SessionTable - DismShutdownInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 Leave DismShutdownInternal - DismShutdownInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll:                                          - DismShutdownInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-20 06:24:51, Info                  DISM   API: PID=21848 TID=18888 DismApi.dll:                                          - DismShutdownInternal
2025-05-20 06:24:51, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-20 06:24:51, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-20 06:24:51, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-25 06:37:32, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll:                                            - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll:                                            - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 74d6a7ec5000ddcd39954c4225db5b71 /MN3iOpEBkepi6nD.0.0.0 - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Enter DismInitializeInternal - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Initialized GlobalConfig - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Initialized SessionTable - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 m_pInternalThread started - CCommandThread::Start
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Created g_internalDismSession - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Leave DismInitializeInternal - DismInitializeInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-25 06:37:32, Info                  DISM   PID=5476 TID=13464 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-25 06:37:32, Info                  DISM   PID=5476 TID=13464 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-25 06:37:32, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-25 06:37:32, Info                  DISM   PID=5476 TID=13464 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-25 06:37:32, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-25 06:37:32, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-25 06:37:32, Info                  DISM   DISM Manager: PID=5476 TID=13464 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-25 06:37:32, Info                  DISM   DISM Imaging Provider: PID=5476 TID=13464 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Enter DismDeleteInternal - DismDeleteInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Leave DismDeleteInternal - DismDeleteInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Enter DismShutdownInternal - DismShutdownInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-25 06:37:32, Info                  DISM   DISM Provider Store: PID=5476 TID=13464 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=13464 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Deleted g_internalDismSession - DismShutdownInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Shutdown SessionTable - DismShutdownInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 Leave DismShutdownInternal - DismShutdownInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll:                                          - DismShutdownInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-25 06:37:32, Info                  DISM   API: PID=5476 TID=15024 DismApi.dll:                                          - DismShutdownInternal
2025-05-25 06:37:32, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-25 06:37:32, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-25 06:37:32, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-27 14:29:14, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll:                                            - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll:                                            - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 5fd443a7e859a5fd96e20760cb006b67 VBMTT6hmP0O8Ztu8YkdyoA.*******.0 - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Enter DismInitializeInternal - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Initialized GlobalConfig - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Initialized SessionTable - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 m_pInternalThread started - CCommandThread::Start
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Created g_internalDismSession - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Leave DismInitializeInternal - DismInitializeInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-27 14:29:14, Info                  DISM   PID=12732 TID=21144 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-27 14:29:14, Info                  DISM   PID=12732 TID=21144 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-27 14:29:14, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-27 14:29:14, Info                  DISM   PID=12732 TID=21144 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-27 14:29:14, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-27 14:29:14, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-27 14:29:14, Info                  DISM   DISM Manager: PID=12732 TID=21144 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-27 14:29:14, Info                  DISM   DISM Imaging Provider: PID=12732 TID=21144 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Enter DismDeleteInternal - DismDeleteInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Leave DismDeleteInternal - DismDeleteInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Enter DismShutdownInternal - DismShutdownInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-27 14:29:14, Info                  DISM   DISM Provider Store: PID=12732 TID=21144 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=21144 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Deleted g_internalDismSession - DismShutdownInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Shutdown SessionTable - DismShutdownInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 Leave DismShutdownInternal - DismShutdownInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll:                                          - DismShutdownInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-27 14:29:14, Info                  DISM   API: PID=12732 TID=16872 DismApi.dll:                                          - DismShutdownInternal
2025-05-27 14:29:14, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-05-27 14:29:14, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-27 14:29:14, Info                         WinRE partition total space [943714304], free space [427831296]
2025-05-28 19:06:56, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-28 19:06:56, Info                         [svchost.exe] Enter WinReGetConfig
2025-05-28 19:06:56, Info                         [svchost.exe] Parameters: configWinDir: NULL
2025-05-28 19:06:56, Info                         [svchost.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-28 19:06:56, Info                         [svchost.exe] Update enhanced config info is enabled.
2025-05-28 19:06:56, Info                         [svchost.exe] WinRE is installed
2025-05-28 19:06:56, Info                         [svchost.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-28 19:06:56, Info                         [svchost.exe] System is WimBoot: FALSE
2025-05-28 19:06:56, Info                         [svchost.exe] WinRE image validated
2025-05-28 19:06:56, Info                         [svchost.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll:                                            - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll:                                            - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll: Parent process command line: C:\Windows\system32\svchost.exe -k netsvcs -p -s wuauserv - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Enter DismInitializeInternal - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Initialized GlobalConfig - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Initialized SessionTable - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 m_pInternalThread started - CCommandThread::Start
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Created g_internalDismSession - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Leave DismInitializeInternal - DismInitializeInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-28 19:06:56, Info                  DISM   PID=30280 TID=23584 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-28 19:06:56, Info                  DISM   PID=30280 TID=23584 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-28 19:06:56, Info                  DISM   PID=30280 TID=23584 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-05-28 19:06:56, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:06:56, Info                  DISM   PID=30280 TID=23584 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-28 19:06:56, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-28 19:06:56, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:06:56, Info                  DISM   DISM Manager: PID=30280 TID=23584 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-28 19:06:56, Info                  DISM   DISM Imaging Provider: PID=30280 TID=23584 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Enter DismDeleteInternal - DismDeleteInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Leave DismDeleteInternal - DismDeleteInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Enter DismShutdownInternal - DismShutdownInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-28 19:06:56, Info                  DISM   DISM Provider Store: PID=30280 TID=23584 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=23584 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Deleted g_internalDismSession - DismShutdownInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Shutdown SessionTable - DismShutdownInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 Leave DismShutdownInternal - DismShutdownInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll:                                          - DismShutdownInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-28 19:06:56, Info                  DISM   API: PID=30280 TID=28340 DismApi.dll:                                          - DismShutdownInternal
2025-05-28 19:12:21, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-05-28 19:12:21, Info                         [wuauclt.exe] Enter WinReGetConfig
2025-05-28 19:12:21, Info                         [wuauclt.exe] Parameters: configWinDir: NULL
2025-05-28 19:12:21, Info                         [wuauclt.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-05-28 19:12:21, Info                         [wuauclt.exe] Update enhanced config info is enabled.
2025-05-28 19:12:21, Info                         [wuauclt.exe] WinRE is installed
2025-05-28 19:12:21, Info                         [wuauclt.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-05-28 19:12:21, Info                         [wuauclt.exe] System is WimBoot: FALSE
2025-05-28 19:12:21, Info                         [wuauclt.exe] WinRE image validated
2025-05-28 19:12:21, Info                         [wuauclt.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll:                                            - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll:                                            - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll: Parent process command line: "C:\Windows\system32\wuauclt.exe" /UpdateDeploymentProvider UpdateDeploymentProvider.dll /ClassId b2c6d460-eec1-4605-b3e0-c65924888667 /RunHandlerComServer - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Enter DismInitializeInternal - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: C:\$WinREAgent\Scratch - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Initialized GlobalConfig - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Initialized SessionTable - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 m_pInternalThread started - CCommandThread::Start
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Created g_internalDismSession - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Leave DismInitializeInternal - DismInitializeInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-28 19:12:21, Info                  DISM   PID=25440 TID=24356 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-05-28 19:12:21, Info                  DISM   PID=25440 TID=24356 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-05-28 19:12:21, Info                  DISM   PID=25440 TID=24356 Scratch directory set to 'C:\$WinREAgent\Scratch'. - CDISMManager::put_ScratchDir
2025-05-28 19:12:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:12:21, Info                  DISM   PID=25440 TID=24356 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-05-28 19:12:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-05-28 19:12:21, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-05-28 19:12:21, Info                  DISM   DISM Manager: PID=25440 TID=24356 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-28 19:12:21, Info                  DISM   DISM Imaging Provider: PID=25440 TID=24356 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Enter DismDeleteInternal - DismDeleteInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Leave DismDeleteInternal - DismDeleteInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Enter DismShutdownInternal - DismShutdownInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-05-28 19:12:21, Info                  DISM   DISM Provider Store: PID=25440 TID=24356 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=24356 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Deleted g_internalDismSession - DismShutdownInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Shutdown SessionTable - DismShutdownInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 Leave DismShutdownInternal - DismShutdownInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll:                                          - DismShutdownInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-05-28 19:12:21, Info                  DISM   API: PID=25440 TID=19976 DismApi.dll:                                          - DismShutdownInternal
2025-06-02 09:25:22, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll:                                            - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll: <----- Starting DismApi.dll session -----> - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll:                                            - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll: Host machine information: OS Version=10.0.19045, Running architecture=amd64, Number of processors=8 - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll: API Version 10.0.19041.5794 - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll: Parent process command line: C:\Windows\System32\WaaSMedicAgent.exe 49e04d61a08979289b4b0613299b32a3 MCAh7Z9OZUOnWBZ2.0.0.0 - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Enter DismInitializeInternal - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Input parameters: LogLevel: 2, LogFilePath: C:\Windows\Logs\WinREAgent\setupact.log, ScratchDirectory: (null) - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Initialized GlobalConfig - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Initialized SessionTable - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Lookup in table by path failed for: DummyPath-2BA51B78-C7F7-4910-B99D-BB7345357CDC - CTransactionalImageTable::LookupImagePath
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Waiting for m_pInternalThread to start - CCommandThread::Start
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 Enter CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 Enter CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 CommandThread StartupEvent signaled - CCommandThread::WaitForStartup
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 m_pInternalThread started - CCommandThread::Start
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Created g_internalDismSession - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Leave DismInitializeInternal - DismInitializeInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Enter DismGetImageInfoInternal - DismGetImageInfoInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Input parameters: ImageFilePath: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE\winre.wim - DismGetImageInfoInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-06-02 09:25:22, Info                  DISM   PID=45980 TID=37952 Scratch directory set to 'C:\Windows\TEMP\'. - CDISMManager::put_ScratchDir
2025-06-02 09:25:22, Info                  DISM   PID=45980 TID=37952 DismCore.dll version: 10.0.19041.3636 - CDISMManager::FinalConstruct
2025-06-02 09:25:22, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-06-02 09:25:22, Info                  DISM   PID=45980 TID=37952 Successfully loaded the ImageSession at "C:\Windows\SYSTEM32\Dism" - CDISMManager::LoadLocalImageSession
2025-06-02 09:25:22, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2025-06-02 09:25:22, Info                  DISM   Initialized Panther logging at C:\Windows\Logs\WinREAgent\setupact.log
2025-06-02 09:25:22, Info                  DISM   DISM Manager: PID=45980 TID=37952 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-06-02 09:25:22, Info                  DISM   DISM Imaging Provider: PID=45980 TID=37952 WIM image specified - CGenericImagingManager::GetImageInfoCollection
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Connecting to the provider located at C:\Windows\SYSTEM32\Dism\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Leave DismGetImageInfoInternal - DismGetImageInfoInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Enter DismDeleteInternal - DismDeleteInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Leave DismDeleteInternal - DismDeleteInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Enter DismShutdownInternal - DismShutdownInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 GetReferenceCount hr: 0x0 - CSessionTable::RemoveSession
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Refcount for DismSession= 1s 0 - CSessionTable::RemoveSession
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Successfully enqueued command object - CCommandThread::EnqueueCommandObject
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 ExecuteLoop: CommandQueue signaled - CCommandThread::ExecuteLoop
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 Successfully dequeued command object - CCommandThread::DequeueCommandObject
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 ExecuteLoop: Cancel signaled - CCommandThread::ExecuteLoop
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 Leave CCommandThread::ExecuteLoop - CCommandThread::ExecuteLoop
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2025-06-02 09:25:22, Info                  DISM   DISM Provider Store: PID=45980 TID=37952 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=37952 Leave CCommandThread::CommandThreadProcedureStub - CCommandThread::CommandThreadProcedureStub
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Deleted g_internalDismSession - DismShutdownInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Shutdown SessionTable - DismShutdownInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 Leave DismShutdownInternal - DismShutdownInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll:                                          - DismShutdownInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll: <----- Ending DismApi.dll session -----> - DismShutdownInternal
2025-06-02 09:25:22, Info                  DISM   API: PID=45980 TID=3448 DismApi.dll:                                          - DismShutdownInternal
2025-06-02 09:25:22, Info                         LogSession: Starting a new log session at [C:\Windows\Logs\WinREAgent]
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Enter WinReGetConfig
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Parameters: configWinDir: NULL
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE config file path: C:\Windows\system32\Recovery\ReAgent.xml
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Update enhanced config info is enabled.
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE is installed
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE is installed at: \\?\GLOBALROOT\device\harddisk0\partition4\Recovery\WindowsRE
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] System is WimBoot: FALSE
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] WinRE image validated
2025-06-02 09:25:22, Info                         [WaaSMedicAgent.exe] Exit WinReGetConfig return value: 1, last error: 0x0
2025-06-02 09:25:22, Info                         WinRE partition total space [943714304], free space [427831296]
