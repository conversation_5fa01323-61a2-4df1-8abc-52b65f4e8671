*%
*% Copyright (c) Microsoft Corporation
*%
*% All rights reserved.
*%

*GPDFileVersion: "1.0"
*GPDSpecVersion: "1.0"
*GPDFileName:    "SendToOneNote.gpd"
*Include:        "StdNames.gpd"
*Include:        "SendToOneNoteNames.gpd"
*ModelName:      "Send To OneNote Driver"
*MasterUnits:    PAIR(1200, 1200)
*ResourceDLL:    "unires.dll"
*PrinterType:    PAGE

*Include:        "msxpsinc.gpd"

*%******************************************************************************
*%                            PageMediaSize - Paper Size
*%******************************************************************************
*Feature: PaperSize
{
    *rcNameID: =PAPER_SIZE_DISPLAY
    *DefaultOption: LETTER

    *Option: A3
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 9667
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(13460, 19440)
                *PrintableOrigin: PAIR(284, 200)
                *CursorOrigin: PAIR(284, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(13628, 19368)
                *PrintableOrigin: PAIR(200, 236)
                *CursorOrigin: PAIR(200, 19604)
            }
        }
    }

    *Option: A4
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4249
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(9352, 13628)
                *PrintableOrigin: PAIR(284, 200)
                *CursorOrigin: PAIR(284, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(9520, 13556)
                *PrintableOrigin: PAIR(200, 236)
                *CursorOrigin: PAIR(200, 13792)
            }
        }
    }

    *Option: B4
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 6391
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(11572, 16796)
                *PrintableOrigin: PAIR(284, 200)
                *CursorOrigin: PAIR(284, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(11740, 16724)
                *PrintableOrigin: PAIR(200, 236)
                *CursorOrigin: PAIR(200, 16960)
            }
        }
    }

    *Option: B5
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 3198
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(7900, 11140)
                *PrintableOrigin: PAIR(352, 300)
                *CursorOrigin: PAIR(300, 100)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(7760, 11140)
                *PrintableOrigin: PAIR(300, 400)
                *CursorOrigin: PAIR(100, 11940)
            }
        }
    }

    *Option: EXECUTIVE
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(8100, 11500)
                *PrintableOrigin: PAIR(300, 300)
                *CursorOrigin: PAIR(300, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(8300, 11500)
                *PrintableOrigin: PAIR(200, 300)
                *CursorOrigin: PAIR(200, 12300)
            }
        }
    }

    *Option: ENV_10
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(4348, 11000)
                *PrintableOrigin: PAIR(300, 200)
                *CursorOrigin: PAIR(300, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(4548, 10920)
                *PrintableOrigin: PAIR(200, 240)
                *CursorOrigin: PAIR(200, 11160)
            }
        }
    }

    *Option: LEGAL
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 1692
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(9000, 15500)
                *PrintableOrigin: PAIR(400, 600)
                *CursorOrigin: PAIR(180, 300)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(9000, 15500)
                *PrintableOrigin: PAIR(400, 900)
                *CursorOrigin: PAIR(180, 16500)
            }
        }
    }

    *Option: LETTER
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 1028
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(9500, 12500)
                *PrintableOrigin: PAIR(400, 400)
                *CursorOrigin: PAIR(300, 300)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(9500, 12200)
                *PrintableOrigin: PAIR(450, 300)
                *CursorOrigin: PAIR(200, 12900)
            }
        }
    }

    *Option: ENV_MONARCH
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(4048, 8600)
                *PrintableOrigin: PAIR(300, 200)
                *CursorOrigin: PAIR(300, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(4248, 8520)
                *PrintableOrigin: PAIR(200, 240)
                *CursorOrigin: PAIR(200, 8760)
            }
        }
    }

    *Option: TABLOID
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(12000, 19200)
                *PrintableOrigin: PAIR(400, 400)
                *CursorOrigin: PAIR(400, 400)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(12000, 19200)
                *PrintableOrigin: PAIR(400, 600)
                *CursorOrigin: PAIR(400, 18000)
            }
        }
    }
}

*%******************************************************************************
*%                             PageOrientation
*%******************************************************************************
*Feature: Orientation
{
    *rcNameID: =ORIENTATION_DISPLAY
    *DefaultOption: PORTRAIT

    *Option: PORTRAIT
    {
        *rcNameID: =PORTRAIT_DISPLAY
    }

    *Option: LANDSCAPE_CC270
    {
        *rcNameID: =LANDSCAPE_DISPLAY
    }
}

*%******************************************************************************
*%                              Resolution
*%******************************************************************************
*Feature: Resolution
{
    *rcNameID: =RESOLUTION_DISPLAY
    *DefaultOption: DPI600

    *Option: DPI600
    {
        *Name: "600 x 600 " =DOTS_PER_INCH
        *DPI: PAIR(600, 600)
        *TextDPI: PAIR(600, 600)
        *SpotDiameter: 100
        *Command: CmdBeginRaster { *Cmd : "<1B>*v7S<1B>*r1A" }
        *Command: CmdEndRaster { *Cmd : "<1B>*rC" }
        *Command: CmdSendBlockData { *Cmd : "<1B>*b" %d{NumOfDataBytes}"W" }
    }

    *Option: DPI1200
    {
        *Name: "1200 x 1200 " =DOTS_PER_INCH
        *DPI: PAIR(1200, 1200)
        *TextDPI: PAIR(1200, 1200)
        *SpotDiameter: 100
        *Command: CmdBeginRaster { *Cmd : "<1B>*v7S<1B>*r1A" }
        *Command: CmdEndRaster { *Cmd : "<1B>*rC" }
        *Command: CmdSendBlockData { *Cmd : "<1B>*b" %d{NumOfDataBytes}"W" }
    }
}

*%******************************************************************************
*%                            Printer Memory
*% WARNING: removing this makes the print driver not install
*%******************************************************************************
*Feature: Memory
{
    *rcNameID: =PRINTER_MEMORY_DISPLAY
    *DefaultOption: 32768KB
    *Option: 16384KB
    {
        *Name: "16MB"
        *MemoryConfigKB: PAIR(16384, 13950)
    }
    *Option: 32768KB
    {
        *Name: "32MB"
        *MemoryConfigKB: PAIR(32768, 28350)
    }
}

*%******************************************************************************
*%                            Color Mode
*% Needed so we advertise ourselves as a color printer (DEVMODE.dmColor)
*%******************************************************************************
*Feature: ColorMode
{
    *rcNameID: =COLOR_PRINTING_MODE_DISPLAY
    *DefaultOption: 24bpp
    *ConcealFromUI?: TRUE

    *Option: 24bpp
    {
        *rcNameID: =24BPP_DISPLAY
        *DevNumOfPlanes: 1
        *DevBPP: 24
        *DrvBPP: 24
    }
}

*%******************************************************************************
*%                         Cursor Commands
*%******************************************************************************
*Command: CmdCR { *Cmd : "<0D>" }
*Command: CmdLF { *Cmd : "<0A>" }
*Command: CmdFF { *Cmd : "<0C>" }


