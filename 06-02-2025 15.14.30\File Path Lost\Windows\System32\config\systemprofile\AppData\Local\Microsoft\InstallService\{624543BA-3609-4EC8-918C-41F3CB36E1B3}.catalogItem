{"catalogItem": {"entryId": "{624543BA-3609-4EC8-918C-41F3CB36E1B3}", "userSID": "S-1-5-21-246032611-354955300-348842393-1002", "userIdentity": "", "productId": "9PG2DK419DRG", "packageFamilyName": "Microsoft.WebpImageExtension_8wekyb3d8bbwe", "mainPackageFamilyName": "", "contentId": "", "categoryId": "00c2490a-ef1d-43c9-bdba-b84ecc77d2ea", "fulfillmentId": "", "publisherCertificateName": "CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US", "packageIdentityName": "Microsoft.WebpImageExtension", "platformDependencyXmlBlob": "{\"blob.version\":1688867040526336,\"content.bundledPackages\":[\"Microsoft.WebpImageExtension_1.1.1711.0_x64__8wekyb3d8bbwe\",\"Microsoft.WebpImageExtension_1.1.1711.0_x86__8wekyb3d8bbwe\",\"Microsoft.WebpImageExtension_1.1.1711.0_arm64__8wekyb3d8bbwe\"],\"content.isMain\":false,\"content.packageId\":\"Microsoft.WebpImageExtension_1.1.1711.0_neutral_~_8wekyb3d8bbwe\",\"content.productId\":\"db5753a1-f0d0-415e-ad5e-9bf05b98c105\",\"content.targetPlatforms\":[{\"platform.maxVersionTested\":2814751249596416,\"platform.minVersion\":2814750931222528,\"platform.target\":0}],\"content.type\":7,\"policy\":{\"category.first\":\"app\",\"category.second\":\"Utilities & tools\",\"optOut.backupRestore\":false,\"optOut.removeableMedia\":false},\"policy2\":{\"ageRating\":1,\"optOut.DVR\":false,\"thirdPartyAppRatings\":[{\"level\":7,\"systemId\":3},{\"level\":12,\"systemId\":5},{\"level\":48,\"systemId\":12},{\"level\":27,\"systemId\":9},{\"level\":76,\"systemId\":16},{\"level\":68,\"systemId\":15},{\"level\":54,\"systemId\":13}]}}", "packageFullName": "Microsoft.WebpImageExtension_1.1.1711.0_neutral_~_8wekyb3d8bbwe", "images": [{"ImagePurpose": "Logo", "Uri": "//store-images.s-microsoft.com/image/apps.56008.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.1a94d024-31bc-4fae-856d-f2393965b3cb", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 50, "Height": 50}, {"ImagePurpose": "Logo", "Uri": "//store-images.s-microsoft.com/image/apps.39957.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.a2811993-efe5-4f5e-bd4c-f39b13b62bf2", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 75, "Height": 75}, {"ImagePurpose": "Logo", "Uri": "//store-images.s-microsoft.com/image/apps.23697.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.ee93605a-2f49-42f5-9bd7-bb0f8fbd4749", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 100, "Height": 100}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.45904.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.05b34a7c-1031-4266-8d6c-8042230ce560", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 150, "Height": 150}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.57350.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.a41ca13f-cb09-42d8-8167-c7986d8b0bb0", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 225, "Height": 225}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.34199.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.655e73b9-6887-4439-8380-56e1b8d4140a", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 300, "Height": 300}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.52598.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.5ba81b2e-719c-421c-8324-4dd325869ac3", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 44, "Height": 44}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.47585.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.da96f994-d905-4c32-b1d6-59782c69a514", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 66, "Height": 66}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.29953.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.c2be1055-77a8-48a1-88d7-e0ec399b0ede", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 88, "Height": 88}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.59153.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.807a072d-fb9e-4069-a240-6b9d166a730c", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 310, "Height": 310}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.11542.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.6e8cd4b8-4785-4b98-80f9-448dbce40eb3", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 465, "Height": 465}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.59369.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.0b72b875-85d2-4128-86cc-e47f3a60bde1", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 620, "Height": 620}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.46146.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.acf17dbd-9b47-4d1d-8112-61c7126ac715", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 71, "Height": 71}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.29989.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.b32b1bff-471e-4f5d-8998-e494c9a5eb1a", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 107, "Height": 107}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.59551.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.0b7f7178-7159-4988-bb43-fd631f4742e8", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 142, "Height": 142}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.22190.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.4f41587d-2154-4c3b-9df9-53b6e536f73f", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 310, "Height": 150}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.31937.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.f660566d-6148-499f-b035-9cd9e1ce19f2", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 465, "Height": 225}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.31213.14189431855827103.c494221c-c639-4dba-b873-c8176038e480.0a130d72-9e43-401a-9e9d-96fbec6edaa8", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 620, "Height": 300}, {"ImagePurpose": "Screenshot", "Uri": "//store-images.s-microsoft.com/image/apps.17543.14189431855827103.1754bf27-6ca7-4a78-804c-5e8ec04e855a.668c041c-4435-4615-ab59-5433917e5c30", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 2480, "Height": 1200}], "bundledProducts": [], "packageFormat": "AppxBundle", "productTitle": "WebP Image Extension", "fulfillmentPluginId": "WU", "fulfillmentData": "{\"ProductId\":\"9PG2DK419DRG\",\"WuBundleId\":\"ac0e3a10-b219-4a31-9e1d-9e8873a5a31d\",\"WuCategoryId\":\"00c2490a-ef1d-43c9-bdba-b84ecc77d2ea\",\"PackageFamilyName\":\"Microsoft.WebpImageExtension_8wekyb3d8bbwe\",\"SkuId\":\"0010\",\"Content\":null,\"PackageFeatures\":null}", "packageRelativeAppIds": ["App"], "canFulfill": true, "hrInstallAllowed": 0, "created": "2025-04-09T14:30:54-05:00", "requiresElevation": false, "market": "", "isVisibleInAppList": false, "isExclusivityFailed": false, "isCompanionApp": false, "isPlugin": false, "skuId": "0010", "availabilityId": "B3H6CZ32PX5N", "catalogId": "", "workId": ""}}