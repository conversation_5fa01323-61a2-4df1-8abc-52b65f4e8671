<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for ServicingStack 10.0.19041.5676" displayName="Servicing Stack 10.0.19041.5676" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="" creationTimeStamp="2025-03-12T05:32:03Z" lastUpdateTimeStamp="2025-03-12T05:32:03Z">
  <assemblyIdentity name="Wrapper-DF3875977D2566FFF9623DF3765BFB670EFA43FB0C834FEBB8D0EDC4CE401943" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5054682" releaseType="Update" restart="possible" selfUpdate="true" permanence="permanent" psfName="SSU-19041.5676-x64.psf">
    <parent buildCompare="EQ" integrate="standalone" disposition="detect">
      <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="*******" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="Shared-A5058E46B38E45E0D030D007C2E232810195F5FA6842BC1D6A2F7D39597887C1_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Shared-A5058E46B38E45E0D030D007C2E232810195F5FA6842BC1D6A2F7D39597887C1" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
  </package>
</assembly>
