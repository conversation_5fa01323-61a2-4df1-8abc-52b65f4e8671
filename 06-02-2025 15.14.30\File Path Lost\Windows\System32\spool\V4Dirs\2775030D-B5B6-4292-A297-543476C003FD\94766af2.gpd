*%
*% Copyright (c) Microsoft Corporation
*%
*% All rights reserved.
*%

*GPDFileVersion: "1.0"
*GPDSpecVersion: "1.0"
*GPDFileName:    "SendToOneNote.gpd"
*%%% Copyright (c) 1997-1999  Microsoft Corporation
*%%% value macros for standard feature names and standard option names
*%%% used in older Unidrv's.

*CodePage: 1252      *% Windows 3.1 US (ANSI) code page

*Feature: RESDLL
{
    *Name: "resource dll files"
    *ConcealFromUI?: TRUE

    *Option: UniresDLL
    {
        *Name: "unires.dll"
    }
}

*Macros: StdFeatureNames
{
    ORIENTATION_DISPLAY:                RESDLL.UniresDLL.11100
    PAPER_SIZE_DISPLAY:                 RESDLL.UniresDLL.11101
    PAPER_SOURCE_DISPLAY:               RESDLL.UniresDLL.11102
    RESOLUTION_DISPLAY:                 RESDLL.UniresDLL.11103
    MEDIA_TYPE_DISPLAY:                 RESDLL.UniresDLL.11104
    TEXT_QUALITY_DISPLAY:               RESDLL.UniresDLL.11105
    COLOR_PRINTING_MODE_DISPLAY:        RESDLL.UniresDLL.11106
    PRINTER_MEMORY_DISPLAY:             RESDLL.UniresDLL.11107
    TWO_SIDED_PRINTING_DISPLAY:         RESDLL.UniresDLL.11108
    PAGE_PROTECTION_DISPLAY:            RESDLL.UniresDLL.11109
    HALFTONING_DISPLAY:                 RESDLL.UniresDLL.11110
    OUTPUTBIN_DISPLAY:                  RESDLL.UniresDLL.11111
    IMAGECONTROL_DISPLAY:               RESDLL.UniresDLL.11112
    PRINTDENSITY_DISPLAY:               RESDLL.UniresDLL.11113
    GRAPHICSMODE_DISPLAY:               RESDLL.UniresDLL.11114
    TEXTHALFTONE_DISPLAY:               RESDLL.UniresDLL.11115
    GRAPHICSHALFTONE_DISPLAY:           RESDLL.UniresDLL.11116
    PHOTOHALFTONE_DISPLAY:              RESDLL.UniresDLL.11117
}

*Macros: StdPaperSizeNames
{
    RCID_DMPAPER_SYSTEM_NAME:               0x7fffffff
    LETTER_DISPLAY:                         RESDLL.UniresDLL.10000
    LETTERSMALL_DISPLAY:                    RESDLL.UniresDLL.10001
    TABLOID_DISPLAY:                        RESDLL.UniresDLL.10002
    LEDGER_DISPLAY:                         RESDLL.UniresDLL.10003
    LEGAL_DISPLAY:                          RESDLL.UniresDLL.10004
    STATEMENT_DISPLAY:                      RESDLL.UniresDLL.10005
    EXECUTIVE_DISPLAY:                      RESDLL.UniresDLL.10006
    A3_DISPLAY:                             RESDLL.UniresDLL.10007
    A4_DISPLAY:                             RESDLL.UniresDLL.10008
    A4SMALL_DISPLAY:                        RESDLL.UniresDLL.10009
    A5_DISPLAY:                             RESDLL.UniresDLL.10010
    B4_DISPLAY:                             RESDLL.UniresDLL.10011
    B5_DISPLAY:                             RESDLL.UniresDLL.10012
    FOLIO_DISPLAY:                          RESDLL.UniresDLL.10013
    QUARTO:                                 RESDLL.UniresDLL.10014
    10X14_DISPLAY:                          RESDLL.UniresDLL.10015
    11X17_DISPLAY:                          RESDLL.UniresDLL.10016
    NOTE_DISPLAY:                           RESDLL.UniresDLL.10017
    ENV_9_DISPLAY:                          RESDLL.UniresDLL.10018
    ENV_10_DISPLAY:                         RESDLL.UniresDLL.10019
    ENV_11_DISPLAY:                             RESDLL.UniresDLL.10020
    ENV_12_DISPLAY:                             RESDLL.UniresDLL.10021
    ENV_14_DISPLAY:                             RESDLL.UniresDLL.10022
    CSHEET_DISPLAY:                             RESDLL.UniresDLL.10023
    DSHEET_DISPLAY:                             RESDLL.UniresDLL.10024
    ESHEET_DISPLAY:                             RESDLL.UniresDLL.10025
    ENV_DL_DISPLAY:                             RESDLL.UniresDLL.10026
    ENV_C5_DISPLAY:                             RESDLL.UniresDLL.10027
    ENV_C3_DISPLAY:                             RESDLL.UniresDLL.10028
    ENV_C4_DISPLAY:                             RESDLL.UniresDLL.10029
    ENV_C6_DISPLAY:                             RESDLL.UniresDLL.10030
    ENV_C65_DISPLAY:                            RESDLL.UniresDLL.10031
    ENV_B4_DISPLAY:                             RESDLL.UniresDLL.10032
    ENV_B5_DISPLAY:                             RESDLL.UniresDLL.10033
    ENV_B6_DISPLAY:                             RESDLL.UniresDLL.10034
    ENV_ITALY_DISPLAY:                          RESDLL.UniresDLL.10035
    ENV_MONARCH_DISPLAY:                        RESDLL.UniresDLL.10036
    ENV_PERSONAL_DISPLAY:                       RESDLL.UniresDLL.10037
    FANFOLD_US_DISPLAY:                         RESDLL.UniresDLL.10038
    FANFOLD_STD_GERMAN_DISPLAY:                 RESDLL.UniresDLL.10039
    FANFOLD_LGL_GERMAN_DISPLAY:                 RESDLL.UniresDLL.10040
    ISO_B4_DISPLAY:                             RESDLL.UniresDLL.10041
    JAPANESE_POSTCARD_DISPLAY:                  RESDLL.UniresDLL.10042
    9X11_DISPLAY:                               RESDLL.UniresDLL.10043
    10X11_DISPLAY:                              RESDLL.UniresDLL.10044
    15X11_DISPLAY:                              RESDLL.UniresDLL.10045
    ENV_INVITE_DISPLAY:                         RESDLL.UniresDLL.10046
    RESERVED1:                                  RESDLL.UniresDLL.10047
    RESERVED2:                                  RESDLL.UniresDLL.10048
    LETTER_EXTRA_DISPLAY:                       RESDLL.UniresDLL.10049
    LEGAL_EXTRA_DISPLAY:                        RESDLL.UniresDLL.10050
    TABLOID_EXTRA_DISPLAY:                      RESDLL.UniresDLL.10051
    A4_EXTRA_DISPLAY:                           RESDLL.UniresDLL.10052
    LETTER_TRANSVERSE_DISPLAY:                  RESDLL.UniresDLL.10053
    A4_TRANSVERSE_DISPLAY:                      RESDLL.UniresDLL.10054
    LETTER_EXTRA_TRANSVERSE_DISPLAY:            RESDLL.UniresDLL.10055
    A_PLUS_DISPLAY:                             RESDLL.UniresDLL.10056
    B_PLUS_DISPLAY:                             RESDLL.UniresDLL.10057
    LETTER_PLUS_DISPLAY:                        RESDLL.UniresDLL.10058
    A4_PLUS_DISPLAY:                            RESDLL.UniresDLL.10059
    A5_TRANSVERSE_DISPLAY:                      RESDLL.UniresDLL.10060
    B5_TRANSVERSE_DISPLAY:                      RESDLL.UniresDLL.10061
    A3_EXTRA_DISPLAY:                           RESDLL.UniresDLL.10062
    A5_EXTRA_DISPLAY:                           RESDLL.UniresDLL.10063
    B5_EXTRA_DISPLAY:                           RESDLL.UniresDLL.10064
    A2_DISPLAY:                                 RESDLL.UniresDLL.10065
    A3_TRANSVERSE_DISPLAY:                      RESDLL.UniresDLL.10066
    A3_EXTRA_TRANSVERSE_DISPLAY:                RESDLL.UniresDLL.10067
    DBL_JAPANESE_POSTCARD_DISPLAY:              RESDLL.UniresDLL.10068
    A6_DISPLAY:                                 RESDLL.UniresDLL.10069
    JENV_KAKU2_DISPLAY:                         RESDLL.UniresDLL.10070
    JENV_KAKU3_DISPLAY:                         RESDLL.UniresDLL.10071
    JENV_CHOU3_DISPLAY:                         RESDLL.UniresDLL.10072
    JENV_CHOU4_DISPLAY:                         RESDLL.UniresDLL.10073
    LETTER_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10074
    A3_ROTATED_DISPLAY:                         RESDLL.UniresDLL.10075
    A4_ROTATED_DISPLAY:                         RESDLL.UniresDLL.10076
    A5_ROTATED_DISPLAY:                         RESDLL.UniresDLL.10077
    B4_JIS_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10078
    B5_JIS_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10079
    JAPANESE_POSTCARD_ROTATED_DISPLAY:          RESDLL.UniresDLL.10080
    DBL_JAPANESE_POSTCARD_ROTATED_DISPLAY:      RESDLL.UniresDLL.10081
    A6_ROTATED_DISPLAY:                         RESDLL.UniresDLL.10082
    JENV_KAKU2_ROTATED_DISPLAY:                 RESDLL.UniresDLL.10083
    JENV_KAKU3_ROTATED_DISPLAY:                 RESDLL.UniresDLL.10084
    JENV_CHOU3_ROTATED_DISPLAY:                 RESDLL.UniresDLL.10085
    JENV_CHOU4_ROTATED_DISPLAY:                 RESDLL.UniresDLL.10086
    B6_JIS_DISPLAY:                             RESDLL.UniresDLL.10087
    B6_JIS_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10088
    12X11_DISPLAY:                              RESDLL.UniresDLL.10089
    JENV_YOU4_DISPLAY:                          RESDLL.UniresDLL.10090
    JENV_YOU4_ROTATED_DISPLAY:                  RESDLL.UniresDLL.10091
    P16K_DISPLAY:                               RESDLL.UniresDLL.10092
    P32K_DISPLAY:                               RESDLL.UniresDLL.10093
    P32KBIG_DISPLAY:                            RESDLL.UniresDLL.10094
    PENV_1_DISPLAY:                             RESDLL.UniresDLL.10095
    PENV_2_DISPLAY:                             RESDLL.UniresDLL.10096
    PENV_3_DISPLAY:                             RESDLL.UniresDLL.10097
    PENV_4_DISPLAY:                             RESDLL.UniresDLL.10098
    PENV_5_DISPLAY:                             RESDLL.UniresDLL.10099
    PENV_6_DISPLAY:                             RESDLL.UniresDLL.10100
    PENV_7_DISPLAY:                             RESDLL.UniresDLL.10101
    PENV_8_DISPLAY:                             RESDLL.UniresDLL.10102
    PENV_9_DISPLAY:                             RESDLL.UniresDLL.10103
    PENV_10_DISPLAY:                            RESDLL.UniresDLL.10104
    P16K_ROTATED_DISPLAY:                       RESDLL.UniresDLL.10105
    P32K_ROTATED_DISPLAY:                       RESDLL.UniresDLL.10106
    P32KBIG_ROTATED_DISPLAY:                    RESDLL.UniresDLL.10107
    PENV_1_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10108
    PENV_2_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10109
    PENV_3_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10110
    PENV_4_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10111
    PENV_5_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10112
    PENV_6_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10113
    PENV_7_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10114
    PENV_8_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10115
    PENV_9_ROTATED_DISPLAY:                     RESDLL.UniresDLL.10116
    PENV_10_ROTATED_DISPLAY:                    RESDLL.UniresDLL.10117

    USER_DEFINED_SIZE_DISPLAY:                  RESDLL.UniresDLL.10255
}

*Macros: StdInputBinNames
{
    UPPER_TRAY_DISPLAY:                         RESDLL.UniresDLL.10256
    LOWER_TRAY_DISPLAY:                         RESDLL.UniresDLL.10257
    MIDDLE_TRAY_DISPLAY:                        RESDLL.UniresDLL.10258
    MANUAL_FEED_DISPLAY:                        RESDLL.UniresDLL.10259
    ENV_FEED_DISPLAY:                           RESDLL.UniresDLL.10260
    ENV_MANUAL_DISPLAY:                         RESDLL.UniresDLL.10261
    AUTO_DISPLAY:                               RESDLL.UniresDLL.10262
    TRACTOR_DISPLAY:                            RESDLL.UniresDLL.10263
    SMALL_FORMAT_DISPLAY:                       RESDLL.UniresDLL.10264
    LARGE_FORMAT_DISPLAY:                       RESDLL.UniresDLL.10265
    LARGE_CAP_DISPLAY:                          RESDLL.UniresDLL.10266
    CASSETTE_DISPLAY:                           RESDLL.UniresDLL.10267
}

*Macros: StdMediaTypeNames
{
    PLAIN_PAPER_DISPLAY:                        RESDLL.UniresDLL.10512
    TRANSPARENCY_DISPLAY:                       RESDLL.UniresDLL.10513
    GLOSSY_PAPER_DISPLAY:                       RESDLL.UniresDLL.10514
}

*Macros: StdTextQualityNames
{
    LETTER_QUALITY_DISPLAY:                     RESDLL.UniresDLL.10768
    NEAR_LETTER_QUALITY_DISPLAY:                RESDLL.UniresDLL.10769
    MEMO_QUALITY_DISPLAY:                       RESDLL.UniresDLL.10770
    DRAFT_QUALITY_DISPLAY:                      RESDLL.UniresDLL.10771
    TEXT_QUALITY_DISPLAY:                       RESDLL.UniresDLL.10772
}

*Macros: OtherStdNames
{
    PORTRAIT_DISPLAY:                   RESDLL.UniresDLL.11025
    LANDSCAPE_DISPLAY:                  RESDLL.UniresDLL.11026
    MONO_DISPLAY:                       RESDLL.UniresDLL.11030
    COLOR_DISPLAY:                      RESDLL.UniresDLL.11031
    8BPP_DISPLAY:                       RESDLL.UniresDLL.11032
    24BPP_DISPLAY:                      RESDLL.UniresDLL.11033
    NONE_DISPLAY:                       RESDLL.UniresDLL.11040
    FLIP_ON_LONG_EDGE_DISPLAY:          RESDLL.UniresDLL.11041
    FLIP_ON_SHORT_EDGE_DISPLAY:         RESDLL.UniresDLL.11042
    ON_DISPLAY:                         RESDLL.UniresDLL.11090
    OFF_DISPLAY:                        RESDLL.UniresDLL.11091
    DOTS_PER_INCH:                      "dots per inch"
    HT_AUTO_SELECT_DISPLAY:             RESDLL.UniresDLL.11050
    HT_SUPERCELL_DISPLAY:               RESDLL.UniresDLL.11051
    HT_DITHER6X6_DISPLAY:               RESDLL.UniresDLL.11052
    HT_DITHER8X8_DISPLAY:               RESDLL.UniresDLL.11053
}

*Macros:  StdPersonalities
{
    PERSONALITY_LIPS_DISPLAY:       RESDLL.UniresDLL.11500
    PERSONALITY_ESCP2_DISPLAY:      RESDLL.UniresDLL.11501
    PERSONALITY_PPDS_DISPLAY:       RESDLL.UniresDLL.11502
    PERSONALITY_CaPSL_DISPLAY:      RESDLL.UniresDLL.11503
    PERSONALITY_KPDL_DISPLAY:       RESDLL.UniresDLL.11504
    PERSONALITY_TextOnly_DISPLAY:   RESDLL.UniresDLL.11505
    PERSONALITY_201PL_DISPLAY:      RESDLL.UniresDLL.11506
    PERSONALITY_ART_DISPLAY:        RESDLL.UniresDLL.11507
    PERSONALITY_ESCPage_DISPLAY:    RESDLL.UniresDLL.11508
    PERSONALITY_ESCP_DISPLAY:       RESDLL.UniresDLL.11509
    PERSONALITY_KS_DISPLAY:         RESDLL.UniresDLL.11510
    PERSONALITY_KSSM_DISPLAY:       RESDLL.UniresDLL.11511
    PERSONALITY_PAGES_DISPLAY:      RESDLL.UniresDLL.11512
    PERSONALITY_PCL_DISPLAY:        RESDLL.UniresDLL.11513
    PERSONALITY_RPDL_DISPLAY:       RESDLL.UniresDLL.11514
    PERSONALITY_Unknown_DISPLAY:    RESDLL.UniresDLL.11515
    PERSONALITY_HPGL2_DISPLAY:      RESDLL.UniresDLL.11516
    PERSONALITY_PCLXL_DISPLAY:      RESDLL.UniresDLL.11517

    PERSONALITY_HPGL2:     "HPGL2"
    PERSONALITY_PCLXL:     "PCLXL"
}

*Macros: GraphicModes
{
    GRAPHICSMODE_RASTER_DISPLAY:     RESDLL.UniresDLL.11601
    GRAPHICSMODE_HPGL2_DISPLAY:      =PERSONALITY_HPGL2_DISPLAY
    GRAPHICSMODE_PCLXL_DISPLAY:      =PERSONALITY_PCLXL_DISPLAY
}

*Macros: HalftoneSettings
{
    DETAIL_HT_DISPLAY:              RESDLL.UniresDLL.11401
    SMOOTH_HT_DISPLAY:              RESDLL.UniresDLL.11402
    BASIC_HT_DISPLAY:               RESDLL.UniresDLL.11403
}

*%
*% Copyright (c) Microsoft Corporation
*%
*% All rights reserved.

*CodePage: 1252      *% Windows 3.1 US (ANSI) code page

*Feature: RESDLL
{
    *Name: "resource dll files"
    *ConcealFromUI?: TRUE

    *Option: UniresDLL
    {
        *Name: "unires.dll"
    }
}


*ModelName:      "Send To OneNote Driver"
*MasterUnits:    PAIR(1200, 1200)
*ResourceDLL:    "unires.dll"
*PrinterType:    PAGE

*%%% Copyright (c) 2005  Microsoft Corporation

*IsXPSDriver?: TRUE



*%******************************************************************************
*%                            PageMediaSize - Paper Size
*%******************************************************************************
*Feature: PaperSize
{
    *rcNameID: =PAPER_SIZE_DISPLAY
    *DefaultOption: LETTER

    *Option: A3
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 9667
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(13460, 19440)
                *PrintableOrigin: PAIR(284, 200)
                *CursorOrigin: PAIR(284, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(13628, 19368)
                *PrintableOrigin: PAIR(200, 236)
                *CursorOrigin: PAIR(200, 19604)
            }
        }
    }

    *Option: A4
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4249
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(9352, 13628)
                *PrintableOrigin: PAIR(284, 200)
                *CursorOrigin: PAIR(284, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(9520, 13556)
                *PrintableOrigin: PAIR(200, 236)
                *CursorOrigin: PAIR(200, 13792)
            }
        }
    }

    *Option: B4
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 6391
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(11572, 16796)
                *PrintableOrigin: PAIR(284, 200)
                *CursorOrigin: PAIR(284, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(11740, 16724)
                *PrintableOrigin: PAIR(200, 236)
                *CursorOrigin: PAIR(200, 16960)
            }
        }
    }

    *Option: B5
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 3198
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(7900, 11140)
                *PrintableOrigin: PAIR(352, 300)
                *CursorOrigin: PAIR(300, 100)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(7760, 11140)
                *PrintableOrigin: PAIR(300, 400)
                *CursorOrigin: PAIR(100, 11940)
            }
        }
    }

    *Option: EXECUTIVE
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(8100, 11500)
                *PrintableOrigin: PAIR(300, 300)
                *CursorOrigin: PAIR(300, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(8300, 11500)
                *PrintableOrigin: PAIR(200, 300)
                *CursorOrigin: PAIR(200, 12300)
            }
        }
    }

    *Option: ENV_10
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(4348, 11000)
                *PrintableOrigin: PAIR(300, 200)
                *CursorOrigin: PAIR(300, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(4548, 10920)
                *PrintableOrigin: PAIR(200, 240)
                *CursorOrigin: PAIR(200, 11160)
            }
        }
    }

    *Option: LEGAL
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 1692
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(9000, 15500)
                *PrintableOrigin: PAIR(400, 600)
                *CursorOrigin: PAIR(180, 300)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(9000, 15500)
                *PrintableOrigin: PAIR(400, 900)
                *CursorOrigin: PAIR(180, 16500)
            }
        }
    }

    *Option: LETTER
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 1028
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(9500, 12500)
                *PrintableOrigin: PAIR(400, 400)
                *CursorOrigin: PAIR(300, 300)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(9500, 12200)
                *PrintableOrigin: PAIR(450, 300)
                *CursorOrigin: PAIR(200, 12900)
            }
        }
    }

    *Option: ENV_MONARCH
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(4048, 8600)
                *PrintableOrigin: PAIR(300, 200)
                *CursorOrigin: PAIR(300, 200)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(4248, 8520)
                *PrintableOrigin: PAIR(200, 240)
                *CursorOrigin: PAIR(200, 8760)
            }
        }
    }

    *Option: TABLOID
    {
        *rcNameID: =RCID_DMPAPER_SYSTEM_NAME
        *PageProtectMem: 4109
        *switch: Orientation
        {
            *case: PORTRAIT
            {
                *PrintableArea: PAIR(12000, 19200)
                *PrintableOrigin: PAIR(400, 400)
                *CursorOrigin: PAIR(400, 400)
            }
            *case: LANDSCAPE_CC270
            {
                *PrintableArea: PAIR(12000, 19200)
                *PrintableOrigin: PAIR(400, 600)
                *CursorOrigin: PAIR(400, 18000)
            }
        }
    }
}

*%******************************************************************************
*%                             PageOrientation
*%******************************************************************************
*Feature: Orientation
{
    *rcNameID: =ORIENTATION_DISPLAY
    *DefaultOption: PORTRAIT

    *Option: PORTRAIT
    {
        *rcNameID: =PORTRAIT_DISPLAY
    }

    *Option: LANDSCAPE_CC270
    {
        *rcNameID: =LANDSCAPE_DISPLAY
    }
}

*%******************************************************************************
*%                              Resolution
*%******************************************************************************
*Feature: Resolution
{
    *rcNameID: =RESOLUTION_DISPLAY
    *DefaultOption: DPI600

    *Option: DPI600
    {
        *Name: "600 x 600 " =DOTS_PER_INCH
        *DPI: PAIR(600, 600)
        *TextDPI: PAIR(600, 600)
        *SpotDiameter: 100
        *Command: CmdBeginRaster { *Cmd : "<1B>*v7S<1B>*r1A" }
        *Command: CmdEndRaster { *Cmd : "<1B>*rC" }
        *Command: CmdSendBlockData { *Cmd : "<1B>*b" %d{NumOfDataBytes}"W" }
    }

    *Option: DPI1200
    {
        *Name: "1200 x 1200 " =DOTS_PER_INCH
        *DPI: PAIR(1200, 1200)
        *TextDPI: PAIR(1200, 1200)
        *SpotDiameter: 100
        *Command: CmdBeginRaster { *Cmd : "<1B>*v7S<1B>*r1A" }
        *Command: CmdEndRaster { *Cmd : "<1B>*rC" }
        *Command: CmdSendBlockData { *Cmd : "<1B>*b" %d{NumOfDataBytes}"W" }
    }
}

*%******************************************************************************
*%                            Printer Memory
*% WARNING: removing this makes the print driver not install
*%******************************************************************************
*Feature: Memory
{
    *rcNameID: =PRINTER_MEMORY_DISPLAY
    *DefaultOption: 32768KB
    *Option: 16384KB
    {
        *Name: "16MB"
        *MemoryConfigKB: PAIR(16384, 13950)
    }
    *Option: 32768KB
    {
        *Name: "32MB"
        *MemoryConfigKB: PAIR(32768, 28350)
    }
}

*%******************************************************************************
*%                            Color Mode
*% Needed so we advertise ourselves as a color printer (DEVMODE.dmColor)
*%******************************************************************************
*Feature: ColorMode
{
    *rcNameID: =COLOR_PRINTING_MODE_DISPLAY
    *DefaultOption: 24bpp
    *ConcealFromUI?: TRUE

    *Option: 24bpp
    {
        *rcNameID: =24BPP_DISPLAY
        *DevNumOfPlanes: 1
        *DevBPP: 24
        *DrvBPP: 24
    }
}

*%******************************************************************************
*%                         Cursor Commands
*%******************************************************************************
*Command: CmdCR { *Cmd : "<0D>" }
*Command: CmdLF { *Cmd : "<0A>" }
*Command: CmdFF { *Cmd : "<0C>" }



