<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>SpeechDiagnostic</ID>
    <Version>2.1</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters>
        <Parameter>
          <Name>DeviceID</Name>
          <DefaultValue/>
        </Parameter>
      </Parameters>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>MF_SpeechDiagnostic.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint>
    </ExtensionPoint>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_MicProblem</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-3</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers/>
      <Verifier/>
      <ContextParameters>
        <Parameter>
          <Name>SkipAudioRecordingDiagnostic</Name>
          <DefaultValue/>
        </Parameter>
        <Parameter>
          <Name>SelectedDevice</Name>
          <DefaultValue>None</DefaultValue>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_SpeakerProblem</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-40</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers/>
      <Verifier/>
      <ContextParameters>
        <Parameter>
          <Name>SkipAudioPlaybackDiagnostic</Name>
          <DefaultValue/>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_CalibrationRequired</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>AdapterName</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-4</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_CalibrateMicrophone</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-5</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>CalibrationReason</Name>
                <DefaultValue>Default</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>CalibrationConsent</Name>
                <DefaultValue>false</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>AdapterName</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>MicLevel</Name>
                <DefaultValue>-1</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>Description</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>DeviceId</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>JackInfo</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>State</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>IsDefault</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_CalibrationRequired.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint>
          </ExtensionPoint>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>AdapterName</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_CalibrationRequired.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-6</Name>
              <Description/>
            </DisplayInformation>
            <Value>calibrate</Value>
            <ExtensionPoint/>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-7</Name>
              <Description/>
            </DisplayInformation>
            <Value>skip</Value>
            <ExtensionPoint/>
          </Choice>
        </Choices>
        <ID>IT_MicSetup</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-8</Name>
          <Description>@diagpackage.dll,-9</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>warning</InteractionIcon>
          <RTFDescription>@diagpackage.dll,-10</RTFDescription>
          <CommandLinks/>
          <NoCache/>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>true</AllowDynamicResponses>
        <Choices/>
        <ID>IT_SelectDevice</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-11</Name>
          <Description/>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-12</Name>
              <Description/>
            </DisplayInformation>
            <Value>set</Value>
            <ExtensionPoint/>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-13</Name>
              <Description/>
            </DisplayInformation>
            <Value>skip</Value>
            <ExtensionPoint/>
          </Choice>
        </Choices>
        <ID>IT_ConfirmSetDefault</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-14</Name>
          <Description>@diagpackage.dll,-15</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>warning</InteractionIcon>
          <CommandLinks/>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-16</Name>
              <Description/>
            </DisplayInformation>
            <Value>calibrate</Value>
            <ExtensionPoint/>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-17</Name>
              <Description/>
            </DisplayInformation>
            <Value>skip</Value>
            <ExtensionPoint/>
          </Choice>
        </Choices>
        <ID>IT_TroublePage</ID>
        <DisplayInformation>
          <Parameters>
            <Parameter>
              <Name>Description</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <Name>@diagpackage.dll,-18</Name>
          <Description>@diagpackage.dll,-19</Description>
        </DisplayInformation>
        <ContextParameters>
          <Parameter>
            <Name>Description</Name>
            <DefaultValue>Setting up your microphone might help improve speech accuracy if you've moved your PC or microphone recently. Would you like to set up the mic?</DefaultValue>
          </Parameter>
        </ContextParameters>
        <ExtensionPoint>
          <InteractionIcon>warning</InteractionIcon>
          <RTFDescription>@diagpackage.dll,-20</RTFDescription>
          <CommandLinks/>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-21</Name>
              <Description/>
            </DisplayInformation>
            <Value>yes</Value>
            <ExtensionPoint/>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-22</Name>
              <Description/>
            </DisplayInformation>
            <Value>no</Value>
            <ExtensionPoint/>
          </Choice>
        </Choices>
        <ID>IT_RetryCalibration</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-23</Name>
          <Description>@diagpackage.dll,-24</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <!--<InteractionIcon>warning</InteractionIcon>-->
          <CommandLinks/>
          <NoCache/>
        </ExtensionPoint>
      </SingleResponseInteraction>
      <SingleResponseInteraction>
        <AllowDynamicResponses>false</AllowDynamicResponses>
        <Choices>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-50</Name>
              <Description>@diagpackage.dll,-51</Description>
            </DisplayInformation>
            <Value>capture</Value>
            <ExtensionPoint/>
          </Choice>
          <Choice>
            <DisplayInformation>
              <Parameters/>
              <Name>@diagpackage.dll,-52</Name>
              <Description>@diagpackage.dll,-53</Description>
            </DisplayInformation>
            <Value>render</Value>
            <ExtensionPoint/>
          </Choice>
        </Choices>
        <ID>IT_SelectCaptureOrRender</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-60</Name>
          <Description>@diagpackage.dll,-61</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <CommandLinks/>
          <NoCache/>
        </ExtensionPoint>
      </SingleResponseInteraction>
    </SingleResponseInteractions>
    <MultipleResponseInteractions/>
    <TextInteractions>
      <TextInteraction>
        <RegularExpression></RegularExpression>
        <ID>IT_EntryPoint</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-25</Name>
          <Description/>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <NoUI/>
        </ExtensionPoint>
      </TextInteraction>
    </TextInteractions>
    <PauseInteractions>
      <PauseInteraction>
        <ID>IT_RunningOnRemoteSession</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-26</Name>
          <Description>@diagpackage.dll,-27</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>error</InteractionIcon>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>IT_SkippedSetDefault</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-28</Name>
          <Description>@diagpackage.dll,-29</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>error</InteractionIcon>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>IT_CalibrationNotification</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-30</Name>
          <Description>@diagpackage.dll,-31</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <InteractionIcon>info</InteractionIcon>
        </ExtensionPoint>
      </PauseInteraction>
    </PauseInteractions>
    <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-100</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-101</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-102</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-103</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-105</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-106</HelpKeywords>
    <Feedback>
      <ContextId>327</ContextId>
    </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>