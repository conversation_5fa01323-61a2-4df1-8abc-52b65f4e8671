{ " b u c k e t " : 5 9 , " m o s t _ r e c e n t _ r e g i o n " : " U S " , " o v e r r i d e s " : [ { " A E R I A L _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / a { q u a d k e y } . j p e g ? n = z & g = { g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 5 2 3 4 } " , " A E R I A L _ W I T H _ R O A D S _ A N D _ L A B E L S _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = A , G , L { d p i } & s h a d i n g = h i l l & m k t = { l a n g u a g e } & u r = { r e g i o n } & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " A E R I A L _ W I T H _ R O A D S _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = A , G , L { d p i } & s h a d i n g = h i l l & m k t = { l a n g u a g e } & u r = { r e g i o n } & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " C U S T O M _ A D V E R T I S E D _ G E N E R A T I O N _ D E P E N D E N T _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " { c u s t o m } \ " ,   \ " v e r s i o n \ " :   1 5 2 5 4 } " , " C U S T O M _ O N D E M A N D _ G E N E R A T I O N _ D E P E N D E N T _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " { c u s t o m } \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " D E M _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / d { q u a d k e y } ? d i u = h r d t m & g = { g e n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 8 1 6 } " , " D E T A I L _ L A Y E R _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / d r e { q u a d k e y } ? g = { g e n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 0 9 0 } " , " G E N E R I C _ C O P Y R I G H T _ U R L _ K E Y " : " h t t p s : / / d e v . v i r t u a l e a r t h . n e t / R E S T / v 1 / I m a g e r y / C o p y r i g h t / { c u l t u r e } / 2 4 3 ? k e y = { k e y } & i n c l u d e M i c r o s o f t C o p y r i g h t = 1 " , " I M A G E R Y _ C O P Y R I G H T _ U R L _ K E Y " : " h t t p s : / / d e v . v i r t u a l e a r t h . n e t / R E S T / v 1 / I m a g e r y / C o p y r i g h t / { l a n g u a g e } / { i m a g e r y S e t } / { z o o m } / { m i n L a t } / { m i n L o n } / { m a x L a t } / { m a x L o n } ? o u t p u t = x m l & d i r = { h e a d i n g } & k e y = { c r e d e n t i a l s } " , " I M A G E R Y _ S E T _ C O P Y R I G H T _ U R L _ K E Y " : " h t t p s : / / d e v . v i r t u a l e a r t h . n e t / R E S T / v 1 / I m a g e r y / C o p y r i g h t / { c u l t u r e } / { i m a g e r y S e t } / { z o o m l e v e l } / { m i n - L a t } / { m i n - L o n } / { m a x - L a t } / { m a x - L o n } ? k e y = { k e y } & m s c o p y = 1 " , " L A B E L _ O V E R R I D E S _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / m s 2 ? m s t = m s t l o & m s v = m s v 8 0 & g = { g e n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 5 2 5 4 } " , " L O C A T I O N _ S E R V I C E _ U R L _ K E Y " : " h t t p s : / / d e v . v i r t u a l e a r t h . n e t / R E S T / v 1 / L o c a t i o n s { e n d } " , " O D V S _ B A S E M A P E X T 1 _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 3 ,   \ " i n d e x \ " :   \ " O D V S _ B A S E M A P E X T 1 _ I N D E X _ E N D P O I N T _ K E Y \ " ,   \ " o n t o l o g y \ " : \ " O N T O L O G Y _ B A S E M A P _ E N D P O I N T _ K E Y \ " ,   \ " u r l \ "   :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { c h u n k i d } , { c l i e n t v e r s i o n } \ " } " , " O D V S _ B A S E M A P E X T 1 _ I N D E X _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 3 ,   \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d i ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { p a r t } \ " ,   \ " v e r s i o n \ " :   1 6 6 3 } " , " O D V S _ B A S E M A P _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 0 ,   \ " i n d e x \ " :   \ " O D V S _ B A S E M A P _ I N D E X _ E N D P O I N T _ K E Y \ " ,   \ " o n t o l o g y \ " :   \ " O N T O L O G Y _ B A S E M A P _ E N D P O I N T _ K E Y \ " ,   \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { c h u n k i d } , { c l i e n t v e r s i o n } \ " } " , " O D V S _ B A S E M A P _ I N D E X _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 0 ,   \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d i ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { p a r t } \ " ,   \ " v e r s i o n \ " :   3 0 0 9 } " , " O D V S _ D E M _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 2 ,   \ " i n d e x \ " :   \ " O D V S _ D E M _ I N D E X _ E N D P O I N T _ K E Y \ " ,   \ " u r l \ "   :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { c h u n k i d } , { c l i e n t v e r s i o n } \ " } " , " O D V S _ D E M _ I N D E X _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 2 ,   \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d i ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { p a r t } \ " ,   \ " v e r s i o n \ " :   2 3 5 9 } " , " O D V S _ L A N D M A R K _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 1 ,   \ " i n d e x \ " :   \ " O D V S _ L A N D M A R K _ I N D E X _ E N D P O I N T _ K E Y \ " ,   \ " o n t o l o g y \ " : \ " O N T O L O G Y _ L A N D M A R K _ E N D P O I N T _ K E Y \ " ,   \ " u r l \ "   :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { c h u n k i d } , { c l i e n t v e r s i o n } \ " } " , " O D V S _ L A N D M A R K _ I N D E X _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   1 1 ,   \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d i ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { p a r t } \ " ,   \ " v e r s i o n \ " :   2 8 8 8 } " , " O D V S _ O R G A N I Z A T I O N _ I M A G E S _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   2 ,   \ " i n d e x \ " :   \ " O D V S _ O R G A N I Z A T I O N _ I M A G E S _ I N D E X _ E N D P O I N T _ K E Y \ " ,   \ " u r l \ "   :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { c h u n k i d } , { c l i e n t v e r s i o n } \ " } " , " O D V S _ O R G A N I Z A T I O N _ I M A G E S _ I N D E X _ E N D P O I N T _ K E Y " : " { \ " d a t a T y p e \ " :   2 ,   \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g d i ? p v = 1 & r = { d a t a t y p e } , { v e r s i o n } , { p a r t } \ " ,   \ " v e r s i o n \ " :   5 3 8 } " , " O D V S _ R E G I O N _ C A T A L O G _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / o d v s / g r i ? p v = 1 & r = { v e r s i o n } \ " ,   \ " v e r s i o n \ " :   3 0 8 0 } " , " O D V S _ R E S O U R C E _ D A T A _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / o d v s { q u a d k e y } ? o d s = o d s _ r e s & o d t = o d t _ i & g = { v e r s i o n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   5 8 0 6 } " , " O D V S _ R V F S C H E M A _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / o d v s { q u a d k e y } ? o d s = o d s _ s c h e m a & g = { v e r s i o n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 2 3 6 8 } " , " O N T O L O G Y _ B A S E M A P _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / r v f o 2 ? d s = d s _ b a s e m a p _ h e a d m o b & g = { v e r s i o n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 2 6 7 3 } " , " O N T O L O G Y _ L A N D M A R K _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / r v f o 2 ? d s = d s _ l a n d m a r k _ h e a d m o b & g = { v e r s i o n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   5 0 6 2 } " , " P R O V I D E R _ L I S T _ C O P Y R I G H T _ U R L _ K E Y " : " h t t p s : / / d e v . v i r t u a l e a r t h . n e t / R E S T / v 1 / I m a g e r y / C o p y r i g h t / { c u l t u r e } / 2 4 3 ? k e y = { k e y } & i n c l u d e M i c r o s o f t C o p y r i g h t = 1 " , " R A S T E R _ T E R R A I N _ H I G H _ C O N T R A S T _ L I G H T _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g & c s t l = h c l & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R A S T E R _ T E R R A I N _ H I G H _ C O N T R A S T _ L I G H T _ W I T H _ L A B E L S _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g , l { d p i } & m k t = { l a n g u a g e } & u r = { r e g i o n } & c s t l = h c l & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R A S T E R _ T E R R A I N _ H I G H _ C O N T R A S T _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g & c s t l = h c & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R A S T E R _ T E R R A I N _ H I G H _ C O N T R A S T _ W I T H _ L A B E L S _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g , l { d p i } & m k t = { l a n g u a g e } & u r = { r e g i o n } & c s t l = h c & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R A S T E R _ T E R R A I N _ S Y M B O L I C _ D A R K _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g & c s t l = s n & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R A S T E R _ T E R R A I N _ S Y M B O L I C _ D A R K _ W I T H _ L A B E L S _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g , l { d p i } & m k t = { l a n g u a g e } & u r = { r e g i o n } & c s t l = s n & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R A S T E R _ T E R R A I N _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g { d p i } & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R A S T E R _ T E R R A I N _ W I T H _ L A B E L S _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = g , l { d p i } & m k t = { l a n g u a g e } & u r = { r e g i o n } & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " R O U T I N G _ S E R V I C E _ U R L _ K E Y " : " h t t p s : / / d e v . v i r t u a l e a r t h . n e t / R E S T / v 1 / R o u t e s / { r o u t e R e q u e s t O p t i o n s } & c = { l a n g u a g e } & U R = { r e g i o n } & k e y = { c r e d e n t i a l s } " , " R S F S T Y L E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / m s 2 ? m s t = m s t r s f & m s v = m s v 8 0 & g = { v e r s i o n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 5 1 7 5 } " , " S T R E E T S I D E _ C O V E R A G E M A P _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = Z , H C & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " S T R E E T S I D E _ P A N O R A M A T I L E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / h s { b u b b l e i d } { d a t a e l e m e n t i n d e x } { t i l e i n d e x } . j x r ? g = { g e n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 5 1 7 8 } " , " S T R E E T S I D E _ T H U M B N A I L _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / h s { q u a d k e y } { s u b d o m a i n } . j x r ? g = { g e n } & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 5 1 7 8 } " , " S U R F A C E _ M O D E L _ C O V E R A G E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / c m { q u a d k e y } ? g = { g e n } & n = z & c m t = c m p r 3 d & t f = 3 d v 3 \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 2 1 9 } " , " S U R F A C E _ M O D E L _ C O V E R A G E _ L O D _ R A N G E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / c m 2 ? g = { g e n } & n = f & c m t = c m p r 3 d & t f = 3 d v 3 \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 2 1 9 } " , " S U R F A C E _ M O D E L _ C O V E R A G E _ O N T O L O G Y _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / c m 3 ? g = { g e n } & n = f & c m t = c m p r 3 d & t f = 3 d v 3 \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 2 1 9 } " , " T E R R A I N _ M O D E L _ C O V E R A G E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / c m { q u a d k e y } ? g = { g e n } & n = z & c m t = h r c m d t m r \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 8 1 6 } " , " T E R R A I N _ M O D E L _ C O V E R A G E _ L O D _ R A N G E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / c m 2 ? g = { g e n } & n = f & c m t = h r c m d t m r \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 8 1 6 } " , " T E R R A I N _ M O D E L _ C O V E R A G E _ O N T O L O G Y _ E N D P O I N T _ K E Y " : " { \ " u r l \ " :   \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / c m 3 ? g = { g e n } & n = f & c m t = h r c m d t m r \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 8 1 6 } " , " T I N _ M E S H _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t { s u b d o m a i n } . s s l . a k . t i l e s . v i r t u a l e a r t h . n e t / t i l e s / m t x { q u a d k e y } ? g = { g e n } & t f = 3 d v 3 & n = z \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   1 4 2 1 9 } " , " V E N U E M A P S _ T E X T U R E _ E N D P O I N T _ K E Y " : " { \ " u r l \ " : \ " h t t p s : / / t 0 . s s l . a k . d y n a m i c . t i l e s . v i r t u a l e a r t h . n e t / c o m p / c h / { q u a d k e y } ? i t = z , v f & m k t = { l a n g u a g e } & v i d = { v e n u e i d } & v l v = { f l o o r t y p e } & n = z & o g = { o d g e n } \ " ,   \ " h e a d e r \ " :   \ " I P S _ R E Q U E S T _ H E A D E R _ K E Y \ " ,   \ " v e r s i o n \ " :   2 6 6 5 } " , " l a n g u a g e _ k e y " : " e n - u s " , " r e g i o n _ k e y " : " U S " , " t i m e s t a m p _ k e y " : " 2 0 2 5 / 0 5 / 2 8   0 5 : 1 2 : 1 4 " } ] } 
 