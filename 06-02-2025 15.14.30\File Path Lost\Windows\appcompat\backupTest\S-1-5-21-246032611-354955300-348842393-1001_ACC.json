##tT<## 20250118_145217
{"Install" : [{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - es-mx",
"programId": "000029b84ebffd5e41f06188519dfd47f60f0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - en-us",
"programId": "000055093bdd3f687d76cbb0ee3e5c1b7bd50000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : [{"productName": "microsoft office",
"originalFileName": "msaccess.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msaccess.exe"
},{"aumid": "Microsoft.Office.MSACCESS.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\access.lnk"
},{"productName": "microsoft office",
"originalFileName": "appvlp.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18227.0",
"binFileVersion": "16.0.18227.20002",
"path": "%programfiles%\\microsoft office\\root\\client\\appvlp.exe"
},{"aumid": "Microsoft.Office.DATABASECOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\database compare.lnk"
},{"productName": "microsoft office",
"originalFileName": "excel.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\excel.exe"
},{"aumid": "Microsoft.Office.EXCEL.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\excel.lnk"
},{"productName": "microsoft office",
"originalFileName": "setlang.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\setlang.exe"
},{"aumid": "Microsoft.Office.SETLANG.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\office language preferences.lnk"
},{"productName": "microsoft onenote",
"originalFileName": "onenote.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\onenote.exe"
},{"aumid": "Microsoft.Office.ONENOTE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onenote.lnk"
},{"productName": "microsoft outlook",
"originalFileName": "outlook.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\outlook.exe"
},{"aumid": "Microsoft.Office.OUTLOOK.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\outlook (classic).lnk"
},{"productName": "microsoft office",
"originalFileName": "powerpnt.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\powerpnt.exe"
},{"aumid": "Microsoft.Office.POWERPNT.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\powerpoint.lnk"
},{"productName": "microsoft office",
"originalFileName": "mspub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\mspub.exe"
},{"aumid": "Microsoft.Office.MSPUB.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\publisher.lnk"
},{"productName": "microsoft office",
"originalFileName": "ocpubmgr.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\ocpubmgr.exe"
},{"aumid": "Microsoft.Office.OcPubMgr.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\skype for business recording manager.lnk"
},{"productName": "microsoft office",
"originalFileName": "lync.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\lync.exe"
},{"aumid": "Microsoft.Office.lync.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\skype for business.lnk"
},{"aumid": "Microsoft.Office.SPREADSHEETCOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\spreadsheet compare.lnk"
},{"aumid": "Microsoft.Office.OneNote.MemoryPreview",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\sticky notes (new).lnk"
},{"productName": "microsoft office",
"originalFileName": "msoev.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msoev.exe"
},{"aumid": "Microsoft.Office.msoev.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\telemetry log for office.lnk"
},{"productName": "microsoft office",
"originalFileName": "winword.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\winword.exe"
},{"aumid": "Microsoft.Office.WINWORD.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\word.lnk"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\VLC media player",
"programId": "0000863d5786bd660050e532b4fa5b66170e0000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files\\VideoLAN\\VLC",
"files" : [{"path": "%programfiles%\\videolan\\vlc\\documentation.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\documentation.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\documentation.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\news.txt"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\news.txt",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\release notes.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\videolan website.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\videolan website.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\videolan website.lnk"
},{"productName": "vlc media player",
"originalFileName": "vlc.exe",
"companyName": "videolan",
"binProductVersion": "3.0.18.0",
"binFileVersion": "3.0.18.0",
"path": "%programfiles%\\videolan\\vlc\\vlc.exe"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{51325390-AE6A-68FC-A315-0950CC83A166}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player - reset preferences and cache files.lnk"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%public%\\desktop\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{30BD9A02-CB9A-93FD-A859-09C8803F2346}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player skinned.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\uninstall.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - fr-ca",
"programId": "000091d9c85e9191ed4199cc21e4e403fcff0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft Edge",
"programId": "000092b6db162d4cfc1f8e8b492d66dbb3760000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files (x86)\\Microsoft\\Edge\\Application",
"files" : [{"productName": "microsoft edge",
"originalFileName": "cookie_exporter.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\cookie_exporter.exe"
},{"productName": "microsoft edge",
"originalFileName": "elevation_service.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\elevation_service.exe"
},{"productName": "microsoft edge",
"originalFileName": "identity_helper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\identity_helper.exe"
},{"productName": "ietoedge bho",
"originalFileName": "ie_to_edge_stub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\bho\\ie_to_edge_stub.exe"
},{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\119.0.2151.44\\msedge.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1FC1A6C2-576E-489A-9B4A-92D21F542136}",
"programId": "00009e26d04962c79c9b1834c0c8068bafa600000000",
"compatFlags": "0",
"files" : [{"productName": "microsoft® windows® operating system",
"originalFileName": "expediteupdater",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\expediteupdater.exe"
},{"productName": "microsoft® windows® operating system",
"originalFileName": "uhssvc",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\uhssvc.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{731F6BAA-A986-45A4-8936-7C3AAAAA760B}",
"programId": "0000a8571596f0d284d93f5c20946e72f4f600000904",
"compatFlags": "4",
"restoreAction": "RootDirPath|C:\\Program Files\\Microsoft Office\\root\\integration\\Addons\\",
"files" : [{"productName": "microsoft teams",
"originalFileName": "setup.exe",
"companyName": "microsoft corporation",
"binProductVersion": "1.5.0.30767",
"binFileVersion": "1.5.0.30767",
"path": "%programfiles(x86)%\\teams installer\\teams.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\OneDriveSetup.exe",
"programId": "0000bf803468de7ad95869e0f9e481c147100000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|c:\\program files\\microsoft onedrive\\24.244.1204.0003",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "filecoauth.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filecoauth.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesyncconfig.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesyncconfig.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesynchelper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesynchelper.exe"
},{"productName": "microsoft sharepoint",
"originalFileName": "microsoft.sharepoint.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.exe"
},{"productName": "microsoft sharepoint native messaging client",
"originalFileName": "microsoft.sharepoint.nativemessaging.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.nativemessagingclient.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft EdgeWebView",
"programId": "0000e96975da1b54f6379ab67db1bf4e2b560000ffff",
"compatFlags": "4",
"restoreAction": "RootDirPath|C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application",
"files" : [{"productName": "microsoft edge",
"originalFileName": "cookie_exporter.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\cookie_exporter.exe"
},{"productName": "microsoft edge",
"originalFileName": "elevation_service.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\elevation_service.exe"
},{"productName": "microsoft edge",
"originalFileName": "identity_helper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\identity_helper.exe"
},{"productName": "ietoedge bho",
"originalFileName": "ie_to_edge_stub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\bho\\ie_to_edge_stub.exe"
},{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edgewebview\\application\\119.0.2151.44\\msedge.exe"
}]
},{"path": "c:\\program files (x86)\\microsoft\\edge\\application\\msedge.exe",
"programId": "0000eb85c5a8a2001da5f2bdeb7a030b31970000ffff",
"compatFlags": "0",
"files" : [{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "119.0.2151.44",
"binFileVersion": "119.0.2151.44",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\msedge.exe"
},{"aumid": "MSEdge",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft edge.lnk"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F132AF7F-7BCA-4EDE-8A7C-958108FE7DBC}",
"programId": "0000f3ba61ac2186f429d7e48ceb429a7bb60000ffff",
"compatFlags": "16384",
"restoreAction": "RootDirPath|C:\\Program Files\\Realtek\\Audio\\HDA",
"files" : [{"productName": "hd audio background process",
"originalFileName": "rthdvbgproc.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.225",
"binFileVersion": "1.0.0.225",
"path": "%programfiles%\\realtek\\audio\\hda\\ravbg64.exe"
},{"productName": "hd audio control panel",
"originalFileName": "rtdcpl.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "1.0.0.13",
"binFileVersion": "1.0.0.13",
"path": "%programfiles%\\realtek\\audio\\hda\\rtdcpl64.exe"
},{"productName": "realtek audio service",
"originalFileName": "rtkaudioservice.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.68",
"binFileVersion": "1.0.0.68",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkaudioservice64.exe"
},{"productName": "realtek hd audio manager",
"originalFileName": "rtkngui.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.502.0",
"binFileVersion": "1.0.502.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkngui64.exe"
},{"productName": "realtek hd auido update and remove driver tool",
"originalFileName": "rtlupd.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "3.0.0.0",
"binFileVersion": "3.0.0.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtlupd64.exe"
}]
},{"path": "c:\\program files\\microsoft onedrive\\onedrive.exe",
"programId": "0006bf803468de7ad95869e0f9e481c1471000000904",
"compatFlags": "0",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "onedrive.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\onedrive.exe"
},{"aumid": "Microsoft.SkyDrive.Desktop",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onedrive.lnk"
}]
}],
"Update" : [],
"Uninstall" : [{"programId": "@ÀÃ­ßystemRoot]}
##tT>##
##tT<## 20250409_115508
{"Install" : [{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - es-mx",
"programId": "000029b84ebffd5e41f06188519dfd47f60f0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - en-us",
"programId": "000055093bdd3f687d76cbb0ee3e5c1b7bd50000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : [{"productName": "microsoft office",
"originalFileName": "msaccess.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msaccess.exe"
},{"aumid": "Microsoft.Office.MSACCESS.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\access.lnk"
},{"productName": "microsoft office",
"originalFileName": "appvlp.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18227.0",
"binFileVersion": "16.0.18227.20002",
"path": "%programfiles%\\microsoft office\\root\\client\\appvlp.exe"
},{"aumid": "Microsoft.Office.DATABASECOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\database compare.lnk"
},{"productName": "microsoft office",
"originalFileName": "excel.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\excel.exe"
},{"aumid": "Microsoft.Office.EXCEL.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\excel.lnk"
},{"productName": "microsoft office",
"originalFileName": "setlang.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\setlang.exe"
},{"aumid": "Microsoft.Office.SETLANG.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\office language preferences.lnk"
},{"productName": "microsoft onenote",
"originalFileName": "onenote.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\onenote.exe"
},{"aumid": "Microsoft.Office.ONENOTE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onenote.lnk"
},{"productName": "microsoft outlook",
"originalFileName": "outlook.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\outlook.exe"
},{"aumid": "Microsoft.Office.OUTLOOK.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\outlook (classic).lnk"
},{"productName": "microsoft office",
"originalFileName": "powerpnt.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\powerpnt.exe"
},{"aumid": "Microsoft.Office.POWERPNT.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\powerpoint.lnk"
},{"productName": "microsoft office",
"originalFileName": "mspub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\mspub.exe"
},{"aumid": "Microsoft.Office.MSPUB.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\publisher.lnk"
},{"productName": "microsoft office",
"originalFileName": "ocpubmgr.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\ocpubmgr.exe"
},{"aumid": "Microsoft.Office.OcPubMgr.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\skype for business recording manager.lnk"
},{"productName": "microsoft office",
"originalFileName": "lync.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\lync.exe"
},{"aumid": "Microsoft.Office.lync.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\skype for business.lnk"
},{"aumid": "Microsoft.Office.SPREADSHEETCOMPARE.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\spreadsheet compare.lnk"
},{"aumid": "Microsoft.Office.OneNote.MemoryPreview",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\sticky notes (new).lnk"
},{"productName": "microsoft office",
"originalFileName": "msoev.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\msoev.exe"
},{"aumid": "Microsoft.Office.msoev.exe.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft office tools\\telemetry log for office.lnk"
},{"productName": "microsoft office",
"originalFileName": "winword.exe",
"companyName": "microsoft corporation",
"binProductVersion": "16.0.18324.20194",
"binFileVersion": "16.0.18324.20194",
"path": "%programfiles%\\microsoft office\\root\\office16\\winword.exe"
},{"aumid": "Microsoft.Office.WINWORD.EXE.15",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\word.lnk"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\VLC media player",
"programId": "0000863d5786bd660050e532b4fa5b66170e0000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files\\VideoLAN\\VLC",
"files" : [{"path": "%programfiles%\\videolan\\vlc\\documentation.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\documentation.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\documentation.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\news.txt"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\news.txt",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\release notes.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\videolan website.url"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\videolan\\vlc\\videolan website.url",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\videolan website.lnk"
},{"productName": "vlc media player",
"originalFileName": "vlc.exe",
"companyName": "videolan",
"binProductVersion": "3.0.18.0",
"binFileVersion": "3.0.18.0",
"path": "%programfiles%\\videolan\\vlc\\vlc.exe"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{51325390-AE6A-68FC-A315-0950CC83A166}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player - reset preferences and cache files.lnk"
},{"aumid": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe",
"path": "%public%\\desktop\\vlc media player.lnk"
},{"aumid": "Microsoft.AutoGenerated.{30BD9A02-CB9A-93FD-A859-09C8803F2346}",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\videolan\\vlc media player skinned.lnk"
},{"path": "%programfiles%\\videolan\\vlc\\uninstall.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\O365ProPlusRetail - fr-ca",
"programId": "000091d9c85e9191ed4199cc21e4e403fcff0000ffff",
"compatFlags": "0",
"restoreAction": "AddPlaceholderCustom|FWLINK~2235010;RootDirPath|C:\\Program Files\\Microsoft Office",
"files" : []
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1FC1A6C2-576E-489A-9B4A-92D21F542136}",
"programId": "00009e26d04962c79c9b1834c0c8068bafa600000000",
"compatFlags": "0",
"files" : [{"productName": "microsoft® windows® operating system",
"originalFileName": "expediteupdater",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\expediteupdater.exe"
},{"productName": "microsoft® windows® operating system",
"originalFileName": "uhssvc",
"companyName": "microsoft corporation",
"binProductVersion": "10.0.19041.3626",
"binFileVersion": "10.0.19041.3626",
"path": "%programfiles%\\microsoft update health tools\\uhssvc.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{731F6BAA-A986-45A4-8936-7C3AAAAA760B}",
"programId": "0000a8571596f0d284d93f5c20946e72f4f600000904",
"compatFlags": "4",
"restoreAction": "RootDirPath|C:\\Program Files\\Microsoft Office\\root\\integration\\Addons\\",
"files" : [{"productName": "microsoft teams",
"originalFileName": "setup.exe",
"companyName": "microsoft corporation",
"binProductVersion": "1.5.0.30767",
"binFileVersion": "1.5.0.30767",
"path": "%programfiles(x86)%\\teams installer\\teams.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\OneDriveSetup.exe",
"programId": "0000bf803468de7ad95869e0f9e481c147100000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|c:\\program files\\microsoft onedrive\\24.244.1204.0003",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "filecoauth.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filecoauth.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesyncconfig.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesyncconfig.exe"
},{"productName": "microsoft onedrive",
"originalFileName": "filesynchelper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\filesynchelper.exe"
},{"productName": "microsoft sharepoint",
"originalFileName": "microsoft.sharepoint.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.exe"
},{"productName": "microsoft sharepoint native messaging client",
"originalFileName": "microsoft.sharepoint.nativemessaging.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\24.244.1204.0003\\microsoft.sharepoint.nativemessagingclient.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft Edge",
"programId": "0000c4d0f33b9aab940ec62ff604c4863eb10000ffff",
"compatFlags": "0",
"restoreAction": "RootDirPath|C:\\Program Files (x86)\\Microsoft\\Edge\\Application",
"files" : [{"productName": "microsoft edge",
"originalFileName": "cookie_exporter.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\cookie_exporter.exe"
},{"productName": "microsoft edge",
"originalFileName": "elevation_service.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\elevation_service.exe"
},{"productName": "microsoft edge",
"originalFileName": "identity_helper.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\identity_helper.exe"
},{"productName": "ietoedge bho",
"originalFileName": "ie_to_edge_stub.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\132.0.2957.115\\bho\\ie_to_edge_stub.exe"
},{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\msedge.exe"
}]
},{"path": "HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F132AF7F-7BCA-4EDE-8A7C-958108FE7DBC}",
"programId": "0000f3ba61ac2186f429d7e48ceb429a7bb60000ffff",
"compatFlags": "16384",
"restoreAction": "RootDirPath|C:\\Program Files\\Realtek\\Audio\\HDA",
"files" : [{"productName": "hd audio background process",
"originalFileName": "rthdvbgproc.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.225",
"binFileVersion": "1.0.0.225",
"path": "%programfiles%\\realtek\\audio\\hda\\ravbg64.exe"
},{"productName": "hd audio control panel",
"originalFileName": "rtdcpl.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "1.0.0.13",
"binFileVersion": "1.0.0.13",
"path": "%programfiles%\\realtek\\audio\\hda\\rtdcpl64.exe"
},{"productName": "realtek audio service",
"originalFileName": "rtkaudioservice.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.0.68",
"binFileVersion": "1.0.0.68",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkaudioservice64.exe"
},{"productName": "realtek hd audio manager",
"originalFileName": "rtkngui.exe",
"companyName": "realtek semiconductor",
"binProductVersion": "1.0.502.0",
"binFileVersion": "1.0.502.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtkngui64.exe"
},{"productName": "realtek hd auido update and remove driver tool",
"originalFileName": "rtlupd.exe",
"companyName": "realtek semiconductor corp.",
"binProductVersion": "3.0.0.0",
"binFileVersion": "3.0.0.0",
"path": "%programfiles%\\realtek\\audio\\hda\\rtlupd64.exe"
}]
},{"path": "c:\\program files\\microsoft onedrive\\onedrive.exe",
"programId": "0006bf803468de7ad95869e0f9e481c1471000000904",
"compatFlags": "0",
"files" : [{"productName": "microsoft onedrive",
"originalFileName": "onedrive.exe",
"companyName": "microsoft corporation",
"binProductVersion": "24.244.1204.3",
"binFileVersion": "24.244.1204.3",
"path": "%programfiles%\\microsoft onedrive\\onedrive.exe"
},{"aumid": "Microsoft.SkyDrive.Desktop",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\onedrive.lnk"
}]
}],
"Update" : [{"path": "c:\\program files (x86)\\microsoft\\edge\\application\\msedge.exe",
"programId": "000092b6db162d4cfc1f8e8b492d66dbb3760000ffff",
"compatFlags": "0",
"files" : [{"productName": "microsoft edge",
"originalFileName": "msedge.exe",
"companyName": "microsoft corporation",
"binProductVersion": "132.0.2957.115",
"binFileVersion": "132.0.2957.115",
"path": "%programfiles(x86)%\\microsoft\\edge\\application\\msedge.exe"
},{"aumid": "MSEdge",
"path": "%programdata%\\microsoft\\windows\\start menu\\programs\\microsoft edge.lnk"
}]
}],
"Uninstall" : [{"programId": "0000e96975da1b54f6379ab67db1bf4e2b560000ffff",
"files" : [{"aumid": "MSEdge"
}]
},{"programId": "0000eb85c5a8a2001da5f2bdeb7a030b31970000ffff",
"files" : [{"aumid": "MSEdge"
}]
},{"programId": "@ÀF`ÈystemRoot]}
##tT>##
