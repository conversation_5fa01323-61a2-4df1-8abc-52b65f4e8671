4265093 (10428) - exe\logging.cpp:0841: --- START 2025\4\9 14:58:27 PM ---
4265093 (10428) - exe\main.cpp:4044: WinSAT regisrty key missing - create the winsat key
4265093 (10428) - exe\main.cpp:4363: WinSAT registry node is created or present
4265093 (10428) - exe\main.cpp:4394: Command Line = "C:\Windows\system32\winsat.exe" disk -wsswap
4265093 (10428) - exe\main.cpp:4016: INFO: The axe results path environment variable is not set. Assuming we aren't running under AXE.
4265093 (10428) - exe\main.cpp:4500: INFO: Winsat is not running in AXE mode.
4265093 (10428) - exe\main.cpp:4529: Skipping writing the Initial values to the registry - Axe Mode
4265093 (10428) - exe\main.cpp:2531: > DWM not running.
4265093 (10428) - exe\main.cpp:2496: > EMD service will be restored on exit.
4265109 (10428) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

4265109 (10428) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=FALSE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
4265109 (10428) - exe\main.cpp:3826: > Power 'execution' request successfully set.
4265109 (10428) - exe\main.cpp:1764: > Run Assessment disk -wsswap -drive C:
4265109 (10428) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
4265109 (10428) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
4265109 (10428) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
4265109 (10428) - storage\diskprof.cpp:0698: Random seed is 1744228707
4265671 (10428) - exe\main.cpp:3865: > Power request 'execution' successfully cleared.
4265687 (10428) - exe\processresults.cpp:4767: > Wrote wsswap throughput, from the profiler, to the registry 526336
4265687 (10428) - exe\processresults.cpp:4777: > Wrote wsswap interference, from the profiler, to the registry 65536
4265687 (10428) - exe\processresults.cpp:4854: Skipping Registry Entry for random read disk score
4265687 (10428) - exe\processwinsaterror.cpp:0340: Skipping writing the exit code, cant msg and why msg to registry
4265687 (10428) - exe\main.cpp:2764: > Successfully reenabled EMD.
4265687 (10428) - exe\main.cpp:5040: > exit value = 0.

4266906 (5248) - exe\logging.cpp:0841: --- START 2025\4\9 14:58:29 PM ---
4266906 (5248) - exe\main.cpp:4363: WinSAT registry node is created or present
4266921 (5248) - exe\main.cpp:4394: Command Line = C:\Windows\system32\winsat.exe formal -log -cancelevent 5d8d160c-0a4e-40e9-969d-10939cf2691b
4266921 (5248) - exe\main.cpp:4016: INFO: The axe results path environment variable is not set. Assuming we aren't running under AXE.
4266921 (5248) - exe\main.cpp:4500: INFO: Winsat is not running in AXE mode.
4266921 (5248) - exe\processwinsaterror.cpp:0146: ERROR: tried to read resource strings, unknown exception occured
4266921 (5248) - exe\main.cpp:4593: > IsFormal=TRUE  IsMoobe=FALSE.
4266921 (5248) - exe\watchdog.cpp:0113: WatchDogThreadProc Launched with priority 0
4266968 (5248) - exe\main.cpp:4692: Watch dog system enabled
4266968 (5248) - exe\main.cpp:4707: Main watch dog timer set to 600.0 seconds
4266968 (5248) - common\winsatutilities.cpp:1116: PNPID from DX9 call = PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4266984 (5248) - common\winsatutilities.cpp:1182: Index [0]: PNPID = PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4266984 (5248) - common\winsatutilities.cpp:1206: Matching device PnPID is PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4267000 (5248) - common\winsatutilities.cpp:1182: Index [1]: PNPID = ROOT\BasicDisplay
4267000 (5248) - exe\datastore.cpp:1027: No files found in the datastore location C:\Windows\Performance\WinSAT\DataStore
4267000 (5248) - exe\datastore.cpp:1142: DEBUG::We could not find a match for Assessment DATASTORE_DWM
4267000 (5248) - exe\datastore.cpp:1027: No files found in the datastore location C:\Windows\Performance\WinSAT\DataStore
4267000 (5248) - exe\datastore.cpp:1142: DEBUG::We could not find a match for Assessment DATASTORE_GRAPHICS_3D
4267000 (5248) - exe\datastore.cpp:1027: No files found in the datastore location C:\Windows\Performance\WinSAT\DataStore
4267000 (5248) - exe\datastore.cpp:1142: DEBUG::We could not find a match for Assessment DATASTORE_CPU
4267000 (5248) - exe\datastore.cpp:1027: No files found in the datastore location C:\Windows\Performance\WinSAT\DataStore
4267000 (5248) - exe\datastore.cpp:1142: DEBUG::We could not find a match for Assessment DATASTORE_MEM
4267000 (5248) - exe\datastore.cpp:1027: No files found in the datastore location C:\Windows\Performance\WinSAT\DataStore
4267000 (5248) - exe\datastore.cpp:1142: DEBUG::We could not find a match for Assessment DATASTORE_DISK
4267015 (5248) - exe\main.cpp:2516: > DWM Running.
4267015 (5248) - exe\main.cpp:2523: > turning off DWM.
4267015 (5248) - exe\main.cpp:2496: > EMD service will be restored on exit.
4267609 (5248) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

4267609 (5248) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=TRUE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
4267609 (5248) - exe\main.cpp:3826: > Power 'execution' request successfully set.
4267609 (5248) - exe\main.cpp:1764: > Run Assessment features 
4267609 (5248) - common\winsatutilities.cpp:1116: PNPID from DX9 call = PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4267609 (5248) - common\winsatutilities.cpp:1182: Index [0]: PNPID = PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4267609 (5248) - common\winsatutilities.cpp:1206: Matching device PnPID is PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4267609 (5248) - common\winsatutilities.cpp:1182: Index [1]: PNPID = ROOT\BasicDisplay
4267609 (5248) - exe\main.cpp:1764: > Run Assessment dwm -aname DWM -time 10 -fbc 10 -disp off -normalw 1 -alphaw 2 -width 1280 -height 1024 -winwidth C(1144) -winheight C(915) -rendertotex 6 -rtdelta 3 -nolock
4267609 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278312 (5248) - exe\processresults.cpp:4125: > Wrote video memory bandwidth to the registry 8282000
4278312 (5248) - exe\main.cpp:2039: > DWM Assessment results processing SUCCESS
4278312 (5248) - exe\main.cpp:1764: > Run Assessment d3d -aname Batch -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 300 -batchcnt C(10) -objs C(26) -rendertotex 6 -rtdelta 3 -texpobj C(1)
4278312 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278312 (5248) - d3d\graphicsop.cpp:0294: D3D Assessment was called but intentionally not run. Instead, we provide predefined metrics and scores.
4278375 (5248) - exe\main.cpp:1764: > Run Assessment d3d -aname Alpha -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 300 -batchcnt C(75) -objs C(26) -rendertotex 6 -rtdelta 3 -texpobj C(1)
4278375 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278375 (5248) - d3d\graphicsop.cpp:0294: D3D Assessment was called but intentionally not run. Instead, we provide predefined metrics and scores.
4278453 (5248) - exe\main.cpp:1764: > Run Assessment d3d -aname Tex -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 500 -batchcnt C(125) -objs C(20) -noalpha -texshader -totaltex 10 -texpobj C(4) -rendertotex 6 -rtdelta 3
4278453 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278453 (5248) - d3d\graphicsop.cpp:0294: D3D Assessment was called but intentionally not run. Instead, we provide predefined metrics and scores.
4278531 (5248) - exe\main.cpp:1764: > Run Assessment d3d -aname ALU -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 500 -batchcnt C(125) -objs C(20) -noalpha -alushader -totaltex 10 -texpobj C(1) -rendertotex 6 -rtdelta 3
4278531 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278531 (5248) - d3d\graphicsop.cpp:0294: D3D Assessment was called but intentionally not run. Instead, we provide predefined metrics and scores.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment d3d -dx10  -aname Batch -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 300 -batchcnt C(10) -objs C(26) -rendertotex 6 -rtdelta 3 -texpobj C(1)
4278593 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278593 (5248) - d3d\graphicsop.cpp:0215: Driver is not WDDM 1.1 or WDDM 1.2; aborting DX10 assessment.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment d3d -dx10  -aname Alpha -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 300 -batchcnt C(75) -objs C(26) -rendertotex 6 -rtdelta 3 -texpobj C(1)
4278593 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278593 (5248) - d3d\graphicsop.cpp:0215: Driver is not WDDM 1.1 or WDDM 1.2; aborting DX10 assessment.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment d3d -dx10  -aname Tex -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 500 -batchcnt C(125) -objs C(20) -noalpha -texshader -totaltex 10 -texpobj C(4) -rendertotex 6 -rtdelta 3
4278593 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278593 (5248) - d3d\graphicsop.cpp:0215: Driver is not WDDM 1.1 or WDDM 1.2; aborting DX10 assessment.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment d3d -dx10  -aname ALU -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 500 -batchcnt C(125) -objs C(20) -noalpha -alushader -totaltex 10 -texpobj C(1) -rendertotex 6 -rtdelta 3
4278593 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278593 (5248) - d3d\graphicsop.cpp:0215: Driver is not WDDM 1.1 or WDDM 1.2; aborting DX10 assessment.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment d3d -dx10  -aname GeomF4 -time 7 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 150;200;241 -batchcnt C(50);C(200);C(300) -objs C(12);C(26);C(45) -noalpha -geomf4shader -texpobj C(0) -rendertotex 6 -rtdelta 3 -tierframes 60 -tiertime 1
4278593 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278593 (5248) - d3d\graphicsop.cpp:0215: Driver is not WDDM 1.1 or WDDM 1.2; aborting DX10 assessment.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment d3d -dx10  -aname GeomV8 -time 7 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 75;100;120 -batchcnt C(25);C(100);C(150) -objs C(8);C(17);C(29) -noalpha -geomv8shader -texpobj C(0) -rendertotex 6 -rtdelta 3 -tierframes 60 -tiertime 1
4278593 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278593 (5248) - d3d\graphicsop.cpp:0215: Driver is not WDDM 1.1 or WDDM 1.2; aborting DX10 assessment.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment d3d -dx10  -aname CBuffer -time 5 -fbc 10 -disp off -animate 10 -width 1280 -height 1024 -totalobj 75 -batchcnt C(25) -objs C(8) -rendertotex 6 -rtdelta 3 -texpobj C(1) -cbuffershader -cbufa 2 -cbuff 5 -cbufp 6
4278593 (5248) - d3d\graphicsop.cpp:0193: GraphicsOp::AssessmentThread Launched with priority 0
4278593 (5248) - d3d\graphicsop.cpp:0215: Driver is not WDDM 1.1 or WDDM 1.2; aborting DX10 assessment.
4278593 (5248) - exe\main.cpp:1764: > Run Assessment moobego 
4278593 (5248) - exe\main.cpp:1764: > Run Assessment cpu -encryption -up
4278609 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4278609 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4278625 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4281656 (5248) - exe\main.cpp:1764: > Run Assessment cpu -compression -up
4281671 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4281671 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4281687 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4291718 (5248) - exe\main.cpp:1764: > Run Assessment cpu -encryption2 -up
4291734 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4291734 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4291750 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4294796 (5248) - exe\main.cpp:1764: > Run Assessment cpu -compression2 -up
4294796 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4294796 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4294812 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4304875 (5248) - exe\main.cpp:1764: > Run Assessment cpu -encryption
4304875 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304875 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4304890 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4314984 (5248) - exe\main.cpp:1764: > Run Assessment cpu -compression
4314984 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4314984 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4315000 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4325140 (5248) - exe\main.cpp:1764: > Run Assessment cpu -encryption2
4325171 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325171 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4325187 (5248) - cpu\encryption.cpp:0164: CPU Encryption thread priority changed to 15
4335281 (5248) - exe\main.cpp:1764: > Run Assessment cpu -compression2
4335296 (5248) - exe\WinSATOp.h:1134: Adjusted the priority to 2
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335296 (5248) - exe\WinSATOp.h:0820:  CPU ThreadEntryPoint Launched with priority 0
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4335312 (5248) - cpu\compression.cpp:0180: CPU Compression thread priority changed to 15
4345484 (5248) - exe\main.cpp:1764: > Run Assessment mem 
4345500 (5248) - memory\memat.cpp:1920: Adjusted the priority of Memory Assessment thread to 2
4345515 (5248) - memory\memat.cpp:1231: DoOperationThreadEntryPoint Launched with priority 0
4345515 (5248) - memory\memat.cpp:1231: DoOperationThreadEntryPoint Launched with priority 0
4345515 (5248) - memory\memat.cpp:0610: Adjusted the priority of the Memory copy thread to 15
4345515 (5248) - memory\memat.cpp:0610: Adjusted the priority of the Memory copy thread to 15
4345515 (5248) - memory\memat.cpp:1231: DoOperationThreadEntryPoint Launched with priority 0
4345515 (5248) - memory\memat.cpp:1231: DoOperationThreadEntryPoint Launched with priority 0
4345515 (5248) - memory\memat.cpp:0610: Adjusted the priority of the Memory copy thread to 15
4345515 (5248) - memory\memat.cpp:0610: Adjusted the priority of the Memory copy thread to 15
4350750 (5248) - exe\main.cpp:1764: > Run Assessment disk -seq -read -n 0
4350765 (5248) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
4350765 (5248) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
4350765 (5248) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
4350765 (5248) - storage\diskprof.cpp:0698: Random seed is 1744228792
4350875 (5248) - storage\diskprof.cpp:3830: No Registry or pbip, using the default value of permitted BG Interference
4350875 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4350875 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4350875 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351000 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351000 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351000 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351093 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351093 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351093 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351203 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351203 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351203 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351328 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351328 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351328 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351437 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351437 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351437 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351531 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351531 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351531 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351640 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351640 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351640 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351796 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351796 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351796 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4351968 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4351968 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4351968 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4352062 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4352062 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4352062 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4352218 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4352218 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4352218 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4352312 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4352312 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4352312 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4352437 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4352437 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4352437 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4352531 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4352531 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4352531 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4352656 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4352656 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 256, Allowed interference = 256 Actual Interference = 1 
4352656 (5248) - storage\diskprof.cpp:3922: CCC - Normalized the high IO values - SEQ Heuristic
4352656 (5248) - exe\main.cpp:1764: > Run Assessment disk -ran -read -n 0
4352656 (5248) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
4352656 (5248) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
4352656 (5248) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
4352656 (5248) - storage\diskprof.cpp:0698: Random seed is 1744228794
4352781 (5248) - storage\diskprof.cpp:3830: No Registry or pbip, using the default value of permitted BG Interference
4352781 (5248) - storage\diskprof.cpp:3833: Permitted background Interference in percentage = 100
4352781 (5248) - storage\diskprof.cpp:3891: info: WinSAT IOs = 1000, Allowed interference = 1000 Actual Interference = 1 
4352781 (5248) - storage\diskprof.cpp:3898: CCC - Skipping the heuristic for random assessment
4352796 (5248) - exe\main.cpp:3865: > Power request 'execution' successfully cleared.
4352796 (5248) - exe\processresults.cpp:4125: > Wrote video memory bandwidth to the registry 8282000
4352796 (5248) - exe\processresults.cpp:4849: > Wrote random read disk score, from the profiler, to the registry 391014
4352796 (5248) - exe\processresults.cpp:0985: > Wrote cpu expression compression score to the registry 243
4352796 (5248) - exe\processresults.cpp:1030: Total physical mem available to the OS : 31.9 GB (34,263,474,176 bytes)
4352796 (5248) - exe\processresults.cpp:1131: > Wrote memory throughput to the registry 20417
4352796 (5248) - exe\processresults.cpp:1323: > Wrote random read disk score (throughput) to the registry 391014
4352796 (5248) - exe\processresults.cpp:1349: > Wrote sequential read disk score (throughput) to the registry 529655
4352796 (5248) - exe\processresults.cpp:1739: > Wrote CRS score to the registry 67
4352796 (5248) - exe\processresults.cpp:1760: > Wrote memory score to the registry 89
4352796 (5248) - exe\processresults.cpp:1779: > Wrote cpu score to the registry 89
4352796 (5248) - exe\processresults.cpp:1800: > Wrote DWM score to the registry 67
4352796 (5248) - exe\processresults.cpp:1820: > Wrote D3D score to the registry 99
4352796 (5248) - exe\processresults.cpp:1840: > Wrote disk score to the registry 81
4352812 (5248) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

4352812 (5248) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=TRUE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
4352812 (5248) - common\winsatutilities.cpp:1116: PNPID from DX9 call = PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4352812 (5248) - common\winsatutilities.cpp:1182: Index [0]: PNPID = PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4352812 (5248) - common\winsatutilities.cpp:1206: Matching device PnPID is PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06
4352812 (5248) - common\winsatutilities.cpp:1182: Index [1]: PNPID = ROOT\BasicDisplay
4352843 (5248) - exe\processresults.cpp:4125: > Wrote video memory bandwidth to the registry 8282000
4352843 (5248) - exe\processresults.cpp:4849: > Wrote random read disk score, from the profiler, to the registry 391014
4352843 (5248) - exe\processresults.cpp:0985: > Wrote cpu expression compression score to the registry 243
4352843 (5248) - exe\processresults.cpp:1030: Total physical mem available to the OS : 31.9 GB (34,263,474,176 bytes)
4352843 (5248) - exe\processresults.cpp:1131: > Wrote memory throughput to the registry 20417
4352843 (5248) - exe\processresults.cpp:1323: > Wrote random read disk score (throughput) to the registry 391014
4352843 (5248) - exe\processresults.cpp:1349: > Wrote sequential read disk score (throughput) to the registry 529655
4352843 (5248) - exe\processresults.cpp:1739: > Wrote CRS score to the registry 67
4352843 (5248) - exe\processresults.cpp:1760: > Wrote memory score to the registry 89
4352843 (5248) - exe\processresults.cpp:1779: > Wrote cpu score to the registry 89
4352843 (5248) - exe\processresults.cpp:1800: > Wrote DWM score to the registry 67
4352843 (5248) - exe\processresults.cpp:1820: > Wrote D3D score to the registry 99
4352843 (5248) - exe\processresults.cpp:1840: > Wrote disk score to the registry 81
4352875 (5248) - common\winsatutilities.cpp:1458: Enabling the WinSAT Task
4352875 (5248) - exe\processwinsaterror.cpp:0319: Writing exit code, cant msg and why msg to registry 
4352875 (5248) - exe\main.cpp:2764: > Successfully reenabled EMD.
4352875 (5248) - exe\main.cpp:2790: > Composition restarted
4352875 (5248) - exe\watchdog.cpp:0339: Watch dog system shutdown
4352875 (5248) - exe\main.cpp:5040: > exit value = 0.

4417312 (9760) - exe\logging.cpp:0841: --- START 2025\4\9 15:00:59 PM ---
4417312 (9760) - exe\main.cpp:4363: WinSAT registry node is created or present
4417312 (9760) - exe\main.cpp:4394: Command Line = "C:\Windows\system32\winsat.exe" disk -wsswap
4417312 (9760) - exe\main.cpp:4016: INFO: The axe results path environment variable is not set. Assuming we aren't running under AXE.
4417312 (9760) - exe\main.cpp:4500: INFO: Winsat is not running in AXE mode.
4417312 (9760) - exe\main.cpp:4529: Skipping writing the Initial values to the registry - Axe Mode
4417312 (9760) - exe\main.cpp:2531: > DWM not running.
4417312 (9760) - exe\main.cpp:2496: > EMD service will be restored on exit.
4417312 (9760) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

4417312 (9760) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=FALSE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
4417312 (9760) - exe\main.cpp:3826: > Power 'execution' request successfully set.
4417312 (9760) - exe\main.cpp:1764: > Run Assessment disk -wsswap -drive C:
4417312 (9760) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
4417328 (9760) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
4417328 (9760) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
4417328 (9760) - storage\diskprof.cpp:0698: Random seed is 1744228859
4417890 (9760) - exe\main.cpp:3865: > Power request 'execution' successfully cleared.
4417890 (9760) - exe\processresults.cpp:4767: > Wrote wsswap throughput, from the profiler, to the registry 525312
4417890 (9760) - exe\processresults.cpp:4777: > Wrote wsswap interference, from the profiler, to the registry 0
4417890 (9760) - exe\processresults.cpp:4854: Skipping Registry Entry for random read disk score
4417890 (9760) - exe\processwinsaterror.cpp:0340: Skipping writing the exit code, cant msg and why msg to registry
4417890 (9760) - exe\main.cpp:2764: > Successfully reenabled EMD.
4417890 (9760) - exe\main.cpp:5040: > exit value = 0.

72375578 (3604) - exe\logging.cpp:0841: --- START 2025\4\29 16:33:17 PM ---
72375578 (3604) - exe\main.cpp:4363: WinSAT registry node is created or present
72375578 (3604) - exe\main.cpp:4394: Command Line = "C:\Windows\system32\winsat.exe" disk -wsswap
72375578 (3604) - exe\main.cpp:4016: INFO: The axe results path environment variable is not set. Assuming we aren't running under AXE.
72375578 (3604) - exe\main.cpp:4500: INFO: Winsat is not running in AXE mode.
72375578 (3604) - exe\main.cpp:4529: Skipping writing the Initial values to the registry - Axe Mode
72375593 (3604) - exe\main.cpp:2531: > DWM not running.
72375593 (3604) - exe\main.cpp:2496: > EMD service will be restored on exit.
72375593 (3604) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

72375593 (3604) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=FALSE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
72375593 (3604) - exe\main.cpp:3826: > Power 'execution' request successfully set.
72375593 (3604) - exe\main.cpp:1764: > Run Assessment disk -wsswap -drive C:
72375593 (3604) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
72375593 (3604) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
72375593 (3604) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
72375593 (3604) - storage\diskprof.cpp:0698: Random seed is 1745962397
72376187 (3604) - exe\main.cpp:3865: > Power request 'execution' successfully cleared.
72376203 (3604) - exe\processresults.cpp:4767: > Wrote wsswap throughput, from the profiler, to the registry 516096
72376203 (3604) - exe\processresults.cpp:4777: > Wrote wsswap interference, from the profiler, to the registry 786435
72376203 (3604) - exe\processresults.cpp:4854: Skipping Registry Entry for random read disk score
72376203 (3604) - exe\processwinsaterror.cpp:0340: Skipping writing the exit code, cant msg and why msg to registry
72376203 (3604) - exe\main.cpp:2764: > Successfully reenabled EMD.
72376218 (3604) - exe\main.cpp:5040: > exit value = 0.

72791062 (9688) - exe\logging.cpp:0841: --- START 2025\4\29 16:40:12 PM ---
72791062 (9688) - exe\main.cpp:4363: WinSAT registry node is created or present
72791062 (9688) - exe\main.cpp:4394: Command Line = "C:\Windows\system32\winsat.exe" disk -wsswap
72791062 (9688) - exe\main.cpp:4016: INFO: The axe results path environment variable is not set. Assuming we aren't running under AXE.
72791078 (9688) - exe\main.cpp:4500: INFO: Winsat is not running in AXE mode.
72791078 (9688) - exe\main.cpp:4529: Skipping writing the Initial values to the registry - Axe Mode
72791078 (9688) - exe\main.cpp:2531: > DWM not running.
72791078 (9688) - exe\main.cpp:2496: > EMD service will be restored on exit.
72791078 (9688) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

72791078 (9688) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=FALSE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
72791078 (9688) - exe\main.cpp:3826: > Power 'execution' request successfully set.
72791078 (9688) - exe\main.cpp:1764: > Run Assessment disk -wsswap -drive C:
72791078 (9688) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
72791078 (9688) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
72791078 (9688) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
72791078 (9688) - storage\diskprof.cpp:0698: Random seed is 1745962812
72791703 (9688) - exe\main.cpp:3865: > Power request 'execution' successfully cleared.
72791703 (9688) - exe\processresults.cpp:4767: > Wrote wsswap throughput, from the profiler, to the registry 517120
72791703 (9688) - exe\processresults.cpp:4777: > Wrote wsswap interference, from the profiler, to the registry 2490373
72791703 (9688) - exe\processresults.cpp:4854: Skipping Registry Entry for random read disk score
72791703 (9688) - exe\processwinsaterror.cpp:0340: Skipping writing the exit code, cant msg and why msg to registry
72791703 (9688) - exe\main.cpp:2764: > Successfully reenabled EMD.
72791703 (9688) - exe\main.cpp:5040: > exit value = 0.

22225171 (10492) - exe\logging.cpp:0841: --- START 2025\5\29 16:34:16 PM ---
22225171 (10492) - exe\main.cpp:4363: WinSAT registry node is created or present
22225171 (10492) - exe\main.cpp:4394: Command Line = "C:\Windows\system32\winsat.exe" disk -wsswap
22225171 (10492) - exe\main.cpp:4016: INFO: The axe results path environment variable is not set. Assuming we aren't running under AXE.
22225171 (10492) - exe\main.cpp:4500: INFO: Winsat is not running in AXE mode.
22225171 (10492) - exe\main.cpp:4529: Skipping writing the Initial values to the registry - Axe Mode
22225171 (10492) - exe\main.cpp:2531: > DWM not running.
22225171 (10492) - exe\main.cpp:2496: > EMD service will be restored on exit.
22225171 (10492) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

22225171 (10492) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=FALSE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
22225171 (10492) - exe\main.cpp:3826: > Power 'execution' request successfully set.
22225171 (10492) - exe\main.cpp:1764: > Run Assessment disk -wsswap -drive C:
22225171 (10492) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
22225187 (10492) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
22225187 (10492) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
22225187 (10492) - storage\diskprof.cpp:0698: Random seed is 1748554456
22225890 (10492) - exe\main.cpp:3865: > Power request 'execution' successfully cleared.
22225890 (10492) - exe\processresults.cpp:4767: > Wrote wsswap throughput, from the profiler, to the registry 390144
22225890 (10492) - exe\processresults.cpp:4777: > Wrote wsswap interference, from the profiler, to the registry 2228233
22225890 (10492) - exe\processresults.cpp:4854: Skipping Registry Entry for random read disk score
22225890 (10492) - exe\processwinsaterror.cpp:0340: Skipping writing the exit code, cant msg and why msg to registry
22225890 (10492) - exe\main.cpp:2764: > Successfully reenabled EMD.
22225906 (10492) - exe\main.cpp:5040: > exit value = 0.

22227062 (18884) - exe\logging.cpp:0841: --- START 2025\5\29 16:34:18 PM ---
22227062 (18884) - exe\main.cpp:4363: WinSAT registry node is created or present
22227062 (18884) - exe\main.cpp:4394: Command Line = "C:\Windows\system32\winsat.exe" disk -wsswap
22227078 (18884) - exe\main.cpp:4016: INFO: The axe results path environment variable is not set. Assuming we aren't running under AXE.
22227078 (18884) - exe\main.cpp:4500: INFO: Winsat is not running in AXE mode.
22227078 (18884) - exe\main.cpp:4529: Skipping writing the Initial values to the registry - Axe Mode
22227078 (18884) - exe\main.cpp:2531: > DWM not running.
22227078 (18884) - exe\main.cpp:2496: > EMD service will be restored on exit.
22227078 (18884) - exe\main.cpp:0816: > WinSAT info: Version=V10.0 Build-19041.3636

22227078 (18884) - exe\main.cpp:0948: > IsOfficial=TRUE  IsFormal=FALSE  IsMoobe=FALSE  RanOverTs=FALSE  RanOnbatteries=FALSE
22227078 (18884) - exe\main.cpp:3826: > Power 'execution' request successfully set.
22227078 (18884) - exe\main.cpp:1764: > Run Assessment disk -wsswap -drive C:
22227078 (18884) - storage\diskprof.cpp:0161: CCC Support not disabled in registry
22227078 (18884) - storage\diskprof.cpp:0677: DiskProfilerThreadProc Launched with priority 0
22227078 (18884) - storage\diskprof.cpp:0690: ITERATION NUMBER 1
22227078 (18884) - storage\diskprof.cpp:0698: Random seed is 1748554458
22227671 (18884) - exe\main.cpp:3865: > Power request 'execution' successfully cleared.
22227687 (18884) - exe\processresults.cpp:4767: > Wrote wsswap throughput, from the profiler, to the registry 525312
22227687 (18884) - exe\processresults.cpp:4777: > Wrote wsswap interference, from the profiler, to the registry 0
22227687 (18884) - exe\processresults.cpp:4854: Skipping Registry Entry for random read disk score
22227687 (18884) - exe\processwinsaterror.cpp:0340: Skipping writing the exit code, cant msg and why msg to registry
22227687 (18884) - exe\main.cpp:2764: > Successfully reenabled EMD.
22227687 (18884) - exe\main.cpp:5040: > exit value = 0.
