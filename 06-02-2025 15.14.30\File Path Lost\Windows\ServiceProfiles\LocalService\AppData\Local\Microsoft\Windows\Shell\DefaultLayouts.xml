<?xml version="1.0" encoding="utf-8"?>
<FullDefaultLayoutTemplate
    xmlns="http://schemas.microsoft.com/Start/2014/FullDefaultLayout"
    xmlns:start="http://schemas.microsoft.com/Start/2014/StartLayout"
    Version="1">
    <StartLayoutCollection>
        <!-- 6 cell wide Desktop layout for Education -->
        <StartLayout
            GroupCellWidth="6"
            EducationModeEnabled="true">
            <start:Group
                LocalizedNameResourceTag="TileGrid_EducationGroup">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub" />
                <start:Tile Size="2x2" Column="2" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Word" />
                <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Excel" />
                <start:Tile Size="2x2" Column="0" Row="2" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Outlook" />
                <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!PowerPoint" />
                <start:Tile Size="2x2" Column="4" Row="2" AppUserModelID="Microsoft.Office.OneNote_8wekyb3d8bbwe!microsoft.onenoteim" />
                <start:DesktopApplicationTile Size="2x2" Column="0" Row="4" DesktopApplicationID="Microsoft.SkyDrive.Desktop" />
                <start:SecondaryTile
                    AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
                    TileID="32555428050"
                    DisplayName="Teams"
                    Size="2x2"
                    Row="4"
                    Column="2"
                    Arguments="-contentTile -url 0 https://teams.microsoft.com"
                    Square150x150LogoUri="data:image/png;base64,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"
                    Square71x71LogoUri="data:image/png;base64,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"
                    ShowNameOnSquare150x150Logo="true"
                    ShowNameOnWide310x150Logo="false"
                    BackgroundColor="#404040"
                    ForegroundText="light" />
                <start:Tile Size="2x2" Column="4" Row="4" AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge" />
            </start:Group>
        </StartLayout>

        <!-- 6 cell wide Desktop Commercial layout with no office installed -->
        <StartLayout
            GroupCellWidth="6"
            CommercialDevice="true"
            OfficeSKU="None">
            <start:Group
                LocalizedNameResourceTag="TileGrid_DefaultGroup1">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub" />
                <start:Tile Size="2x2" Column="2" Row="0" AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge" />
                <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="Microsoft.WindowsStore_8wekyb3d8bbwe!App" />
            </start:Group>
        </StartLayout>

        <!-- 6 cell wide Desktop Commercial layout with office installed -->
        <StartLayout
            GroupCellWidth="6"
            CommercialDevice="true"
            OfficeSKU="Desktop|Desktop2016|DesktopBridge|DesktopBridgeSubscription|Mobile">
            <start:Group
                LocalizedNameResourceTag="TileGrid_OfficeGroup">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub" />
                <start:Tile Size="2x2" Column="2" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Word" />
                <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Excel" />
                <start:Tile Size="2x2" Column="0" Row="2" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Outlook" />
                <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!PowerPoint" />
                <start:Tile Size="2x2" Column="4" Row="2" AppUserModelID="Microsoft.Office.OneNote_8wekyb3d8bbwe!microsoft.onenoteim" />
                <start:DesktopApplicationTile Size="2x2" Column="0" Row="4" DesktopApplicationID="Microsoft.SkyDrive.Desktop" />
                <start:DesktopApplicationTile Size="2x2" Column="2" Row="4" DesktopApplicationID="com.squirrel.Teams.Teams" />
            </start:Group>
            <start:Group
                LocalizedNameResourceTag="TileGrid_DefaultGroup2">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge" />
                <start:Tile Size="2x2" Column="2" Row="0" AppUserModelID="Microsoft.WindowsStore_8wekyb3d8bbwe!App" />
            </start:Group>
        </StartLayout>

        <!-- 6 cell wide Desktop layout with targeted content tiles -->
        <StartLayout
            GroupCellWidth="6"
            TargetedContentTilesEnabled="true">
            <start:Group
                LocalizedNameResourceTag="TileGrid_DefaultGroup1">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub" />
                <start:Folder Name="" Size="2x2" Column="2" Row="0">
                    <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Outlook" />
                    <start:Tile Size="2x2" Column="2" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Word" />
                    <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Excel" />
                    <start:DesktopApplicationTile Size="2x2" Column="0" Row="2" DesktopApplicationID="Microsoft.SkyDrive.Desktop" />
                    <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!PowerPoint" />
                    <start:Tile Size="2x2" Column="4" Row="2" AppUserModelID="Microsoft.Office.OneNote_8wekyb3d8bbwe!microsoft.onenoteim" />
                    <start:Tile Size="2x2" Column="0" Row="4" AppUserModelID="Microsoft.SkypeApp_kzf8qxf38zg5c!App" />
                </start:Folder>
                <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="microsoft.windowscommunicationsapps_8wekyb3d8bbwe!Microsoft.WindowsLive.Mail" />
                <start:Tile Size="2x2" Column="0" Row="2" AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge" />
                <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Windows.Photos_8wekyb3d8bbwe!App" />
                <start:TargetedContentTile Size="2x2" Column="4" Row="2" TileID="T~DynamicLayouts:1!1" />
            </start:Group>
            <start:Group
                LocalizedNameResourceTag="TileGrid_DefaultGroup2">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.WindowsStore_8wekyb3d8bbwe!App" />
                <start:TargetedContentTile Size="2x2" Column="2" Row="0" TileID="T~DynamicLayouts:1!2" />
                <start:TargetedContentTile Size="2x2" Column="4" Row="0" TileID="T~DynamicLayouts:1!3" />
                <start:TargetedContentTile Size="2x2" Column="0" Row="2" TileID="T~DynamicLayouts:1!4" />
                <start:TargetedContentTile Size="2x2" Column="2" Row="2" TileID="T~DynamicLayouts:1!5" />
                <start:Folder LocalizedNameResourceTag="TileGrid_PlayFolder" Size="2x2" Column="4" Row="2">
                    <start:TargetedContentTile Size="2x2" Column="0" Row="0" TileID="T~DynamicLayouts:2!1" />
                    <start:TargetedContentTile Size="2x2" Column="2" Row="0" TileID="T~DynamicLayouts:2!2" />
                    <start:TargetedContentTile Size="2x2" Column="4" Row="0" TileID="T~DynamicLayouts:2!3" />
                    <start:TargetedContentTile Size="2x2" Column="0" Row="2" TileID="T~DynamicLayouts:2!4" />
                    <start:TargetedContentTile Size="2x2" Column="2" Row="2" TileID="T~DynamicLayouts:2!5" />
                    <start:TargetedContentTile Size="2x2" Column="4" Row="2" TileID="T~DynamicLayouts:2!6" />
                </start:Folder>
            </start:Group>
        </StartLayout>

        <!-- N-SKU 6 cell wide Desktop layout -->
        <StartLayout
            GroupCellWidth="6"
            SKU="DesktopN|DesktopEnterpriseN">
            <start:Group
                LocalizedNameResourceTag="TileGrid_DefaultGroup1">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub" />
                <start:Folder Name="" Size="2x2" Column="2" Row="0">
                    <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Outlook" />
                    <start:Tile Size="2x2" Column="2" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Word" />
                    <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Excel" />
                    <start:DesktopApplicationTile Size="2x2" Column="0" Row="2" DesktopApplicationID="Microsoft.SkyDrive.Desktop" />
                    <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!PowerPoint" />
                    <start:Tile Size="2x2" Column="4" Row="2" AppUserModelID="Microsoft.Office.OneNote_8wekyb3d8bbwe!microsoft.onenoteim" />
                </start:Folder>
                <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="microsoft.windowscommunicationsapps_8wekyb3d8bbwe!Microsoft.WindowsLive.Mail" />
                <start:Tile Size="2x2" Column="0" Row="2" AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge" />
                <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Windows.Photos_8wekyb3d8bbwe!App" />
                <start:Tile Size="2x2" Column="4" Row="2" AppUserModelID="Microsoft.WindowsStore_8wekyb3d8bbwe!App" />
            </start:Group>
        </StartLayout>

        <!-- 6 cell wide Desktop layout without targeted content tiles-->
        <StartLayout
            GroupCellWidth="6"
            TargetedContentTilesEnabled="false">
            <start:Group
                LocalizedNameResourceTag="TileGrid_DefaultGroup1">
                <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub" />
                <start:Folder Name="" Size="2x2" Column="2" Row="0">
                    <start:Tile Size="2x2" Column="0" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Outlook" />
                    <start:Tile Size="2x2" Column="2" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Word" />
                    <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Excel" />
                    <start:DesktopApplicationTile Size="2x2" Column="0" Row="2" DesktopApplicationID="Microsoft.SkyDrive.Desktop" />
                    <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!PowerPoint" />
                    <start:Tile Size="2x2" Column="4" Row="2" AppUserModelID="Microsoft.Office.OneNote_8wekyb3d8bbwe!microsoft.onenoteim" />
                </start:Folder>
                <start:Tile Size="2x2" Column="4" Row="0" AppUserModelID="microsoft.windowscommunicationsapps_8wekyb3d8bbwe!Microsoft.WindowsLive.Mail" />
                <start:Tile Size="2x2" Column="0" Row="2" AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge" />
                <start:Tile Size="2x2" Column="2" Row="2" AppUserModelID="Microsoft.Windows.Photos_8wekyb3d8bbwe!App" />
                <start:Tile Size="2x2" Column="4" Row="2" AppUserModelID="Microsoft.WindowsStore_8wekyb3d8bbwe!App" />
            </start:Group>
        </StartLayout>

        <!-- Long Term Servicing Branch 6 cell -->
        <StartLayout
            GroupCellWidth="6"
            SKU="LongTermServicingBranch">
            <start:Group>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
                    Size="2x2"
                    Row="0"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Windows.ContactSupport_cw5n1h2txyewy!App"
                    Size="2x2"
                    Row="0"
                    Column="2"/>
                <start:Tile
                    AppUserModelID="Microsoft.Windows.FeatureOnDemand.InsiderHub_cw5n1h2txyewy!App"
                    Size="2x2"
                    Row="0"
                    Column="4"/>
            </start:Group>
        </StartLayout>

        <!-- PPI 8 cell wide -->
        <StartLayout
            GroupCellWidth="8"
            SKU="PPI">
            <start:Group>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
                    Size="2x2"
                    Row="0"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.Getstarted_8wekyb3d8bbwe!App"
                    Size="4x2"
                    Row="0"
                    Column="2"/>
                <start:Tile
                    AppUserModelID="Microsoft.Office.PowerPoint_8wekyb3d8bbwe!Microsoft.pptim"
                    Size="2x2"
                    Row="2"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.Office.Word_8wekyb3d8bbwe!Microsoft.Word"
                    Size="2x2"
                    Row="2"
                    Column="2"/>
                <start:Tile
                    AppUserModelID="Microsoft.Office.Excel_8wekyb3d8bbwe!Microsoft.Excel"
                    Size="2x2"
                    Row="2"
                    Column="4"/>
                <start:Tile
                    AppUserModelID="c5e2524a-ea46-4f67-841f-6a9465d9d515_cw5n1h2txyewy!App"
                    Size="2x2"
                    Row="4"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="microsoft.microsoftskydrive_8wekyb3d8bbwe!App"
                    Size="2x2"
                    Row="4"
                    Column="2"/>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftPowerBIForWindows_8wekyb3d8bbwe!Microsoft.MicrosoftPowerBIForWindows"
                    Size="2x2"
                    Row="4"
                    Column="4"/>
            </start:Group>
        </StartLayout>

        <!-- Server 6 cell wide -->
        <StartLayout
            GroupCellWidth="6"
            SKU="Server|ServerSolution">
            <start:Group
                LocalizedNameResourceTag="TileGrid_Server_DefaultGroup1">
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Server Manager.lnk"
                    Size="2x2"
                    Row="0"
                    Column="0"/>
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%APPDATA%\Microsoft\Windows\Start Menu\Programs\Windows PowerShell\Windows PowerShell.lnk"
                    Size="2x2"
                    Row="0"
                    Column="2"/>
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%APPDATA%\Microsoft\Windows\Start Menu\Programs\Windows PowerShell\Windows PowerShell ISE.lnk"
                    Size="2x2"
                    Row="0"
                    Column="4"/>
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%APPDATA%\Microsoft\Windows\Start Menu\Programs\System Tools\Administrative Tools.lnk"
                    Size="2x2"
                    Row="2"
                    Column="0"/>
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\System Tools\Task Manager.lnk"
                    Size="2x2"
                    Row="2"
                    Column="2"/>
                <start:DesktopApplicationTile
                    DesktopApplicationID="Microsoft.Windows.ControlPanel"
                    Size="2x2"
                    Row="2"
                    Column="4"/>
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Accessories\Remote Desktop Connection.lnk"
                    Size="2x2"
                    Row="4"
                    Column="0"/>
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Administrative Tools\Event Viewer.lnk"
                    Size="2x2"
                    Row="4"
                    Column="2"/>
                <start:DesktopApplicationTile
                    DesktopApplicationLinkPath="%APPDATA%\Microsoft\Windows\Start Menu\Programs\System Tools\File Explorer.lnk"
                    Size="2x2"
                    Row="4"
                    Column="4"/>
            </start:Group>
        </StartLayout>

    </StartLayoutCollection>
    <Windows8UpgradeGroupsCollection>
        <Windows8UpgradeGroups
            Region="CN|DE|ES|FR|GB|IT|US|JP|IN|AU|CA|BR|MX"
            PreInstalledAppsEnabled="true">
            <PrependGroup>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
                    Size="2x2"
                    Row="0"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.Windows.Cortana_cw5n1h2txyewy!CortanaUI"
                    Size="2x2"
                    Row="0"
                    Column="2"/>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub"
                    Size="2x2"
                    Row="0"
                    Column="4"/>
                <start:Tile
                    AppUserModelID="Microsoft.XboxApp_8wekyb3d8bbwe!Microsoft.XboxApp"
                    Size="2x2"
                    Row="2"
                    Column="0"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout2.1"
                    Arguments="creative-ms:default?p=DefaultStartLayout2&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo2.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="2"
                    Column="2"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout2.2"
                    Arguments="creative-ms:default?p=DefaultStartLayout2&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo2.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="2"
                    Column="4"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout1.1"
                    Arguments="creative-ms:default?p=DefaultStartLayout1&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo1.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="4"
                    Column="0"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout1.2"
                    Arguments="creative-ms:default?p=DefaultStartLayout1&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo1.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="4"
                    Column="2"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout1.3"
                    Arguments="creative-ms:default?p=DefaultStartLayout1&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo1.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="4"
                    Column="4"/>
            </PrependGroup>
        </Windows8UpgradeGroups>
        <Windows8UpgradeGroups
            PreInstalledAppsEnabled="true">
            <PrependGroup>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
                    Size="4x2"
                    Row="0"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub"
                    Size="2x2"
                    Row="0"
                    Column="4"/>
                <start:Tile
                    AppUserModelID="Microsoft.XboxApp_8wekyb3d8bbwe!Microsoft.XboxApp"
                    Size="2x2"
                    Row="2"
                    Column="0"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout2.1"
                    Arguments="creative-ms:default?p=DefaultStartLayout2&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo2.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="2"
                    Column="2"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout2.2"
                    Arguments="creative-ms:default?p=DefaultStartLayout2&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo2.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="2"
                    Column="4"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout1.1"
                    Arguments="creative-ms:default?p=DefaultStartLayout1&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo1.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="4"
                    Column="0"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout1.2"
                    Arguments="creative-ms:default?p=DefaultStartLayout1&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo1.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="4"
                    Column="2"/>
                <start:SecondaryTile
                    AppUserModelID="Microsoft.Windows.ContentDeliveryManager_cw5n1h2txyewy!App"
                    TileID="PreInstalled.DefaultStartLayout1.3"
                    Arguments="creative-ms:default?p=DefaultStartLayout1&amp;launch=ms-get-started://redirect%3Fid=placeholdertiles"
                    DisplayName=" "
                    ShowNameOnSquare150x150Logo="true"
                    Square150x150LogoUri="ms-appx:///Experiences/PreInstalledApps/DefaultSquareTileLogo1.png"
                    IsSuggestedApp="true"
                    Size="2x2"
                    Row="4"
                    Column="4"/>
            </PrependGroup>
        </Windows8UpgradeGroups>
        <Windows8UpgradeGroups
            Region="CN|DE|ES|FR|GB|IT|US|JP|IN|AU|CA|BR|MX">
            <PrependGroup>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
                    Size="2x2"
                    Row="0"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.Windows.Cortana_cw5n1h2txyewy!CortanaUI"
                    Size="2x2"
                    Row="0"
                    Column="2"/>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub"
                    Size="2x2"
                    Row="0"
                    Column="4"/>
                <start:Tile
                    AppUserModelID="Microsoft.XboxApp_8wekyb3d8bbwe!Microsoft.XboxApp"
                    Size="4x2"
                    Row="2"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.Getstarted_8wekyb3d8bbwe!App"
                    Size="2x2"
                    Row="2"
                    Column="4"/>
            </PrependGroup>
        </Windows8UpgradeGroups>
        <Windows8UpgradeGroups>
            <PrependGroup>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
                    Size="4x2"
                    Row="0"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub"
                    Size="2x2"
                    Row="0"
                    Column="4"/>
                <start:Tile
                    AppUserModelID="Microsoft.XboxApp_8wekyb3d8bbwe!Microsoft.XboxApp"
                    Size="4x2"
                    Row="2"
                    Column="0"/>
                <start:Tile
                    AppUserModelID="Microsoft.Getstarted_8wekyb3d8bbwe!App"
                    Size="2x2"
                    Row="2"
                    Column="4"/>
            </PrependGroup>
        </Windows8UpgradeGroups>
    </Windows8UpgradeGroupsCollection>
    <InstalledOfficeMobileSuiteTiles>
        <SecondaryTile
            AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
            TileID="26310719480"
            DisplayName="Outlook"
            Size="2x2"
            Row="0"
            Column="0"
            Arguments="-contentTile -url 0 https://outlook.com"
            Square150x150LogoUri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACPtJREFUeNrsnVuIJFcZx/+nqu+90z3TO7szmWx2dzTqZjaaGAkkJnh50Bij5CVgVIQ86ItGENaHBYWALyqI7IOXF0FBUDHqQ5AoixAIhoSAMSxrBA1i4mZ3Nuvsbs/MTnd1XY7fOVXVl7l0ZkPPrFP9/8GhTnXVdPd0/+b7vnP6VI/SWoOQUePwJSAUi1AsQrEIoViEYhGKRQjFIhSLUCxCKBahWIRiEUKxCMUiFIsQikUoFqFYhFAsQrEIxSKEYhGKRSgWIRSLUCxCsQihWIRiEYpFCMUiFItQLEIoFqFYhGIRQrEIxSIUixCKRSgWoViEUCxCsQjFIoRiEYpFKBYhFItQLEKxCHlLcln6ZZRSb+vH1m13A520wRsz9I/fc2P8R6WSlpdWSKL3zsr10RMTcPMaf/rOf2WvIy3K7Iubpb+S64hYqVQF9dVnn1Ad76SzciUOIQP3oTZRTaUPNuS8eF9vCIQKulqDLhR/hFP3npAbvP7IxYiVnYhVcpYvn3SalzYRpl+Wdf3153U3w6SLb1PeKvS+xpclVH1TdgNp4WZpkcX73pXK/O45tba8+w/evpb+UTt90ZNiZUku5Xu7/8hBJxXLzaJUFOvGvqluVqPVuNdYVq7SwSNJTaQGt9JUWi9Jm71tOr7N7Duq11eD/ZnDNai+47bvON3jZt/0T/8qu1JRrHgsFpfOqm/fDulUrwg3/cGTCMUaSilqnu+N2gYiVzJ9oSTaFCeAaDKJVKBg4yZW/eTZn8rmsZHHtFYTweoVuBMNKO0yeI1h8f7YjrxIlUmEzUvQMprTOkIGp50o1o0ibK9Cm6mJKKRYFGt0ExNRS8TqeNBGLE2xKNaIxNKBSBX6YlgUe0W5OCrs5/hMAQ+8u9zdf/51D8+/1n4rr6xQEq7sB8Ws2ylWl1vqOXz/0/tx75HShmP/aQb44pOX8LeLnS3MUmi+sYKV4ApURUEVRETHjQN+/1SFnQB1Nkyabuh3dc1u1BuLVFgrOTj9pTl88GjZvsHLnsYLEqnONUO7f3gyj998YRa3TOa2jlgjmbfQO3CnFOuG8a2P70e97FqJnjxzDQvfex2P/HwR9/zgHH7y4oq93Rw/8aEpjNwsI1PS4o1O1l05FGuv8+CxqpXHRKivPXVp4NgTp5fwykXfHv/MnROje9BUqKRvZTJzYGEoG9kq16FYe5jbZ4vdaPXrM6ubnvPHf6zFHxhLM+lyY4mlrlOotBsLpSNpRqig10o333kU8bJoirVX66tUGjVk1JeeM5JUaGWK4q0IhTBIhOpti/tvfSoRa+fX2nNUOHpUf8TZUpyeVGo7hfcwobopUHfnu6IoToM2ckUyYJD+wY98uFaeWq4uPvP75b6nmplhYuYj1tlFD47j2Hb/fGXTc957U6l7znP/bm3m3bq6qb+tO2YmUEUeE6kiiUyR70vrIOr4CNttqMhH450zOPjw5yFSdbIasTIvVrMdWVlMRLr/HVV89v31geNm/6GFCXv8D39f3TLupaktXr+l+9xK9yN7jg7lPJPufEl7IpMRKmq1ZdvGxGwdjWPz6AQ5+F4IZHjN+1hMkH7j6Tfx7OPztv/DR+bwSRHJRDJT2BuprICtEN99ZmnLfBpcW0Jo1m51RNJ8CTqdII3DVVe6N172488TjYiBb9e375up4dB9tyFUObRWfbPEC47uLuzi0uQ9mw4vtPGV317Atx+asSPETx2vSeuLaiLV47+7YM/bogSLZZH0pqw0YZIf+***************************+ZEoXqocKVtvZZmx+Ujnly9dxdOvrOBzH5iUlFhBveRKmgzx53+t4Rd/uWr7Q4cAJgJFQTzCc8KB4WJ3jsrIJSnPRKmZ9x1FbX7OZEX4rSBefWoFjAMdxcpUvRXix88t2XbdQ8vAE0Ok1i5IRJKUpo0hOvms0EgVGOl8VA4VceCOBahSGZ4XJRdUyGnmdHshhrZyUSxi3Zm9awFL59rijmdmzcWMXK88kkjmyk033Xc3qkeO2sJcSerrlmBRIlTaV8kUBMWiWe7UNG695w5cffU8Fs/8U27IxwckWjUWjqFx+3FJew68tSAuv5yeRHEfveuvkf3lXBRrGxQqItFlyYSBg/1334UpkWjxxb/Cl9Raf88x5KcOoONpK5HqRqm+QWOEZEmNtlnT9JkK9xY/wygvqDAZzHWgmq/a+si0wJe6qVrHoU98DDrUWHlzTWr1MLmQtSeRSX0DdRUGI1jWxRrHrzEyb7/JY1Ozj379gh72DVXmCuZcEW5lAoXZeRRvfhdyE1NQxZI8lmsFcnMO2ssevBUvnn1wep87ximx11fJdYkq+bmzj84cho2F9uuMQp2hN2OsU2H+4BEMFUvymlMQsSRC5eoH5NUqdFeO6uQLsEwEy0uqzJfzaF1pSfQKBmqpgdoqiqXSDov3bP/yjbmhKcksM1b5ApxSFY7IpQolK5ZeV32n3VKjjEjSohHM3m9aV20yMtSKYmU3Yk0fGjo6U6YSd3OxXIWyTYtaQo/a4kpoI5OSFFc5UIW/2oHf8rccGeoIFCuruLXp4QtV0u9uMOlPBIO9UKKXBoe+sFUjo2sFi+ys/bqiHhQruyOXYmWbJ/ZWtujrmIQy0aswWUKw5iP0gvijnySCUaxsx6ztzzt0893beJGlsHeLOfjXOvGymrSop1jZZNuXno4ivEgazO8rIvJDiWAds8JrVPf8f8k4X2Lv+5fPnxpcDbrzzck7yNeKCJoXT5nnkNXYNa4TpCZSF6Xtk1bG7i4P1olMZvGX+fpkLxWME6R7H/PGmu9YX0veWHcXHzsVyyzq6iTPI3MpcRzF0n1iadyYixnSb28L+vqaYu19sZBEjGjjsG9Xn4fOolTjnAr1CMd7o3ouLN4J4XQDoViEYhFCsQjFIhSLEIpFKBahWIRQLEKxCMUihGIRikUoFiEUi1AsQrEIoViEYhGKRQjFIhSLUCxCKBahWIRiEUKxCMUiFIsQikUoFqFYhGLxJSAUi1AsQrEIoViEYhGKRQjFIhSLUCxCKBahWIRiEUKxCMUiFIuQkfA/AQYA81NuxwjWIl0AAAAASUVORK5CYII="
            Square71x71LogoUri="data:image/png;base64,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"
            ShowNameOnSquare150x150Logo="true"
            ShowNameOnWide310x150Logo="false"
            BackgroundColor="#404040"
            ForegroundText="light"/>
        <Tile
            AppUserModelID="Microsoft.Office.Word_8wekyb3d8bbwe!Microsoft.Word"
            Size="2x2"
            Row="0"
            Column="2"/>
        <Tile
            AppUserModelID="Microsoft.Office.Excel_8wekyb3d8bbwe!Microsoft.Excel"
            Size="2x2"
            Row="0"
            Column="4"/>
        <Tile
            AppUserModelID="Microsoft.Office.PowerPoint_8wekyb3d8bbwe!Microsoft.pptim"
            Size="2x2"
            Row="2"
            Column="0"/>
    </InstalledOfficeMobileSuiteTiles>
    <InstalledOfficeDesktopSuiteTiles>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Outlook.lnk"
            Size="2x2"
            Row="0"
            Column="0"/>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Word.lnk"
            Size="2x2"
            Row="0"
            Column="2"/>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Excel.lnk"
            Size="2x2"
            Row="0"
            Column="4"/>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\PowerPoint.lnk"
            Size="2x2"
            Row="2"
            Column="0"/>
    </InstalledOfficeDesktopSuiteTiles>
    <InstalledOfficeDesktop2016SuiteTiles>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Outlook 2016.lnk"
            Size="2x2"
            Row="0"
            Column="0"/>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Word 2016.lnk"
            Size="2x2"
            Row="0"
            Column="2"/>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Excel 2016.lnk"
            Size="2x2"
            Row="0"
            Column="4"/>
        <DesktopApplicationTile
            DesktopApplicationLinkPath="%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\PowerPoint 2016.lnk"
            Size="2x2"
            Row="2"
            Column="0"/>
    </InstalledOfficeDesktop2016SuiteTiles>
    <InstalledOfficeDesktopBridgeSuiteTiles>
        <Tile
            AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Outlook"
            Size="2x2"
            Row="0"
            Column="0"/>
        <Tile
            AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Word"
            Size="2x2"
            Row="0"
            Column="2"/>
        <Tile
            AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!Excel"
            Size="2x2"
            Row="0"
            Column="4"/>
        <Tile
            AppUserModelID="Microsoft.Office.Desktop_8wekyb3d8bbwe!PowerPoint"
            Size="2x2"
            Row="2"
            Column="0"/>
    </InstalledOfficeDesktopBridgeSuiteTiles>
    <WebOfficeTiles>
        <!-- The argument values for these secondary tiles need to come from creating the tiles with edge, then exporting the start layout -->
        <SecondaryTile
            AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
            TileID="26310719480"
            DisplayName="Outlook"
            Size="2x2"
            Row="0"
            Column="0"
            Arguments="-contentTile -url 0 https://outlook.com"
            Square150x150LogoUri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACPtJREFUeNrsnVuIJFcZx/+nqu+90z3TO7szmWx2dzTqZjaaGAkkJnh50Bij5CVgVIQ86ItGENaHBYWALyqI7IOXF0FBUDHqQ5AoixAIhoSAMSxrBA1i4mZ3Nuvsbs/MTnd1XY7fOVXVl7l0ZkPPrFP9/8GhTnXVdPd0/+b7vnP6VI/SWoOQUePwJSAUi1AsQrEIoViEYhGKRQjFIhSLUCxCKBahWIRiEUKxCMUiFIsQikUoFqFYhFAsQrEIxSKEYhGKRSgWIRSLUCxCsQihWIRiEYpFCMUiFItQLEIoFqFYhGIRQrEIxSIUixCKRSgWoViEUCxCsQjFIoRiEYpFKBYhFItQLEKxCHlLcln6ZZRSb+vH1m13A520wRsz9I/fc2P8R6WSlpdWSKL3zsr10RMTcPMaf/rOf2WvIy3K7Iubpb+S64hYqVQF9dVnn1Ad76SzciUOIQP3oTZRTaUPNuS8eF9vCIQKulqDLhR/hFP3npAbvP7IxYiVnYhVcpYvn3SalzYRpl+Wdf3153U3w6SLb1PeKvS+xpclVH1TdgNp4WZpkcX73pXK/O45tba8+w/evpb+UTt90ZNiZUku5Xu7/8hBJxXLzaJUFOvGvqluVqPVuNdYVq7SwSNJTaQGt9JUWi9Jm71tOr7N7Duq11eD/ZnDNai+47bvON3jZt/0T/8qu1JRrHgsFpfOqm/fDulUrwg3/cGTCMUaSilqnu+N2gYiVzJ9oSTaFCeAaDKJVKBg4yZW/eTZn8rmsZHHtFYTweoVuBMNKO0yeI1h8f7YjrxIlUmEzUvQMprTOkIGp50o1o0ibK9Cm6mJKKRYFGt0ExNRS8TqeNBGLE2xKNaIxNKBSBX6YlgUe0W5OCrs5/hMAQ+8u9zdf/51D8+/1n4rr6xQEq7sB8Ws2ylWl1vqOXz/0/tx75HShmP/aQb44pOX8LeLnS3MUmi+sYKV4ApURUEVRETHjQN+/1SFnQB1Nkyabuh3dc1u1BuLVFgrOTj9pTl88GjZvsHLnsYLEqnONUO7f3gyj998YRa3TOa2jlgjmbfQO3CnFOuG8a2P70e97FqJnjxzDQvfex2P/HwR9/zgHH7y4oq93Rw/8aEpjNwsI1PS4o1O1l05FGuv8+CxqpXHRKivPXVp4NgTp5fwykXfHv/MnROje9BUqKRvZTJzYGEoG9kq16FYe5jbZ4vdaPXrM6ubnvPHf6zFHxhLM+lyY4mlrlOotBsLpSNpRqig10o333kU8bJoirVX66tUGjVk1JeeM5JUaGWK4q0IhTBIhOpti/tvfSoRa+fX2nNUOHpUf8TZUpyeVGo7hfcwobopUHfnu6IoToM2ckUyYJD+wY98uFaeWq4uPvP75b6nmplhYuYj1tlFD47j2Hb/fGXTc957U6l7znP/bm3m3bq6qb+tO2YmUEUeE6kiiUyR70vrIOr4CNttqMhH450zOPjw5yFSdbIasTIvVrMdWVlMRLr/HVV89v31geNm/6GFCXv8D39f3TLupaktXr+l+9xK9yN7jg7lPJPufEl7IpMRKmq1ZdvGxGwdjWPz6AQ5+F4IZHjN+1hMkH7j6Tfx7OPztv/DR+bwSRHJRDJT2BuprICtEN99ZmnLfBpcW0Jo1m51RNJ8CTqdII3DVVe6N172488TjYiBb9e375up4dB9tyFUObRWfbPEC47uLuzi0uQ9mw4vtPGV317Atx+asSPETx2vSeuLaiLV47+7YM/bogSLZZH0pqw0YZIf+***************************+ZEoXqocKVtvZZmx+Ujnly9dxdOvrOBzH5iUlFhBveRKmgzx53+t4Rd/uWr7Q4cAJgJFQTzCc8KB4WJ3jsrIJSnPRKmZ9x1FbX7OZEX4rSBefWoFjAMdxcpUvRXix88t2XbdQ8vAE0Ok1i5IRJKUpo0hOvms0EgVGOl8VA4VceCOBahSGZ4XJRdUyGnmdHshhrZyUSxi3Zm9awFL59rijmdmzcWMXK88kkjmyk033Xc3qkeO2sJcSerrlmBRIlTaV8kUBMWiWe7UNG695w5cffU8Fs/8U27IxwckWjUWjqFx+3FJew68tSAuv5yeRHEfveuvkf3lXBRrGxQqItFlyYSBg/1334UpkWjxxb/Cl9Raf88x5KcOoONpK5HqRqm+QWOEZEmNtlnT9JkK9xY/wygvqDAZzHWgmq/a+si0wJe6qVrHoU98DDrUWHlzTWr1MLmQtSeRSX0DdRUGI1jWxRrHrzEyb7/JY1Ozj379gh72DVXmCuZcEW5lAoXZeRRvfhdyE1NQxZI8lmsFcnMO2ssevBUvnn1wep87ximx11fJdYkq+bmzj84cho2F9uuMQp2hN2OsU2H+4BEMFUvymlMQsSRC5eoH5NUqdFeO6uQLsEwEy0uqzJfzaF1pSfQKBmqpgdoqiqXSDov3bP/yjbmhKcksM1b5ApxSFY7IpQolK5ZeV32n3VKjjEjSohHM3m9aV20yMtSKYmU3Yk0fGjo6U6YSd3OxXIWyTYtaQo/a4kpoI5OSFFc5UIW/2oHf8rccGeoIFCuruLXp4QtV0u9uMOlPBIO9UKKXBoe+sFUjo2sFi+ys/bqiHhQruyOXYmWbJ/ZWtujrmIQy0aswWUKw5iP0gvijnySCUaxsx6ztzzt0893beJGlsHeLOfjXOvGymrSop1jZZNuXno4ivEgazO8rIvJDiWAds8JrVPf8f8k4X2Lv+5fPnxpcDbrzzck7yNeKCJoXT5nnkNXYNa4TpCZSF6Xtk1bG7i4P1olMZvGX+fpkLxWME6R7H/PGmu9YX0veWHcXHzsVyyzq6iTPI3MpcRzF0n1iadyYixnSb28L+vqaYu19sZBEjGjjsG9Xn4fOolTjnAr1CMd7o3ouLN4J4XQDoViEYhFCsQjFIhSLEIpFKBahWIRQLEKxCMUihGIRikUoFiEUi1AsQrEIoViEYhGKRQjFIhSLUCxCKBahWIRiEUKxCMUiFIsQikUoFqFYhGLxJSAUi1AsQrEIoViEYhGKRQjFIhSLUCxCKBahWIRiEUKxCMUiFIuQkfA/AQYA81NuxwjWIl0AAAAASUVORK5CYII="
            Square71x71LogoUri="data:image/png;base64,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"
            ShowNameOnSquare150x150Logo="true"
            ShowNameOnWide310x150Logo="false"
            BackgroundColor="#404040"
            ForegroundText="light"/>
        <SecondaryTile
            AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
            TileID="7603651830"
            DisplayName="Word"
            Size="2x2"
            Row="0"
            Column="2"
            Arguments="-contentTile -url 0 https://word.office.com"
            Square150x150LogoUri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABUtJREFUeNrsnD1vHEUYgGf243K+2MQOdpQUkA6UmApouCIVUtKkoUECFxFSOiQKCiT+ARIFvSVIQYMiJKQ0UIBECluKlEhImI+GEEURgSTYsg323e3tMnPM7I3Xvg9LF+/d7vNIq/3wrDV3+/h935n1rkySRACMGo+vABALEAsQCwCxALEAsQAQCxALEAsAsQCxALEAEAsQCxALALEAsQCxABALEAsQCwCxALEAsQAQCxALEAsAsQCxALEAEAsQCxALALEAsQCxABALEAsQCwCxALEAsQAQCxALEAsAsQCxALEAEAsQCxALALEgH4IifRgp5dBNzTp8+YNP50/XLy/m0d+HKzfW7nz0zmO12VJLYo8nSTL516IIH+KQYkmzVC5/s7Pc2o2X2q12Lv31Q1+EVf/zGxerV9Vuo+vV5F+TQkWswziolmpjO1pq7bZy60TUiEQchUtq810TtWJqrMmWSn9uP2pGuXfG9CHU/XGiKWJN8Of2kzj/lGP64IpFxCpA5BoXChWtEGu8+iKL9OUGoswkQ9bKsnDXHbGeJjt/3h1KqqB2QgTHZ9WmZySDUol1+q1vP1OrK8O2//325lDtZhYa4tQLkQhnTgrpBchVwhrrytP4pVuPG6K58VDEUVNlz7aewsQcUuFoePTLb6L6JBBepSakHxK1GBWOZsgWN/5REauhgpW+/UPEQqwRmZXoNNhuKcNipVWCXGVNhb8uXxDP1AKx+W8kXrx6Mz2uj+mfWV7/8JZYu7ed7t/6pC6eW6hmzpOmtiJalT5irfy0noqkRbHUz8/taVc/N7dHOtvWnp/mwlQoxCq3WD9vpNuLZ2cckWYzYnX3X3LauecDYjlirDvCTB8YobIRbNFp554PiJWi6yZdJ2leO7dfnuWv76fpzx5zxXLrLsnUAmK5/GjksOnOjVYff3l3XxSzqZA0iFh9WXXSmY5G9fOze6LZWkY8G7FWs2mQgMV0w96R4YZ4/41uYW5Toq2f9FrLpIt7N5rp87Js/7UpGu0nwjsWCxmqkaO+IY1x5YxYK5mIZYt4G6lsytNTDJdene9TuCMQYvWYdnjzwplOoe6Ks3ZvK2136ZWFfUU7XiHWwDrLSnX/0W5nyW6nE6MHTDPgFWL1HBl2pxG2eqbLniNCphsQa38B31+c1cx+tj0wKjwQPa1w5u3vev78i5t/dBYgYh09ZELEwizEmpx6YarCl1DyGuuaGPEDFdL3xO6DH0Rz/YGII1/IyrSQXoVnDQd9byV8jZGO0vpdCXPVsxf7V+3SU79TyRRMCa86L7zaKSHD4+pwOFKxNr5/73m1+lv8/yqjdhHeY1Tqp3S8qWcH1lZShp17gzKcNs8U2nuERCvE6iVWZXawWFomv6KiVk2dEJqnoREHsfqJVT05eDSo06GWS6c/vTDeQayBNVk4Paho68rVSYE2WhGyEKvviO/YsC0dl5AKsQYPI4cXCxDrEGaNS0cK97BimSvRVhLtXM/dqObWV4X8ky3pBKmO1Po/+/RjOVNm/6gmp9xHqvUz+/oZtW2z6FdyM0E6wdiLumPWQQ55MTZL0yxxkVJiUFKpYhMppNmPjlisJBOxIrONWAURy17cPO7R2H60ne3CvB+pzKPCOCNWXpInooCvsilzjTVOQ/3CTTcUalQI4wN3VAGxALEAsQAQCxALEAsAsQCxALEAEAsQCxALALEAsQCxABALEAsQCwCxALEAsQAQCxALEAsAsQCxALEAEAsQCxALALEAsQCxABALEAsQCwCxALEAsQAQCxALEAsAsQCxALEAEAsQC4rHfwIMAF4veo56iMzcAAAAAElFTkSuQmCC"
            Square71x71LogoUri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEcAAABHCAYAAABVsFofAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA8ZJREFUeNrsms9PE0EUx99Mf1IpsRiIRJEY40EgFo0XOHjQgxw4eSHRs3+B8eZ/4N+hiQdvxsSDHosXoyg/NPGCxNioRKAr9sfujvPa6XaLbUU6u0zhvTDZ6bDd6X72O2/mvR0mhACy1sYJAcEhOASH4BAcgkNwCA7BISM4BIfgEByCQ3AIzmGxqI6LMMb2fKoqQZhQpbmxi0xnNIQHUIcRVyUoQEihrEpLUCbCQUvMPS+/KlulbFA5a1RvvD+x+PRGfEZ+LPqAma2ci/ceny9u/c46FSfQjlzHzcpDSpaKLLbpDhmHD3/3YD4fNBg01QcO3YiOoRsGnCCdcLt70tJvWA6ZCcdu+1+G94MzHmO6+jNnKt+L/frysY0j5cB4BGLpQYj2Z6p1UwBpgXPy1os38jDV6Zz19+WO10imHTiVdSWkE7oAGaOcqW4vULRKkH+9CImhc8AiMfnsD37xbkz4gOPAKRbAtUtyVetSbLWbjlMqgKjI9VsVjjhccO7MjsLXh9fgyf3LXtvMhUy1Dcv81RGvHc/BNvxOnY5wypKLjQGRCWz0wllesxSQ4w044436tK99cqy/6TuoHBxO1fDCkG0xWuHkVn969Ql189NSOX4VoY0OJWEgFf3rO2DYViHtPie3utkEAlWE6lj/XvSgTIylm879z7RH78JZUEpA5dTVs7RW8BQyM57xhtSCXzUGmvYVcm5lE+7eRJ+S9tSzoBSCDhmV5ClnZbNptrK+bUEZfgBPunKZE9cVThgEx6ecumNG5fj9Dg6v3e3hxqYHGFuhj0E4s1eGYHvH9mYkrE/4Zin8bDCbYBaB/hkot9KmvsvfGMgmKDgNX7L82WrZ7q+r6co4OExHTnfk9suuLxJJxMD68Aziw5PSIQ/u2yHnH10/Iw8bspRkcUUXN2hUbHUkhtW+2ES4SlOYQ0nXbPW2m5wO4xJIpQD29oYMPD8Bix2TbdEDB6UFjhznlzooE/vI9J2dy7chg7IBHh8AnhquZQENcc6h5ZAj6dPtnQ2Lyr8kcKkYSamWcDdgeIUGh/cNd3I4tWGEYI6icpgcNp1PYI1XNHDU4PC9dKUNipbMUNBTeX23Q0VOQ0vNLyJblW57c5ahtstCCyAtK+R/JKrwJRSG4ZinSIGm99gtDLPyuLsC45UdqG0mEKbvzxHqh1rqqfIAFCsUHEeFDU6v+Bz84bbvyAIIGOrycFQRvQQHFBgHwtv21jM7u4TvyELox5yUxWE12mpLcAgOwSE4BIfgEByCQ3DICA7BITgEh+AYaH8EGADmbyvtoskfJQAAAABJRU5ErkJggg=="
            ShowNameOnSquare150x150Logo="true"
            ShowNameOnWide310x150Logo="false"
            BackgroundColor="#404040"
            ForegroundText="light"/>
        <SecondaryTile
            AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
            TileID="6501008900"
            DisplayName="Excel"
            Size="2x2"
            Row="0"
            Column="4"
            Arguments="-contentTile -url 0 https://excel.office.com"
            Square150x150LogoUri="data:image/png;base64,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"
            Square71x71LogoUri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEcAAABHCAYAAABVsFofAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA/tJREFUeNrsm11IFFEUgM+dmV13/WG1B7MH7ZcwkG3tqcxABUOMiB4iFMLHiIhSyaA/KqoXIUKCIHqWwpeiiP7MF0F7ylKpEETWejAy/9dtd2eme69jK7azafPTrHsOHO7O3XHm3m/POffec69EVVVASSwCIkA4CAfhIByEg3AQDsJBOCgIB+EgHISDcBAOwlkrIpnxEELIim5bVlohqqbxCgOZTsmGH4BomqkpsQgQoxDSVFkOyclwsna+Ot8dnp71q8rK2+zbsmHlLxEEEHM8H3q2namglzNUZaOAJBvACEVnDxbP/Zjyy5Hoqv5YmV/d/WpU9tMij+q8GdZjR0AWg61PxlcL5l9EicRY4aXqMsN1BTsshwGyeZARzIhtkk0NJrCSWMNGPeOh2hQwto5Wka8T+r3JdIOYlQFEov0SRTMAEbNM0LDkXqx8R4tAsnvmQuP6X9LBV5xyQXZ+AZAM2jNJtHY2ZLPlBIw+QI5GYeL9ZyDZbiBuCkf4/3QctXxgQzcdjtm0FtdWf0iMTk1o4HbK2QZL4BwpqYJgy2Oue4pKftc37q3jdQOn23VMx1knPiyB0zHwBt5+GQSBTumbyut5XaEvH5r31fO65mdtiYfxdElZXOm8z1frZRv9ULN9NzRRMOy6d3QAXgz1WjP2pgqcwbFh6Ojv5ECuVR+Ho/5q/rnx6W39CWC6wGFy+fU9mImEoCivgLvTre52GJ0as3Da5sx5jk5/CbeWeDKMJFk5EHCNhUEKExA8MhCRwAQMrV3LaTvUDLneHBj8Nsw731JxDEoKtkKqmI5lcGqLy+DAjnLuTg0Pr0JPsJ9/vlFzImXcyhI4Pk823Dncwq3lQd9LGJ0cg1OPWvl1+eYA1AX2J3SrtIBzrrKBu9P0zzm48PwurwtSQAwUg3Cz9iQH6HTTIWacQ867VGX4IWKGG5SuEZB8XhqQ3Twghzdlrvo5k9e7dtFihOosW5CoBjromLWVE91KcgwcluRalsDzjITWRMzpMwZGABKKcVdykgGZYjnUz0uTwPewdWdOaeEnXTguEVQvS5W6F5JcJM3cSsrL1J9HM4uhgIQMieeRSdrB8Xn14TAYDBBzL6pOoWMHHD6UsuH5LwuxBSbmgFFTyXJU5jp2vSslFp5LGjuvyspHy18kKyzom7JPbtoMOckEjn3B9q2zqK6nug5M2sdO8AMwIFNUv2tlmKrs5PM5rGWy1lDW6BDE97KteE9EWzZENViOtpxF6xE1KC4wcS97WYxRNEAMTEyrV51+smvxV1W0hlt5sktdYjEpcbJracOtzk2YuvFF8F+nUyBlgXAQDsJBOAgHBeEgHISDcBAOwkE4CAfhoMTllwADANSdC/JpyiY3AAAAAElFTkSuQmCC"
            ShowNameOnSquare150x150Logo="true"
            ShowNameOnWide310x150Logo="false"
            BackgroundColor="#404040"
            ForegroundText="light"/>
        <SecondaryTile
            AppUserModelID="Microsoft.MicrosoftEdge_8wekyb3d8bbwe!MicrosoftEdge"
            TileID="38975140460"
            DisplayName="PowerPoint"
            Size="2x2"
            Row="2"
            Column="0"
            Arguments="-contentTile -url 0 https://powerpoint.office.com"
            Square150x150LogoUri="data:image/png;base64,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"
            Square71x71LogoUri="data:image/png;base64,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"
            ShowNameOnSquare150x150Logo="true"
            ShowNameOnWide310x150Logo="false"
            BackgroundColor="#404040"
            ForegroundText="light"/>
    </WebOfficeTiles>
</FullDefaultLayoutTemplate>
