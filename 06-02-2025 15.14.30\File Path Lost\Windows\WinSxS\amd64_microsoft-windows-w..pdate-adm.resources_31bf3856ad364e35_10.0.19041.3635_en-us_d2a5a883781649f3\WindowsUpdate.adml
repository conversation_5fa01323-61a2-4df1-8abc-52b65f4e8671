<?xml version="1.0" encoding="utf-8"?>
<!--  (c) 2006 Microsoft Corporation  -->
<!--
  Note that white space is preserved as is in the text shown in the Group Policy UI.
  Don't add extra line breaks at the beginning and end of text strings,
  and make sure that lines of text start in the FIRST column.
  -->
<policyDefinitionResources xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://schemas.microsoft.com/GroupPolicy/2006/07/PolicyDefinitions">
  <displayName>enter display name here</displayName>
  <description>enter description here</description>
  <resources>
    <stringTable>
      <string id="WU_SUPPORTED_Windows7ToXPSP2">Windows 7, Windows Server 2008 R2, Windows Vista, Windows XP SP2</string>
      <string id="WU_SUPPORTED_Windows7_To_Win2kSP3_Or_XPSP1">Windows 7, Windows Server 2008 R2, Windows Vista, Windows Server 2003, Windows XP SP2, Windows XP SP1 , Windows 2000 SP4, Windows 2000 SP3</string>
      <string id="WU_SUPPORTED_Win2kSP3_Or_XPSP1_NoWinRT">At least Windows XP Professional Service Pack 1 or Windows 2000 Service Pack 3, excluding Windows RT</string>
      <string id="WU_SUPPORTED_WindowsXPSP1_NoWinRT">At least Windows Server 2003 operating systems or Windows XP Professional with SP1, excluding Windows RT</string>
      <string id="WU_SUPPORTED_Win2kSP3_Or_XPSP1_Through_Win81_or_Server2012R2">At least Windows XP Professional Service Pack 1 or At least Windows 2000 Service Pack 3 through Windows 8.1 or Windows Server 2012 R2 with most current service pack. Not supported on Windows 10 and above.</string>
      <string id="WU_SUPPORTED_WindowsVista_Through_Win81_or_Server2012R2">At least Windows Vista through Windows 8.1 or Windows Server 2012 R2 with most current service pack. Not supported on Windows 10 and above.</string>
      <string id="WU_SUPPORTED_XPSP1_or_Win2kSP3_AUOption7_SUPPORTED_Server2016">Windows XP Professional Service Pack 1 or At least Windows 2000 Service Pack 3
Option 7 only supported on servers of at least Windows Server 2016 edition​</string>
      <string id="WU_SUPPORTED_Windows_Server_2019_Windows_10_0_1809">At least Windows Server 2019, or Windows 10 Version 1809​</string>
      <string id="WU_SUPPORTED_Windows_Server_2022_Windows_10_0_2004">At least Windows Server 2022, or Windows 10 Version 2004​</string>
      <string id="WU_SUPPORTED_Server2016_Through_Server2022_Or_Windows10">Windows Server 2016 through Windows Server 2022, or Windows 10​</string>
      <string id="AUDontShowUasHelp">This policy setting allows you to manage whether the 'Install Updates and Shut Down' option is displayed in the Shut Down Windows dialog box.

If you enable this policy setting, 'Install Updates and Shut Down' will not appear as a choice in the Shut Down Windows dialog box, even if updates are available for installation when the user selects the Shut Down option in the Start menu.

If you disable or do not configure this policy setting, the 'Install Updates and Shut Down' option will be available in the Shut Down Windows dialog box if updates are available when the user selects the Shut Down option in the Start menu.</string>
      <string id="AUDontShowUasPolicy">Do not display 'Install Updates and Shut Down' option in Shut Down Windows dialog box</string>
      <string id="AUNoUasDefaultHelp_Mach">This policy setting allows you to manage whether the 'Install Updates and Shut Down' option is allowed to be the default choice in the Shut Down Windows dialog.

If you enable this policy setting, the user's last shut down choice (Hibernate, Restart, etc.) is the default option in the Shut Down Windows dialog box, regardless of whether the 'Install Updates and Shut Down' option is available in the 'What do you want the computer to do?' list.

If you disable or do not configure this policy setting, the 'Install Updates and Shut Down' option will be the default option in the Shut Down Windows dialog box if updates are available for installation at the time the user selects the Shut Down option in the Start menu.

Note that this policy setting has no impact if the Computer Configuration\Administrative Templates\Windows Components\Windows Update\Do not display 'Install Updates and Shut Down' option in Shut Down Windows dialog box policy setting is enabled.</string>
      <string id="AUNoUasDefaultHelp_User">This policy setting allows you to manage whether the 'Install Updates and Shut Down' option is allowed to be the default choice in the Shut Down Windows dialog.

If you enable this policy setting, the user's last shut down choice (Hibernate, Restart, etc.) is the default option in the Shut Down Windows dialog box, regardless of whether the 'Install Updates and Shut Down' option is available in the 'What do you want the computer to do?' list.

If you disable or do not configure this policy setting, the 'Install Updates and Shut Down' option will be the default option in the Shut Down Windows dialog box if updates are available for installation at the time the user selects the Shut Down option in the Start menu.

Note that this policy setting has no impact if the User Configuration\Administrative Templates\Windows Components\Windows Update\Do not display 'Install Updates and Shut Down' option in Shut Down Windows dialog box policy setting is enabled.</string>
      <string id="AUNoUasDefaultPolicy">Do not adjust default option to 'Install Updates and Shut Down' in Shut Down Windows dialog box</string>
      <string id="RemoveWindowsUpdate">Remove access to use all Windows Update features</string>
      <string id="RemoveWindowsUpdate_Help">This setting allows you to remove access to Windows Update.

If you enable this setting, all Windows Update features are removed. This includes blocking access to the Windows Update Web site at http://windowsupdate.microsoft.com, from the Windows Update hyperlink on the Start menu, and also on the Tools menu in Internet Explorer. Windows automatic updating is also disabled; you will neither be notified about nor will you receive critical updates from Windows Update. This setting also prevents Device Manager from automatically installing driver updates from the Windows Update Web site.

If enabled you can configure one of the following notification options:

0 = Do not show any notifications

This setting will remove all access to Windows Update features and no notifications will be shown.

1 = Show restart required notifications

This setting will show notifications about restarts that are required to complete an installation.

On Windows 8 and Windows RT, if this policy is Enabled, then only notifications related to restarts and the inability to detect updates will be shown. The notification options are not supported. Notifications on the login screen will always show up.</string>
      <string id="RemoveWindowsUpdateModeAll">0 - Do not show any notifications</string>
      <string id="RemoveWindowsUpdateModeReboot">1 - Show restart required notifications</string>
      <string id="AutoUpdateCfg">Configure Automatic Updates</string>
      <string id="AutoUpdateCfg_Help">Specifies whether this computer will receive security updates and other important downloads through the Windows automatic updating service.

Note: This policy does not apply to Windows RT.

This setting lets you specify whether automatic updates are enabled on this computer. If the service is enabled, you must select one of the four options in the Group Policy Setting:

        2 = Notify before downloading and installing any updates.

        When Windows finds updates that apply to this computer, users will be notified that updates are ready to be downloaded. After going to Windows Update, users can download and install any available updates.

        3 = (Default setting) Download the updates automatically and notify when they are ready to be installed

        Windows finds updates that apply to the computer and downloads them in the background (the user is not notified or interrupted during this process). When the downloads are complete, users will be notified that they are ready to install. After going to Windows Update, users can install them.

        4 = Automatically download updates and install them on the schedule specified below.

        When "Automatic" is selected as the scheduled install time, Windows will automatically check, download, and install updates. The device will reboot as per Windows default settings unless configured by group policy. (Applies to Windows 10, version 1809 and higher)

        Specify the schedule using the options in the Group Policy Setting. For version 1709 and above, there is an additional choice of limiting updating to a weekly, bi-weekly, or monthly occurrence. If no schedule is specified, the default schedule for all installations will be every day at 3:00 AM. If any updates require a restart to complete the installation, Windows will restart the computer automatically. (If a user is signed in to the computer when Windows is ready to restart, the user will be notified and given the option to delay the restart.)

        On Windows 8 and later, you can set updates to install during automatic maintenance instead of a specific schedule. Automatic maintenance will install updates when the computer is not in use and avoid doing so when the computer is running on battery power. If automatic maintenance is unable to install updates for 2 days, Windows Update will install updates right away. Users will then be notified about an upcoming restart, and that restart will only take place if there is no potential for accidental data loss.

        5 = Allow local administrators to select the configuration mode that Automatic Updates should notify and install updates. (This option has not been carried over to any Win 10 Versions)

        With this option, local administrators will be allowed to use the Windows Update control panel to select a configuration option of their choice. Local administrators will not be allowed to disable the configuration for Automatic Updates.

        7 = Notify for install and notify for restart. (Windows Server only)

        With this option from Windows Server 2016, applicable only to Server SKU devices, local administrators will be allowed to use Windows Update to proceed with installations or reboots manually.

If the status for this policy is set to Disabled, any updates that are available on Windows Update must be downloaded and installed manually. To do this, search for Windows Update using Start.

If the status is set to Not Configured, use of Automatic Updates is not specified at the Group Policy level. However, an administrator can still configure Automatic Updates through Control Panel.</string>

      <string id="AutoUpdateModeNotifyToInstallNotifyToReboot">7 - Auto Download, Notify to install, Notify to Restart</string>
      <string id="AutoUpdateModeAdminChooses">5 - Allow local admin to choose setting</string>
      <string id="AutoUpdateModeAuto">4 - Auto download and schedule the install</string>
      <string id="AutoUpdateModeDownload">3 - Auto download and notify for install</string>
      <string id="AutoUpdateModeNotify">2 - Notify for download and auto install</string>
      <string id="AutoUpdateSchDay_Everyday">0 - Every day</string>
      <string id="AutoUpdateSchDay_Friday">6 - Every Friday</string>
      <string id="AutoUpdateSchDay_Monday">2 - Every Monday</string>
      <string id="AutoUpdateSchDay_Saturday">7 - Every Saturday</string>
      <string id="AutoUpdateSchDay_Sunday">1 - Every Sunday</string>
      <string id="AutoUpdateSchDay_Thursday">5 - Every Thursday</string>
      <string id="AutoUpdateSchDay_Tuesday">3 - Every Tuesday</string>
      <string id="AutoUpdateSchDay_Wednesday">4 - Every Wednesday</string>
      <string id="AutoUpdateSchTime0">00:00</string>
      <string id="AutoUpdateSchTime1">01:00</string>
      <string id="AutoUpdateSchTime10">10:00</string>
      <string id="AutoUpdateSchTime11">11:00</string>
      <string id="AutoUpdateSchTime12">12:00</string>
      <string id="AutoUpdateSchTime13">13:00</string>
      <string id="AutoUpdateSchTime14">14:00</string>
      <string id="AutoUpdateSchTime15">15:00</string>
      <string id="AutoUpdateSchTime16">16:00</string>
      <string id="AutoUpdateSchTime17">17:00</string>
      <string id="AutoUpdateSchTime18">18:00</string>
      <string id="AutoUpdateSchTime19">19:00</string>
      <string id="AutoUpdateSchTime2">02:00</string>
      <string id="AutoUpdateSchTime20">20:00</string>
      <string id="AutoUpdateSchTime21">21:00</string>
      <string id="AutoUpdateSchTime22">22:00</string>
      <string id="AutoUpdateSchTime23">23:00</string>
      <string id="AutoUpdateSchTime3">03:00</string>
      <string id="AutoUpdateSchTime4">04:00</string>
      <string id="AutoUpdateSchTime5">05:00</string>
      <string id="AutoUpdateSchTime6">06:00</string>
      <string id="AutoUpdateSchTime7">07:00</string>
      <string id="AutoUpdateSchTime8">08:00</string>
      <string id="AutoUpdateSchTime9">09:00</string>
      <string id="AutoUpdateSchTimeAuto">Automatic</string>
      <string id="CorpWuURL">Specify intranet Microsoft update service location</string>
      <string id="CorpWuURL_Help">
        Specifies an intranet server to host updates from Microsoft Update. You can then use this update service to automatically update computers on your network.

        This setting lets you specify a server on your network to function as an internal update service. The Automatic Updates client will search this service for updates that apply to the computers on your network.

        To use this setting, you must set two server name values: the server from which the Automatic Updates client detects and downloads updates, and the server to which updated workstations upload statistics. You can set both values to be the same server. An optional server name value can be specified to configure Windows Update Agent to download updates from an alternate download server instead of the intranet update service.

        If the status is set to Enabled, the Automatic Updates client connects to the specified intranet Microsoft update service (or alternate download server), instead of Windows Update, to search for and download updates. Enabling this setting means that end users in your organization don't have to go through a firewall to get updates, and it gives you the opportunity to test updates before deploying them.

        If the status is set to Disabled or Not Configured, and if Automatic Updates is not disabled by policy or user preference, the Automatic Updates client connects directly to the Windows Update site on the Internet.

        The alternate download server configures the Windows Update Agent to download files from an alternative download server instead of the intranet update service.

        The option to download files with missing Urls allows content to be downloaded from the Alternate Download Server when there are no download Urls for files in the update metadata. This option should only be used when the intranet update service does not provide download Urls in the update metadata for files which are present on the alternate download server.

        Note: If the "Configure Automatic Updates" policy is disabled, then this policy has no effect.

        Note: If the "Alternate Download Server" is not set, it will use the intranet update service by default to download updates.

        Note: The option to "Download files with no Url..." is only used if the "Alternate Download Server" is set.

        Note: This policy is not supported on Windows RT. Setting this policy will not have any effect on Windows RT PCs.

        To ensure the highest level of security, Microsoft recommends securing WSUS with TLS/SSL protocol, thereby using HTTPS based intranet servers to keep systems secure. If a proxy is required, we recommend configuring system proxy. To ensure highest levels of security, additionally leverage WSUS TLS certificate pinning on all devices.

        In order to keep clients inherently secure, we are no longer allowing intranet servers to leverage user proxy by default for detecting updates. If you need to leverage user proxy for detecting updates while using an intranet server despite the vulnerabilities it presents, you must configure the proxy behavior to "Allow user proxy to be used as a fallback if detection using system proxy fails".

        Detection for updates against intranet servers will fail when user proxy is needed as a fallback and the alternate proxy behavior is not configured.</string>
      <string id="OnlyUseSystemProxyForUpdateDetection">Only use system proxy for detecting updates (default)</string>
      <string id="AllowUserProxyAsFallbackForUpdateDetection">Allow user proxy to be used as a fallback if detection using system proxy fails</string>
      <string id="UpdateClassPolicySource_Title">Specify source service for specific classes of Windows Updates</string>
      <string id="UpdateClassPolicySource_Help">When this policy is enabled, devices will receive Windows updates for the classes listed from the specified update source: either Windows Update or Windows Server Update Service. 

        Note: To receive any updates from the Windows Server Update Service you must have properly configured an intranet Microsoft update service location via the "Specify intranet Microsoft update service location" policy.

        If this policy is not configured or is disabled, the device will continue to detect updates per your other policy configurations. 

        Note: If you are using "Do not allow deferral policies to cause scans against Windows Update" currently to ensure devices only scan against your specified server, we recommend configuring this policy instead or in addition to such.</string>
      <string id="UpdateClassPolicySourceSelectionWU">Windows Update</string>
      <string id="UpdateClassPolicySourceSelectionWSUS">Windows Server Update Services</string>
      <string id="DetectionFrequency_Help">Specifies the hours that Windows will use to determine how long to wait before checking for available updates. The exact wait time is a sum of the specific value and a random variant of 0-4 hours.

        If the status is set to Enabled, Windows will check for available updates at the specified interval.

        If the status is set to Disabled or Not Configured, Windows will check for available updates at the default interval of 22 hours.

        Note: The "Specify intranet Microsoft update service location" setting must be enabled for this policy to have effect.

        Note: If the "Configure Automatic Updates" policy is disabled, this policy has no effect.

        Note: This policy is not supported on Windows RT. Setting this policy will not have any effect on Windows RT PCs.</string>
      <string id="DetectionFrequency_Title">Automatic Updates detection frequency</string>
      <string id="ElevateNonAdmins_Help">This policy setting allows you to control whether non-administrative users will receive update notifications based on the "Configure Automatic Updates" policy setting.

If you enable this policy setting, Windows Automatic Update and Microsoft Update will include non-administrators when determining which logged-on user should receive update notifications. Non-administrative users will be able to install all optional, recommended, and important content for which they received a notification. Users will not see a User Account Control window and do not need elevated permissions to install these updates, except in the case of updates that contain User Interface , End User License Agreement , or Windows Update setting changes.

There are two situations where the effect of this setting depends on the operating system: Hide/Restore updates, and Cancel an install.

On XP: If you enable this policy setting, users will not see a User Account Control window and do not need elevated permissions to do either of these update-related tasks.

On Vista: If you enable this policy setting, users will not see a User Account Control window and do not need elevated permissions to do either of these tasks. If you do not enable this policy setting, then users will always see an Account Control window and require elevated permissions to do either of these tasks.

On Windows 7 : This policy setting has no effect. Users will always see an Account Control window and require elevated permissions to do either of these tasks.

On Windows 8 and Windows RT: This policy setting has no effect. Users will always see an Account Control window and require elevated permissions to do either of these tasks.

If you disable this policy setting, then only administrative users will receive update notifications.

Note: On Windows 8 and Windows RT this policy setting is enabled by default. In all prior versions of windows, it is disabled by default.

If the "Configure Automatic Updates" policy setting is disabled or is not configured, then the Elevate Non-Admin policy setting has no effect.</string>
      <string id="ElevateNonAdmins_Title">Allow non-administrators to receive update notifications</string>
      <string id="ImmediateInstall_Help">Specifies whether Automatic Updates should automatically install certain updates that neither interrupt Windows services nor restart Windows.

If the status is set to Enabled, Automatic Updates will immediately install these updates once they are downloaded and ready to install.

If the status is set to Disabled, such updates will not be installed immediately.

Note: If the "Configure Automatic Updates" policy is disabled, this policy has no effect.</string>
      <string id="ImmediateInstall_Title">Allow Automatic Updates immediate installation</string>
      <string id="IncludeRecommendedUpdates_Help">Specifies whether Automatic Updates will deliver both important as well as recommended updates from the Windows Update update service.

When this policy is enabled, Automatic Updates will install recommended updates as well as important updates from Windows Update update service.

When disabled or not configured Automatic Updates will continue to deliver important updates if it is already configured to do so.</string>
      <string id="IncludeRecommendedUpdates_Title">Turn on recommended updates via Automatic Updates</string>
      <string id="FeaturedSoftwareNotification_Help">This policy setting allows you to control whether users see detailed enhanced notification messages about featured software from the Microsoft Update service. Enhanced notification messages convey the value and promote the installation and use of optional software. This policy setting is intended for use in loosely managed environments in which you allow the end user access to the Microsoft Update service.

If you enable this policy setting, a notification message will appear on the user's computer when featured software is available. The user can click the notification to open the Windows Update Application and get more information about the software or install it. The user can also click "Close this message" or "Show me later" to defer the notification as appropriate.

In Windows 7, this policy setting will only control detailed notifications for optional applications. In Windows Vista, this policy setting controls detailed notifications for optional applications and updates.

If you disable or do not configure this policy setting, Windows 7 users will not be offered detailed notification messages for optional applications, and Windows Vista users will not be offered detailed notification messages for optional applications or updates.

By default, this policy setting is disabled.

If you are not using the Microsoft Update service, then the Software Notifications policy setting has no effect.

If the "Configure Automatic Updates" policy setting is disabled or is not configured, then the Software Notifications policy setting has no effect.
      </string>
      <string id="FeaturedSoftwareNotification_Title">Turn on Software Notifications</string>
      <string id="AUPowerManagement_Help">Specifies whether the Windows Update will use the Windows Power Management features to automatically wake up the system from sleep, if there are updates scheduled for installation.

Windows Update will only automatically wake up the system if Windows Update is configured to install updates automatically. If the system is in sleep when the scheduled install time occurs and there are updates to be applied, then Windows Update will use the Windows Power management features to automatically wake the system up to install the updates.

Windows update will also wake the system up and install an update if an install deadline occurs.

The system will not wake unless there are updates to be installed. If the system is on battery power, when Windows Update wakes it up, it will not install updates and the system will automatically return to sleep in 2 minutes.</string>
      <string id="AUPowerManagement_Title">Enabling Windows Update Power Management to automatically wake up the system to install scheduled updates</string>
      <string id="NoAutoRebootWithLoggedOnUsers_Help">Specifies that to complete a scheduled installation, Automatic Updates will wait for the computer to be restarted by any user who is logged on, instead of causing the computer to restart automatically.

If the status is set to Enabled, Automatic Updates will not restart a computer automatically during a scheduled installation if a user is logged in to the computer. Instead, Automatic Updates will notify the user to restart the computer.

Be aware that the computer needs to be restarted for the updates to take effect.

If the status is set to Disabled or Not Configured, Automatic Updates will notify the user that the computer will automatically restart in 5 minutes to complete the installation.

Note: This policy applies only when Automatic Updates is configured to perform scheduled installations of updates. If the "Configure Automatic Updates" policy is disabled, this policy has no effect.</string>
      <string id="NoAutoRebootWithLoggedOnUsers_Title">No auto-restart with logged on users for scheduled automatic updates installations</string>
      <string id="AlwaysAutoRebootAtScheduledTime_Title">Always automatically restart at the scheduled time</string>
      <string id="AlwaysAutoRebootAtScheduledTime_Help">If you enable this policy, a restart timer will always begin immediately after Windows Update installs important updates, instead of first notifying users on the login screen for at least two days.

The restart timer can be configured to start with any value from 15 to 180 minutes. When the timer runs out, the restart will proceed even if the PC has signed-in users.

If you disable or do not configure this policy, Windows Update will not alter its restart behavior.

If the "No auto-restart with logged on users for scheduled automatic updates installations" policy is enabled, then this policy has no effect.</string>

      <string id="NoAutoUpdate">Windows Automatic Updates</string>
      <string id="NoAutoUpdate_Help">This setting controls automatic updates to a user's computer.

Whenever a user connects to the Internet, Windows searches for updates available for the software and hardware on their computer and automatically downloads them. This happens in the background, and the user is prompted when downloaded components are ready to be installed, or prior to downloading, depending on their configuration.

If you enable this setting, it prohibits Windows from searching for updates.

If you disable or do not configure it, Windows searches for updates and automatically downloads them.

Note: Windows Update is an online catalog customized for your computer that consists of items such as drivers, critical updates, Help files, and Internet products that you can download to keep your computer up to date.

Also, see the "Remove links and access to Windows Update" setting. If the "Remove links and access to Windows Update" setting is enabled, the links to Windows Update on the Start menu are also removed.

Note: If you have installed Windows XP Service Pack 1 or the update to Automatic Updates that was released after Windows XP was originally shipped, then you should use the new Automatic Updates settings located at: 'Computer Configuration / Administrative Templates / Windows Update'</string>
      <string id="RebootRelaunchTimeout_Help">
        Specifies the amount of time for Automatic Updates to wait before prompting again with a scheduled restart.

        If the status is set to Enabled, a scheduled restart will occur the specified number of minutes after the previous prompt for restart was postponed.

        If the status is set to Disabled or Not Configured, the default interval is 10 minutes.

        Note: This policy applies only when Automatic Updates is configured to perform scheduled installations of updates. If the "Configure Automatic Updates" policy is disabled, this policy has no effect. This policy has no effect on Windows RT</string>
      <string id="RebootRelaunchTimeout_Title">Re-prompt for restart with scheduled installations</string>
      <string id="RebootWarningTimeout_Help">Specifies the amount of time for Automatic Updates to wait before proceeding with a scheduled restart.

If the status is set to Enabled, a scheduled restart will occur the specified number of minutes after the installation is finished.

If the status is set to Disabled or Not Configured, the default wait time is 15 minutes.

Note: This policy applies only when Automatic Updates is configured to perform scheduled installations of updates. If the "Configure Automatic Updates" policy is disabled, this policy has no effect.</string>
      <string id="RebootWarningTimeout_Title">Delay Restart for scheduled installations</string>
      <string id="RescheduleWaitTime_Help">Specifies the amount of time for Automatic Updates to wait, following system startup, before proceeding with a scheduled installation that was missed previously.

If the status is set to Enabled, a scheduled installation that did not take place earlier will occur the specified number of minutes after the computer is next started.

If the status is set to Disabled, a missed scheduled installation will occur with the next scheduled installation.

If the status is set to Not Configured, a missed scheduled installation will occur one minute after the computer is next started.

Note: This policy applies only when Automatic Updates is configured to perform scheduled installations of updates. If the "Configure Automatic Updates" policy is disabled, this policy has no effect.</string>
      <string id="RescheduleWaitTime_Title">Reschedule Automatic Updates scheduled installations</string>
      <string id="TargetGroup_Help">Specifies the target group name or names that should be used to receive updates from an intranet Microsoft update service.

If the status is set to Enabled, the specified target group information is sent to the intranet Microsoft update service which uses it to determine which updates should be deployed to this computer.

If the intranet Microsoft update service supports multiple target groups this policy can specify multiple group names separated by semicolons. Otherwise, a single group must be specified.

If the status is set to Disabled or Not Configured, no target group information will be sent to the intranet Microsoft update service.

Note: This policy applies only when the intranet Microsoft update service this computer is directed to is configured to support client-side targeting. If the "Specify intranet Microsoft update service location" policy is disabled or not configured, this policy has no effect.
Note: This policy is not supported on Windows RT. Setting this policy will not have any effect on Windows RT PCs.</string>
      <string id="TargetGroup_Title">Enable client-side targeting</string>
      <string id="WindowsUpdateCat">Windows Update</string>
      <string id="TrustedPublisher_Title">Allow signed updates from an intranet Microsoft update service location </string>
      <string id="TrustedPublisher_Help">This policy setting allows you to manage whether Automatic Updates accepts updates signed by entities other than Microsoft when the update is found on an intranet Microsoft update service location.

If you enable this policy setting, Automatic Updates accepts updates received through an intranet Microsoft update service location, if they are signed by a certificate found in the "Trusted Publishers" certificate store of the local computer.

If you disable or do not configure this policy setting, updates from an intranet Microsoft update service location must be signed by Microsoft.

Note: Updates from a service other than an intranet Microsoft update service must always be signed by Microsoft and are not affected by this policy setting.
Note: This policy is not supported on Windows RT. Setting this policy will not have any effect on Windows RT PCs.</string>
      <string id="DoNotConnectToWindowsUpdateInternetLocations_Title">Do not connect to any Windows Update Internet locations</string>
      <string id="DoNotConnectToWindowsUpdateInternetLocations_Help">Even when Windows Update is configured to receive updates from an intranet update service, it will periodically retrieve information from the public Windows Update service to enable future connections to Windows Update, and other services like Microsoft Update or the Windows Store.

Enabling this policy will disable that functionality, and may cause connection to public services such as the Windows Store to stop working.

Note: This policy applies only when this PC is configured to connect to an intranet update service using the "Specify intranet Microsoft update service location" policy.</string>
      <string id="DeferUpdateCat">Windows Update for Business</string>
      <string id="ManagePreviewBuilds_Title">Manage preview builds</string>
      <string id="ManagePreviewBuilds_Help">Enable this policy to manage which updates you receive prior to the update being released to the world.

Dev Channel
Ideal for highly technical users. Insiders in the Dev Channel will receive builds from our active development branch that is earliest in a development cycle. These builds are not matched to a specific Windows 10 release.

Beta Channel
Ideal for feature explorers who want to see upcoming Windows 10 features. Your feedback will be especially important here as it will help our engineers ensure key issues are fixed before a major release.

Release Preview Channel (default)
Insiders in the Release Preview Channel will have access to the upcoming release of Windows 10 prior to it being released to the world. These builds are supported by Microsoft. The Release Preview Channel is where we recommend companies preview and validate upcoming Windows 10 releases before broad deployment within their organization.

Release Preview Channel, Quality Updates Only
Ideal for those who want to validate the features and fixes coming soon to their current version. Note: released feature updates will continue to be offered in accordance with configured policies when this option is selected.

Note: Preview Build enrollment requires a telemetry level setting of 2 or higher and your domain registered on insider.windows.com. For additional information on Preview Builds, see: https://aka.ms/wipforbiz

If you disable or do not configure this policy, Windows Update will not offer you any pre-release updates and you will receive such content once released to the world. Disabling this policy will cause any devices currently on a pre-release build to opt out and stay on the latest Feature Update once released.</string>
      <string id="DevChannel">Dev Channel</string>
      <string id="BetaChannel">Beta Channel</string>
      <string id="ReleasePreviewChannel">Release Preview Channel</string>
      <string id="ReleasePreviewQualityOnly">Release Preview of Quality Updates Only</string>
      <string id="DeferFeatureUpdates_Title">Select when Preview Builds and Feature Updates are received</string>
      <string id="DeferFeatureUpdates_Help">Enable this policy to specify when to receive Feature Updates.

Defer Updates | This enables devices to defer taking the next Feature Update available to your channel for up to 14 days for all the pre-release channels and up to 365 days for the Semi-Annual Channel. Or, if the device is updating from the Semi-Annual Channel, a version for the device to move to and/or stay on until the policy is updated or the device reaches end of service can be specified. Note: If you set both policies, the version specified will take precedence and the deferrals will not be in effect. Please see the Windows Release Information page for OS version information.

Pause Updates | To prevent Feature Updates from being received on their scheduled time, you can temporarily pause Feature Updates. The pause will remain in effect for 35 days from the specified start date or until the field is cleared (Quality Updates will still be offered).</string>
      <string id="DeferQualityUpdates_Title">Select when Quality Updates are received</string>
      <string id="DeferQualityUpdates_Help">Enable this policy to specify when to receive quality updates.

You can defer receiving quality updates for up to 30 days.

To prevent quality updates from being received on their scheduled time, you can temporarily pause quality updates. The pause will remain in effect for 35 days or until you clear the start date field.

To resume receiving Quality Updates which are paused, clear the start date field.

If you disable or do not configure this policy, Windows Update will not alter its behavior.</string>
      <string id="TargetReleaseVersion_Title">Select the target Feature Update version</string>
      <string id="TargetReleaseVersion_Help">Enter the product and version as listed on the Windows Update target version page:

                        aka.ms/WindowsTargetVersioninfo

The device will request that Windows Update product and version in subsequent scans.

Entering a target product and clicking OK or Apply means I accept the Microsoft Software License Terms for it found at aka.ms/WindowsTargetVersioninfo. If an organization is licensing the software, I am authorized to bind the organization.

If you enter an invalid value, you will remain on your current version until you correct the values to a supported product and version.</string>
      <string id="DisableWUfBSafeguards">Disable safeguards for Feature Updates</string>
      <string id="DisableWUfBSafeguards_Help">
          Enable this setting when Feature Updates should be deployed to devices without blocking on any safeguard holds. Safeguard holds are known compatibility issues that block the upgrade from being deployed to affected devices until the issue is resolved. Enabling this policy can allow an organization to deploy the Feature Update to devices for testing, or to deploy the Feature Update without blocking on safeguard holds.
      </string>
      <string id="ExcludeWUDriversInQualityUpdate_Title">Do not include drivers with Windows Updates</string>
      <string id="ExcludeWUDriversInQualityUpdate_Help">Enable this policy to not include drivers with Windows quality updates.

If you disable or do not configure this policy, Windows Update will include updates that have a Driver classification.</string>
      <string id="ActiveHours_Title">Turn off auto-restart for updates during active hours</string>
      <string id="ActiveHours_Help">If you enable this policy, the PC will not automatically restart after updates during active hours. The PC will attempt to restart outside of active hours.

Note that the PC must restart for certain updates to take effect.

If you disable or do not configure this policy and have no other reboot group policies, the user selected active hours will be in effect.

If any of the following two policies are enabled, this policy has no effect:
    1. No auto-restart with logged on users for scheduled automatic updates installations.
    2. Always automatically restart at scheduled time.

Note that the default max active hours range is 18 hours from the active hours start time unless otherwise configured via the Specify active hours range for auto-restarts policy.</string>
      <string id="ActiveHoursTime10AM">10 AM</string>
      <string id="ActiveHoursTime10PM">10 PM</string>
      <string id="ActiveHoursTime11AM">11 AM</string>
      <string id="ActiveHoursTime11PM">11 PM</string>
      <string id="ActiveHoursTime12AM">12 AM</string>
      <string id="ActiveHoursTime12PM">12 PM</string>
      <string id="ActiveHoursTime1AM">1 AM</string>
      <string id="ActiveHoursTime1PM">1 PM</string>
      <string id="ActiveHoursTime2AM">2 AM</string>
      <string id="ActiveHoursTime2PM">2 PM</string>
      <string id="ActiveHoursTime3AM">3 AM</string>
      <string id="ActiveHoursTime3PM">3 PM</string>
      <string id="ActiveHoursTime4AM">4 AM</string>
      <string id="ActiveHoursTime4PM">4 PM</string>
      <string id="ActiveHoursTime5AM">5 AM</string>
      <string id="ActiveHoursTime5PM">5 PM</string>
      <string id="ActiveHoursTime6AM">6 AM</string>
      <string id="ActiveHoursTime6PM">6 PM</string>
      <string id="ActiveHoursTime7AM">7 AM</string>
      <string id="ActiveHoursTime7PM">7 PM</string>
      <string id="ActiveHoursTime8AM">8 AM</string>
      <string id="ActiveHoursTime8PM">8 PM</string>
      <string id="ActiveHoursTime9AM">9 AM</string>
      <string id="ActiveHoursTime9PM">9 PM</string>

      <string id="AutoRestartDeadline_Title">Specify deadline before auto-restart for update installation</string>
      <string id="AutoRestartDeadline_Help">Specify the deadline before the PC will automatically restart to apply updates. The deadline can be set 2 to 14 days past the default restart date.

The restart may happen inside active hours.

If you disable or do not configure this policy, the PC will restart according to the default schedule.

Enabling either of the following two policies will override the above policy:
    1. No auto-restart with logged on users for scheduled automatic updates installations.
    2. Always automatically restart at scheduled time.
      </string>
      <string id="AutoRestartDeadlineDay2">2</string>
      <string id="AutoRestartDeadlineDay3">3</string>
      <string id="AutoRestartDeadlineDay4">4</string>
      <string id="AutoRestartDeadlineDay5">5</string>
      <string id="AutoRestartDeadlineDay6">6</string>
      <string id="AutoRestartDeadlineDay7">7</string>
      <string id="AutoRestartDeadlineDay8">8</string>
      <string id="AutoRestartDeadlineDay9">9</string>
      <string id="AutoRestartDeadlineDay10">10</string>
      <string id="AutoRestartDeadlineDay11">11</string>
      <string id="AutoRestartDeadlineDay12">12</string>
      <string id="AutoRestartDeadlineDay13">13</string>
      <string id="AutoRestartDeadlineDay14">14</string>
      <string id="AutoRestartDeadlineDay15">15</string>
      <string id="AutoRestartDeadlineDay16">16</string>
      <string id="AutoRestartDeadlineDay17">17</string>
      <string id="AutoRestartDeadlineDay18">18</string>
      <string id="AutoRestartDeadlineDay19">19</string>
      <string id="AutoRestartDeadlineDay20">20</string>
      <string id="AutoRestartDeadlineDay21">21</string>
      <string id="AutoRestartDeadlineDay22">22</string>
      <string id="AutoRestartDeadlineDay23">23</string>
      <string id="AutoRestartDeadlineDay24">24</string>
      <string id="AutoRestartDeadlineDay25">25</string>
      <string id="AutoRestartDeadlineDay26">26</string>
      <string id="AutoRestartDeadlineDay27">27</string>
      <string id="AutoRestartDeadlineDay28">28</string>
      <string id="AutoRestartDeadlineDay29">29</string>
      <string id="AutoRestartDeadlineDay30">30</string>

      <string id="DisableUXWUAccess_Title">Remove access to use all Windows Update features</string>
      <string id="DisableUXWUAccess_Help">This setting allows you to remove access to scan Windows Update.

If you enable this setting user access to Windows Update scan, download and install is removed.
      </string>

      <string id="DisablePauseUXAccess_Title">Remove access to "Pause updates" feature</string>
      <string id="DisablePauseUXAccess_Help">This setting allows to remove access to "Pause updates" feature.

Once enabled user access to pause updates is removed.
      </string>

      <string id="ActiveHoursMaxRange_Title">Specify active hours range for auto-restarts</string>
      <string id="ActiveHoursMaxRange_Help">Enable this policy to specify the maximum number of hours from the start time that users can set their active hours.

The max active hours range can be set between 8 and 18 hours.

If you disable or do not configure this policy, the default max active hours range will be used.
      </string>
      <string id="ActiveHoursMaxRange8">8</string>
      <string id="ActiveHoursMaxRange9">9</string>
      <string id="ActiveHoursMaxRange10">10</string>
      <string id="ActiveHoursMaxRange11">11</string>
      <string id="ActiveHoursMaxRange12">12</string>
      <string id="ActiveHoursMaxRange13">13</string>
      <string id="ActiveHoursMaxRange14">14</string>
      <string id="ActiveHoursMaxRange15">15</string>
      <string id="ActiveHoursMaxRange16">16</string>
      <string id="ActiveHoursMaxRange17">17</string>
      <string id="ActiveHoursMaxRange18">18</string>

      <string id="AutoRestartRequiredNotificationDismissal_Title">Configure auto-restart required notification for updates</string>
      <string id="AutoRestartRequiredNotificationDismissal_Help">Enable this policy to specify the method by which the auto-restart required notification is dismissed. When a restart is required to install updates, the auto-restart required notification is displayed. By default, the notification is automatically dismissed after 25 seconds.

The method can be set to require user action to dismiss the notification.

If you disable or do not configure this policy, the default method will be used.
      </string>
      <string id="AutoDismissal">1 - Auto</string>
      <string id="UserAction">2 - User Action</string>

      <string id="AutoRestartNotificationConfig_Title">Configure auto-restart reminder notifications for updates</string>
      <string id="AutoRestartNotificationConfig_Help">Enable this policy to specify when auto-restart reminders are displayed.

You can specify the amount of time prior to a scheduled restart to notify the user.

If you disable or do not configure this policy, the default period will be used.
      </string>
      <string id="AutoRestartNotificationSchd15">15</string>
      <string id="AutoRestartNotificationSchd30">30</string>
      <string id="AutoRestartNotificationSchd60">60</string>
      <string id="AutoRestartNotificationSchd120">120</string>
      <string id="AutoRestartNotificationSchd240">240</string>

      <string id="AutoRestartNotificationDisable_Title">Turn off auto-restart notifications for update installations</string>
      <string id="AutoRestartNotificationDisable_Help">This policy setting allows you to control whether users receive notifications for auto restarts for update installations including reminder and warning notifications.

Enable this policy to turn off all auto restart notifications.

If you disable or do not configure this policy, the default notification behaviors will be unchanged.
      </string>

      <string id="RestartWarningSchd_Title">Configure auto-restart warning notifications schedule for updates</string>
      <string id="RestartWarningSchd_Help">Enable this policy to control when notifications are displayed to warn users about a scheduled restart for the update installation deadline. Users are not able to postpone the scheduled restart once the deadline has been reached and the restart is automatically executed.

Specifies the amount of time prior to a scheduled restart to display the warning reminder to the user.

You can specify the amount of time prior to a scheduled restart to notify the user that the auto restart is imminent to allow them time to save their work.

If you disable or do not configure this policy, the default notification behaviors will be used.
      </string>
      <string id="RestartWarnRemindHours2">2</string>
      <string id="RestartWarnRemindHours4">4</string>
      <string id="RestartWarnRemindHours8">8</string>
      <string id="RestartWarnRemindHours12">12</string>
      <string id="RestartWarnRemindHours24">24</string>
      <string id="RestartWarnMins15">15</string>
      <string id="RestartWarnMins30">30</string>
      <string id="RestartWarnMins60">60</string>
      <string id="EngagedRestartTransitionSchedule_Title">Specify Engaged restart transition and notification schedule for updates</string>
      <string id="EngagedRestartTransitionSchedule_Help">Enable this policy to control the timing before transitioning from Auto restarts scheduled outside of active hours to Engaged restart, which requires the user to schedule. The period can be set between 0 and 30 days from the time the restart becomes pending.

You can specify the number of days a user can snooze Engaged restart reminder notifications. The snooze period can be set between 1 and 3 days.

You can specify the deadline in days before automatically scheduling and executing a pending restart regardless of active hours. The deadline can be set between 2 and 30 days from the time the restart becomes pending. If configured, the pending restart will transition from Auto-restart to Engaged restart (pending user schedule) to automatically executed, within the specified period.

If you do not specify a deadline or if the deadline is set to 0, the PC won't automatically restart and will require the person to schedule it prior to restart.

If you disable or do not configure this policy, the PC will restart following the default schedule.

Enabling any of the following policies will override the above policy:
    1. No auto-restart with logged on users for scheduled automatic updates installations
    2. Always automatically restart at scheduled time
    3. Specify deadline before auto-restart for update installation
      </string>
      <string id="EngagedRestart_0">0</string>
      <string id="EngagedRestart_1">1</string>
      <string id="EngagedRestart_2">2</string>
      <string id="EngagedRestart_3">3</string>
      <string id="EngagedRestart_4">4</string>
      <string id="EngagedRestart_5">5</string>
      <string id="EngagedRestart_6">6</string>
      <string id="EngagedRestart_7">7</string>
      <string id="EngagedRestart_8">8</string>
      <string id="EngagedRestart_9">9</string>
      <string id="EngagedRestart_10">10</string>
      <string id="EngagedRestart_11">11</string>
      <string id="EngagedRestart_12">12</string>
      <string id="EngagedRestart_13">13</string>
      <string id="EngagedRestart_14">14</string>
      <string id="EngagedRestart_15">15</string>
      <string id="EngagedRestart_16">16</string>
      <string id="EngagedRestart_17">17</string>
      <string id="EngagedRestart_18">18</string>
      <string id="EngagedRestart_19">19</string>
      <string id="EngagedRestart_20">20</string>
      <string id="EngagedRestart_21">21</string>
      <string id="EngagedRestart_22">22</string>
      <string id="EngagedRestart_23">23</string>
      <string id="EngagedRestart_24">24</string>
      <string id="EngagedRestart_25">25</string>
      <string id="EngagedRestart_26">26</string>
      <string id="EngagedRestart_27">27</string>
      <string id="EngagedRestart_28">28</string>
      <string id="EngagedRestart_29">29</string>
      <string id="EngagedRestart_30">30</string>
      <string id="EngagedRestartSnooze_1">1</string>
      <string id="EngagedRestartSnooze_2">2</string>
      <string id="EngagedRestartSnooze_3">3</string>

      <string id="SetEDURestart_Title">Update Power Policy for Cart Restarts</string>
      <string id="SetEDURestart_Help">Enabling this policy for EDU devices that remain on Carts overnight will skip power checks to ensure update reboots will happen at the scheduled install time.</string>

      <string id="AllowAutoWindowsUpdateDownloadOverMeteredNetwork_Title">Allow updates to be downloaded automatically over metered connections</string>
      <string id="AllowAutoWindowsUpdateDownloadOverMeteredNetwork_Help">Enabling this policy will automatically download updates, even over metered data connections (charges may apply)</string>

      <string id="DisableDualScan_Title">Do not allow update deferral policies to cause scans against Windows Update</string>
      <string id="DisableDualScan_Help">Enable this policy to not allow update deferral policies to cause scans against Windows Update.

If this policy is disabled or not configured, then the Windows Update client may initiate automatic scans against Windows Update while update deferral policies are enabled.
Note: This policy applies only when the intranet Microsoft update service this computer is directed to is configured to support client-side targeting. If the "Specify intranet Microsoft update service location" policy is disabled or not configured, this policy has no effect.
      </string>
      <string id="UpdateNotificationLevel_Title">Display options for update notifications</string>
      <string id="UpdateNotificationLevel_Help">0 (default) – Use the default Windows Update notifications
1 – Turn off all notifications, excluding restart warnings
2 – Turn off all notifications, including restart warnings

This policy allows you to define what Windows Update notifications users see. This policy doesn’t control how and when updates are downloaded and installed.

Important: if you choose not to get update notifications and also define other Group policy so that devices aren’t automatically getting updates, neither you nor device users will be aware of critical security, quality, or feature updates, and your devices may be at risk.
      </string>
      <string id="DefaultNotification">0 (default) – Default OS Windows Update notifications</string>
      <string id="ExcludingRebootWarnings">1 – Disable all notifications, excluding restart warnings</string>
      <string id="IncludingRebootWarnings">2 – Disable all notifications, including restart warnings</string>
      <string id="ComplianceDeadline_Title">Specify deadlines for automatic updates and restarts</string>
      <string id="ComplianceDeadline_Help">This policy lets you specify the number of days before quality and feature updates are installed on devices automatically, and a grace period after which required restarts occur automatically.

Set deadlines for feature updates and quality updates to meet your compliance goals. Updates will be downloaded and installed as soon as they are offered and automatic restarts will be attempted outside of active hours. Once the deadline has passed, restarts will occur regardless of active hours, and users will not be able to reschedule. If the deadline is set to 0 days, the update will be installed immediately upon offering, but might not finish within the day due to device availability and network connectivity.

Set a grace period for feature updates and quality updates to guarantee users a minimum time to manage their restarts once updates are installed. Users will be able to schedule restarts during the grace period and Windows can still automatically restart outside of active hours if users choose not to schedule restarts. The grace period might not take effect if users already have more than the number of days set as grace period to manage their restart, based on deadline configurations.

You can set the device to delay restarting until both the deadline and grace period have expired.

If you disable or do not configure this policy, devices will get updates and will restart according to the default schedule.

This policy will override the following policies:
1.  Specify deadline before auto restart for update installation
2.  Specify Engaged restart transition and notification schedule for updates
3.  Always automatically restart at the scheduled time
4.  Configure Automatic Updates
      </string>
      <string id="ComplianceDeadlineDay0">0</string>
      <string id="ComplianceDeadlineDay1">1</string>
      <string id="ComplianceDeadlineDay2">2</string>
      <string id="ComplianceDeadlineDay3">3</string>
      <string id="ComplianceDeadlineDay4">4</string>
      <string id="ComplianceDeadlineDay5">5</string>
      <string id="ComplianceDeadlineDay6">6</string>
      <string id="ComplianceDeadlineDay7">7</string>
      <string id="ComplianceDeadlineDay8">8</string>
      <string id="ComplianceDeadlineDay9">9</string>
      <string id="ComplianceDeadlineDay10">10</string>
      <string id="ComplianceDeadlineDay11">11</string>
      <string id="ComplianceDeadlineDay12">12</string>
      <string id="ComplianceDeadlineDay13">13</string>
      <string id="ComplianceDeadlineDay14">14</string>
      <string id="ComplianceDeadlineDay15">15</string>
      <string id="ComplianceDeadlineDay16">16</string>
      <string id="ComplianceDeadlineDay17">17</string>
      <string id="ComplianceDeadlineDay18">18</string>
      <string id="ComplianceDeadlineDay19">19</string>
      <string id="ComplianceDeadlineDay20">20</string>
      <string id="ComplianceDeadlineDay21">21</string>
      <string id="ComplianceDeadlineDay22">22</string>
      <string id="ComplianceDeadlineDay23">23</string>
      <string id="ComplianceDeadlineDay24">24</string>
      <string id="ComplianceDeadlineDay25">25</string>
      <string id="ComplianceDeadlineDay26">26</string>
      <string id="ComplianceDeadlineDay27">27</string>
      <string id="ComplianceDeadlineDay28">28</string>
      <string id="ComplianceDeadlineDay29">29</string>
      <string id="ComplianceDeadlineDay30">30</string>
    </stringTable>

    <presentationTable>
      <presentation id="RemoveWindowsUpdate">
        <dropdownList refId="RemoveWindowsUpdateMode" defaultItem="0">Configure notifications:</dropdownList>
      </presentation>
      <presentation id="AutoUpdateCfg">
        <dropdownList refId="AutoUpdateMode" defaultItem="1">Configure automatic updating:</dropdownList>
        <text>The following settings are only required and applicable if 4 is selected.</text>
        <checkBox refId="AutoUpdateAutomaticMaintenanceEnabled" defaultChecked="false">Install during automatic maintenance</checkBox>
        <dropdownList refId="AutoUpdateSchDay" defaultItem="0">Scheduled install day: </dropdownList>
        <dropdownList refId="AutoUpdateSchTime" defaultItem="3">Scheduled install time:</dropdownList>
        <text>If you have selected "4 – Auto download and schedule the install" for your scheduled install day and specified a schedule, you also have the option to limit updating to a weekly, bi-weekly or monthly occurrence, using the options below:</text>
        <checkBox refId="AutoUpdateSchEveryWeek" defaultChecked="true">Every week</checkBox>
        <checkBox refId="AutoUpdateSchFirstWeek" defaultChecked="false">First week of the month</checkBox>
        <checkBox refId="AutoUpdateSchSecondWeek" defaultChecked="false">Second week of the month</checkBox>
        <checkBox refId="AutoUpdateSchThirdWeek" defaultChecked="false">Third week of the month</checkBox>
        <checkBox refId="AutoUpdateSchFourthWeek" defaultChecked="false">Fourth week of the month</checkBox>
        <text> </text>
        <checkBox refId="AllowMUUpdateServiceId" defaultChecked="false">Install updates for other Microsoft products</checkBox>
      </presentation>
      <presentation id="CorpWuURL">
        <textBox refId="CorpWUURL_Name">
          <label>Set the intranet update service for detecting updates:</label>
        </textBox>
        <textBox refId="CorpWUStatusURL_Name">
          <label>Set the intranet statistics server:</label>
        </textBox>
        <textBox refId="CorpWUContentHost_Name">
          <label>Set the alternate download server:</label>
        </textBox>
        <text>(example: https://IntranetUpd01)</text>
        <checkBox refId="CorpWUFillEmptyContentUrls" defaultChecked="false">Download files with no Url in the metadata if alternate download server is set.</checkBox>
        <checkBox refId="CorpWUDoNotEnforceEnterpriseTLSCertPinningForUpdateDetection" defaultChecked="false">Do not enforce TLS certificate pinning for Windows Update client for detecting updates.</checkBox>
        <dropdownList refId="SetProxyBehaviorForUpdateDetection" noSort="true" defaultItem="0">Select the proxy behavior for Windows Update client for detecting updates:</dropdownList>
      </presentation>
      <presentation id="UpdateClassPolicySource_Title">
        <text>Specify source service for the following classes of Windows updates:</text>
        <dropdownList refId="CorpWUSetPolicyDrivenUpdateSourceForFeatureUpdates" noSort="true" defaultItem="0">Feature Updates</dropdownList>
        <dropdownList refId="CorpWUSetPolicyDrivenUpdateSourceForQualityUpdates" noSort="true" defaultItem="0">Quality Updates</dropdownList>
        <dropdownList refId="CorpWUSetPolicyDrivenUpdateSourceForDriverUpdates" noSort="true" defaultItem="0">Driver Updates</dropdownList>
        <dropdownList refId="CorpWUSetPolicyDrivenUpdateSourceForOtherUpdates" noSort="true" defaultItem="0">Other Updates</dropdownList>
      </presentation>
      <presentation id="DetectionFrequency_Title">
        <text>Check for updates at the following</text>
        <decimalTextBox refId="DetectionFrequency_Hour2" defaultValue="22">interval (hours): </decimalTextBox>
      </presentation>
      <presentation id="RebootRelaunchTimeout_Title">
        <text>Wait the following period before</text>
        <text>prompting again with a scheduled</text>
        <decimalTextBox refId="RebootRelaunchTimeout_Minutes3" defaultValue="10">restart (minutes): </decimalTextBox>
      </presentation>
      <presentation id="RebootWarningTimeout_Title">
        <text>Wait the following period before</text>
        <text>proceeding with a scheduled</text>
        <decimalTextBox refId="RebootWarningTimeout_Minutes3" defaultValue="5">restart (minutes): </decimalTextBox>
      </presentation>
      <presentation id="RescheduleWaitTime_Title">
        <text>Wait after system</text>
        <decimalTextBox refId="RescheduleWaitTime_Minutes2">startup (minutes): </decimalTextBox>
      </presentation>
      <presentation id="TargetGroup_Title">
        <textBox refId="TargetGroup_Name">
          <label>Target group name for this computer</label>
        </textBox>
      </presentation>
      <presentation id="IdleShutdownTimerDuration_Title">
        <text>Wait the following period before</text>
        <text>shutting down the service when</text>
        <decimalTextBox refId="IdleShutdownTimerDuration_Minutes" defaultValue="10">idle (minutes): </decimalTextBox>
      </presentation>
      <presentation id="AlwaysAutoRebootAtScheduledTime_Presentation">
        <text>The restart timer will give users</text>
        <text>this much time to save their</text>
        <decimalTextBox refId="AlwaysAutoRebootAtScheduledTime_Minutes" defaultValue="15">work (minutes): </decimalTextBox>
      </presentation>
      <presentation id="TargetReleaseVersion">
        <textBox refId="ProductVersionId">
          <label>Which Windows product version would you like to receive feature updates for? e.g., Windows 10</label>
        </textBox>
        <textBox refId="TargetReleaseVersionId">
          <label>Target Version for Feature Updates</label>
        </textBox>
      </presentation>
      <presentation id="ManagePreviewBuilds">
        <dropdownList refId="BranchReadinessLevelId" noSort="true" defaultItem="0">Select the Windows readiness level for the updates you want to receive:</dropdownList>
      </presentation>
      <presentation id="DeferFeatureUpdates">
       <decimalTextBox refId="DeferFeatureUpdatesPeriodId" defaultValue="0">How many days after a Feature Update is released would you like to defer the update before it is offered to the device?</decimalTextBox>
        <textBox refId="PauseFeatureUpdatesStartId">
          <label>Pause Preview Builds or Feature Updates starting:</label>
        </textBox>
        <text>(format yyyy-mm-dd example: 2016-10-30)</text>
      </presentation>
      <presentation id="DeferQualityUpdates">
        <decimalTextBox refId="DeferQualityUpdatesPeriodId" defaultValue="0">After a quality update is released, defer receiving it for this many days:</decimalTextBox>
        <textBox refId="PauseQualityUpdatesStartId">
          <label>Pause Quality Updates starting</label>
        </textBox>
        <text>(format yyyy-mm-dd example: 2016-10-30)</text>
      </presentation>
      <presentation id="ActiveHours_Title">
        <text>Active Hours</text>
        <dropdownList refId="ActiveHoursStartTime" noSort="true" defaultItem="8">Start: </dropdownList>
        <dropdownList refId="ActiveHoursEndTime" noSort="true" defaultItem="17">End:</dropdownList>
      </presentation>
      <presentation id="AutoRestartDeadline_Title">
        <text>Specify the number of days before a pending restart will automatically be executed outside of active hours: </text>
        <dropdownList refId="AutoRestartDeadline" noSort="true" defaultItem="5">Quality Updates (days): </dropdownList>
        <dropdownList refId="AutoRestartDeadlineForFeatureUpdates" noSort="true" defaultItem="5">Feature Updates (days): </dropdownList>
      </presentation>
      <presentation id="ActiveHoursMaxRange_Title">
        <text>Specify the max active hours range:</text>
        <dropdownList refId="ActiveHoursMaxRange" noSort="true" defaultItem="10">Max range: </dropdownList>
      </presentation>
      <presentation id="AutoRestartRequiredNotificationDismissal_Title">
        <text>Specify the method by which the auto-restart required notification is dismissed:</text>
        <dropdownList refId="AutoRestartRequiredNotificationDismissal" noSort="true" defaultItem="0">Method: </dropdownList>
      </presentation>
      <presentation id="AutoRestartNotificationConfig_Title">
        <text>Specify the period for auto-restart reminder notifications:</text>
        <dropdownList refId="AutoRestartNotificationSchd" noSort="true" defaultItem="0">Period (min): </dropdownList>
      </presentation>
      <presentation id="RestartWarningSchd_Title">
        <text>Specify the period for auto-restart warning reminder notifications:</text>
        <dropdownList refId="RestartWarnRemind" noSort="true" defaultItem="1">Reminder (hours): </dropdownList>
        <text>Specify the period for auto-restart immiment warning notifications:</text>
        <dropdownList refId="RestartWarn" noSort="true" defaultItem="0">Warning (mins): </dropdownList>
      </presentation>
      <presentation id="EngagedRestartTransitionSchedule_Title">
        <text>Transition : Specify the timing before transitioning from Auto-restart to Engaged restart (pending user schedule).</text>
        <text>Snooze : Specify snooze for Engaged restart reminder notifications.</text>
        <text>Deadline : Specify the deadline before a pending restart will automatically be executed outside of active hours.</text>
        <text></text>
        <text>For Quality Updates : </text>
        <dropdownList refId="EngagedRestartTransitionSchedule" noSort="true" defaultItem="7">Transition (days) : </dropdownList>
        <dropdownList refId="EngagedRestartSnoozeSchedule" noSort="true" defaultItem="2">Snooze (days) :     </dropdownList>
        <dropdownList refId="EngagedRestartDeadline" noSort="true" defaultItem="0">Deadline (days) :  </dropdownList>
        <text></text>
        <text>For Feature Updates : </text>
        <dropdownList refId="EngagedRestartTransitionScheduleForFeatureUpdates" noSort="true" defaultItem="7">Transition (days) : </dropdownList>
        <dropdownList refId="EngagedRestartSnoozeScheduleForFeatureUpdates" noSort="true" defaultItem="2">Snooze (days) :     </dropdownList>
        <dropdownList refId="EngagedRestartDeadlineForFeatureUpdates" noSort="true" defaultItem="0">Deadline (days) :  </dropdownList>
      </presentation>
      <presentation id="UpdateNotificationLevel_Title">
        <dropdownList refId="UpdateNotificationLevel" noSort="true" defaultItem="0">Specify the update notifications display options :</dropdownList>
      </presentation>
      <presentation id="ComplianceDeadline_Title">
        <text>Quality Updates</text>
        <dropdownList refId="ConfigureDeadlineForQualityUpdates" noSort="true" defaultItem="7">Deadline (days):</dropdownList>
        <dropdownList refId="ConfigureDeadlineGracePeriod" noSort="true" defaultItem="2">Grace period (days):</dropdownList>
        <text></text>
        <text>Feature Updates</text>
        <dropdownList refId="ConfigureDeadlineForFeatureUpdates" noSort="true" defaultItem="7">Deadline (days):</dropdownList>
        <dropdownList refId="ConfigureDeadlineGracePeriodForFeatureUpdates" noSort="true" defaultItem="2">Grace Period (days):</dropdownList>
        <text></text>
        <checkBox refId="ConfigureDeadlineNoAutoReboot" defaultChecked="false">Don't auto-restart until end of grace period</checkBox>
      </presentation>
    </presentationTable>
  </resources>
</policyDefinitionResources>
