[Device Install Log]
     OS Version = 10.0.19045
     Service Pack = 0.0
     Suite = 0x0100
     ProductType = 1
     Architecture = amd64

[BeginLog]

[Boot Session: 2024/09/09 08:45:17.500]

>>>  [Sysprep Specialize - {4c4c4544-0050-4e10-8044-c7c04f573132}]
>>>  Section start 2024/09/09 08:45:31.223
     set: System Information:
     set:      BIOS Release Date: 10/24/2018
     set:      BIOS Vendor: Dell Inc.
     set:      BIOS Version: A24
     set:      System Manufacturer: Dell Inc.
     set:      System Product Name: OptiPlex 9020
     set:      System SKU: 05A4
     set:      System Version: 00
     set: Initialized PnP data. Time = 63 ms. 08:45:31.301
     dvi: {DIF_REMOVE} 08:45:31.317
     dvi:      Default installer: Enter 08:45:31.317
     dvi:           {Remove DEVICE}
     dvi:                {Delete Device - SWD\PRINTENUM\PRINTQUEUES}
     dvi:                {Delete Device - exit(0x00000000)}
     dvi:           {Remove DEVICE exit (0x00000000)}
     dvi:      Default installer: Exit
     dvi: {DIF_REMOVE - exit(0x00000000)} 08:45:31.317
     dvi: {DIF_REMOVE} 08:45:31.317
     dvi:      Default installer: Enter 08:45:31.317
     dvi:           {Remove DEVICE}
     dvi:                {Delete Device - SWD\PRINTENUM\{A543B91A-1175-4389-A134-BB2848C4F73A}}
     dvi:                {Delete Device - exit(0x00000000)}
     dvi:           {Remove DEVICE exit (0x00000000)}
     dvi:      Default installer: Exit
     dvi: {DIF_REMOVE - exit(0x00000000)} 08:45:31.317
     dvi: {DIF_REMOVE} 08:45:31.317
     dvi:      Default installer: Enter 08:45:31.317
     dvi:           {Remove DEVICE}
     dvi:                {Delete Device - SWD\PRINTENUM\{BA478E2D-C4DE-4249-B09B-BEDA76A6B21E}}
     dvi:                {Delete Device - exit(0x00000000)}
     dvi:           {Remove DEVICE exit (0x00000000)}
     dvi:      Default installer: Exit
     dvi: {DIF_REMOVE - exit(0x00000000)} 08:45:31.332
     dvi: {DIF_REMOVE} 08:45:31.332
     dvi:      Default installer: Enter 08:45:31.332
     dvi:           {Remove DEVICE}
     dvi:                {Delete Device - SWD\PRINTENUM\{7B387864-DDBB-4137-B90D-2D1A9A51CCC5}}
     dvi:                {Delete Device - exit(0x00000000)}
     dvi:           {Remove DEVICE exit (0x00000000)}
     dvi:      Default installer: Exit
     dvi: {DIF_REMOVE - exit(0x00000000)} 08:45:31.332
     dvi: {DIF_REMOVE} 08:45:31.332
     dvi:      Default installer: Enter 08:45:31.332
     dvi:           {Remove DEVICE}
     dvi:                {Delete Device - SWD\PRINTENUM\{EBCFFD04-48D3-42F7-AC67-5822A8783CB3}}
     dvi:                {Delete Device - exit(0x00000000)}
     dvi:           {Remove DEVICE exit (0x00000000)}
     dvi:      Default installer: Exit
     dvi: {DIF_REMOVE - exit(0x00000000)} 08:45:31.332
     dvi: {DIF_REMOVE} 08:45:31.332
     dvi:      Default installer: Enter 08:45:31.332
     dvi:           {Remove DEVICE}
     dvi:                {Delete Device - SWD\PRINTENUM\{D68A0BA5-5017-4E0B-B105-96F0A0B2A8D2}}
     dvi:                {Delete Device - exit(0x00000000)}
     dvi:           {Remove DEVICE exit (0x00000000)}
     dvi:      Default installer: Exit
     dvi: {DIF_REMOVE - exit(0x00000000)} 08:45:31.348
     set: Removed non-present devices. Time = 46 ms. 08:45:31.348
     set: Updated driver package signatures. Time = 32 ms. 08:45:31.379
     set: Installed devices. Time = 1531 ms. 08:45:32.926
     set: No device state migrated.
     set: Cleaned up unneeded PnP data. Time = 31 ms. 08:45:32.957
     set: Installed primitive drivers. Time = 47 ms. 08:45:33.004
<<<  Section end 2024/09/09 08:45:33.004
<<<  [Exit status: SUCCESS]


>>>  [Setup Update Driver Package Signatures]
>>>  Section start 2024/09/09 08:45:31.348
     set: oem3.inf/btfilter.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
     set: oem8.inf/mtkbtfilter.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
     set: oem9.inf/mtkwl6ex.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
     set: oem7.inf/netrtwlane601.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
     set: oem1.inf/prnms001.inf -> Signed by 'Microsoft Windows' (SignerScore = 0x0D000003).
     set: oem2.inf/prnms006.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
     set: oem0.inf/prnms009.inf -> Signed by 'Microsoft Windows' (SignerScore = 0x0D000003).
     set: oem6.inf/qcwlan64.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
     set: oem4.inf/rtkfilter.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
     set: oem5.inf/rtkfilter.inf -> Signed by 'Microsoft Windows Hardware Compatibility Publisher' (SignerScore = 0x0D000005).
<<<  Section end 2024/09/09 08:45:31.379
<<<  [Exit status: SUCCESS]


>>>  [Setup Plug and Play Device Install]
>>>  Section start 2024/09/09 08:45:31.379
     set: ACPI\PNP0C01\1 -> Configured [machine.inf:*PNP0C01,NO_DRV_MBRES] (ConfigFlags = 0x00000000).
     set: ACPI\PNP0C02\1 -> Configured [machine.inf:*PNP0C02,NO_DRV_MBRES] (ConfigFlags = 0x00000000).
     set: ACPI\PNP0C02\2 -> Configured [machine.inf:*PNP0C02,NO_DRV_MBRES] (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_1 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_2 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_3 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_4 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_5 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_6 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_7 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\GENUINEINTEL_-_INTEL64_FAMILY_6_MODEL_60_-_INTEL(R)_CORE(TM)_I7-4770_CPU_@_3.40GHZ\_8 -> Configured [cpu.inf:ACPI\GenuineIntel_-_Intel64,IntelPPM_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_8C2D&SUBSYS_05A41028&REV_04\3&11583659&1&D0 -> Configured [usbport.inf:PCI\VEN_8086&DEV_8C2D,EHCI.Dev.NT] and started (ConfigFlags = 0x00000000).
     set: HDAUDIO\FUNC_01&VEN_10EC&DEV_0280&SUBSYS_102805A4&REV_1000\4&51C6720&0&0001 -> Configured [hdaudio.inf:HDAUDIO\FUNC_01,HdAudModel] and started (ConfigFlags = 0x00000000).
     set: ROOT\VOLMGR\0000 -> Configured [volmgr.inf:ROOT\VOLMGR,Volmgr] and started (ConfigFlags = 0x00000000).
     set: ACPI\INT0800\4&2D89444A&0 -> Configured [machine.inf:*INT0800,NO_DRV_MEM] (ConfigFlags = 0x00000000).
     set: ROOT\BASICDISPLAY\0000 -> Configured [basicdisplay.inf:ROOT\BasicDisplay,MSBDD_Fallback] and started (ConfigFlags = 0x00000000).
     set: ACPI\THERMALZONE\TZ00 -> Configured [machine.inf:ACPI\ThermalZone,NO_DRV] and started (ConfigFlags = 0x00000000).
     set: ACPI\THERMALZONE\TZ01 -> Configured [machine.inf:ACPI\ThermalZone,NO_DRV] and started (ConfigFlags = 0x00000000).
     set: USB\VID_8087&PID_8000\5&2719E06A&0&1 -> Configured [usb.inf:USB\Class_09,StandardHub.Dev.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0C14\0 -> Configured [wmiacpi.inf:*PNP0C14,WMIMAP_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0100\4&2D89444A&0 -> Configured [machine.inf:*PNP0100,NO_DRV_X] (ConfigFlags = 0x00000000).
     set: ROOT\VID\0000 -> Configured [wvid.inf:ROOT\VID,Vid_Device_Client.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0103\0 -> Configured [machine.inf:*PNP0103,NO_DRV_HPET] (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_8C26&SUBSYS_05A41028&REV_04\3&11583659&1&E8 -> Configured [usbport.inf:PCI\VEN_8086&DEV_8C26,EHCI.Dev.NT] and started (ConfigFlags = 0x00000000).
     set: ROOT\COMPOSITEBUS\0000 -> Configured [compositebus.inf:ROOT\CompositeBus,CompositeBus_Device.NT] and started (ConfigFlags = 0x00000000).
     set: ROOT\VDRVROOT\0000 -> Configured [vdrvroot.inf:ROOT\vdrvroot,VDRVROOT] and started (ConfigFlags = 0x00000000).
     set: ROOT\SPACEPORT\0000 -> Configured [spaceport.inf:Root\Spaceport,Spaceport_Install] and started (ConfigFlags = 0x00000000).
     set: USB\VID_8087&PID_8008\5&DBBB610&0&1 -> Configured [usb.inf:USB\Class_09,StandardHub.Dev.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0501\1 -> Configured [msports.inf:*PNP0501,ComPort.NT] and started (ConfigFlags = 0x00000400).
     set: ROOT\KDNIC\0000 -> Configured [kdnic.inf:root\kdnic,KdNic.ndi] and started (ConfigFlags = 0x00000000).
     set: USB\ROOT_HUB30\4&70E181F&0&0 -> Configured [usbhub3.inf:USB\ROOT_HUB30,Generic.Install.NT] and started (ConfigFlags = 0x00000000).
     set: STORAGE\VOLUME\{8334E873-6EC2-11EF-A57D-806E6F6E6963}#0000000000100000 -> Configured [volume.inf:STORAGE\Volume,volume_install.NT] and started (ConfigFlags = 0x00000000).
     set: USB\ROOT_HUB20\4&2061926D&0 -> Configured [usbport.inf:USB\ROOT_HUB20,ROOTHUB.Dev.NT] and started (ConfigFlags = 0x00000000).
     set: ROOT\UMBUS\0000 -> Configured [umbus.inf:root\umbus,UmBusRoot_Device.NT] and started (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_8C20&SUBSYS_05A41028&REV_04\3&11583659&1&D8 -> Configured [hdaudbus.inf:PCI\CC_0403,HDAudio_Device.NT] and started (ConfigFlags = 0x00000000).
     set: ROOT\ACPI_HAL\0000 -> Configured [hal.inf:acpiapic,ACPI_AMD64_HAL] and started (ConfigFlags = 0x00000000).
!    set: PCI\VEN_8086&DEV_8C3D&SUBSYS_05A41028&REV_04\3&11583659&1&B3 -> Configured [null] and unstarted with problem CM_PROB_FAILED_INSTALL (28) [0xC0000490] (ConfigFlags = 0x00000040).
     set: ACPI\PNP0A08\0 -> Configured [pci.inf:*PNP0A08,PCI_ROOT] and started (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_8C31&SUBSYS_05A41028&REV_04\3&11583659&1&A0 -> Configured [usbxhci.inf:PCI\CC_0C0330,Generic.Install.NT] and started (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_8C4E&SUBSYS_05A41028&REV_04\3&11583659&1&F8 -> Configured [machine.inf:PCI\CC_0601,MSISADRV] and started (ConfigFlags = 0x00000000).
     set: ACPI_HAL\PNP0C08\0 -> Configured [acpi.inf:*PNP0C08,ACPI_Inst.NT] and started (ConfigFlags = 0x00000000).
     set: ROOT\BASICRENDER\0000 -> Configured [basicrender.inf:ROOT\BasicRender,BasicRender] and started (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_0C0C&SUBSYS_05A41028&REV_06\3&11583659&1&18 -> Configured [hdaudbus.inf:PCI\CC_0403,HDAudio_Device.NT] and started (ConfigFlags = 0x00000000).
     set: DISPLAY\HWP2950\4&5E649E&0&UID0 -> Configured [monitor.inf:*PNP09FF,PnPMonitor.Install] and started (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_0412&SUBSYS_05A41028&REV_06\3&11583659&1&10 -> Configured [display.inf:PCI\CC_0300,MSBDA] and started (ConfigFlags = 0x00000000).
!    set: PCI\VEN_8086&DEV_8C3A&SUBSYS_05A41028&REV_04\3&11583659&1&B0 -> Configured [null] and unstarted with problem CM_PROB_FAILED_INSTALL (28) [0xC0000490] (ConfigFlags = 0x00000040).
     set: PCI\VEN_8086&DEV_0C00&SUBSYS_05A41028&REV_06\3&11583659&1&00 -> Configured [machine.inf:PCI\CC_0600,NO_DRV] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0B00\4&2D89444A&0 -> Configured [machine.inf:*PNP0B00,NO_DRV_X] and started (ConfigFlags = 0x00000000).
     set: STORAGE\VOLUME\{8334E873-6EC2-11EF-A57D-806E6F6E6963}#0000000007500000 -> Configured [volume.inf:STORAGE\Volume,volume_install.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\FIXEDBUTTON\2&DABA3FF&1 -> Configured [machine.inf:ACPI\FixedButton,NO_DRV] and started (ConfigFlags = 0x00000000).
     set: PCI\VEN_8086&DEV_2822&SUBSYS_05A41028&REV_04\3&11583659&1&FA -> Configured [iastorav.inf:PCI\VEN_8086&DEV_2822&CC_0104,iaStorAVC_inst] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0000\4&2D89444A&0 -> Configured [machine.inf:*PNP0000,NO_DRV_PIC] (ConfigFlags = 0x00000000).
!    set: PCI\VEN_8086&DEV_153A&SUBSYS_05A41028&REV_04\3&11583659&1&C8 -> Setting CONFIGFLAG_REINSTALL on unstarted device with problem CM_PROB_REINSTALL (18) [0xC0000493].
     set: ACPI\PNP0C02\10 -> Configured [machine.inf:*PNP0C02,NO_DRV_MBRES] (ConfigFlags = 0x00000000).
     set: ACPI\INT3F0D\4&2D89444A&0 -> Configured [machine.inf:*PNP0C02,NO_DRV_MBRES] (ConfigFlags = 0x00000000).
     set: SCSI\DISK&VEN_SAMSUNG&PROD_SSD\4&58CA072&0&000000 -> Configured [disk.inf:GenDisk,disk_install.NT] and started (ConfigFlags = 0x00000000).
!    set: PCI\VEN_8086&DEV_8C22&SUBSYS_05A41028&REV_04\3&11583659&1&FB -> Configured [null] and unstarted with problem CM_PROB_FAILED_INSTALL (28) [0xC0000490] (ConfigFlags = 0x00000040).
     set: ACPI\PNP0C0C\AA -> Configured [machine.inf:*PNP0C0C,NO_DRV] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0200\4&2D89444A&0 -> Configured [machine.inf:*PNP0200,NO_DRV_X] (ConfigFlags = 0x00000000).
     set: STORAGE\VOLUME\{8334E873-6EC2-11EF-A57D-806E6F6E6963}#0000000006500000 -> Configured [volume.inf:STORAGE\Volume,volume_install.NT] and started (ConfigFlags = 0x00000000).
     set: ROOT\NDISVIRTUALBUS\0000 -> Configured [ndisvirtualbus.inf:ROOT\NdisVirtualBus,NdisVirtualBus_Device.NT] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0C02\111 -> Configured [machine.inf:*PNP0C02,NO_DRV_MBRES] (ConfigFlags = 0x00000000).
!    set: HDAUDIO\FUNC_01&VEN_8086&DEV_2807&SUBSYS_80860101&REV_1000\4&1095690&0&0001 -> Configured [hdaudio.inf:HDAUDIO\FUNC_01,HdAudModel] and unstarted with problem CM_PROB_FAILED_START (10) [0xC0000001] (ConfigFlags = 0x00000000).
     set: ROOT\MSSMBIOS\0000 -> Configured [mssmbios.inf:ROOT\mssmbios,MSSMBIOS_DRV] and started (ConfigFlags = 0x00000000).
     set: USB\ROOT_HUB20\4&27C19238&0 -> Configured [usbport.inf:USB\ROOT_HUB20,ROOTHUB.Dev.NT] and started (ConfigFlags = 0x00000000).
     set: ROOT\SYSTEM\0000 -> Configured [swenum.inf:ROOT\SWENUM,SWENUM] and started (ConfigFlags = 0x00000000).
     set: ACPI\PNP0C04\4&2D89444A&0 -> Configured [machine.inf:*PNP0C04,NO_DRV_X] (ConfigFlags = 0x00000000).
     set: ROOT\RDPBUS\0000 -> Configured [rdpbus.inf:ROOT\RDPBUS,RDPBUS] and started (ConfigFlags = 0x00000000).
     set: STORAGE\VOLUME\{8334E873-6EC2-11EF-A57D-806E6F6E6963}#0000001BBA500000 -> Configured [volume.inf:STORAGE\Volume,volume_install.NT] and started (ConfigFlags = 0x00000000).
     set: Waiting for system services to start... 08:45:31.395
     set: Completed waiting for system services to start. 08:45:32.238
     set: DeviceInstall service already running. 08:45:32.254
     set: Waiting for pending device installs... 08:45:32.254
     set: Completed pending device installs. 08:45:32.910
     set: PCI\VEN_8086&DEV_153A&SUBSYS_05A41028&REV_04\3&11583659&1&C8 -> Configured [net1ic64.inf:PCI\VEN_8086&DEV_153A,E153A.10.0.1] and started (ConfigFlags = 0x00000000).
     set: SWD\MMDEVAPI\MICROSOFTGSWAVETABLESYNTH -> Configured [c_swdevice.inf:SWD\GenericRaw,SoftwareDevice] and started (ConfigFlags = 0x00000000).
     set: Devices pre-configured: 68 (68 internal, 0 external)
     set: Devices pre-installed: 0
     set: Devices need install: 2 (2 internal, 0 external)
     set: Devices configured: 2 (2 internal, 0 external)
     set: Devices installed: 0
     set: Devices non-present: 0
<<<  Section end 2024/09/09 08:45:32.926
<<<  [Exit status: SUCCESS]


>>>  [Setup online Device Install (Hardware initiated) - ACPI\PNP0501\1]
>>>  Section start 2024/09/09 08:45:32.254
     utl: {Select Drivers - ACPI\PNP0501\1} 08:45:32.332
     utl:      Driver Node:
     utl:           Status         - Selected | Installed
     utl:           Driver INF     - msports.inf (C:\Windows\System32\DriverStore\FileRepository\msports.inf_amd64_f2e8231e8b60f214\msports.inf)
     utl:           Class GUID     - {4d36e978-e325-11ce-bfc1-08002be10318}
     utl:           Driver Version - 06/21/2006,10.0.19041.1
     utl:           Configuration  - *PNP0501 [ComPort.NT]
     utl:           Driver Rank    - 00FF0002
     utl:           Signer Score   - Inbox (0D000003)
     utl: {Select Drivers - exit(0x00000000} 08:45:32.332
!    dvi: Device class {4d36e978-e325-11ce-bfc1-08002be10318} is not configurable.
     dvi: Searching for hardware ID(s):
     dvi:      acpi\ven_pnp&dev_0501
     dvi:      acpi\pnp0501
     dvi:      *pnp0501
     dvi: Class GUID of device changed to: {4d36e978-e325-11ce-bfc1-08002be10318}.
     dvi: Waiting for previous device install to complete. 08:45:32.379
     ndv: {Core Device Install} 08:45:32.488
     dvi:      {Install Device - ACPI\PNP0501\1} 08:45:32.488
     dvi:           Device Status: 0x0180200a
     dvi:           Config Flags: 0x00000400
     dvi:           Parent Device: PCI\VEN_8086&DEV_8C4E&SUBSYS_05A41028&REV_04\3&11583659&1&F8
     dvi:           {DIF_ALLOW_INSTALL} 08:45:32.551
     dvi:                Using exported function 'PortsClassInstaller' in module 'C:\Windows\system32\MsPorts.Dll'.
     dvi:                Class installer == MsPorts.Dll,PortsClassInstaller
     dvi:                Class installer: Enter 08:45:32.551
     dvi:                Class installer: Exit
     dvi:                Default installer: Enter 08:45:32.551
     dvi:                Default installer: Exit
     dvi:           {DIF_ALLOW_INSTALL - exit(0xe000020e)} 08:45:32.551
     dvi:           {DIF_INSTALLDEVICEFILES} 08:45:32.567
     dvi:                Class installer: Enter 08:45:32.567
     dvi:                Class installer: Exit
     dvi:                Default installer: Enter 08:45:32.567
     dvi:                Default installer: Exit
     dvi:           {DIF_INSTALLDEVICEFILES - exit(0x00000000)} 08:45:32.567
     dvi:           {_SCAN_FILE_QUEUE} 08:45:32.567
     flq:                File 'C:\Windows\system32\DRIVERS\serial.sys' pruned from copy.
     flq:                File 'C:\Windows\system32\DRIVERS\serenum.sys' pruned from copy.
     dvi:           {_SCAN_FILE_QUEUE - exit(0x00000000)} 08:45:32.567
     flq:           {FILE_QUEUE_COMMIT} 08:45:32.567
     flq:           {FILE_QUEUE_COMMIT - exit(0x00000000)} 08:45:32.567
     dvi:           {DIF_REGISTER_COINSTALLERS} 08:45:32.567
     dvi:                Reset Device: Resetting device configuration. 08:45:32.582
     dvi:                Reset Device: Resetting device configuration completed. 08:45:32.801
     dvi:                Class installer: Enter 08:45:32.879
     dvi:                Class installer: Exit
     dvi:                Default installer: Enter 08:45:32.879
     inf:                     {Install from INF Section - ComPort.NT.CoInstallers} 08:45:32.879
     inf:                          Flags         - 0x001001ee
     inf:                     {Install from INF Section - exit(0x00000000)} 08:45:32.879
     dvi:                Default installer: Exit
     dvi:           {DIF_REGISTER_COINSTALLERS - exit(0x00000000)} 08:45:32.879
     dvi:           {DIF_INSTALLINTERFACES} 08:45:32.879
     dvi:                Class installer: Enter 08:45:32.879
     dvi:                Class installer: Exit
     dvi:                Default installer: Enter 08:45:32.879
     dvi:                Default installer: Exit
     dvi:           {DIF_INSTALLINTERFACES - exit(0x00000000)} 08:45:32.879
     dvi:           {DIF_INSTALLDEVICE} 08:45:32.879
     dvi:                Class installer: Enter 08:45:32.879
     inf:                     {Install from INF Section - ComPort.NT} 08:45:32.879
     inf:                          Flags         - 0x00000004
     inf:                     {Install from INF Section - exit(0x00000000)} 08:45:32.879
     dvi:                     {Install DEVICE}
     inf:                          {Install from INF Section - ComPort.NT} 08:45:32.879
     inf:                               Flags         - 0x001005ee
     inf:                          {Install from INF Section - exit(0x00000000)} 08:45:32.879
     inf:                          {Install from INF Section - ComPort.NT.Hw} 08:45:32.879
     inf:                               Flags         - 0x00100004
     inf:                          {Install from INF Section - exit(0x00000000)} 08:45:32.879
     dvi:                          {Writing Device Properties}
     dvi:                               Strong Name=msports.inf:1080904711deb522:ComPort:10.0.19041.1:*pnp0501
     dvi:                          {Writing Device Properties - Complete}
     inf:                          AddService=Serial,0x00000002,Serial_Service_Inst,Serial_EventLog_Inst  (msports.inf line 275)
     dvi:                          Add Service: Modified existing service 'Serial'.
     inf:                          {Install from INF Section - Serial_Service_Inst} 08:45:32.879
     inf:                               Flags         - 0x00100004
     inf:                          {Install from INF Section - exit(0x00000000)} 08:45:32.895
     inf:                          {Install from INF Section - Serial_EventLog_Inst} 08:45:32.895
     inf:                               Flags         - 0x00100004
     inf:                          {Install from INF Section - exit(0x00000000)} 08:45:32.895
     inf:                          AddService=Serenum,,Serenum_Service_Inst  (msports.inf line 276)
     dvi:                          Add Service: Modified existing service 'Serenum'.
     inf:                          {Install from INF Section - Serenum_Service_Inst} 08:45:32.895
     inf:                               Flags         - 0x00100004
     inf:                          {Install from INF Section - exit(0x00000000)} 08:45:32.895
     dvi:                     {Install DEVICE exit (0x00000000)}
     dvi:                     Install Device: Configuring device class. 08:45:32.895
     dvi:                     Install Device: Configuring device class completed. 08:45:32.895
     dvi:                     Device Status: 0x0180200a
     dvi:                     Install Device: Removing device 'ACPI\PNP0501\1' and sub-tree. 08:45:32.895
     dvi:                     Install Device: Removing device sub-tree completed. 08:45:32.895
     dvi:                     Install Device: Restarting device. 08:45:32.895
     dvi:                     Install Device: Restarting device completed. 08:45:32.910
     dvi:                Class installer: Exit
     dvi:           {DIF_INSTALLDEVICE - exit(0x00000000)} 08:45:32.910
     dvi:           {DIF_NEWDEVICEWIZARD_FINISHINSTALL} 08:45:32.910
     dvi:                Class installer: Enter 08:45:32.910
     dvi:                Class installer: Exit
     dvi:                Default installer: Enter 08:45:32.910
     dvi:                Default installer: Exit
     dvi:           {DIF_NEWDEVICEWIZARD_FINISHINSTALL - exit(0xe000020e)} 08:45:32.910
     dvi:      {Install Device - exit(0x00000000)} 08:45:32.910
     dvi: {Core Device Install - exit(0x00000000)} 08:45:32.910
     dvi: {DIF_DESTROYPRIVATEDATA} 08:45:32.910
     dvi:      Class installer: Enter 08:45:32.910
     dvi:      Class installer: Exit
     dvi:      Default installer: Enter 08:45:32.910
     dvi:      Default installer: Exit
     dvi: {DIF_DESTROYPRIVATEDATA - exit(0xe000020e)} 08:45:32.910
<<<  Section end 2024/09/09 08:45:32.910
<<<  [Exit status: SUCCESS]


>>>  [Setup online Device Install (Hardware initiated) - PCI\VEN_8086&DEV_153A&SUBSYS_05A41028&REV_04\3&11583659&1&C8]
>>>  Section start 2024/09/09 08:45:32.254
     utl: {Select Drivers - PCI\VEN_8086&DEV_153A&SUBSYS_05A41028&REV_04\3&11583659&1&C8} 08:45:32.270
     utl:      Driver Node:
     utl:           Status         - Selected
     utl:           Driver INF     - net1ic64.inf (C:\Windows\System32\DriverStore\FileRepository\net1ic64.inf_amd64_5f033e913d34d111\net1ic64.inf)
     utl:           Class GUID     - {4d36e972-e325-11ce-bfc1-08002be10318}
     utl:           Driver Version - 06/12/2018,12.17.10.8
     utl:           Configuration  - PCI\VEN_8086&DEV_153A
     utl:           Driver Rank    - 00FF2001
     utl:           Signer Score   - Inbox (0D000003)
     utl: {Select Drivers - exit(0x00000000} 08:45:32.270
     dvi: {Core Device Install} 08:45:32.270
     dvi:      {Configure Device - PCI\VEN_8086&DEV_153A&SUBSYS_05A41028&REV_04\3&11583659&1&C8} 08:45:32.270
     dvi:           Device Status: 0x01802400 [0x12 - 0xc0000493]
     dvi:           Config Flags: 0x00000020
     dvi:           Parent Device: ACPI\PNP0A08\0
     sto:           {Configure Driver Package: C:\Windows\System32\DriverStore\FileRepository\net1ic64.inf_amd64_5f033e913d34d111\net1ic64.inf}
     sto:                Source Filter  = PCI\VEN_8086&DEV_153A
     inf:                Class GUID     = {4d36e972-e325-11ce-bfc1-08002be10318}
     inf:                Class Options  = Configurable
     inf:                {Configure Driver: Intel(R) Ethernet Connection I217-LM}
     inf:                     Section Name = E153A.10.0.1
     inf:                     {Add Service: e1i65x64}
     inf:                          Start Type    = 3
     inf:                          Service Type  = 1
     inf:                          Error Control = 1
     inf:                          Image Path    = \SystemRoot\System32\drivers\e1i65x64.sys
     inf:                          Display Name  = Intel(R) PRO/1000 PCI Express Network Connection Driver I
     inf:                          Group         = NDIS
     inf:                          Updated service 'e1i65x64'.
     inf:                     {Add Service: exit(0x00000000)}
     inf:                     Hardware Id  = PCI\VEN_8086&DEV_153A
     inf:                     {Configure Driver Configuration: E153A.10.0.1}
     inf:                          Service Name  = e1i65x64
     inf:                          Included INFs = pci.inf
     inf:                          Config Flags  = 0x00000000
     inf:                     {Configure Driver Configuration: exit(0x00000000)}
     inf:                {Configure Driver: exit(0x00000000)}
!    inf:                Needed section [NO_DRV] not found. Code = 1315, Line = 98
!    inf:                Needed section [NO_DRV] not found. Code = 1315, Line = 98
     flq:                {FILE_QUEUE_COMMIT} 08:45:32.410
     flq:                     Hardlinking 'C:\Windows\System32\DriverStore\FileRepository\net1ic64.inf_amd64_5f033e913d34d111\e1i65x64.sys' to 'C:\Windows\System32\drivers\e1i65x64.sys'.
     cpy:                     Existing file 'C:\Windows\System32\drivers\e1i65x64.sys' remains unchanged.
     flq:                {FILE_QUEUE_COMMIT - exit(0x00000000)} 08:45:32.426
     sto:           {Configure Driver Package: exit(0x00000000)}
     dvi:           Install Device: Configuring device. 08:45:32.426
     dvi:                Configuration: net1ic64.inf:PCI\VEN_8086&DEV_153A,*
     dvi:           Install Device: Configuring device completed. 08:45:32.442
     dvi:           Device Status: 0x01802000
     dvi:           Install Device: Starting device 'PCI\VEN_8086&DEV_153A&SUBSYS_05A41028&REV_04\3&11583659&1&C8'. 08:45:32.442
     dvi:           Install Device: Starting device completed. 08:45:32.488
!    dvi:           Device pending start: Device has problem: 0x38 (CM_PROB_NEED_CLASS_CONFIG), problem status: 0x00000000.
     dvi:      {Configure Device - exit(0x00000000)} 08:45:32.488
     dvi: {Core Device Install - exit(0x00000000)} 08:45:32.488
     dvi: Waiting for device post-install to complete. 08:45:32.488
     dvi: Device post-install completed. 08:45:32.817
     dvi: Device Status: 0x0180200a
<<<  Section end 2024/09/09 08:45:32.817
<<<  [Exit status: SUCCESS]

