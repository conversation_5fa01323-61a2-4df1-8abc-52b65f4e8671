<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for ServicingStack 10.0.19041.5853" displayName="Servicing Stack 10.0.19041.5853" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="" creationTimeStamp="2025-05-09T07:23:38Z" lastUpdateTimeStamp="2025-05-09T07:23:38Z">
  <assemblyIdentity name="Package_for_ServicingStack_5853" version="19041.5853.1.1" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5058526" applicabilityEvaluation="deep" releaseType="Security Update" restart="possible" selfUpdate="true" permanence="permanent" psfName="SSU-19041.5853-x64.psf">
    <parent buildCompare="EQ" integrate="standalone" disposition="detect">
      <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-UtilityVMEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-WinPE-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="Wrapper-0827B676735876FF54CF0637269ACF0E1AF8B3B7189F8BEFFF3ADF611B7DF0A2_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-0827B676735876FF54CF0637269ACF0E1AF8B3B7189F8BEFFF3ADF611B7DF0A2" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-17CEB93C8A19F1AD983421D1EF4FE863F656B81DB5BB1ED9C280F416D43CB75E_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-17CEB93C8A19F1AD983421D1EF4FE863F656B81DB5BB1ED9C280F416D43CB75E" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-193667F8F677A05257FF5DBD2C2720CF6E3775A2164944173C32FCC7802E4E75_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-193667F8F677A05257FF5DBD2C2720CF6E3775A2164944173C32FCC7802E4E75" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-1A01905EAE044945BACED494789AEAAF3CF6A1E6AD038CAC7114B05531BD8424_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-1A01905EAE044945BACED494789AEAAF3CF6A1E6AD038CAC7114B05531BD8424" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-28927E9FC8CD1D86F145CD670A3E5F55B26B420AB6003CDFFF58CD236235DA9D_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-28927E9FC8CD1D86F145CD670A3E5F55B26B420AB6003CDFFF58CD236235DA9D" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-3AB0C06F9F8E283217212A0427A54561DE2371A807354073A1681E2FEDA8DB25_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-3AB0C06F9F8E283217212A0427A54561DE2371A807354073A1681E2FEDA8DB25" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-459DF1EDEE7E64C0EB4FD55B4D54C99A1FB22F291D0EAC1B4453DD16CFB028D1_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-459DF1EDEE7E64C0EB4FD55B4D54C99A1FB22F291D0EAC1B4453DD16CFB028D1" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-49095B608D54F84E56BE284C9D091A8BADFEA0323C26F4C323F60BDB85366EC4_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-49095B608D54F84E56BE284C9D091A8BADFEA0323C26F4C323F60BDB85366EC4" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-611CC83169F7E0FDBD69D2B3A2FE7EBFDCC93CB1D8B0B51BA7AACC6F3A055590_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-611CC83169F7E0FDBD69D2B3A2FE7EBFDCC93CB1D8B0B51BA7AACC6F3A055590" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-7CE7237AF643229D2769C2879518589DCF6C5D06D499B60830C2A18129AB143E_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-7CE7237AF643229D2769C2879518589DCF6C5D06D499B60830C2A18129AB143E" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-AD83DA6C3BA2179527B959244AF48C002AF9A86375D7BE8CD40974A16B8B7254_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-AD83DA6C3BA2179527B959244AF48C002AF9A86375D7BE8CD40974A16B8B7254" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-B5FA95B4255A25044F7C8A03ACADB27CB29F0B209B622574CFAF6DDB5029C0D1_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-B5FA95B4255A25044F7C8A03ACADB27CB29F0B209B622574CFAF6DDB5029C0D1" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-BB8B52500F9E84590976A3ED31B2C81FF449AB0CBD70D51EA45F1E329094E282_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-BB8B52500F9E84590976A3ED31B2C81FF449AB0CBD70D51EA45F1E329094E282" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-D82E94A81A5FEDEED3F9E89F6136E4DBBFF6FBB9C22F1CD626B2A42F8115B243_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-D82E94A81A5FEDEED3F9E89F6136E4DBBFF6FBB9C22F1CD626B2A42F8115B243" version="10.0.19041.5853" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <mum2:packageExtended xmlns:mum2="urn:schemas-microsoft-com:asm.v3" completelyOfflineCapable="undetermined" packageSize="56867007" />
  </package>
</assembly>
