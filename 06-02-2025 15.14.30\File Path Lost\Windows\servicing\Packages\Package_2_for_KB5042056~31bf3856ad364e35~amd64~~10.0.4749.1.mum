<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for KB5042056" displayName="default" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="http://support.microsoft.com/?kbid=5042056" creationTimeStamp="2024-07-16T22:32:32Z" lastUpdateTimeStamp="2024-07-16T22:32:32Z">
  <assemblyIdentity name="Package_2_for_KB5042056" version="10.0.4749.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5042056" releaseType="Update" restart="possible">
    <parent buildCompare="EQ" integrate="separate" disposition="staged">
      <assemblyIdentity name="Microsoft-Windows-NetFx3-OC-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-NetFx3-Server-OC-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-NetFx3-Server-OC-Package" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-NetFx3-WCF-OC-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-NetFx3-WCF-OC-Package" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <parent buildCompare="EQ" integrate="standalone" disposition="detect">
        <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerAzureStackHCICorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerSolutionEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageWorkgroupEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineCorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerWebEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      </parent>
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="5042056-5_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Runtime.Serialization" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="01f25b9d051d29221709f621abcd38ad" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-6_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="4565431d03bc8213a37ee4d298bd73a8" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-7_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="SMDiagnostics" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="cfe5d2cd613b5ffd5fe8dc3039a68f1c" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-8_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-System.ServiceModel" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="330fc652a83c484b84b2113a6277446a" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-9_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="SMSvcHost" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="da69388bc40e1efcab31773358753c1c" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-10_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-System.Runtime.Serialization.Ref" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="ad3e342566e79f682ecb0e750c802c70" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-11_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-M_SVC_MON_SUP_DLL" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="dbba7bf5ef33378b8982d630aa0812dc" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-12_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-SMDiagnostics" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="b72dd9b93ce9fd0014bb6bf5738b7108" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-13_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-System.ServiceModel.Ref" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="c56315b12a3b58c60334e830d2224f8e" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-14_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-SMSvcHost" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="394245d8966fe0cdfb8ef01ab0fe0ec5" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-15_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.IdentityModel" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="626027e1be08ef8decfbded8cb8907af" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-16_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.Ref" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="7c2457a5a805648d0c627ff2487fab8a" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-17_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-System.Runtime.Serialization" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="7d4eb8b4a61bf8a02785a8d12fc4e15c" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-18_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Runtime.Serialization.Ref" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="0b1338a34318240fdd4db1b888372492" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-19_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-System.IdentityModel" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="9ae5bf393d2431caa34e8002b24764c9" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-20_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WCF-System.ServiceModel.WasHosting" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="dc77177a99fb21acd8be025c255752a2" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-21_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.WasHosting" version="10.0.19200.110" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d406936bf6526ddb5b59c8f9418a709f" version="10.0.19200.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
  </package>
</assembly>
