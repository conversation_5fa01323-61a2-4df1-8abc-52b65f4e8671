rem Define %TARGETOS% as the Windows folder (This later becomes C:\Windows)
for /F "tokens=1,2,3 delims= " %%A in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\RecoveryEnvironment" /v TargetOS') DO SET TARGETOS=%%C

rem Define %TARGETOSDRIVE% as the Windows partition (This later becomes C:)
for /F "tokens=1 delims=\" %%A in ('Echo %TARGETOS%') DO SET TARGETOSDRIVE=%%A

rem Execute OEM script
cmd /c %TARGETOSDRIVE%\Recovery\OEM\EnableCustomizations.cmd 

rem delete Windows.old\Users
rmdir /s /q \\?\%TARGETOSDRIVE%\Windows.old\Users

If EXIST %TARGETOSDRIVE%\Windows.old\Users (
  rmdir /s /q \\?\%TARGETOSDRIVE%\Windows.old\Users
)

If EXIST %TARGETOSDRIVE%\Windows.old\Users (
  rmdir /s /q \\?\%TARGETOSDRIVE%\Windows.old\Users
)

If EXIST %TARGETOSDRIVE%\Windows.old\Users (
  rmdir /s /q \\?\%TARGETOSDRIVE%\Windows.old\Users
)

rem if folder still exists on the 4-th time, reboot to trigger a failure
If EXIST %TARGETOSDRIVE%\Windows.old\Users (
rem Windows.old\Users exist
wpeutil reboot
)

rem Deletion succeed

rem exit
EXIT 0
