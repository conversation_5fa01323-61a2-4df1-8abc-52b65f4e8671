<?xml version="1.0" encoding="utf-8"?>
<PackageConfiguration xmlns="http://www.microsoft.com/schemas/dcm/configuration/2008">
  <Execution>
    <Package Path="%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD" />
    <Package Path="%windir%\diagnostics\system\Device">
        <Answers Version="1.0">
            <Interaction ID="IT_SelectDevice">
                <Value>%DEVICEID%</Value>
            </Interaction>
        </Answers>
        <RequiredContext>
            <Parameter>DEVICEID</Parameter>
        </RequiredContext>
    </Package>
    <Name>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-1</Name>
    <Description>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-2</Description>
    <Icon>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-1001</Icon>
  </Execution>

  <Index>
    <Id>WindowsMediaPlayerDVDDiagnostic</Id>
    <RequiresAdminPrivileges>false</RequiresAdminPrivileges>
    <PrivacyUrl>http://go.microsoft.com/fwlink/?LinkId=190175</PrivacyUrl>
    <Version>1.0</Version>
    <PublisherName>Microsoft Corporation</PublisherName>
    <Category>@%windir%\system32\DiagCpl.dll,-411</Category>
    <Keyword>@%windir%\system32\DiagCpl.dll,-20</Keyword>
    <Keyword>@%windir%\system32\DiagCpl.dll,-24</Keyword>
    <Keyword>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-201</Keyword>
    <Keyword>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-203</Keyword>
    <Keyword>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-204</Keyword>
    <Keyword>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-205</Keyword>
    <Keyword>@%windir%\diagnostics\system\WindowsMediaPlayerPlayDVD\DiagPackage.dll,-206</Keyword>
  </Index>
</PackageConfiguration>