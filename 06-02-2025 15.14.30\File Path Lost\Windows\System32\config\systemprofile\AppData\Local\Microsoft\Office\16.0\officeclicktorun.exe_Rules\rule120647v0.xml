﻿<?xml version="1.0" encoding="utf-8"?>
<R Id="120647" V="0" DC="SM" T="Subrule" xmlns="">
  <S>
    <R T="1" R="120645" />
    <SR T="2" R="([Aa][Pp][Aa][Cc][Hh][Ee] [Ss][Oo][Ff][Tt][Ww][Aa][Rr][Ee])">
      <S T="1" F="1" M="Ignore" />
    </SR>
  </S>
  <C T="W" I="0" O="true">
    <S T="2" F="Matched" M="Ignore" />
  </C>
  <C T="W" I="1" O="false">
    <S T="1" F="1" M="Ignore" />
  </C>
  <T>
    <S T="1" />
  </T>
</R>