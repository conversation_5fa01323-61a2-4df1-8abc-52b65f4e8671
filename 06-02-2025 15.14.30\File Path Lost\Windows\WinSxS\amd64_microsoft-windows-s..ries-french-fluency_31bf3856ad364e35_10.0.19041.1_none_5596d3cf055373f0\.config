{"character-map": "charactermap.json", "ime": "ime.json", "models": [{"path": "fr_FR.lm", "type": "term", "version": "5"}, {"path": "fr_FR_word_c.lm1", "type": "term", "version": "5"}, {"path": "emoji_bg_c.lm2", "tags": ["emoji"], "type": "term", "version": "1"}], "punctuation": "punctuation.json", "tags": ["french", "fr", "FR", "id:fr_FR", "id:fr", "name:<PERSON><PERSON><PERSON> (France)"], "timestamp": "2019/03/20 14:24", "version": "313"}