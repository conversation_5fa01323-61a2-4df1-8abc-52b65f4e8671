<?xml version="1.0" encoding="us-ascii"?><Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><cfg:Configuration version="1.1" xmlns:cfg="http://schemas.microsoft.com/Passport/PPCRL"><!--
      When a certificate is rev'd, a line like the following should be 
      added to the cfg:Settings section:
      <cfg:Certificate expired="true">SLCA_BACKUP.CER</cfg:Certificate>
    --><cfg:Settings><cfg:DeviceDNSSuffix>.devicedns.live.com</cfg:DeviceDNSSuffix><cfg:ResolveTimeout>0</cfg:ResolveTimeout><cfg:ConnectTimeout>10000</cfg:ConnectTimeout><cfg:SendTimeout>30000</cfg:SendTimeout><cfg:ReceiveTimeout>30000</cfg:ReceiveTimeout><cfg:MinMinutesBetweenMetaConfigCheck>1440</cfg:MinMinutesBetweenMetaConfigCheck><cfg:ConfigServerSslURI>https://go.microsoft.com/fwlink/?LinkId=859523</cfg:ConfigServerSslURI><cfg:DIDCOMMetaData><cfg:DIDWithAuth>1</cfg:DIDWithAuth><cfg:AssocPDIDToLDID>1</cfg:AssocPDIDToLDID><cfg:Protocol><cfg:CLSID>{1C109E4C-2F30-4EA3-A57A-A290877A2303}</cfg:CLSID><cfg:DATA><![CDATA[<Input version="1" match="20"><Excluded><Controller>Win32_USBControllerDevice</Controller><Controller>Win32_1394ControllerDevice</Controller><Controller>Win32_PCMCIAControllerDevice</Controller><Controller>Win32_ConnectionShare</Controller></Excluded><Class name="Win32_PhysicalMedia" pos="1" hash="32" match="10"><Data>SerialNumber</Data></Class><Class name="Win32_NetworkAdapter" pos="2" hash="32" match="10"><Data>MACAddress</Data></Class><Class name="Win32_BIOS" pos="3" hash="32" match="10"><Data>Manufacturer</Data><Data>SerialNumber</Data></Class></Input>]]></cfg:DATA><cfg:PREFIX>01</cfg:PREFIX></cfg:Protocol><cfg:Protocol><cfg:CLSID>{B9F1D9B8-1DA6-4F17-962F-69EC82EA2704}</cfg:CLSID><cfg:PREFIX>03</cfg:PREFIX><cfg:LOGICAL>1</cfg:LOGICAL><cfg:SYSTEM>1</cfg:SYSTEM></cfg:Protocol><cfg:Protocol><cfg:CLSID>{B9F1D9B8-1DA6-4F17-962F-69EC82EA2704}</cfg:CLSID><cfg:PREFIX>02</cfg:PREFIX><cfg:LOGICAL>1</cfg:LOGICAL></cfg:Protocol><cfg:CONSENTVERSION>1</cfg:CONSENTVERSION></cfg:DIDCOMMetaData><cfg:Certificate>MIIDGjCCAtqgAwIBAgIJAL73rwODGt9LMAkGByqGSM44BAMwIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MB4XDTEzMDcxMTE4NTkwMloXDTE4MDcxMDE4NTkwMlowIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MIIBtjCCASsGByqGSM44BAEwggEeAoGBANWeEczXi1Y1IhcoV0XAWEQpl+lUrcltb7xPsvVCtVw9nuNFr/rlqomR4UYOpIjKGU1TgGAmbdbL0okRAWRKaQcD7PXqusqvifTxAFa+wd8q5rs+kqbgHeXIa1S196O1kr3CcZ+uhqghOveAwNSWhq7Ka4kilGSuCBj+KdXqbbrlAhUAgig/M+/fGdcMbEwSEZJiUC4TdXsCgYByH4zE6dVJmVKgspMDp7cZL9ypq0j2SyQ5XcvJUAhIx+y6m/ZNCE7QZ/V68XaNPywBQuK+Vro9eMy5NBnd+co19MkA9NZGP/lqbCWdJXbeOOe+YcM8swTipvT1hhBhW4majEQn0OTgpSuapf7k+m68NabtygkRaOeRmG5b4Ds4TwOBhAACgYAaYBv/Lj7295dqB2MzgvlBbUsaGfWESECTNGCvKLHdqyPJcdL/7z5DV7aT/UKNvgbOrnx9Gq101N0oJVKu95NF0jG/0CuV9Rkv4MOi2XbFqo7FjFBt+tgG06rtrGhZ9WejkQddf4f2wnoH6PaASrs2u3u05qTiet3RDVrEuxi9GKOBmDCBlTAdBgNVHQ4EFgQUpDlKsWrP86JkIPucE5E+6kWx+TUwUwYDVR0jBEwwSoAUpDlKsWrP86JkIPucE5E+6kWx+TWhJ6QlMCMxITAfBgNVBAMTGFRva2VuIFNpZ25pbmcgUHVibGljIEtleYIJAL73rwODGt9LMBIGA1UdEwEB/wQIMAYBAf8CAQAwCwYDVR0PBAQDAgHGMAkGByqGSM44BAMDLwAwLAIUcRZ1K2iOgGwDLA1NfHF7IwO6ev0CFC3UZ6aEs6jt1O4bZp0I6IsgQin7</cfg:Certificate><cfg:Certificate>MIIDGzCCAtqgAwIBAgIJAPgp/v0oy8SCMAkGByqGSM44BAMwIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MB4XDTE4MDUyMjIxMDMzMloXDTIzMDUyMTIxMDMzMlowIzEhMB8GA1UEAxMYVG9rZW4gU2lnbmluZyBQdWJsaWMgS2V5MIIBtjCCASsGByqGSM44BAEwggEeAoGBAM4f0aJAeyn2dVV84teAnjrt5WeaZGr91uhLqzTYOV1TUrAqL2RqowzMSm3fAb2ClTPrmP8VyVzKVdDeqYbX3fVbIAxc5ZfhcDHRp8X2BXrcgEeWNpTS6MJNm03yzQlDYvj1s97Dqc4pqlIARR8lLTNQogYgNCPhZJPb+CZxV94RAhUA+Do6kXf1J/iCHYD8nmtY7Dv11Z0CgYBX50j5/a2cpZ1+fbLT/6h1wi1KCjtaSVJJkJa07HC67p71HltCDS/yYoLFx+edrsFxTcFSh8+32sgtNyK4SKDems3SzLu6VZlEplxIwoQSV8X1bp9Md6GAm7VqLIwE4KS/muDTfIM5RNL6hu8w25x+W9ck2gCp8CNOkvsFuCkFXwOBhAACgYB30dQ9QJVU5Gtca9vawcGwGoJobglU8VWM7z5RDKtudHqb50xOF+XD0C0zZTmWqLQZ9sy7hs6G4HTKo+ypx+DSf/ikMQ1e6JM04APk9iOHTHYyUmleR56sedKjyOr9BIM6Z2kOgaA5a/Oj4u6kUbKN9vhxfBO03faKF0IbI55ia6OBmDCBlTAdBgNVHQ4EFgQUC8fjWRmxgEhhLKnN/moLSicMXSYwUwYDVR0jBEwwSoAUC8fjWRmxgEhhLKnN/moLSicMXSahJ6QlMCMxITAfBgNVBAMTGFRva2VuIFNpZ25pbmcgUHVibGljIEtleYIJAPgp/v0oy8SCMBIGA1UdEwEB/wQIMAYBAf8CAQAwCwYDVR0PBAQDAgHGMAkGByqGSM44BAMDMAAwLQIUddOojXyY//sI+UfJMMOBIsyAVJUCFQCxe1/9QK9rGxtdG/3SM8gWs7UVww==</cfg:Certificate><cfg:AddressResolutionTTL>900</cfg:AddressResolutionTTL><cfg:ClientTimeOutForRpcCallsNoNetwork>5</cfg:ClientTimeOutForRpcCallsNoNetwork><cfg:ClientTimeoutForRpcCallsWithNetwork>600</cfg:ClientTimeoutForRpcCallsWithNetwork><cfg:LivesspMaxTokenSize>60000</cfg:LivesspMaxTokenSize><cfg:AccountDomain>account.live-int.com</cfg:AccountDomain><cfg:InterruptResolutionDomain>account.live-int.com</cfg:InterruptResolutionDomain><cfg:PasswordReset>account.live-int.com/password/reset</cfg:PasswordReset><cfg:AccountPolicy>sapi</cfg:AccountPolicy><cfg:ConnectAccountPolicy>mbi_ssl</cfg:ConnectAccountPolicy><cfg:StrongAuthPolicy>mbi_ssl_sa</cfg:StrongAuthPolicy><cfg:MinPasswordLength>8</cfg:MinPasswordLength><cfg:MinPasswordCharacterGroups>2</cfg:MinPasswordCharacterGroups><cfg:CookieP3PHeader><![CDATA[CP="CAO DSP COR ADMa DEV CONo TELo CUR PSA PSD TAI IVDo OUR SAMi BUS DEM NAV STA UNI COM INT PHY ONL FIN PUR LOCi CNT"]]></cfg:CookieP3PHeader><cfg:ThrottleFlags>1</cfg:ThrottleFlags><cfg:ThrottleMaxRequests>200</cfg:ThrottleMaxRequests><cfg:ThrottleTotalIntervalSeconds>7200</cfg:ThrottleTotalIntervalSeconds><cfg:ThrottledApplications><cfg:ThrottledApp><cfg:ThrottledAppID>dc2191f2-1801-4fd8-84bd-e776344a34b0</cfg:ThrottledAppID><cfg:ThrottledAppMaxRequests>4</cfg:ThrottledAppMaxRequests></cfg:ThrottledApp></cfg:ThrottledApplications><cfg:NegativeCacheFlags>1</cfg:NegativeCacheFlags><cfg:NegativeCacheMaxRequests>3</cfg:NegativeCacheMaxRequests><cfg:NegativeCacheIntervalSeconds>28800</cfg:NegativeCacheIntervalSeconds><cfg:ConfigVersion>16.000.28287.00</cfg:ConfigVersion><cfg:ThrottleHWBindingMaxRequests>50</cfg:ThrottleHWBindingMaxRequests><cfg:ThrottleHWUpdateMaxRequests>2</cfg:ThrottleHWUpdateMaxRequests><cfg:ThrottleHWUpdateOutOfToleranceMaxRequests>2</cfg:ThrottleHWUpdateOutOfToleranceMaxRequests><cfg:ThrottleTpmBindingMaxRequests>3</cfg:ThrottleTpmBindingMaxRequests><cfg:DeviceProvisioningFailureThreshold>3</cfg:DeviceProvisioningFailureThreshold></cfg:Settings><cfg:ServiceURIs><cfg:DisableDIDCookie>1</cfg:DisableDIDCookie><cfg:MSNDomain>msn-int.com</cfg:MSNDomain><cfg:DADomain>login.live-int.com</cfg:DADomain><cfg:WLDomain>live-int.com</cfg:WLDomain><cfg:SignupDomain>login.live-int.com,signup.live-int.com,account.live-int.com</cfg:SignupDomain><cfg:LoginSrf>login.srf</cfg:LoginSrf><cfg:InvalidName><![CDATA[\%:[],#"<>;()]]></cfg:InvalidName><cfg:InvalidDomain><![CDATA[\%:[],#"<>;'()]]></cfg:InvalidDomain><cfg:InvalidUrl><![CDATA[\[],"<>';()]]></cfg:InvalidUrl><cfg:PostSrf>post.srf</cfg:PostSrf><cfg:ManageEIDsSrf>ManageEIDs.srf</cfg:ManageEIDsSrf><cfg:SecureSrf>secure.srf</cfg:SecureSrf><cfg:LogoutSrf>logout.srf</cfg:LogoutSrf><cfg:TrustedDomain>live-int.com,msn-int.com,zune.net,windowsmarketplace.com,workspace.office.live-int.com,atdmt.com</cfg:TrustedDomain><cfg:URL_PasswordReset>https://login.live-int.com/resetpw.srf</cfg:URL_PasswordReset><cfg:PassportSHA1Auth>https://login.live-int.com/ppsecure/SHA1Auth.srf</cfg:PassportSHA1Auth><cfg:DeviceIdTrustedDomain>*.live-int.com</cfg:DeviceIdTrustedDomain><cfg:DIDManagementDomain>login.live-int.com,account.live-int.com</cfg:DIDManagementDomain><cfg:PREFBINCookieDomain>c.live-int.com,c.msn-int.com</cfg:PREFBINCookieDomain><cfg:WLIDSTS_WCF>https://login.live-int.com/RST2.srf</cfg:WLIDSTS_WCF><cfg:URL_DeviceTOU>https://login.live-int.com/didtou.srf</cfg:URL_DeviceTOU><cfg:DeviceChangeService>https://login.live-int.com/ppsecure/devicechangecredential.srf</cfg:DeviceChangeService><cfg:DeviceAddService>https://login.live-int.com/ppsecure/deviceaddcredential.srf</cfg:DeviceAddService><cfg:DeviceRemoveService>https://login.live-int.com/ppsecure/deviceremovecredential.srf</cfg:DeviceRemoveService><cfg:DeviceAssociateService>https://login.live-int.com/ppsecure/DeviceAssociate.srf</cfg:DeviceAssociateService><cfg:DeviceDisassociateService>https://login.live-int.com/ppsecure/DeviceDisassociate.srf</cfg:DeviceDisassociateService><cfg:DeviceQueryService>https://login.live-int.com/ppsecure/DeviceQuery.srf</cfg:DeviceQueryService><cfg:DeviceUpdateService>https://login.live-int.com/ppsecure/DeviceUpdate.srf</cfg:DeviceUpdateService><cfg:DeviceEnumerateService>https://login.live-int.com/ppsecure/EnumerateDevices.srf</cfg:DeviceEnumerateService><cfg:ResolveUserService>https://login.live-int.com/ppsecure/ResolveUser.srf</cfg:ResolveUserService><cfg:AllowGetProperty>EID</cfg:AllowGetProperty><cfg:RealmInfoService>https://login.live-int.com/getrealminfo.srf</cfg:RealmInfoService><cfg:RealmInfoService6_0>https://login.live-int.com/getuserrealm.srf</cfg:RealmInfoService6_0><cfg:URL_Retention>https://login.live-int.com/retention.srf</cfg:URL_Retention><cfg:URL_Registration>https://signup.live-int.com/signup.aspx</cfg:URL_Registration><cfg:URL_Privacy>http://go.microsoft.com/fwlink/p/?LinkId=253457</cfg:URL_Privacy><cfg:GetUserKeyDataService>https://login.live-int.com/ppsecure/GetUserKeyData.srf</cfg:GetUserKeyDataService><cfg:CPAddUserSignIn>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80502</cfg:CPAddUserSignIn><cfg:CPAddUserSignUp>https://account.live-int.com/InlineSignup.aspx?iww=1&amp;id=80502</cfg:CPAddUserSignUp><cfg:CPAddUserIfExists>https://login.live-int.com/IfExists.srf?uiflavor=4&amp;id=80502</cfg:CPAddUserIfExists><!-- Auth-up URLs and all 806XX site IDs are used for versions of connect flows that can handle receiving a DA token during connect. --><cfg:OOBESignInAuthUp>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80600</cfg:OOBESignInAuthUp><cfg:OOBESignUpAuthUp>https://account.live-int.com/inlinesignup.aspx?iww=1&amp;id=80600</cfg:OOBESignUpAuthUp><cfg:OOBEIfExistsAuthUp>https://login.live-int.com/IfExists.srf?uiflavor=4&amp;id=80600</cfg:OOBEIfExistsAuthUp><cfg:OOBEConnect>https://login.live-int.com/ppsecure/InlineConnect.srf?id=80600</cfg:OOBEConnect><cfg:CPSignInAuthUp>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80601</cfg:CPSignInAuthUp><cfg:CPSignUpAuthUp>https://account.live-int.com/inlinesignup.aspx?iww=1&amp;id=80601</cfg:CPSignUpAuthUp><cfg:CPIfExistsAuthUp>https://login.live-int.com/IfExists.srf?uiflavor=4&amp;id=80601</cfg:CPIfExistsAuthUp><cfg:CPChangePwdAuthUp>https://account.live-int.com/Wizard/Password/Change?id=80601</cfg:CPChangePwdAuthUp><cfg:CPChangePwdOnline>https://login.live-int.com/ppsecure/InlinePOPAuth.srf?id=80601&amp;fid=cp</cfg:CPChangePwdOnline><cfg:CPConnect>https://login.live-int.com/ppsecure/InlineConnect.srf?id=80601</cfg:CPConnect><cfg:OOBEUpgradeSignIn>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80603</cfg:OOBEUpgradeSignIn><cfg:OOBEUpgradeSignUp>https://account.live-int.com/inlinesignup.aspx?iww=1&amp;id=80603</cfg:OOBEUpgradeSignUp><cfg:OOBEUpgradeConnect>https://login.live-int.com/ppsecure/InlineConnect.srf?id=80603</cfg:OOBEUpgradeConnect><cfg:CompleteAccountSignIn>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80604</cfg:CompleteAccountSignIn><cfg:CompleteAccountSignUp>https://account.live-int.com/inlinesignup.aspx?iww=1&amp;id=80604</cfg:CompleteAccountSignUp><cfg:CompleteAccountConnect>https://login.live-int.com/ppsecure/InlineConnect.srf?id=80604</cfg:CompleteAccountConnect><cfg:CXHSignInUpsell>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80604</cfg:CXHSignInUpsell><cfg:NthUserSignIn>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80605</cfg:NthUserSignIn><cfg:NthUserSignUp>https://account.live-int.com/inlinesignup.aspx?iww=1&amp;id=80605</cfg:NthUserSignUp><cfg:NthUserConnect>https://login.live-int.com/ppsecure/InlinePOPAuth.srf?id=80605</cfg:NthUserConnect><cfg:CXHSignIn>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80606</cfg:CXHSignIn><cfg:CXHTransientSignIn>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80607</cfg:CXHTransientSignIn><cfg:CXHReAuth>https://login.live-int.com/ppsecure/InlineLogin.srf?id=80608</cfg:CXHReAuth><cfg:CXHNGCUpsell>https://account.live-int.com/msangcwam</cfg:CXHNGCUpsell><cfg:URL_AccountSettings>https://account.microsoft-int.com/?ref=settings</cfg:URL_AccountSettings><cfg:URL_ProofManage>http://go.microsoft.com/fwlink/?LinkId=249173</cfg:URL_ProofManage><cfg:InlineClientAuth>https://login.live-int.com/ppsecure/InlineClientAuth.srf</cfg:InlineClientAuth><cfg:InlineClientAuthEnd>https://login.live-int.com/ppsecure/InlineDesktop.srf</cfg:InlineClientAuthEnd><cfg:ManageApprover>https://login.live-int.com/ManageApprover.srf</cfg:ManageApprover><cfg:ListSessions>https://login.live-int.com/ListSessions.srf</cfg:ListSessions><cfg:ApproveSession>https://login.live-int.com/ApproveSession.srf</cfg:ApproveSession><cfg:ManageLoginKeys>https://login.live-int.com/ManageLoginKeys.srf</cfg:ManageLoginKeys><cfg:GetAppDataService>https://login.live-int.com/ppsecure/GetAppData.srf</cfg:GetAppDataService></cfg:ServiceURIs></cfg:Configuration></Signature>