<dcmPS:AdvDiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>SearchDiagnostic</ID>
    <Version>1.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Rootcauses>
    <Rootcause>
      <ID>RC_IndexingService</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-101</Name>
        <Description>@diagpackage.dll,-102</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters>
            <Parameter>
              <Name>Action</Name>
              <DefaultValue>Diagnose</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>TS_IndexingService.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_StartIndexingService</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-103</Name>
            <Description>@diagpackage.dll,-104</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_StartIndexingService.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>Action</Name>
              <DefaultValue>Verify</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_IndexingService.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_IndexingServiceCrashing</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-201</Name>
        <Description>@diagpackage.dll,-202</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_IndexingServiceCrashing.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_IndexingServiceCrashing</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-203</Name>
            <Description>@diagpackage.dll,-204</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_FilterHostCrashing</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-301</Name>
        <Description>@diagpackage.dll,-302</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_FilterHostCrashing.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_FilterHostCrashing</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-303</Name>
            <Description>@diagpackage.dll,-304</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_ProtocolHostCrashing</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-401</Name>
        <Description>@diagpackage.dll,-402</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_ProtocolHostCrashing.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_ProtocolHostCrashing</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-403</Name>
            <Description>@diagpackage.dll,-404</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_BadPermissions</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-501</Name>
        <Description>@diagpackage.dll,-502</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_CheckPermissions.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_RestorePermissions</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-503</Name>
            <Description>@diagpackage.dll,-504</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_RestorePermissions.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_CheckPermissions.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_ForcedShutdownNoCorruption</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-601</Name>
        <Description>@diagpackage.dll,-602</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_ForcedShutdownNoCorruption.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_ProtocolHostCrashing</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-603</Name>
            <Description>@diagpackage.dll,-604</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_ForcedShutdownInRecovery</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-701</Name>
        <Description>@diagpackage.dll,-702</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_ForcedShutdownInRecovery.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_ForcedShutdownInRecovery</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-703</Name>
            <Description>@diagpackage.dll,-704</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_SearchApp</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-705</Name>
        <Description>@diagpackage.dll,-706</Description>
      </DisplayInformation>
      <Troubleshooter>
        <Script>
          <Parameters>
            <Parameter>
              <Name>Action</Name>
              <DefaultValue>Diagnose</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>TS_SearchApp.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Troubleshooter>
      <Resolvers>
        <Resolver>
          <ID>RS_ResetWindowsSearchApp</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-707</Name>
            <Description>@diagpackage.dll,-708</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_ResetWindowsSearchApp.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>Action</Name>
              <DefaultValue>Verify</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_SearchApp.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions/>
    <MultipleResponseInteractions>
      <MultipleResponseInteraction>
      <AllowDynamicResponses>false</AllowDynamicResponses>
      <Choices>
          <Choice>
              <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-903</Name>
                  <Description>@diagpackage.dll,-904</Description>
              </DisplayInformation>
              <Value>SearchAppProblem</Value>
              <ExtensionPoint/>
          </Choice>
          <Choice>
              <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-905</Name>
                  <Description>@diagpackage.dll,-906</Description>
              </DisplayInformation>
              <Value>FilesMissingProblem</Value>
              <ExtensionPoint/>
          </Choice>
          <Choice>
              <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-907</Name>
                  <Description>@diagpackage.dll,-908</Description>
              </DisplayInformation>
              <Value>ResourceUsageProblem</Value>
              <ExtensionPoint/>
          </Choice>
          <Choice>
              <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-909</Name>
                  <Description>@diagpackage.dll,-910</Description>
              </DisplayInformation>
              <Value>UnknownProblem</Value>
              <ExtensionPoint/>
          </Choice>
          <Choice>
              <DisplayInformation>
                  <Parameters/>
                  <Name>@diagpackage.dll,-911</Name>
                  <Description>@diagpackage.dll,-912</Description>
              </DisplayInformation>
              <Value>UnknownProblem</Value>
              <ExtensionPoint/>
          </Choice>
        </Choices>
      <ID>IT_ProblemDisplay</ID>
      <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-901</Name>
          <Description>@diagpackage.dll,-902</Description>
      </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint/>
      </MultipleResponseInteraction>
    </MultipleResponseInteractions>
    <TextInteractions>
      <TextInteraction>
        <RegularExpression/>
        <ID>IT_UnknownProblem</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-801</Name>
          <Description>@diagpackage.dll,-802</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <RTFDescription>@diagpackage.dll,-803</RTFDescription>
        </ExtensionPoint>
      </TextInteraction>
    </TextInteractions>
    <PauseInteractions/>
    <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-12</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-16</HelpKeywords>
    <Feedback>
      <ContextId>925</ContextId>
    </Feedback> 
  </ExtensionPoint>
</dcmPS:AdvDiagnosticPackage>