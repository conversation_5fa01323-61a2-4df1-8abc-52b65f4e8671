{"ramps": [{"id": 3, "offset": 0, "share": 100}, {"id": 4, "offset": 0, "share": 100}, {"id": 5, "offset": 0, "share": 100}, {"id": 6, "offset": 0, "share": 100}, {"id": 7, "offset": 0, "share": 1}, {"id": 8, "offset": 0, "share": 1}, {"id": 9, "offset": 0, "share": 1}, {"id": 10, "offset": 0, "share": 1}, {"id": 14, "offset": 0, "share": 1}, {"id": 17, "offset": 0, "share": 100}, {"id": 18, "offset": 0, "share": 100}, {"id": 21, "offset": 0, "share": 1}, {"id": 22, "offset": 0, "share": 1}, {"id": 24, "offset": 0, "share": 100}, {"id": 25, "offset": 0, "share": 100}, {"id": 26, "offset": 0, "share": 100}, {"id": 27, "offset": 0, "share": 100}, {"id": 29, "offset": 0, "share": 100}, {"id": 30, "offset": 0, "share": 100}, {"id": 31, "offset": 31, "share": 100}, {"id": 34, "offset": 0, "share": 100}, {"id": 35, "offset": 0, "share": 100}, {"id": 36, "offset": 0, "share": 100}, {"id": 37, "offset": 0, "share": 100}, {"id": 39, "offset": 0, "share": 100}, {"id": 41, "offset": 0, "share": 100}, {"id": 43, "offset": 0, "share": 100}, {"id": 44, "offset": 0, "share": 100}, {"id": 45, "offset": 0, "share": 50}, {"id": 46, "offset": 0, "share": 50}, {"id": 53, "offset": 0, "share": 1}, {"id": 54, "offset": 0, "share": 100}, {"id": 55, "offset": 0, "share": 100}, {"id": 56, "offset": 0, "share": 100}, {"id": 58, "offset": 0, "share": 100}, {"id": 61, "offset": 0, "share": 100}, {"id": 63, "offset": 0, "share": 100}, {"id": 66, "offset": 0, "share": 100}, {"id": 67, "offset": 0, "share": 10}, {"id": 70, "offset": 0, "share": 100}, {"id": 71, "offset": 0, "share": 100}, {"id": 73, "offset": 0, "share": 100}, {"id": 77, "offset": 0, "share": 100}, {"id": 78, "offset": 0, "share": 100}, {"id": 80, "offset": 0, "share": 100}, {"id": 82, "offset": 0, "share": 100}, {"id": 83, "offset": 0, "share": 100}, {"id": 84, "offset": 0, "share": 100}, {"id": 85, "offset": 0, "share": 100}, {"id": 86, "offset": 0, "share": 100}, {"id": 87, "offset": 0, "share": 100}, {"id": 88, "offset": 0, "share": 100}, {"id": 92, "offset": 0, "share": 100}, {"id": 94, "offset": 0, "share": 100}, {"id": 98, "offset": 0, "share": 100}, {"id": 100, "offset": 0, "share": 100}, {"id": 103, "offset": 0, "share": 100}, {"id": 105, "offset": 100, "share": 100}, {"id": 107, "offset": 23, "share": 100}, {"id": 108, "offset": 0, "share": 100}, {"id": 109, "offset": 0, "share": 100}, {"id": 112, "offset": 0, "share": 100}, {"id": 114, "offset": 0, "share": 100}, {"id": 117, "offset": 0, "share": 100}, {"id": 118, "offset": 0, "share": 100}, {"id": 120, "offset": 0, "share": 100}, {"id": 121, "offset": 0, "share": 100}, {"id": 122, "offset": 50, "share": 100}, {"id": 123, "offset": 0, "share": 10}, {"id": 124, "offset": 0, "share": 100}, {"id": 125, "offset": 0, "share": 100}, {"id": 127, "offset": 0, "share": 100}, {"id": 129, "offset": 0, "share": 100}, {"id": 131, "offset": 0, "share": 10}, {"id": 133, "offset": 0, "share": 100}, {"id": 135, "offset": 0, "share": 100}, {"id": 136, "offset": 0, "share": 100}, {"id": 138, "offset": 0, "share": 100}, {"id": 141, "offset": 0, "share": 100}, {"id": 144, "offset": 0, "share": 100}, {"id": 146, "offset": 0, "share": 100}, {"id": 151, "offset": 0, "share": 100}, {"id": 153, "offset": 0, "share": 100}, {"id": 155, "offset": 0, "share": 100}, {"id": 156, "offset": 0, "share": 100}, {"id": 157, "offset": 0, "share": 100}, {"id": 158, "offset": 0, "share": 100}, {"id": 159, "offset": 0, "share": 100}, {"id": 165, "offset": 0, "share": 100}, {"id": 167, "offset": 0, "share": 100}, {"id": 168, "offset": 0, "share": 100}, {"id": 172, "offset": 0, "share": 100}, {"id": 173, "offset": 0, "share": 100}, {"id": 174, "offset": 0, "share": 100}, {"id": 175, "offset": 0, "share": 100}, {"id": 176, "offset": 0, "share": 100}, {"id": 177, "offset": 0, "share": 100}, {"id": 178, "offset": 0, "share": 100}, {"id": 179, "offset": 0, "share": 5}, {"id": 182, "offset": 0, "share": 100}, {"id": 183, "offset": 0, "share": 100}, {"id": 191, "offset": 0, "share": 100}, {"id": 194, "offset": 0, "share": 100}, {"id": 195, "offset": 0, "share": 100}, {"id": 196, "offset": 0, "share": 100}, {"id": 197, "offset": 0, "share": 100}, {"id": 198, "offset": 0, "share": 100}, {"id": 200, "offset": 0, "share": 100}, {"id": 201, "offset": 0, "share": 100}, {"id": 204, "offset": 0, "share": 100}, {"id": 205, "offset": 0, "share": 100}, {"id": 206, "offset": 0, "share": 100}, {"id": 207, "offset": 0, "share": 100}, {"id": 208, "offset": 0, "share": 100}, {"id": 210, "offset": 0, "share": 100}, {"id": 211, "offset": 0, "share": 100}, {"id": 216, "offset": 0, "share": 100}, {"id": 217, "offset": 0, "share": 100}, {"id": 218, "offset": 0, "share": 100}, {"id": 219, "offset": 0, "share": 100}, {"id": 221, "offset": 0, "share": 100}, {"id": 224, "offset": 0, "share": 100}, {"id": 226, "offset": 0, "share": 100}, {"id": 228, "offset": 0, "share": 100}, {"id": 231, "offset": 0, "share": 100}, {"id": 232, "offset": 0, "share": 100}, {"id": 234, "offset": 0, "share": 100}, {"id": 239, "offset": 0, "share": 100}, {"id": 240, "offset": 0, "share": 100}, {"id": 241, "offset": 0, "share": 100}, {"id": 242, "offset": 0, "share": 100}, {"id": 248, "offset": 0, "share": 100}, {"id": 249, "offset": 0, "share": 100}, {"id": 251, "offset": 0, "share": 100}, {"id": 252, "offset": 0, "share": 100}, {"id": 253, "offset": 0, "share": 100}, {"id": 254, "offset": 0, "share": 100}, {"id": 256, "offset": 0, "share": 100}, {"id": 260, "offset": 0, "share": 100}, {"id": 262, "offset": 0, "share": 100}, {"id": 265, "offset": 0, "share": 100}, {"id": 271, "offset": 0, "share": 100}, {"id": 274, "offset": 0, "share": 100}, {"id": 276, "offset": 0, "share": 100}, {"id": 277, "offset": 0, "share": 100}, {"id": 281, "offset": 0, "share": 100}, {"id": 282, "offset": 0, "share": 100}, {"id": 289, "offset": 0, "share": 100}, {"id": 290, "offset": 0, "share": 100}, {"id": 293, "offset": 0, "share": 100}, {"id": 299, "offset": 0, "share": 100}, {"id": 300, "offset": 0, "share": 100}, {"id": 301, "offset": 0, "share": 100}, {"id": 303, "offset": 0, "share": 100}, {"id": 304, "offset": 0, "share": 100}, {"id": 306, "offset": 0, "share": 100}, {"id": 307, "offset": 0, "share": 100}, {"id": 315, "offset": 0, "share": 100}, {"id": 316, "offset": 0, "share": 100}, {"id": 317, "offset": 0, "share": 100}, {"id": 318, "offset": 0, "share": 100}, {"id": 319, "offset": 0, "share": 100}, {"id": 320, "offset": 0, "share": 100}, {"id": 322, "offset": 0, "share": 100}, {"id": 323, "offset": 0, "share": 100}, {"id": 326, "offset": 0, "share": 100}, {"id": 327, "offset": 0, "share": 100}, {"id": 330, "offset": 0, "share": 100}, {"id": 331, "offset": 0, "share": 100}, {"id": 332, "offset": 0, "share": 100}, {"id": 334, "offset": 0, "share": 100}, {"id": 335, "offset": 0, "share": 100}, {"id": 336, "offset": 0, "share": 100}, {"id": 337, "offset": 0, "share": 100}, {"id": 338, "offset": 0, "share": 100}, {"id": 339, "offset": 0, "share": 100}, {"id": 346, "offset": 0, "share": 100}, {"id": 352, "offset": 0, "share": 100}, {"id": 358, "offset": 0, "share": 100}, {"id": 360, "offset": 0, "share": 100}, {"id": 362, "offset": 0, "share": 100}, {"id": 364, "offset": 0, "share": 100}, {"id": 370, "offset": 0, "share": 100}, {"id": 373, "offset": 0, "share": 100}, {"id": 374, "offset": 17, "share": 100}, {"id": 379, "offset": 0, "share": 100}, {"id": 380, "offset": 0, "share": 100}, {"id": 381, "offset": 0, "share": 100}, {"id": 382, "offset": 0, "share": 100}, {"id": 383, "offset": 0, "share": 100}, {"id": 384, "offset": 0, "share": 100}, {"id": 385, "offset": 0, "share": 100}, {"id": 386, "offset": 0, "share": 100}, {"id": 388, "offset": 0, "share": 100}, {"id": 389, "offset": 0, "share": 100}, {"id": 392, "offset": 0, "share": 100}, {"id": 393, "offset": 0, "share": 100}, {"id": 396, "offset": 0, "share": 100}, {"id": 398, "offset": 0, "share": 100}, {"id": 401, "offset": 0, "share": 100}, {"id": 402, "offset": 0, "share": 100}, {"id": 404, "offset": 0, "share": 100}, {"id": 405, "offset": 0, "share": 100}, {"id": 406, "offset": 0, "share": 100}, {"id": 407, "offset": 0, "share": 100}, {"id": 408, "offset": 0, "share": 100}, {"id": 414, "offset": 0, "share": 100}, {"id": 416, "offset": 0, "share": 100}, {"id": 421, "offset": 0, "share": 100}, {"id": 422, "offset": 0, "share": 100}, {"id": 426, "offset": 0, "share": 100}, {"id": 430, "offset": 0, "share": 100}, {"id": 434, "offset": 0, "share": 100}, {"id": 435, "offset": 0, "share": 100}, {"id": 436, "offset": 0, "share": 100}, {"id": 438, "offset": 0, "share": 100}, {"id": 440, "offset": 0, "share": 100}, {"id": 443, "offset": 0, "share": 100}, {"id": 444, "offset": 0, "share": 100}, {"id": 445, "offset": 0, "share": 100}, {"id": 446, "offset": 0, "share": 100}, {"id": 447, "offset": 0, "share": 100}, {"id": 448, "offset": 0, "share": 100}, {"id": 449, "offset": 0, "share": 100}, {"id": 450, "offset": 0, "share": 100}, {"id": 455, "offset": 0, "share": 100}, {"id": 456, "offset": 0, "share": 100}, {"id": 459, "offset": 0, "share": 25}, {"id": 460, "offset": 0, "share": 100}, {"id": 462, "offset": 0, "share": 100}, {"id": 471, "offset": 0, "share": 100}, {"id": 474, "offset": 0, "share": 100}, {"id": 475, "offset": 0, "share": 100}, {"id": 477, "offset": 0, "share": 50}, {"id": 478, "offset": 0, "share": 100}, {"id": 479, "offset": 0, "share": 100}, {"id": 480, "offset": 0, "share": 100}, {"id": 485, "offset": 0, "share": 100}, {"id": 486, "offset": 0, "share": 100}, {"id": 490, "offset": 0, "share": 100}, {"id": 491, "offset": 0, "share": 100}, {"id": 493, "offset": 0, "share": 100}, {"id": 495, "offset": 0, "share": 100}, {"id": 501, "offset": 0, "share": 100}, {"id": 502, "offset": 0, "share": 100}, {"id": 503, "offset": 0, "share": 100}, {"id": 504, "offset": 0, "share": 100}, {"id": 506, "offset": 0, "share": 100}, {"id": 509, "offset": 0, "share": 100}, {"id": 511, "offset": 0, "share": 100}, {"id": 515, "offset": 0, "share": 100}, {"id": 516, "offset": 0, "share": 100}, {"id": 517, "offset": 0, "share": 100}, {"id": 518, "offset": 23, "share": 100}, {"id": 519, "offset": 0, "share": 100}, {"id": 520, "offset": 0, "share": 100}, {"id": 521, "offset": 0, "share": 100}, {"id": 522, "offset": 0, "share": 100}, {"id": 523, "offset": 23, "share": 100}, {"id": 526, "offset": 0, "share": 100}, {"id": 528, "offset": 0, "share": 100}, {"id": 531, "offset": 0, "share": 100}, {"id": 532, "offset": 23, "share": 100}, {"id": 533, "offset": 0, "share": 100}, {"id": 534, "offset": 0, "share": 100}, {"id": 535, "offset": 0, "share": 100}, {"id": 536, "offset": 0, "share": 100}, {"id": 537, "offset": 0, "share": 100}, {"id": 539, "offset": 0, "share": 100}, {"id": 540, "offset": 0, "share": 100}, {"id": 543, "offset": 0, "share": 100}, {"id": 544, "offset": 0, "share": 100}, {"id": 545, "offset": 0, "share": 100}, {"id": 546, "offset": 0, "share": 100}, {"id": 547, "offset": 0, "share": 100}, {"id": 548, "offset": 0, "share": 100}, {"id": 549, "offset": 0, "share": 100}, {"id": 550, "offset": 0, "share": 100}, {"id": 552, "offset": 0, "share": 100}, {"id": 555, "offset": 0, "share": 100}, {"id": 556, "offset": 0, "share": 100}, {"id": 558, "offset": 0, "share": 100}, {"id": 559, "offset": 0, "share": 100}, {"id": 561, "offset": 0, "share": 100}, {"id": 565, "offset": 23, "share": 100}, {"id": 566, "offset": 0, "share": 100}, {"id": 568, "offset": 0, "share": 100}, {"id": 569, "offset": 0, "share": 100}, {"id": 570, "offset": 0, "share": 100}, {"id": 572, "offset": 0, "share": 100}, {"id": 574, "offset": 0, "share": 100}, {"id": 575, "offset": 0, "share": 100}, {"id": 577, "offset": 0, "share": 100}, {"id": 578, "offset": 0, "share": 100}, {"id": 579, "offset": 0, "share": 100}, {"id": 580, "offset": 0, "share": 100}, {"id": 582, "offset": 0, "share": 100}, {"id": 584, "offset": 23, "share": 60}, {"id": 586, "offset": 0, "share": 100}, {"id": 590, "offset": 0, "share": 100}, {"id": 594, "offset": 0, "share": 100}, {"id": 596, "offset": 0, "share": 100}, {"id": 598, "offset": 0, "share": 1}, {"id": 600, "offset": 0, "share": 100}, {"id": 604, "offset": 0, "share": 100}, {"id": 605, "offset": 0, "share": 100}, {"id": 606, "offset": 0, "share": 100}, {"id": 612, "offset": 0, "share": 100}, {"id": 614, "offset": 0, "share": 100}, {"id": 615, "offset": 17, "share": 100}, {"id": 616, "offset": 0, "share": 100}, {"id": 618, "offset": 0, "share": 100}, {"id": 621, "offset": 0, "share": 100}, {"id": 622, "offset": 0, "share": 100}, {"id": 625, "offset": 0, "share": 100}, {"id": 626, "offset": 0, "share": 100}, {"id": 627, "offset": 0, "share": 100}, {"id": 628, "offset": 0, "share": 100}, {"id": 629, "offset": 0, "share": 100}, {"id": 630, "offset": 0, "share": 100}, {"id": 634, "offset": 0, "share": 100}, {"id": 637, "offset": 0, "share": 100}, {"id": 640, "offset": 0, "share": 100}, {"id": 643, "offset": 0, "share": 100}, {"id": 644, "offset": 0, "share": 100}, {"id": 646, "offset": 0, "share": 100}, {"id": 651, "offset": 0, "share": 100}, {"id": 653, "offset": 0, "share": 100}, {"id": 655, "offset": 0, "share": 100}, {"id": 657, "offset": 0, "share": 100}, {"id": 658, "offset": 0, "share": 100}, {"id": 662, "offset": 0, "share": 100}, {"id": 667, "offset": 0, "share": 100}, {"id": 668, "offset": 0, "share": 100}, {"id": 671, "offset": 0, "share": 100}, {"id": 672, "offset": 0, "share": 100}, {"id": 674, "offset": 0, "share": 100}, {"id": 679, "offset": 0, "share": 100}, {"id": 680, "offset": 0, "share": 100}, {"id": 684, "offset": 0, "share": 25}, {"id": 686, "offset": 0, "share": 100}, {"id": 689, "offset": 0, "share": 100}, {"id": 692, "offset": 0, "share": 100}, {"id": 693, "offset": 0, "share": 100}, {"id": 694, "offset": 0, "share": 100}, {"id": 695, "offset": 0, "share": 100}, {"id": 697, "offset": 0, "share": 100}, {"id": 700, "offset": 0, "share": 100}, {"id": 701, "offset": 0, "share": 100}, {"id": 703, "offset": 23, "share": 100}, {"id": 704, "offset": 0, "share": 100}, {"id": 706, "offset": 0, "share": 100}, {"id": 709, "offset": 0, "share": 50}, {"id": 710, "offset": 0, "share": 100}, {"id": 715, "offset": 0, "share": 100}, {"id": 716, "offset": 0, "share": 100}, {"id": 718, "offset": 23, "share": 100}, {"id": 719, "offset": 0, "share": 100}, {"id": 720, "offset": 0, "share": 100}, {"id": 722, "offset": 0, "share": 100}, {"id": 723, "offset": 0, "share": 100}, {"id": 724, "offset": 0, "share": 100}, {"id": 726, "offset": 0, "share": 100}, {"id": 727, "offset": 0, "share": 100}, {"id": 728, "offset": 0, "share": 100}, {"id": 729, "offset": 0, "share": 100}, {"id": 730, "offset": 0, "share": 100}, {"id": 732, "offset": 0, "share": 100}, {"id": 739, "offset": 0, "share": 100}, {"id": 741, "offset": 0, "share": 100}, {"id": 745, "offset": 0, "share": 100}, {"id": 746, "offset": 23, "share": 100}, {"id": 747, "offset": 23, "share": 100}, {"id": 748, "offset": 0, "share": 1}, {"id": 752, "offset": 0, "share": 100}, {"id": 753, "offset": 0, "share": 100}, {"id": 755, "offset": 0, "share": 100}, {"id": 762, "offset": 0, "share": 100}, {"id": 763, "offset": 23, "share": 100}, {"id": 764, "offset": 23, "share": 100}, {"id": 767, "offset": 0, "share": 100}, {"id": 768, "offset": 0, "share": 100}, {"id": 770, "offset": 0, "share": 100}, {"id": 771, "offset": 0, "share": 100}, {"id": 773, "offset": 0, "share": 100}, {"id": 774, "offset": 0, "share": 100}, {"id": 775, "offset": 0, "share": 100}, {"id": 779, "offset": 0, "share": 100}, {"id": 783, "offset": 0, "share": 100}, {"id": 784, "offset": 0, "share": 100}, {"id": 786, "offset": 0, "share": 100}, {"id": 793, "offset": 0, "share": 100}, {"id": 796, "offset": 0, "share": 100}, {"id": 800, "offset": 0, "share": 100}, {"id": 801, "offset": 0, "share": 100}, {"id": 804, "offset": 0, "share": 100}, {"id": 806, "offset": 0, "share": 100}, {"id": 812, "offset": 0, "share": 100}, {"id": 813, "offset": 0, "share": 100}, {"id": 814, "offset": 0, "share": 100}, {"id": 817, "offset": 0, "share": 100}, {"id": 818, "offset": 0, "share": 100}, {"id": 820, "offset": 0, "share": 100}, {"id": 821, "offset": 0, "share": 100}, {"id": 822, "offset": 0, "share": 100}, {"id": 823, "offset": 0, "share": 100}, {"id": 824, "offset": 0, "share": 100}, {"id": 825, "offset": 0, "share": 100}, {"id": 829, "offset": 0, "share": 100}, {"id": 831, "offset": 0, "share": 100}, {"id": 833, "offset": 0, "share": 100}, {"id": 836, "offset": 0, "share": 100}, {"id": 837, "offset": 0, "share": 100}, {"id": 838, "offset": 0, "share": 100}, {"id": 839, "offset": 0, "share": 100}, {"id": 840, "offset": 0, "share": 100}, {"id": 842, "offset": 23, "share": 100}, {"id": 843, "offset": 0, "share": 100}, {"id": 845, "offset": 0, "share": 100}, {"id": 846, "offset": 0, "share": 100}, {"id": 847, "offset": 0, "share": 100}, {"id": 851, "offset": 0, "share": 100}, {"id": 854, "offset": 0, "share": 100}, {"id": 856, "offset": 0, "share": 100}, {"id": 857, "offset": 0, "share": 100}, {"id": 859, "offset": 0, "share": 100}, {"id": 860, "offset": 0, "share": 100}, {"id": 863, "offset": 0, "share": 100}, {"id": 864, "offset": 0, "share": 100}, {"id": 865, "offset": 0, "share": 100}, {"id": 866, "offset": 0, "share": 100}, {"id": 867, "offset": 0, "share": 100}, {"id": 871, "offset": 0, "share": 100}, {"id": 874, "offset": 0, "share": 100}, {"id": 875, "offset": 0, "share": 100}, {"id": 876, "offset": 0, "share": 100}, {"id": 878, "offset": 0, "share": 100}, {"id": 879, "offset": 0, "share": 10}, {"id": 880, "offset": 0, "share": 100}, {"id": 882, "offset": 0, "share": 50}, {"id": 886, "offset": 0, "share": 50}, {"id": 888, "offset": 0, "share": 100}, {"id": 893, "offset": 0, "share": 100}, {"id": 896, "offset": 0, "share": 100}, {"id": 897, "offset": 0, "share": 100}, {"id": 898, "offset": 0, "share": 100}, {"id": 900, "offset": 0, "share": 100}, {"id": 901, "offset": 0, "share": 100}, {"id": 902, "offset": 0, "share": 100}, {"id": 904, "offset": 0, "share": 100}, {"id": 908, "offset": 0, "share": 100}, {"id": 909, "offset": 0, "share": 100}, {"id": 910, "offset": 0, "share": 100}, {"id": 911, "offset": 23, "share": 100}, {"id": 912, "offset": 0, "share": 100}, {"id": 915, "offset": 0, "share": 100}, {"id": 916, "offset": 0, "share": 100}, {"id": 918, "offset": 0, "share": 100}, {"id": 919, "offset": 0, "share": 100}, {"id": 921, "offset": 0, "share": 100}, {"id": 922, "offset": 0, "share": 100}, {"id": 923, "offset": 0, "share": 100}, {"id": 925, "offset": 0, "share": 100}, {"id": 926, "offset": 0, "share": 100}, {"id": 927, "offset": 0, "share": 100}, {"id": 928, "offset": 0, "share": 100}, {"id": 929, "offset": 0, "share": 100}, {"id": 932, "offset": 0, "share": 100}, {"id": 933, "offset": 0, "share": 100}, {"id": 935, "offset": 0, "share": 100}, {"id": 941, "offset": 0, "share": 100}, {"id": 942, "offset": 0, "share": 100}, {"id": 944, "offset": 0, "share": 100}, {"id": 948, "offset": 0, "share": 100}, {"id": 949, "offset": 0, "share": 100}, {"id": 953, "offset": 0, "share": 100}, {"id": 954, "offset": 0, "share": 100}, {"id": 955, "offset": 0, "share": 100}, {"id": 957, "offset": 0, "share": 100}, {"id": 959, "offset": 0, "share": 100}, {"id": 960, "offset": 0, "share": 100}, {"id": 961, "offset": 0, "share": 100}, {"id": 963, "offset": 0, "share": 100}, {"id": 964, "offset": 0, "share": 100}, {"id": 966, "offset": 0, "share": 100}, {"id": 968, "offset": 0, "share": 100}, {"id": 969, "offset": 0, "share": 100}, {"id": 971, "offset": 0, "share": 100}, {"id": 972, "offset": 0, "share": 100}, {"id": 973, "offset": 0, "share": 100}, {"id": 977, "offset": 0, "share": 100}, {"id": 979, "offset": 0, "share": 100}, {"id": 987, "offset": 0, "share": 100}, {"id": 989, "offset": 0, "share": 100}, {"id": 993, "offset": 0, "share": 100}, {"id": 994, "offset": 0, "share": 100}, {"id": 995, "offset": 0, "share": 100}, {"id": 997, "offset": 0, "share": 100}, {"id": 999, "offset": 0, "share": 100}, {"id": 1000, "offset": 0, "share": 100}, {"id": 1003, "offset": 0, "share": 100}, {"id": 1006, "offset": 0, "share": 100}, {"id": 1007, "offset": 0, "share": 100}, {"id": 1014, "offset": 0, "share": 100}, {"id": 1015, "offset": 0, "share": 100}, {"id": 1016, "offset": 0, "share": 100}, {"id": 1020, "offset": 0, "share": 100}, {"id": 1021, "offset": 0, "share": 100}, {"id": 1022, "offset": 0, "share": 100}, {"id": 1024, "offset": 0, "share": 100}, {"id": 1031, "offset": 0, "share": 100}, {"id": 1032, "offset": 0, "share": 100}, {"id": 1034, "offset": 0, "share": 100}, {"id": 1035, "offset": 0, "share": 100}, {"id": 1037, "offset": 0, "share": 100}, {"id": 1038, "offset": 0, "share": 100}, {"id": 1045, "offset": 0, "share": 100}, {"id": 1046, "offset": 0, "share": 100}, {"id": 1048, "offset": 0, "share": 100}, {"id": 1058, "offset": 0, "share": 100}, {"id": 1059, "offset": 0, "share": 100}, {"id": 1062, "offset": 0, "share": 100}, {"id": 1064, "offset": 0, "share": 100}, {"id": 1068, "offset": 0, "share": 100}, {"id": 1069, "offset": 0, "share": 100}, {"id": 1070, "offset": 0, "share": 100}, {"id": 1072, "offset": 0, "share": 100}, {"id": 1073, "offset": 0, "share": 100}, {"id": 1075, "offset": 0, "share": 100}, {"id": 1076, "offset": 0, "share": 100}, {"id": 1079, "offset": 0, "share": 100}, {"id": 1080, "offset": 0, "share": 100}, {"id": 1082, "offset": 0, "share": 100}, {"id": 1083, "offset": 0, "share": 100}, {"id": 1084, "offset": 0, "share": 100}, {"id": 1085, "offset": 0, "share": 100}, {"id": 1086, "offset": 0, "share": 100}, {"id": 1087, "offset": 0, "share": 100}, {"id": 1088, "offset": 0, "share": 100}, {"id": 1093, "offset": 0, "share": 100}, {"id": 1094, "offset": 0, "share": 100}, {"id": 1097, "offset": 0, "share": 100}, {"id": 1104, "offset": 0, "share": 100}, {"id": 1105, "offset": 0, "share": 100}, {"id": 1106, "offset": 0, "share": 100}, {"id": 1107, "offset": 0, "share": 100}, {"id": 1108, "offset": 0, "share": 100}, {"id": 1109, "offset": 0, "share": 100}, {"id": 1111, "offset": 0, "share": 100}, {"id": 1114, "offset": 0, "share": 100}, {"id": 1115, "offset": 0, "share": 100}, {"id": 1122, "offset": 0, "share": 100}, {"id": 1123, "offset": 0, "share": 100}, {"id": 1124, "offset": 0, "share": 100}, {"id": 1126, "offset": 0, "share": 100}, {"id": 1130, "offset": 0, "share": 100}, {"id": 1138, "offset": 0, "share": 100}, {"id": 1145, "offset": 0, "share": 100}, {"id": 1148, "offset": 0, "share": 100}, {"id": 1149, "offset": 0, "share": 100}, {"id": 1150, "offset": 0, "share": 100}, {"id": 1151, "offset": 0, "share": 100}, {"id": 1152, "offset": 0, "share": 100}, {"id": 1153, "offset": 0, "share": 100}, {"id": 1154, "offset": 0, "share": 100}, {"id": 1155, "offset": 0, "share": 100}, {"id": 1158, "offset": 0, "share": 100}, {"id": 1163, "offset": 0, "share": 100}, {"id": 1165, "offset": 0, "share": 100}, {"id": 1168, "offset": 0, "share": 100}, {"id": 1172, "offset": 0, "share": 100}, {"id": 1176, "offset": 0, "share": 100}, {"id": 1180, "offset": 0, "share": 100}, {"id": 1181, "offset": 0, "share": 100}, {"id": 1182, "offset": 0, "share": 100}, {"id": 1184, "offset": 0, "share": 100}, {"id": 1188, "offset": 0, "share": 100}, {"id": 1189, "offset": 0, "share": 100}, {"id": 1190, "offset": 0, "share": 100}, {"id": 1191, "offset": 0, "share": 100}, {"id": 1192, "offset": 0, "share": 100}, {"id": 1193, "offset": 0, "share": 100}, {"id": 1194, "offset": 0, "share": 100}, {"id": 1195, "offset": 0, "share": 100}, {"id": 1197, "offset": 0, "share": 100}, {"id": 1198, "offset": 0, "share": 100}, {"id": 1201, "offset": 0, "share": 100}, {"id": 1202, "offset": 0, "share": 100}, {"id": 1206, "offset": 0, "share": 100}, {"id": 1210, "offset": 0, "share": 100}, {"id": 1211, "offset": 0, "share": 100}, {"id": 1213, "offset": 0, "share": 100}, {"id": 1215, "offset": 0, "share": 100}, {"id": 1216, "offset": 0, "share": 100}, {"id": 1217, "offset": 0, "share": 100}, {"id": 1218, "offset": 0, "share": 100}, {"id": 1219, "offset": 0, "share": 100}, {"id": 1220, "offset": 0, "share": 100}, {"id": 1223, "offset": 0, "share": 100}, {"id": 1224, "offset": 0, "share": 100}, {"id": 1227, "offset": 0, "share": 100}, {"id": 1230, "offset": 0, "share": 100}, {"id": 1231, "offset": 0, "share": 100}, {"id": 1233, "offset": 0, "share": 100}, {"id": 1234, "offset": 0, "share": 100}, {"id": 1235, "offset": 0, "share": 100}, {"id": 1236, "offset": 0, "share": 100}, {"id": 1237, "offset": 0, "share": 100}, {"id": 1239, "offset": 0, "share": 100}, {"id": 1241, "offset": 0, "share": 100}, {"id": 1245, "offset": 0, "share": 100}, {"id": 1246, "offset": 0, "share": 100}, {"id": 1248, "offset": 0, "share": 100}, {"id": 1249, "offset": 0, "share": 100}, {"id": 1252, "offset": 0, "share": 100}, {"id": 1253, "offset": 0, "share": 100}, {"id": 1254, "offset": 0, "share": 100}, {"id": 1255, "offset": 0, "share": 100}, {"id": 1257, "offset": 0, "share": 100}, {"id": 1258, "offset": 0, "share": 100}, {"id": 1259, "offset": 0, "share": 100}, {"id": 1260, "offset": 0, "share": 100}, {"id": 1261, "offset": 0, "share": 100}, {"id": 1262, "offset": 0, "share": 100}, {"id": 1263, "offset": 0, "share": 100}, {"id": 1264, "offset": 0, "share": 100}, {"id": 1266, "offset": 0, "share": 100}, {"id": 1267, "offset": 0, "share": 100}, {"id": 1271, "offset": 0, "share": 100}, {"id": 1272, "offset": 0, "share": 100}, {"id": 1273, "offset": 0, "share": 100}, {"id": 1274, "offset": 0, "share": 4}, {"id": 1275, "offset": 0, "share": 100}, {"id": 1276, "offset": 0, "share": 100}, {"id": 1277, "offset": 0, "share": 100}, {"id": 1280, "offset": 0, "share": 100}, {"id": 1281, "offset": 0, "share": 100}, {"id": 1282, "offset": 0, "share": 100}, {"id": 1288, "offset": 0, "share": 100}, {"id": 1292, "offset": 0, "share": 100}, {"id": 1293, "offset": 0, "share": 1}, {"id": 1294, "offset": 0, "share": 100}, {"id": 1296, "offset": 0, "share": 100}, {"id": 1297, "offset": 0, "share": 100}, {"id": 1301, "offset": 0, "share": 100}, {"id": 1302, "offset": 0, "share": 100}, {"id": 1304, "offset": 0, "share": 100}, {"id": 1305, "offset": 0, "share": 1}, {"id": 1306, "offset": 0, "share": 100}, {"id": 1309, "offset": 0, "share": 100}, {"id": 1315, "offset": 0, "share": 1}, {"id": 1323, "offset": 0, "share": 100}, {"id": 1331, "offset": 0, "share": 100}, {"id": 1332, "offset": 0, "share": 100}, {"id": 1336, "offset": 0, "share": 100}, {"id": 1338, "offset": 0, "share": 100}, {"id": 1346, "offset": 0, "share": 100}, {"id": 1348, "offset": 0, "share": 100}, {"id": 1354, "offset": 0, "share": 100}, {"id": 1356, "offset": 0, "share": 100}, {"id": 1357, "offset": 0, "share": 100}, {"id": 1358, "offset": 0, "share": 100}, {"id": 1359, "offset": 0, "share": 100}, {"id": 1360, "offset": 0, "share": 100}, {"id": 1361, "offset": 0, "share": 100}, {"id": 1362, "offset": 0, "share": 100}, {"id": 1365, "offset": 0, "share": 100}, {"id": 1369, "offset": 0, "share": 100}, {"id": 1370, "offset": 0, "share": 100}, {"id": 1371, "offset": 0, "share": 100}, {"id": 1372, "offset": 0, "share": 100}, {"id": 1376, "offset": 0, "share": 100}, {"id": 1379, "offset": 0, "share": 100}, {"id": 1384, "offset": 0, "share": 100}, {"id": 1386, "offset": 0, "share": 100}, {"id": 1387, "offset": 0, "share": 1}, {"id": 1390, "offset": 0, "share": 100}, {"id": 1391, "offset": 0, "share": 100}, {"id": 1392, "offset": 0, "share": 100}, {"id": 1393, "offset": 0, "share": 100}, {"id": 1398, "offset": 0, "share": 100}, {"id": 1400, "offset": 0, "share": 100}, {"id": 1401, "offset": 0, "share": 100}, {"id": 1402, "offset": 0, "share": 100}, {"id": 1405, "offset": 0, "share": 100}, {"id": 1406, "offset": 0, "share": 100}, {"id": 1408, "offset": 0, "share": 100}, {"id": 1409, "offset": 0, "share": 100}, {"id": 1410, "offset": 0, "share": 100}, {"id": 1411, "offset": 0, "share": 100}, {"id": 1413, "offset": 0, "share": 100}, {"id": 1420, "offset": 0, "share": 1}, {"id": 1426, "offset": 0, "share": 100}, {"id": 1428, "offset": 0, "share": 100}, {"id": 1429, "offset": 0, "share": 100}, {"id": 1430, "offset": 0, "share": 100}, {"id": 1431, "offset": 0, "share": 100}, {"id": 1432, "offset": 0, "share": 100}, {"id": 1433, "offset": 0, "share": 100}, {"id": 1434, "offset": 0, "share": 100}, {"id": 1435, "offset": 0, "share": 100}, {"id": 1436, "offset": 0, "share": 10}, {"id": 1441, "offset": 0, "share": 50}, {"id": 1442, "offset": 0, "share": 100}, {"id": 1447, "offset": 0, "share": 100}, {"id": 1448, "offset": 0, "share": 100}, {"id": 1449, "offset": 0, "share": 100}, {"id": 1452, "offset": 0, "share": 100}, {"id": 1453, "offset": 0, "share": 100}, {"id": 1454, "offset": 0, "share": 100}, {"id": 1456, "offset": 0, "share": 100}, {"id": 1458, "offset": 0, "share": 100}, {"id": 1459, "offset": 0, "share": 100}, {"id": 1460, "offset": 0, "share": 100}, {"id": 1465, "offset": 0, "share": 100}, {"id": 1466, "offset": 0, "share": 100}, {"id": 1467, "offset": 0, "share": 50}, {"id": 1468, "offset": 0, "share": 100}, {"id": 1469, "offset": 0, "share": 100}, {"id": 1470, "offset": 0, "share": 100}, {"id": 1473, "offset": 0, "share": 100}, {"id": 1474, "offset": 0, "share": 100}, {"id": 1475, "offset": 0, "share": 100}, {"id": 1480, "offset": 0, "share": 100}, {"id": 1483, "offset": 0, "share": 100}, {"id": 1486, "offset": 0, "share": 100}, {"id": 1489, "offset": 0, "share": 100}, {"id": 1490, "offset": 0, "share": 100}, {"id": 1494, "offset": 0, "share": 100}, {"id": 1495, "offset": 0, "share": 100}, {"id": 1496, "offset": 0, "share": 100}, {"id": 1497, "offset": 0, "share": 100}, {"id": 1498, "offset": 0, "share": 100}, {"id": 1505, "offset": 0, "share": 100}, {"id": 1506, "offset": 0, "share": 100}, {"id": 1513, "offset": 0, "share": 100}, {"id": 1515, "offset": 0, "share": 100}, {"id": 1517, "offset": 0, "share": 100}, {"id": 1518, "offset": 0, "share": 100}, {"id": 1519, "offset": 0, "share": 100}, {"id": 1520, "offset": 0, "share": 100}, {"id": 1522, "offset": 0, "share": 100}, {"id": 1523, "offset": 0, "share": 100}, {"id": 1524, "offset": 0, "share": 100}, {"id": 1530, "offset": 0, "share": 100}, {"id": 1531, "offset": 0, "share": 100}, {"id": 1532, "offset": 0, "share": 100}, {"id": 1533, "offset": 0, "share": 100}, {"id": 1535, "offset": 0, "share": 100}, {"id": 1537, "offset": 0, "share": 100}, {"id": 1539, "offset": 0, "share": 100}, {"id": 1540, "offset": 0, "share": 100}, {"id": 1543, "offset": 0, "share": 100}, {"id": 1544, "offset": 0, "share": 100}, {"id": 1545, "offset": 0, "share": 100}, {"id": 1546, "offset": 0, "share": 100}, {"id": 1547, "offset": 0, "share": 100}, {"id": 1548, "offset": 0, "share": 100}, {"id": 1549, "offset": 0, "share": 100}, {"id": 1555, "offset": 0, "share": 100}, {"id": 1558, "offset": 0, "share": 100}, {"id": 1559, "offset": 0, "share": 100}, {"id": 1561, "offset": 0, "share": 100}, {"id": 1562, "offset": 0, "share": 100}, {"id": 1567, "offset": 0, "share": 100}, {"id": 1570, "offset": 0, "share": 100}, {"id": 1571, "offset": 0, "share": 100}, {"id": 1572, "offset": 0, "share": 100}, {"id": 1573, "offset": 0, "share": 100}, {"id": 1574, "offset": 0, "share": 100}, {"id": 1575, "offset": 0, "share": 100}, {"id": 1577, "offset": 0, "share": 100}, {"id": 1588, "offset": 0, "share": 100}, {"id": 1589, "offset": 0, "share": 100}, {"id": 1596, "offset": 0, "share": 100}, {"id": 1598, "offset": 0, "share": 100}, {"id": 1600, "offset": 0, "share": 100}, {"id": 1601, "offset": 0, "share": 100}, {"id": 1609, "offset": 0, "share": 100}, {"id": 1610, "offset": 0, "share": 10}, {"id": 1613, "offset": 0, "share": 100}, {"id": 1615, "offset": 0, "share": 100}, {"id": 1618, "offset": 0, "share": 100}, {"id": 1620, "offset": 0, "share": 100}, {"id": 1621, "offset": 0, "share": 100}, {"id": 1624, "offset": 0, "share": 100}, {"id": 1626, "offset": 0, "share": 100}, {"id": 1627, "offset": 0, "share": 100}, {"id": 1628, "offset": 0, "share": 100}, {"id": 1636, "offset": 0, "share": 100}, {"id": 1640, "offset": 0, "share": 10}, {"id": 1641, "offset": 0, "share": 100}, {"id": 1643, "offset": 0, "share": 100}, {"id": 1646, "offset": 0, "share": 100}, {"id": 1649, "offset": 0, "share": 100}, {"id": 1650, "offset": 0, "share": 100}, {"id": 1652, "offset": 0, "share": 100}, {"id": 1654, "offset": 0, "share": 100}, {"id": 1661, "offset": 0, "share": 100}, {"id": 1664, "offset": 0, "share": 100}, {"id": 1665, "offset": 0, "share": 100}, {"id": 1666, "offset": 0, "share": 100}, {"id": 1668, "offset": 0, "share": 100}, {"id": 1669, "offset": 0, "share": 100}, {"id": 1670, "offset": 0, "share": 100}, {"id": 1673, "offset": 0, "share": 100}, {"id": 1678, "offset": 0, "share": 100}, {"id": 1681, "offset": 0, "share": 100}, {"id": 1682, "offset": 0, "share": 100}, {"id": 1684, "offset": 0, "share": 100}, {"id": 1690, "offset": 0, "share": 100}, {"id": 1693, "offset": 0, "share": 100}, {"id": 1694, "offset": 0, "share": 100}, {"id": 1695, "offset": 0, "share": 100}, {"id": 1696, "offset": 0, "share": 100}, {"id": 1698, "offset": 0, "share": 1}, {"id": 1699, "offset": 0, "share": 1}, {"id": 1703, "offset": 0, "share": 100}, {"id": 1704, "offset": 0, "share": 100}, {"id": 1707, "offset": 0, "share": 100}, {"id": 1710, "offset": 0, "share": 100}, {"id": 1711, "offset": 0, "share": 100}, {"id": 1712, "offset": 0, "share": 100}, {"id": 1716, "offset": 0, "share": 100}, {"id": 1718, "offset": 0, "share": 100}, {"id": 1720, "offset": 0, "share": 100}, {"id": 1721, "offset": 0, "share": 100}, {"id": 1722, "offset": 0, "share": 100}, {"id": 1723, "offset": 0, "share": 100}, {"id": 1724, "offset": 0, "share": 100}, {"id": 1731, "offset": 0, "share": 100}, {"id": 1732, "offset": 0, "share": 100}, {"id": 1734, "offset": 0, "share": 100}, {"id": 1739, "offset": 0, "share": 100}, {"id": 1741, "offset": 0, "share": 1}, {"id": 1742, "offset": 0, "share": 1}, {"id": 1748, "offset": 0, "share": 100}, {"id": 1749, "offset": 0, "share": 100}, {"id": 1755, "offset": 0, "share": 100}, {"id": 1756, "offset": 0, "share": 100}, {"id": 1757, "offset": 0, "share": 100}, {"id": 1762, "offset": 0, "share": 100}, {"id": 1763, "offset": 0, "share": 100}, {"id": 1765, "offset": 0, "share": 1}, {"id": 1766, "offset": 0, "share": 1}, {"id": 1767, "offset": 0, "share": 100}, {"id": 1768, "offset": 0, "share": 100}, {"id": 1769, "offset": 0, "share": 100}, {"id": 1770, "offset": 0, "share": 100}, {"id": 1772, "offset": 0, "share": 1}, {"id": 1773, "offset": 0, "share": 1}, {"id": 1779, "offset": 0, "share": 100}, {"id": 1780, "offset": 0, "share": 100}, {"id": 1782, "offset": 0, "share": 100}, {"id": 1784, "offset": 0, "share": 100}, {"id": 1785, "offset": 0, "share": 100}, {"id": 1786, "offset": 0, "share": 100}, {"id": 1787, "offset": 0, "share": 100}, {"id": 1790, "offset": 0, "share": 100}, {"id": 1795, "offset": 0, "share": 100}, {"id": 1797, "offset": 0, "share": 100}, {"id": 1798, "offset": 0, "share": 10}, {"id": 1800, "offset": 0, "share": 100}, {"id": 1801, "offset": 0, "share": 100}, {"id": 1802, "offset": 0, "share": 100}, {"id": 1803, "offset": 0, "share": 100}, {"id": 1804, "offset": 0, "share": 100}, {"id": 1805, "offset": 0, "share": 100}, {"id": 1806, "offset": 0, "share": 100}, {"id": 1807, "offset": 0, "share": 100}, {"id": 1811, "offset": 0, "share": 100}, {"id": 1814, "offset": 0, "share": 100}, {"id": 1815, "offset": 0, "share": 100}, {"id": 1821, "offset": 0, "share": 100}, {"id": 1825, "offset": 0, "share": 100}, {"id": 1830, "offset": 0, "share": 100}, {"id": 1831, "offset": 0, "share": 100}, {"id": 1833, "offset": 0, "share": 100}, {"id": 1834, "offset": 0, "share": 100}, {"id": 1835, "offset": 0, "share": 100}, {"id": 1836, "offset": 0, "share": 100}, {"id": 1847, "offset": 0, "share": 100}, {"id": 1850, "offset": 0, "share": 100}, {"id": 1851, "offset": 0, "share": 100}, {"id": 1853, "offset": 0, "share": 100}, {"id": 1854, "offset": 0, "share": 100}, {"id": 1855, "offset": 0, "share": 100}, {"id": 1856, "offset": 0, "share": 100}, {"id": 1861, "offset": 0, "share": 100}, {"id": 1863, "offset": 0, "share": 100}, {"id": 1864, "offset": 0, "share": 100}, {"id": 1867, "offset": 0, "share": 100}, {"id": 1868, "offset": 0, "share": 100}, {"id": 1871, "offset": 0, "share": 100}, {"id": 1876, "offset": 0, "share": 100}, {"id": 1878, "offset": 0, "share": 100}, {"id": 1879, "offset": 0, "share": 100}, {"id": 1880, "offset": 0, "share": 100}, {"id": 1882, "offset": 0, "share": 100}, {"id": 1885, "offset": 0, "share": 100}, {"id": 1886, "offset": 0, "share": 100}, {"id": 1887, "offset": 0, "share": 100}, {"id": 1892, "offset": 0, "share": 100}, {"id": 1893, "offset": 0, "share": 100}, {"id": 1894, "offset": 0, "share": 100}, {"id": 1895, "offset": 0, "share": 100}, {"id": 1899, "offset": 0, "share": 100}, {"id": 1900, "offset": 0, "share": 100}, {"id": 1902, "offset": 0, "share": 100}, {"id": 1904, "offset": 0, "share": 100}, {"id": 1906, "offset": 0, "share": 100}, {"id": 1907, "offset": 0, "share": 100}, {"id": 1909, "offset": 0, "share": 100}, {"id": 1910, "offset": 0, "share": 100}, {"id": 1911, "offset": 0, "share": 100}, {"id": 1912, "offset": 0, "share": 100}, {"id": 1913, "offset": 0, "share": 100}, {"id": 1919, "offset": 0, "share": 100}, {"id": 1922, "offset": 0, "share": 100}, {"id": 1925, "offset": 0, "share": 100}, {"id": 1932, "offset": 0, "share": 100}, {"id": 1934, "offset": 0, "share": 100}, {"id": 1937, "offset": 0, "share": 100}, {"id": 1943, "offset": 0, "share": 100}, {"id": 1945, "offset": 0, "share": 100}, {"id": 1946, "offset": 0, "share": 100}, {"id": 1949, "offset": 0, "share": 100}, {"id": 1951, "offset": 0, "share": 100}, {"id": 1952, "offset": 0, "share": 100}, {"id": 1953, "offset": 0, "share": 100}, {"id": 1955, "offset": 0, "share": 100}, {"id": 1959, "offset": 0, "share": 100}, {"id": 1960, "offset": 0, "share": 100}, {"id": 1963, "offset": 0, "share": 100}, {"id": 1967, "offset": 0, "share": 100}, {"id": 1969, "offset": 0, "share": 100}, {"id": 1975, "offset": 0, "share": 100}, {"id": 1977, "offset": 0, "share": 100}, {"id": 1979, "offset": 0, "share": 100}, {"id": 1984, "offset": 0, "share": 100}, {"id": 1989, "offset": 0, "share": 100}, {"id": 1990, "offset": 0, "share": 100}, {"id": 1992, "offset": 0, "share": 100}, {"id": 1995, "offset": 0, "share": 100}, {"id": 1996, "offset": 0, "share": 100}, {"id": 1997, "offset": 0, "share": 100}, {"id": 1998, "offset": 0, "share": 100}, {"id": 2000, "offset": 0, "share": 100}, {"id": 2001, "offset": 0, "share": 100}, {"id": 2009, "offset": 0, "share": 100}, {"id": 2010, "offset": 0, "share": 100}, {"id": 2013, "offset": 0, "share": 100}, {"id": 2014, "offset": 0, "share": 100}, {"id": 2015, "offset": 0, "share": 100}, {"id": 2019, "offset": 0, "share": 100}, {"id": 2022, "offset": 0, "share": 100}, {"id": 2026, "offset": 0, "share": 1}, {"id": 2027, "offset": 0, "share": 100}, {"id": 2029, "offset": 0, "share": 100}, {"id": 2031, "offset": 0, "share": 100}, {"id": 2032, "offset": 0, "share": 100}, {"id": 2035, "offset": 0, "share": 100}, {"id": 2038, "offset": 0, "share": 100}, {"id": 2039, "offset": 0, "share": 100}, {"id": 2041, "offset": 0, "share": 100}, {"id": 2043, "offset": 0, "share": 100}, {"id": 2046, "offset": 0, "share": 100}, {"id": 2058, "offset": 0, "share": 100}, {"id": 2061, "offset": 0, "share": 100}, {"id": 2063, "offset": 0, "share": 100}, {"id": 2064, "offset": 0, "share": 100}, {"id": 2065, "offset": 0, "share": 100}, {"id": 2066, "offset": 0, "share": 100}, {"id": 2071, "offset": 0, "share": 100}, {"id": 2078, "offset": 0, "share": 100}, {"id": 2079, "offset": 0, "share": 100}, {"id": 2080, "offset": 0, "share": 100}, {"id": 2081, "offset": 0, "share": 100}, {"id": 2084, "offset": 0, "share": 100}, {"id": 2089, "offset": 0, "share": 100}, {"id": 2092, "offset": 0, "share": 100}, {"id": 2095, "offset": 0, "share": 100}, {"id": 2100, "offset": 0, "share": 50}, {"id": 2102, "offset": 0, "share": 100}, {"id": 2105, "offset": 0, "share": 100}, {"id": 2107, "offset": 0, "share": 100}, {"id": 2108, "offset": 0, "share": 100}, {"id": 2109, "offset": 0, "share": 100}, {"id": 2111, "offset": 0, "share": 50}, {"id": 2113, "offset": 0, "share": 100}, {"id": 2118, "offset": 0, "share": 10}, {"id": 2119, "offset": 0, "share": 100}, {"id": 2124, "offset": 0, "share": 100}, {"id": 2125, "offset": 0, "share": 100}, {"id": 2126, "offset": 0, "share": 100}, {"id": 2128, "offset": 0, "share": 100}, {"id": 2133, "offset": 0, "share": 100}, {"id": 2137, "offset": 0, "share": 100}, {"id": 2139, "offset": 0, "share": 100}, {"id": 2142, "offset": 0, "share": 100}, {"id": 2144, "offset": 0, "share": 100}, {"id": 2147, "offset": 0, "share": 100}, {"id": 2148, "offset": 0, "share": 100}, {"id": 2152, "offset": 0, "share": 100}, {"id": 2155, "offset": 0, "share": 1}, {"id": 2162, "offset": 0, "share": 100}, {"id": 2163, "offset": 0, "share": 100}, {"id": 2164, "offset": 0, "share": 100}, {"id": 2169, "offset": 0, "share": 100}, {"id": 2174, "offset": 0, "share": 100}, {"id": 2177, "offset": 0, "share": 100}, {"id": 2178, "offset": 0, "share": 100}, {"id": 2179, "offset": 0, "share": 100}, {"id": 2180, "offset": 0, "share": 100}, {"id": 2181, "offset": 0, "share": 100}, {"id": 2182, "offset": 0, "share": 100}, {"id": 2183, "offset": 0, "share": 100}, {"id": 2185, "offset": 0, "share": 10}, {"id": 2190, "offset": 0, "share": 100}, {"id": 2192, "offset": 0, "share": 100}, {"id": 2195, "offset": 0, "share": 100}, {"id": 2199, "offset": 0, "share": 100}, {"id": 2202, "offset": 0, "share": 100}, {"id": 2205, "offset": 0, "share": 100}, {"id": 2206, "offset": 0, "share": 100}, {"id": 2208, "offset": 0, "share": 100}, {"id": 2210, "offset": 0, "share": 100}, {"id": 2212, "offset": 0, "share": 100}, {"id": 2214, "offset": 0, "share": 100}, {"id": 2215, "offset": 0, "share": 100}, {"id": 2217, "offset": 0, "share": 100}, {"id": 2219, "offset": 0, "share": 100}, {"id": 2221, "offset": 0, "share": 100}, {"id": 2226, "offset": 0, "share": 100}, {"id": 2234, "offset": 0, "share": 100}, {"id": 2235, "offset": 0, "share": 100}, {"id": 2236, "offset": 0, "share": 100}, {"id": 2239, "offset": 0, "share": 100}, {"id": 2243, "offset": 0, "share": 100}, {"id": 2248, "offset": 0, "share": 100}, {"id": 2254, "offset": 0, "share": 1}, {"id": 2258, "offset": 0, "share": 100}, {"id": 2263, "offset": 0, "share": 100}, {"id": 2268, "offset": 0, "share": 100}, {"id": 2271, "offset": 0, "share": 100}, {"id": 2274, "offset": 0, "share": 100}, {"id": 2281, "offset": 0, "share": 100}, {"id": 2289, "offset": 0, "share": 100}, {"id": 2290, "offset": 0, "share": 100}, {"id": 2293, "offset": 0, "share": 100}, {"id": 2294, "offset": 0, "share": 100}, {"id": 2298, "offset": 0, "share": 100}, {"id": 2305, "offset": 0, "share": 100}, {"id": 2307, "offset": 0, "share": 100}, {"id": 2308, "offset": 0, "share": 100}, {"id": 2311, "offset": 0, "share": 100}, {"id": 2317, "offset": 0, "share": 100}, {"id": 2318, "offset": 0, "share": 100}, {"id": 2319, "offset": 0, "share": 100}, {"id": 2322, "offset": 0, "share": 100}, {"id": 2325, "offset": 0, "share": 100}, {"id": 2327, "offset": 0, "share": 100}, {"id": 2332, "offset": 0, "share": 100}, {"id": 2335, "offset": 0, "share": 25}, {"id": 2337, "offset": 0, "share": 100}, {"id": 2340, "offset": 0, "share": 100}, {"id": 2342, "offset": 0, "share": 100}, {"id": 2346, "offset": 0, "share": 100}, {"id": 2348, "offset": 0, "share": 100}, {"id": 2349, "offset": 0, "share": 100}, {"id": 2350, "offset": 0, "share": 100}, {"id": 2351, "offset": 0, "share": 100}, {"id": 2354, "offset": 0, "share": 100}, {"id": 2355, "offset": 0, "share": 100}, {"id": 2357, "offset": 0, "share": 100}, {"id": 2362, "offset": 0, "share": 100}, {"id": 2363, "offset": 0, "share": 100}, {"id": 2366, "offset": 0, "share": 100}, {"id": 2367, "offset": 0, "share": 100}, {"id": 2370, "offset": 0, "share": 100}, {"id": 2371, "offset": 0, "share": 100}, {"id": 2372, "offset": 0, "share": 100}, {"id": 2374, "offset": 0, "share": 100}, {"id": 2376, "offset": 0, "share": 100}, {"id": 2377, "offset": 0, "share": 100}, {"id": 2378, "offset": 0, "share": 100}, {"id": 2383, "offset": 0, "share": 100}, {"id": 2385, "offset": 25, "share": 100}, {"id": 2387, "offset": 0, "share": 100}, {"id": 2389, "offset": 0, "share": 100}, {"id": 2391, "offset": 0, "share": 100}, {"id": 2393, "offset": 0, "share": 100}, {"id": 2394, "offset": 0, "share": 25}, {"id": 2395, "offset": 0, "share": 100}, {"id": 2397, "offset": 0, "share": 100}, {"id": 2398, "offset": 0, "share": 100}, {"id": 2399, "offset": 0, "share": 100}, {"id": 2401, "offset": 0, "share": 100}, {"id": 2404, "offset": 0, "share": 100}, {"id": 2406, "offset": 0, "share": 100}, {"id": 2409, "offset": 0, "share": 100}, {"id": 2410, "offset": 0, "share": 100}, {"id": 2411, "offset": 0, "share": 100}, {"id": 2412, "offset": 0, "share": 100}, {"id": 2415, "offset": 0, "share": 100}, {"id": 2416, "offset": 0, "share": 100}, {"id": 2419, "offset": 0, "share": 100}, {"id": 2422, "offset": 0, "share": 100}, {"id": 2423, "offset": 0, "share": 100}, {"id": 2424, "offset": 0, "share": 100}, {"id": 2425, "offset": 0, "share": 100}, {"id": 2427, "offset": 0, "share": 100}, {"id": 2430, "offset": 0, "share": 100}, {"id": 2434, "offset": 0, "share": 100}, {"id": 2439, "offset": 0, "share": 100}, {"id": 2440, "offset": 0, "share": 100}, {"id": 2441, "offset": 0, "share": 100}, {"id": 2444, "offset": 0, "share": 100}, {"id": 2446, "offset": 0, "share": 100}, {"id": 2447, "offset": 0, "share": 10}, {"id": 2448, "offset": 0, "share": 100}, {"id": 2449, "offset": 0, "share": 100}, {"id": 2450, "offset": 0, "share": 100}, {"id": 2451, "offset": 0, "share": 100}, {"id": 2454, "offset": 0, "share": 100}, {"id": 2456, "offset": 0, "share": 100}, {"id": 2458, "offset": 0, "share": 100}, {"id": 2461, "offset": 0, "share": 100}, {"id": 2463, "offset": 0, "share": 100}, {"id": 2467, "offset": 0, "share": 100}, {"id": 2474, "offset": 0, "share": 1}, {"id": 2476, "offset": 0, "share": 100}, {"id": 2480, "offset": 0, "share": 100}, {"id": 2482, "offset": 0, "share": 100}, {"id": 2485, "offset": 0, "share": 100}, {"id": 2486, "offset": 0, "share": 100}, {"id": 2488, "offset": 0, "share": 100}, {"id": 2489, "offset": 0, "share": 100}, {"id": 2490, "offset": 0, "share": 100}, {"id": 2497, "offset": 0, "share": 100}, {"id": 2498, "offset": 0, "share": 100}, {"id": 2499, "offset": 0, "share": 100}, {"id": 2500, "offset": 0, "share": 100}, {"id": 2502, "offset": 0, "share": 100}, {"id": 2505, "offset": 0, "share": 100}, {"id": 2506, "offset": 0, "share": 100}, {"id": 2507, "offset": 0, "share": 100}, {"id": 2508, "offset": 0, "share": 100}, {"id": 2509, "offset": 0, "share": 100}, {"id": 2511, "offset": 0, "share": 25}, {"id": 2512, "offset": 0, "share": 100}, {"id": 2517, "offset": 0, "share": 100}, {"id": 2518, "offset": 0, "share": 100}, {"id": 2520, "offset": 0, "share": 100}, {"id": 2521, "offset": 0, "share": 100}, {"id": 2522, "offset": 0, "share": 100}, {"id": 2523, "offset": 0, "share": 100}, {"id": 2524, "offset": 0, "share": 100}, {"id": 2525, "offset": 0, "share": 100}, {"id": 2526, "offset": 0, "share": 100}, {"id": 2532, "offset": 0, "share": 100}, {"id": 2533, "offset": 0, "share": 50}, {"id": 2535, "offset": 0, "share": 100}, {"id": 2536, "offset": 0, "share": 100}, {"id": 2538, "offset": 0, "share": 100}, {"id": 2546, "offset": 0, "share": 100}, {"id": 2547, "offset": 0, "share": 100}, {"id": 2550, "offset": 0, "share": 100}, {"id": 2552, "offset": 0, "share": 100}, {"id": 2555, "offset": 0, "share": 100}, {"id": 2557, "offset": 0, "share": 100}, {"id": 2561, "offset": 0, "share": 100}, {"id": 2563, "offset": 0, "share": 100}, {"id": 2567, "offset": 0, "share": 100}, {"id": 2568, "offset": 0, "share": 100}, {"id": 2578, "offset": 0, "share": 100}, {"id": 2582, "offset": 0, "share": 100}, {"id": 2584, "offset": 0, "share": 100}, {"id": 2586, "offset": 0, "share": 100}, {"id": 2588, "offset": 0, "share": 100}, {"id": 2590, "offset": 0, "share": 100}, {"id": 2591, "offset": 0, "share": 100}, {"id": 2592, "offset": 0, "share": 100}, {"id": 2595, "offset": 0, "share": 100}, {"id": 2596, "offset": 0, "share": 100}, {"id": 2598, "offset": 0, "share": 100}, {"id": 2601, "offset": 0, "share": 100}, {"id": 2602, "offset": 0, "share": 100}, {"id": 2606, "offset": 0, "share": 100}, {"id": 2607, "offset": 0, "share": 50}, {"id": 2608, "offset": 0, "share": 100}, {"id": 2610, "offset": 0, "share": 100}, {"id": 2612, "offset": 0, "share": 100}, {"id": 2617, "offset": 0, "share": 5}, {"id": 2618, "offset": 0, "share": 100}, {"id": 2619, "offset": 0, "share": 100}, {"id": 2620, "offset": 0, "share": 100}, {"id": 2623, "offset": 0, "share": 100}, {"id": 2626, "offset": 0, "share": 100}, {"id": 2629, "offset": 0, "share": 100}, {"id": 2636, "offset": 0, "share": 100}, {"id": 2641, "offset": 0, "share": 100}, {"id": 2645, "offset": 0, "share": 100}, {"id": 2646, "offset": 0, "share": 50}, {"id": 2648, "offset": 0, "share": 100}, {"id": 2649, "offset": 0, "share": 100}, {"id": 2650, "offset": 0, "share": 100}, {"id": 2652, "offset": 0, "share": 100}, {"id": 2653, "offset": 0, "share": 100}, {"id": 2655, "offset": 0, "share": 100}, {"id": 2656, "offset": 0, "share": 100}, {"id": 2657, "offset": 0, "share": 100}, {"id": 2658, "offset": 0, "share": 100}, {"id": 2659, "offset": 0, "share": 100}, {"id": 2660, "offset": 0, "share": 100}, {"id": 2662, "offset": 0, "share": 100}, {"id": 2663, "offset": 0, "share": 100}, {"id": 2665, "offset": 0, "share": 25}, {"id": 2666, "offset": 0, "share": 25}, {"id": 2668, "offset": 0, "share": 100}, {"id": 2669, "offset": 0, "share": 100}, {"id": 2670, "offset": 0, "share": 100}, {"id": 2671, "offset": 0, "share": 100}, {"id": 2673, "offset": 0, "share": 100}, {"id": 2675, "offset": 0, "share": 100}, {"id": 2676, "offset": 0, "share": 100}, {"id": 2677, "offset": 0, "share": 100}, {"id": 2678, "offset": 0, "share": 100}, {"id": 2679, "offset": 0, "share": 100}, {"id": 2684, "offset": 0, "share": 100}, {"id": 2686, "offset": 0, "share": 100}, {"id": 2687, "offset": 0, "share": 100}, {"id": 2692, "offset": 0, "share": 100}, {"id": 2693, "offset": 0, "share": 100}, {"id": 2694, "offset": 0, "share": 100}, {"id": 2695, "offset": 0, "share": 100}, {"id": 2698, "offset": 0, "share": 100}, {"id": 2703, "offset": 0, "share": 100}, {"id": 2712, "offset": 0, "share": 100}, {"id": 2714, "offset": 0, "share": 100}, {"id": 2720, "offset": 0, "share": 100}, {"id": 2723, "offset": 0, "share": 100}, {"id": 2724, "offset": 0, "share": 100}, {"id": 2732, "offset": 0, "share": 100}, {"id": 2734, "offset": 0, "share": 100}, {"id": 2735, "offset": 0, "share": 100}, {"id": 2742, "offset": 0, "share": 100}, {"id": 2745, "offset": 0, "share": 100}, {"id": 2746, "offset": 0, "share": 100}, {"id": 2752, "offset": 0, "share": 100}, {"id": 2753, "offset": 0, "share": 100}, {"id": 2754, "offset": 0, "share": 100}, {"id": 2758, "offset": 0, "share": 100}, {"id": 2760, "offset": 0, "share": 100}, {"id": 2761, "offset": 0, "share": 100}, {"id": 2762, "offset": 0, "share": 100}, {"id": 2763, "offset": 0, "share": 100}, {"id": 2764, "offset": 0, "share": 100}, {"id": 2765, "offset": 0, "share": 100}, {"id": 2768, "offset": 0, "share": 100}, {"id": 2776, "offset": 0, "share": 50}, {"id": 2777, "offset": 0, "share": 100}, {"id": 2780, "offset": 0, "share": 100}, {"id": 2782, "offset": 0, "share": 100}, {"id": 2783, "offset": 0, "share": 100}, {"id": 2789, "offset": 0, "share": 100}, {"id": 2791, "offset": 0, "share": 100}, {"id": 2793, "offset": 0, "share": 100}, {"id": 2794, "offset": 0, "share": 100}, {"id": 2795, "offset": 0, "share": 100}, {"id": 2797, "offset": 0, "share": 100}, {"id": 2803, "offset": 0, "share": 100}, {"id": 2804, "offset": 0, "share": 100}, {"id": 2806, "offset": 0, "share": 25}, {"id": 2809, "offset": 0, "share": 100}, {"id": 2810, "offset": 0, "share": 100}, {"id": 2811, "offset": 0, "share": 100}, {"id": 2813, "offset": 0, "share": 100}, {"id": 2815, "offset": 0, "share": 100}, {"id": 2818, "offset": 0, "share": 100}, {"id": 2819, "offset": 0, "share": 10}, {"id": 2821, "offset": 0, "share": 1}, {"id": 2825, "offset": 0, "share": 100}, {"id": 2827, "offset": 0, "share": 100}, {"id": 2828, "offset": 0, "share": 100}, {"id": 2830, "offset": 0, "share": 100}, {"id": 2834, "offset": 0, "share": 100}, {"id": 2836, "offset": 0, "share": 100}, {"id": 2844, "offset": 0, "share": 100}, {"id": 2846, "offset": 0, "share": 100}, {"id": 2848, "offset": 0, "share": 100}, {"id": 2849, "offset": 0, "share": 100}, {"id": 2850, "offset": 0, "share": 100}, {"id": 2852, "offset": 0, "share": 50}, {"id": 2854, "offset": 0, "share": 100}, {"id": 2855, "offset": 0, "share": 100}, {"id": 2857, "offset": 0, "share": 100}, {"id": 2858, "offset": 0, "share": 100}, {"id": 2859, "offset": 0, "share": 100}, {"id": 2861, "offset": 0, "share": 100}, {"id": 2862, "offset": 0, "share": 1}, {"id": 2863, "offset": 0, "share": 100}, {"id": 2865, "offset": 0, "share": 100}, {"id": 2867, "offset": 0, "share": 100}, {"id": 2868, "offset": 0, "share": 100}, {"id": 2870, "offset": 0, "share": 100}, {"id": 2872, "offset": 0, "share": 100}, {"id": 2874, "offset": 0, "share": 10}, {"id": 2876, "offset": 0, "share": 100}, {"id": 2877, "offset": 0, "share": 100}, {"id": 2883, "offset": 0, "share": 50}, {"id": 2884, "offset": 0, "share": 1}, {"id": 2885, "offset": 0, "share": 100}, {"id": 2895, "offset": 0, "share": 1}, {"id": 2898, "offset": 0, "share": 100}, {"id": 2909, "offset": 0, "share": 100}, {"id": 2910, "offset": 0, "share": 50}, {"id": 2913, "offset": 0, "share": 100}, {"id": 2915, "offset": 0, "share": 1}, {"id": 2929, "offset": 0, "share": 10}, {"id": 2946, "offset": 0, "share": 25}], "settings": [{"id": 0, "value": "VRR1TASCLT*|odsp35*|VNBP101*|*\\Abby|*\\moeuser|moeuser|*\\drxaccount|drxaccount|*\\TASClient|TASClient|PYXIDIS\\pulsar|PYXIDIS\\tank|LocalAdminUser|AdminUAC|*\\cloudtest|cloudtest|NTDEV\\ntlab"}, {"id": 1, "value": "0x8004de25|0x0000de2a|0x8004de40|0x8004de42|0x8004de43|0x8004de44|0x8004de85|0x8004de8b|0x8004deb2|0x8004deb6|0x8004dea0|0x8004deaa|0x8004deac|0x8004deae|0x8004deaf|0x8004dea3|0x8004dea9"}, {"id": 10, "value": "0"}, {"id": 105, "value": "-**********|-**********|-**********"}, {"id": 108, "value": "0x8004de40|0x8004de81|0x00000001"}, {"id": 109, "value": "350000"}, {"id": 11, "value": "desktop.ini|thumbs.db|Microsoft Edge.lnk"}, {"id": 110, "value": "3600"}, {"id": 111, "value": "604800"}, {"id": 114, "value": "2"}, {"id": 117, "value": "7200000 "}, {"id": 118, "value": "300"}, {"id": 119, "value": "2048"}, {"id": 120, "value": "2048"}, {"id": 121, "value": "fpc.msedge.net/conf/v2/odsync/fpconfig.min.json"}, {"id": 122, "value": "100"}, {"id": 123, "value": "389260"}, {"id": 124, "value": "6"}, {"id": 125, "value": "Other"}, {"id": 127, "value": "1"}, {"id": 128, "value": "10"}, {"id": 129, "value": "6300000 "}, {"id": 130, "value": "120"}, {"id": 131, "value": "clients.config.office.net/user/v1.0/windows/ack"}, {"id": 132, "value": "roaming.officeapps.live.com/rs/RoamingSoapService.svc"}, {"id": 133, "value": "clients.config.office.net/user/v1.0/windows/policies"}, {"id": 134, "value": "roaming.officeapps.live.com/rs/RoamingSoapService.svc"}, {"id": 137, "value": "30"}, {"id": 138, "value": "600"}, {"id": 139, "value": "3"}, {"id": 14, "value": "600 "}, {"id": 140, "value": "14400"}, {"id": 143, "value": "604800"}, {"id": 144, "value": "14400"}, {"id": 145, "value": "10"}, {"id": 149, "value": "[{\n  \"name\": \"OD_CUSTOM_PROFILE\",\n  \"rules\": [\n    { \"netCost\": \"restricted\",                              \"timers\": [ -1, -1, -1 ] },\n    { \"netCost\": \"high\",        \"powerState\": \"unknown\",    \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"high\",        \"powerState\": \"battery\",    \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"high\",        \"powerState\": \"charging\",   \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"low\",         \"powerState\": \"unknown\",    \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"low\",         \"powerState\": \"battery\",    \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"low\",         \"powerState\": \"charging\",   \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"unknown\",     \"powerState\": \"unknown\",    \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"unknown\",     \"powerState\": \"battery\",    \"timers\": [ 300, 30, 10 ] },\n    { \"netCost\": \"unknown\",     \"powerState\": \"charging\",   \"timers\": [ 300, 30, 10 ] },\n    {                                                       \"timers\": [ -1, -1, -1 ] }\n  ]\n}]"}, {"id": 15, "value": "604800"}, {"id": 150, "value": "fpc.msedge.net/conf/v2/odsync2/fpconfig.min.json"}, {"id": 151, "value": "pst,nst,ost"}, {"id": 152, "value": "*.laccdb|*.tpm|thumbs.db|EhThumbs.db|Desktop.ini|.DS_Store|Icon\\r|.lock|.849C9593-D756-4E56-8D6E-42412F2A707B"}, {"id": 155, "value": "pst,nst,ost"}, {"id": 158, "value": "1"}, {"id": 159, "value": "604800"}, {"id": 16, "value": "|one|onepkg|onetoc|onetoc2|"}, {"id": 160, "value": "604800"}, {"id": 161, "value": "1"}, {"id": 162, "value": "43200"}, {"id": 163, "value": "21600"}, {"id": 164, "value": "3"}, {"id": 165, "value": "90"}, {"id": 166, "value": "120"}, {"id": 167, "value": "15"}, {"id": 168, "value": "120000"}, {"id": 169, "value": "1.2.0.9"}, {"id": 17, "value": "|doc|docm|docx|odt|odp|pps|ppsm|ppsx|ppt|pptm|pptx|vsd|vsdx|ods|xls|xlsb|xlsm|xlsx|"}, {"id": 170, "value": "https://clients.config.office.net/collector/v1.0/inventoryodb"}, {"id": 178, "value": "-895221759|-2147024809|-2147023508|-2147023579|-2147164433|-2147164592|-2147162950"}, {"id": 179, "value": "0"}, {"id": 18, "value": "|pst|"}, {"id": 180, "value": "0.01"}, {"id": 181, "value": "0.03"}, {"id": 182, "value": "3"}, {"id": 183, "value": "30"}, {"id": 184, "value": "10"}, {"id": 185, "value": "ar;bg;ca;cs;da;de;el;en;en-GB;en-US;es;et;eu;fi;fr;gl;he;hi;hr;hu;id;it;ja;kk;ko;lt;lv;ms;nb-NO;nl;pl;pt-BR;pt-PT;ro;ru;sk;sl;sr-Cyrl-RS;sr-Latn-RS;sv;th;tr;uk;vi;zh-CN;zh-TW;"}, {"id": 187, "value": "lnk|xlsx|xls|url|exe|zip|rar|rdp|appref-ms|msi|website|xlsm|xml|xlsb|pub|mht|accdb|bak|reg|mpp|ini|pbix|mdb|indd|nal|dwg|saz|oft|cer|pfx|dll|nfo|ics|rdg|vsdx|tsv|config|partial|script|kql|evtx|har|csl|ipynb|webm|iso|tm7|ts|application|nupkg|cab|mov|oxps|stp|dpk"}, {"id": 188, "value": "1440"}, {"id": 189, "value": "7200000"}, {"id": 19, "value": "22528"}, {"id": 190, "value": "1800000"}, {"id": 191, "value": "0.05"}, {"id": 192, "value": "|pdf:20|accdb:20|exe:20|mp4:60|mdb:20|pst:1200|myox:15|"}, {"id": 195, "value": "900 "}, {"id": 2, "value": "0x8004de40|0x8004de42|0x8004de85|0x8004de8b|0x8004deb6|0x8004de9b|0x8004ded0|0x8004ded2|0x8004de45|0x8004de8a|0x8004ded5"}, {"id": 20, "value": "133120"}, {"id": 201, "value": "lnk|xlsx|xls|xlsb|xlsm|docx|doc|docm|pptx|pub|url|exe|zip|rar|rdp|appref-ms|msi|website|txt|msg|xml|mht|accdb|csv|wav|bak|reg|mpp|html|pst|ini|pbix|mdb|indd|nal|dwg|saz|oft|cer|pfx|dll|nfo|ics|rdg|vsdx|tsv|config|partial|script|one|kql|evtx|har|csl|ipynb|webm|iso|tm7|ts|application|nupkg|cab|oxps|stp|dpk"}, {"id": 204, "value": "6|37|78|79|80|106|107|146|151|155|178|179|182|193|208|232|256|283|300|349|372|376|379|406|417|420|428|432|466|479|484|522|532|533|541|550|584|587|588|591|592|616|618|629|653|655|685|688|707|715|729|733|758|766|767|797|804|835|848|854|872|875|882|885|895|903|910|923|938|969|990|996|998|1006|1007|1009|1011|1012|1030|1040|1045|1049|1050|1087|1096|1099|1142|1147|1149|1154|1171|1194|1197|1207|1208|1209|1214|1273|1280|1288|1303|1313|1349|1366|1387|1423|1466|1507|1514|1533|1551|1602|1633|1650|1654|1704|1708|1709|1722|1734|1741|1761|1762|1785|1787|1802|1811|1834|1835|1839|1845|1846|1856|1857|1858|1865|1867|1868|1883|1892|1897|1898|1908|1910|1914|1922|1929|1937|1945|1946|1949|1984|1990|1999|2043|2047|2052|2053|2062|2083|2094|2103|2107|2121|2125|2140|2158|2160|2177|2180|2186|2195|2206|2216|2232|2233|2240|2251|2253|2268|2290|2303|2312|2313|2317|2328|2339|2363|2373|2395|2419|2433|2440|2447|2455|2456|2507|2530|2553|2559|2579|2597|2601|2607|2620|2630|2635|2648|2665|2678|2698|2705|2713|2729|2742|2760|2761|2765|2770|2772|2785|2843|2847|2875|2887|2908|2921|2950|2970|2975|2992|3009|3013|3034|3054|3055|3069|3085|3090|3097|3108|3122|3139|3164|3171|3179|3189|3202|3209|3210|3244|3246|3253|3254|3265|3288|3296|3350|3351|3369|3387|3551|3557|3566|3567|3593|3609|3627|3628|3632|3661|3664|3665|3670|3679|3746|3841|3847|3854|3858|3894|3903|3929|3981|3983|3990|4008|4019|4065|4069|4101|19999"}, {"id": 205, "value": "dwg"}, {"id": 206, "value": "bak"}, {"id": 208, "value": "14"}, {"id": 209, "value": "0.0"}, {"id": 21, "value": "lnk|xlsx|xls|xlsb|xlsm|docx|doc|docm|pptx|pub|url|exe|zip|rar|rdp|appref-ms|msi|website|txt|msg|xml|mht|accdb|csv|wav|bak|reg|mpp|html|pst|ini|pbix|mdb|indd|nal|dwg|saz|oft|cer|pfx|dll|nfo|ics|rdg|vsdx|tsv|config|partial|script|one|kql|evtx|har|csl|ipynb|webm|iso|tm7|ts|application|nupkg|cab|oxps|stp|dpk"}, {"id": 210, "value": "0.0"}, {"id": 213, "value": "20"}, {"id": 214, "value": "180"}, {"id": 216, "value": "VaultUnlockScenario:-2144272294;AddMountedFolderScenario:38;"}, {"id": 22, "value": "minCountForConsideration:6"}, {"id": 222, "value": "15"}, {"id": 223, "value": "Level:Request,Verbose:Rotate:1/100;\nName:ReportChangeEnumerationTimeDelay:Rotate:1/30;\nScenario:DownloadScenario,UploadMetadataScenario,UploadMetadataBatchScenario:Rotate:1/30;\nScenario:UploadScenario,Upload:Rotate:1/1200;\nScenario:MetadataDownload:Random:0.25;\nApi:ProcessLocalChange*:Random:0.000025;\nApi:UXMacLocalization:Random:0.0001;\nName:ScenarioHeaderMissing:Rotate:1/20;\nName:StorageProviderUriSourceResult,CrossScopeMoveBytesTransferred,UpdateRingSource,StorageApiScenarioHeaderDiffersInTrace,FALDeleteCompletion,DeviceHealthScore,OOBERequestHandlerClientKFMUpsellState:Rotate:0/1;\nName:CloudFilesApiResult:Rotate:1/100;\nApi:SyncPerfScenario:Rotate:0/1;\nApi:MacboxFPEnumChangesCompleteWorkingSet:Rotate:1/10;\nApi:MacboxFPExtFALConnectionFetch,MacboxFPExtFetchComplete:Rotate:1/2;\nApi:ReparentChildrenOfFolderInRecycleBin:Random:0.1;\nName:IdleJobExecutionDelayed:Random:0.0001;\nName:RealizerChangeInterpreterFilterBackfill:Random:0.01;\nName:UploadCxPUnableToResolveToken:Random:0.001;\nApi:MacboxUserVisibleFetch:Random:0.0025;\nName:RealizerAwareChangeEnumerationStatsResult:Random:10.0;\nName:ChangeEnumerationMissingQuotaState:Random:2.5;\nFunnel:Infrastructure:Rotate:1%+1/100;\nFunnel:hiddenRoot:Rotate:1%+1/100;\nName:OOBERequestHandlerSyncClientSignInState:Random:0.0001;\nName:ClientFileGetOCSIPropertiesFailed,IdleJobManagerCompletedJob:Random:10.0;\nName:OneDriveStorageProviderHandlerAPI,HashValidatingDirtyBit,ShouldEnablePlaceholdersState:Random:2.5;\nApi:WinboxConvertToPlaceholder:Random:1.0;\nApi:UploadCxPGetTokenFromPath:Random:1.0;\nNucleus:FonDSyncInit:Random:0.001;\nNucleus:FonDSync:Random:0.001;\nNucleus:QueryFailureHandler:Random:0.001;\nNucleus:GetBuildAsset:Random:0.01;\nNucleus:AutoStartStatus:Random:0.01;\nNucleus:AuthAcquireTokenSilently:Random:0.25;\nStallError:NewStall_*:Random:0.1;\nApi:IncomingWrapper:Random:0.1;\nApi:NO_OP,OneDriveWasStarted,CreateOfflineProviderInstance,GetOfflineProviderInstance,DestroyOfflineProviderInstance,NotifyEventHandler:Random:0.01;\nName:OCSIPropertiesValidationForClientFile:Random:0.00001;\nName:OCSIPropertiesValidationForUnrealizedFile:Random:0.00001;\nApi:ValidateOCSIPropertiesForClientFile:Random:0.1;\nApi:UXReactNativeDataBridgeRouting_*:Random:0.25;\nApi:FileSyncErrors_ODB_FileSyncError*:Rotate:1/7;\nApi:FileSyncErrors_FileSyncError*:Rotate:1/30;\nScenario:ThumbnailScenario:Rotate:1/600;\nApi:LoggingScenarioReporting:Rotate:1/120000;\nApi:CoreSyncDehydration:Random:2.5;\nApi:OCSI_CoreSyncAPI_SetProperties:Random:10.0;\nScenario:StartOCSIScenario:Rotate:1/240;\nName:SyncEngineSignIn,ScopeInit,ProvisionUserFolder,DownloadClientPolicy:Random:10.0;\nName:UserAuthSignIn,SPODiscoverTenant,EmailHRD:Random:10.0;\nName:QuantumPlaceholderFileMonitorCompletion:Random:0.125;\nName:ScenarioCompleted:Random:1.0;\nName:HashComplete:Random:1.0;\nName:UploadScenarioUnlikelyToSucceed,IrisParsingServiceError,RequestThumbnailResults:Random:2.5;\nApi:ErrorsNode,Request,MissingParentNode:Random:25.0;\nName:UsqQuotaStateExperimentTriggered:Rotate:0/1;\nScenario:NotificationLatencyScenario,FindChangesScenario,CheckQuotaInfoScenario:Rotate:1/30;\nApi:OneAuthApiResult:Rotate:1/20;\nApi:ExistingGetOnline:Rotate:0/1;\nScenario:ExistingGetOnline,FREGetOnline:Rotate:1/1;\nName:SendSyncProgressData,SendSyncInfoData:Rotate:1/1;\nName:ActivityCenterOpen,RNActivityCenterOpen,CloudFileHydration,ContextMenuCommand,FinderContextMenuCommand,OfficeFileSave,OfficeFileOpenRead,StorageProviderUIAction:Rotate:1/1;\nName:MigrationScan,MigrationOperation,MigrationRedirectOperation,MigrationManagerPerformingKnownFolderAudit:Rotate:1/1;\nName:SurveyManagerCampaignFunnel:Rotate:1/1;\nApi:OneDriveXRetryAfterThrottledTransfers:Rotate:1/1;\nApi:UXReactNativeFloodgateSurveySubmission,UXFloodgateSurveySubmission,Download_*:Rotate:1/1;\nApi:StorageEnumChanges,StartupWorkerPoolStart,StartupInitGlobalClientState,StartupSyncEngineScenario,StartupInitSyncEngineCore,FindChangesOther,StorageInlineUploadsScenario,StorageUploadBatchFileCommit,StorageGetClientPolicy,FindChangesWnsNotification,StorageRetrieveQuotaState,StorageUploadMetadataScenario,StorageSyncVerificationScenario,StorageStreamingDownloadBlock:Rotate:1/100;\nApi:StorageUploadBatchMetadata,RefreshAdvised,StorageGetThumbnail,StorageEnumChanges_SyncVerification,StorageRetrieveQuotaStateScenario,InvalidPolicyOnScopeInitialization,StorageStreamingBitsCreateSession,StorageUploadBatchInlineFile,FindChangesErrorHandling,FindChangesPollingWNSChannelNotConnected,FindChangesPollingSubscriptionConnected,PlatformConnectToWNSScenario,StorageRetrieveSpaceUsed,PlatformConnectToWNS,StorageGetItemMetadata,StorageGetLiveFolder:Rotate:1/100;\nName:FileDBLoadedFilesFoldersFromUniqueLibs:Rotate:1/100;\nName:BlockTimeout:Rotate:1/100;\nName:SyncTelemetryHelperVerifyDiskFileSizeWhenUpdatingDb:Rotate:1/100;\nName:PendingUploadTimeInQueue:Rotate:1/100;\nName:PendingUploadTimeInQueueSkipped:Rotate:1/100;\nName:NonFatalOneDriveAssert:Rotate:1/100;\nName:PlaceholderFetchSyncEngineIO:Rotate:1/100;\nName:ScenarioQosWrapperAdditionalInformation:Rotate:1/100;\nName:WindowsSEEnumChangeResult:Rotate:1/100;\nName:RNActivityCenterUIAction:Rotate:1/100;\nName:ActivityCenterUIAction:Rotate:1/100;\nName:BannerOperations:Rotate:1/100;\nName:OneDriveUIActionTaken:Rotate:1/100;\nName:RecordDevice:Rotate:1/1;\nApi:CoreSyncWNSDeleteSubscriptions,CoreSyncWNSSubscriptions:Rotate:1/100;\nApi:Permissions_ReadWrite_ItemPermissionChange,Permissions_TransientReadOnly_ItemPermissionChange:Rotate:1/100;\nApi:MacboxFCP*,MacboxPHU*,MacboxSRM*:Rotate:1/100;\nApi:AuthToServiceOneAuth:Rotate:1/100;\nApi:StorageLogUploadGetLocation,StorageLogUploadBatchScenario,AzureLogUploadBatch:Rotate:1/100;\nApi:SilentSignInRetryFailureResult:Rotate:1/80;\nApi:OneDriveLauncherStartInstance,ReportOneDriveUpDateRingRampRecency,DownloadPreSignInSettingsConfig:Random:25.0;\nName:ThrottledTransferKeyScenarioThrottled:Rotate:1/1;\nNucleus:NucleusLocalContentPerf:Random:0.00001;\nName:QuantumPlaceHoldersManagerIsFeaturedEnabled:Random:0.00001;\nScenario:A*,B*,C*,D*,E*,F*,G*,H*,I*,J*,K*,L*,M*,N*,O*,P*,Q*,R*,S*,T*,U*,V*,W*,X*,Y*,Z*,a*,b*,c*,d*,e*,f*,g*,h*,i*,j*,k*,l*,m*,n*,o*,p*,q*,r*,s*,t*,u*,v*,w*,x*,y*,z*:Rotate:1/4;\nName:A*,B*,C*,D*,E*,F*,G*,H*,I*,J*,K*,L*,M*,N*,O*,P*,Q*,R*,S*,T*,U*,V*,W*,X*,Y*,Z*,a*,b*,c*,d*,e*,f*,g*,h*,i*,j*,k*,l*,m*,n*,o*,p*,q*,r*,s*,t*,u*,v*,w*,x*,y*,z*:Rotate:1/4;\nApi:A*,B*,C*,D*,E*,F*,G*,H*,I*,J*,K*,L*,M*,N*,O*,P*,Q*,R*,S*,T*,U*,V*,W*,X*,Y*,Z*,a*,b*,c*,d*,e*,f*,g*,h*,i*,j*,k*,l*,m*,n*,o*,p*,q*,r*,s*,t*,u*,v*,w*,x*,y*,z*:Rotate:1/4;"}, {"id": 226, "value": "********"}, {"id": 227, "value": "*******"}, {"id": 228, "value": " "}, {"id": 229, "value": "21.109.0530.0001"}, {"id": 231, "value": "lnk|xlsx|xls|xlsb|xlsm|pub|url|exe|zip|rar|rdp|appref-ms|msi|website|msg|xml|mht|accdb|csv|wav|bak|reg|mpp|pst|ini|pbix|mdb|indd|nal|saz|oft|cer|pfx|dll|nfo|ics|rdg|vsdx|tsv|config|partial|script|one|kql|evtx|har|csl|ipynb|webm|iso|tm7|ts|application|nupkg|cab|oxps|stp|dpk"}, {"id": 232, "value": "lnk|xlsx|xls|xlsb|xlsm|pub|url|exe|zip|rar|rdp|appref-ms|msi|website|msg|xml|mht|accdb|csv|wav|bak|reg|mpp|pst|ini|pbix|mdb|indd|nal|saz|oft|cer|pfx|dll|nfo|ics|rdg|vsdx|tsv|config|partial|script|one|kql|evtx|har|csl|ipynb|webm|iso|tm7|ts|application|nupkg|cab|oxps|stp|dpk"}, {"id": 233, "value": "2.9.7653.47581"}, {"id": 237, "value": "50"}, {"id": 238, "value": "900|300|600|900"}, {"id": 24, "value": "4"}, {"id": 240, "value": "3600"}, {"id": 241, "value": "10000"}, {"id": 243, "value": "900"}, {"id": 244, "value": "900"}, {"id": 245, "value": "P1D"}, {"id": 246, "value": "200000"}, {"id": 249, "value": "300000"}, {"id": 25, "value": "15"}, {"id": 250, "value": "20"}, {"id": 251, "value": "180"}, {"id": 252, "value": "5"}, {"id": 253, "value": "180"}, {"id": 254, "value": "1"}, {"id": 26, "value": "100"}, {"id": 269, "value": "25|26|27|29|19|10|23|30|35|14,22.235.1109.0001"}, {"id": 273, "value": "250000"}, {"id": 274, "value": "100000"}, {"id": 283, "value": "siteUrl|listId"}, {"id": 284, "value": "100,siteUrl=https?:\\/\\/(www\\.)?laposte\\.sharepoint\\.com\\b(.*),INF,lh-lap|100,siteUrl=.*\\s.*,24.178.0903.0001,lh-sws|100,listId=acc3ba2d-929f-41d1-aee5-4c99be7b0277,INF,ln-lh|100,listId=a0fec449-25ba-4637-97d6-57660475b73b,INF,ln-lo|100,listId=2a7093aa-c228-4672-a093-aa7f86b3e13b,INF,ln-lo"}, {"id": 285, "value": "ModernListTemplateTypeId|ClientFormCustomFormatter|ContentTypeIdToNameMap|isModerated|listName|FormRenderModes|EnableMinorVersions"}, {"id": 286, "value": "100,ModernListTemplateTypeId=3a867b4a-7429-0e1a-b02e-bf4b240ff1ce,INF,tm-pl|100,ModernListTemplateTypeId=b117a022-9f8b-002d-bda8-fa266f0ff1ce,22.176.0821.0001,tm-pro|100,ModernListTemplateTypeId=a5126f70-467d-4257-954b-95fba9bb008c,22.176.0821.0001,tm-pro|100,ModernListTemplateTypeId=3a7c53be-a128-0ff9-9f97-7b6f700ff1ce,22.176.0821.0001,tm_rec|100,ModernListTemplateTypeId=92946f48-bed9-4299-825d-dc3768f59d54,22.176.0821.0001,tm_rec|100,ModernListTemplateTypeId=9a429811-2ab5-07bc-b5a0-2de9590ff1ce,22.176.0821.0001,tm-co|100,ModernListTemplateTypeId=bd8bdab7-5730-4f57-9050-0bd66c988b15,22.176.0821.0001,tm-co|100,ContentTypeIdToNameMap=.*:.*:.*,22.055.0313.0001,ct|100,listName=.ACC3BA2D-929F-41D1-AEE5-4C99BE7B0277.,INF,ln-la|100,FormRenderModes=.*:.*:.*\"RenderType\" : 3.*,INF,frn|100,EnableMinorVersions=true,INF,emv"}, {"id": 287, "value": "ClientSideComponentProperties|SchemaXml|Formula|TypeAsString|InternalName|IsPathRendered"}, {"id": 288, "value": "100,ClientSideComponentProperties=^(?!null).*,22.002.0103.0004|100,SchemaXml=.*AppendOnly.\\\"TRUE\\\".*Type.\\\"Note\\\".*,22.222.1023.0001,notes|100,SchemaXml=.*Type.\\\"Note\\\".*AppendOnly.\\\"TRUE\\\".*,22.222.1023.0001,notes|100,Formula=.+,22.025.0202.0001,frml|100,TypeAsString=TaxonomyFieldTypeMulti,INF,tax-mlt|100,InternalName=_ApprovalStatus,INF,ap_st|100,TypeAsString=Likes,INF,rtg|100,IsPathRendered=true,INF,ipr"}, {"id": 289, "value": "modernType|viewXml|query"}, {"id": 290, "value": "100,modernType=KANBAN,21.229.1104.0001,<PERSON><PERSON><PERSON>|100,viewXml=.*<CustomOrder>.+</CustomOrder>.*,INF,cu-ord|100,modernType=TILES,22.045.0227.0001,tiles|100,query=.*<Where>.*<Eq><FieldRef Name.\"_ModerationStatus\" /><Value Type.\"ModStat\">.+</Value></Eq>.*</Where>.*,INF,ca-fil|100,viewXml=.*<ViewFields>.*<FieldRef Name.\"MediaServiceImageTags\"/>.*</ViewFields>.*,INF,ms-img,ALL,FILES|100,viewXml=.*<ViewFields>.*<FieldRef Name.\\\"Originator\\\"\\/>.*<\\/ViewFields>.*,INF,originator,ALL,ALL|100,viewXml=.*<ViewFields>.*<FieldRef Name.\\\"_ComplianceTagUserId\\\"\\/>.*<\\/ViewFields>.*,INF,compliance-userid,ALL,ALL"}, {"id": 291, "value": "TimeZone_Id|RegionalSetting_Locale"}, {"id": 292, "value": "100,TimeZone_Id=^5[3-9],21.230.1107.0004,tz|100,TimeZone_Id=^[6-9]\\d,21.230.1107.0004,tz|100,TimeZone_Id=^\\d{3},21.230.1107.0004,tz|100,RegionalSetting_Locale=^1025|100,RegionalSetting_Locale=^1052|100,RegionalSetting_Locale=^1098|100,RegionalSetting_Locale=^1164|100,RegionalSetting_Locale=^2068|100,RegionalSetting_Locale=^6170|100,RegionalSetting_Locale=^7194|100,RegionalSetting_Locale=^8218|100,RegionalSetting_Locale=^12314|100,RegionalSetting_Locale=^102[68],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^103[47],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^104[12467],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^105[2467],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^106[2-68],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1079,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^108[0-36],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^108[89],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^109[0-79],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^110[0-8],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^111[014578],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^112[1-58],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^113[0-6],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^114[468],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^115[02-9],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^116[049],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^2049,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^20[56]8,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^207[07],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^209[24],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^210[78],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^211[057],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^2128,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^214[13],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^2155,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^307[36],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^3131,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^3179,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^4097,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^410[03],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^4106,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^4122,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^4155,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^512[147],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^513[02],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^5146,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^5179,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^6145,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^615[46],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^6170,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^6203,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^7169,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^717[78],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^7194,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^7227,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^8193,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^820[12],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^8218,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^8251,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^9217,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^922[56],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^9242,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^9275,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1024[19],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^10250,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^10266,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^11265,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1127[34],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^11290,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^12289,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1229[78],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^12314,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^13313,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1332[12],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^14337,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^14346,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^15361,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^15370,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^16385,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1639[34],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1741[78],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1844[12],INF,dis-loc,MACOS|100,RegionalSetting_Locale=^19466,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^20490,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^21514,INF,dis-loc,MACOS|100,RegionalSetting_Locale=^1026,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1029,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1035,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1036,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1038,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1044,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1045,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1047,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1049,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1051,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1052,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1053,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1058,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1059,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1061,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1062,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1063,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1064,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1074,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1076,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1078,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1079,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1083,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1087,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1088,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1090,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1091,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1092,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1132,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1133,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1134,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1150,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1154,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1155,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1156,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1157,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^1160,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2055,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2064,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2068,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2070,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2077,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2092,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2107,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2115,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^2143,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^3079,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^3084,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^3131,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^4108,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^4155,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^5127,22.146.0710.0001,wr-enc|100,RegionalSetting_Locale=^102[6-79],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^103[0-24-68-9],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^104[03-689],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^105[0-357-9],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^106[0-68-9],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^107[0-18-9],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^108[037-8],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^109[0-2],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^1108,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^1110,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^112[2-3],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^113[13-5],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^1146,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^115[04-79],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^116[04],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^206[07-8],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^207[07],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^209[24],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^2107,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^211[05],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^2143,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^2155,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^3079,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^308[24],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^3131,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^410[38],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^4122,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^4155,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^513[02],21.270.0102.0001,frmt|100,RegionalSetting_Locale=^5146,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^5179,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^6156,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^6170,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^6203,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^7177,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^7194,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^7227,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^8202,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^8218,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^8251,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^9226,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^9242,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^9275,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^10266,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^11274,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^11290,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^12298,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^12314,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^13322,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^14346,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^15370,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^16394,21.270.0102.0001,frmt|100,RegionalSetting_Locale=^102[679],22.033.0213.0001,frmt|100,RegionalSetting_Locale=^103[02578],22.033.0213.0001,frmt|100,RegionalSetting_Locale=^104[4-689],22.033.0213.0001,frmt|100,RegionalSetting_Locale=^105[014578],22.033.0213.0001,frmt|100,RegionalSetting_Locale=^106[1-3689],22.033.0213.0001,frmt|100,RegionalSetting_Locale=^1071,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^108[167],22.033.0213.0001,frmt|100,RegionalSetting_Locale=^1106,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^1110,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^1152,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^2049,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^2070,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^2108,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^2110,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^3073,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^4097,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^4122,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^5121,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^5146,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^6145,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^7169,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^8193,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^9217,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^9242,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^10241,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^10266,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^11265,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^11290,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^12289,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^13313,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^14337,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^15361,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^16385,22.033.0213.0001,frmt|100,RegionalSetting_Locale=^16393,22.040.0220.0001,frmt|100,RegionalSetting_Locale=^1029,INF,alt-sep|100,RegionalSetting_Locale=^1042,24.054.0314.0001,kor-dis-loc"}, {"id": 294, "value": "604800"}, {"id": 297, "value": "ctxId|tick|expirationDateTime|ClientForms|notificationUrl|subscribeID|lastItemModifiedTime|PageContextInfo"}, {"id": 30, "value": "30"}, {"id": 302, "value": "60"}, {"id": 303, "value": "0"}, {"id": 304, "value": "&CallerScenarioId={Scenario}&CallerId={Caller}"}, {"id": 305, "value": "8192"}, {"id": 306, "value": "2"}, {"id": 309, "value": "Linear"}, {"id": 310, "value": "2"}, {"id": 311, "value": "2"}, {"id": 318, "value": "500"}, {"id": 320, "value": "65535"}, {"id": 321, "value": "600000"}, {"id": 322, "value": "2"}, {"id": 323, "value": "5"}, {"id": 324, "value": "30"}, {"id": 325, "value": "22.012.0116.0001"}, {"id": 329, "value": "100"}, {"id": 330, "value": "0x8004da9a|0x8004da12|0x8004da17"}, {"id": 331, "value": "|db:60|package:60|db-wal:60|accdb:30|log:30|txt:30|"}, {"id": 334, "value": "RuntimeBroker.exe"}, {"id": 36, "value": "40"}, {"id": 37, "value": "4096"}, {"id": 38, "value": "10240"}, {"id": 39, "value": "5120"}, {"id": 40, "value": "133120"}, {"id": 41, "value": "|pst|"}, {"id": 42, "value": "|pst|"}, {"id": 43, "value": "|one|onepkg|onetoc|onetoc2|"}, {"id": 45, "value": "200"}, {"id": 46, "value": "18.168.0820.0001"}, {"id": 47, "value": "18.168.0820.0001"}, {"id": 48, "value": "100"}, {"id": 49, "value": "1534956090"}, {"id": 5, "value": "0"}, {"id": 51, "value": "7200 "}, {"id": 52, "value": "7200"}, {"id": 53, "value": "2 "}, {"id": 538, "value": "0,Etc/UTC|1,Etc/GMT|2,Europe/London|3,Europe/Paris|4,Europe/Berlin|5,Europe/Bucharest|6,Europe/Budapest|7,Europe/Minsk|8,America/Sao_Paulo|9,America/Halifax|10,America/New_York|11,America/Chicago|12,America/Denver|13,America/Los_Angeles|14,America/Anchorage|15,Pacific/Honolulu|16,Pacific/Apia|17,Pacific/Auckland|18,Australia/Brisbane|19,Australia/Adelaide|20,Asia/Tokyo|21,Asia/Singapore|22,Asia/Bangkok|23,Asia/Calcutta|24,Asia/Dubai|25,Asia/Tehran|26,Asia/Baghdad|27,Asia/Jerusalem|28,America/St_Johns|29,Atlantic/Azores|30,Etc/GMT+2|31,Atlantic/Reykjavik|32,America/Cayenne|33,America/La_Paz|34,America/Indianapolis|35,America/Bogota|36,America/Regina|37,America/Mexico_City|38,America/Phoenix|39,Etc/GMT+12|40,Pacific/Fiji|41,Pacific/Guadalcanal|42,Australia/Hobart|43,Pacific/Port_Moresby|44,Australia/Darwin|45,Asia/Shanghai|46,Asia/Novosibirsk|47,Asia/Tashkent|48,Asia/Kabul|49,Africa/Cairo|50,Africa/Johannesburg|51,Europe/Moscow|53,Atlantic/Cape_Verde|54,Asia/Baku|55,America/Guatemala|56,Africa/Nairobi|57,Europe/Warsaw|58,Asia/Yekaterinburg|59,Europe/Kiev|60,America/Godthab|61,Asia/Rangoon|62,Asia/Katmandu|63,Asia/Irkutsk|64,Asia/Krasnoyarsk|65,America/Santiago|66,Asia/Colombo|67,Pacific/Tongatapu|68,Asia/Vladivostok|69,Africa/Lagos|70,Asia/Yakutsk|71,Asia/Almaty|72,Asia/Seoul|73,Australia/Perth|74,Asia/Riyadh|75,Asia/Taipei|76,Australia/Sydney|77,America/Chihuahua|78,America/Tijuana|79,Asia/Amman|80,Asia/Beirut|81,America/Cuiaba|82,Asia/Tbilisi|83,Africa/Windhoek|84,Asia/Yerevan|85,America/Buenos_Aires|86,Africa/Casablanca|87,Asia/Karachi|88,America/Caracas|89,Indian/Mauritius|90,America/Montevideo|91,America/Asuncion|92,Asia/Kamchatka|93,Etc/UTC|94,Asia/Ulaanbaatar|95,Etc/GMT+11|96,Etc/GMT+2|97,Etc/GMT-12|98,Asia/Damascus|99,Asia/Magadan|100,Europe/Kaliningrad|101,Europe/Istanbul|102,Asia/Dhaka|103,America/Bahia|104,Europe/Chisinau|106,Europe/Samara|107,Asia/Srednekolymsk|108,Asia/Kamchatka|109,Europe/Minsk|110,Europe/Astrakhan|111,Asia/Barnaul|112,Asia/Tomsk|114,Asia/Sakhalin|115,Asia/Omsk"}, {"id": 539, "value": "5121|15361|3073|2049|11265|13313|12289|4097|6145|8193|16385|1025|10241|7169|14337|9217|1068|1069|8218|5146|1026|1027|2052|3076|5124|4100|1028|4122|1050|1029|1030|1164|2067|1043|3081|10249|4105|9225|16393|6153|8201|17417|5129|13321|18441|7177|11273|2057|1033|12297|1061|1035|2060|3084|1036|5132|6156|4108|1110|3079|1031|5127|4103|2055|1032|1037|1081|1038|1057|2108|1040|2064|1041|1087|1042|1062|1063|1071|1086|2110|1044|2068|1045|1046|2070|1048|1049|7194|12314|10266|6170|11290|9242|1051|11274|16394|13322|9226|5130|7178|12298|17418|4106|18442|2058|19466|6154|15370|10250|20490|1034|3082|21514|14346|8202|1053|2077|1054|1055|1058|1066|1106"}, {"id": 540, "value": "1093|2052|3076|5124|4100|1028|1095|1081|1041|1099|1111|1102|1096|1103|1097|1098|1152|1042"}, {"id": 541, "value": "1052|1118|1101|2117|1093|2052|4100|1028|1164|1125|3081|4105|9225|16393|6153|8201|17417|5129|13321|18441|11273|2057|1033|1124|1032|1095|1037|1081|2141|1117|1041|1099|1158|1111|1042|1086|2110|1100|1153|1102|1148|1121|1096|1065|1094|1131|3179|1103|9226|7178|2058|6154|20490|21514|14346|8202|1114|1097|1098|1054|1056|1152|1066|1144|5121|15361|3073|2049|11265|13313|12289|4097|6145|8193|16385|1025|10241|7169|14337|9217"}, {"id": 543, "value": "2067=%d %B|1043=%d %B|2052=%m月%d日|3076=%m月%d日|5124=%m月%d日|4100=%m月%d日|1028=%m月%d日|1053=den %d %B|2077=den %d %B|2060=%d %B|3084=%d %B|1036=%d %B|5132=%d %B|6156=%d %B|4108=%d %B|3079=%d. %B|1031=%d. %B|5127=%d. %B|4103=%d. %B|2055=%d. %B|1040=%d %B|2064=%d %B|1041=%m月%d日|1042=%B %d일|1049=%d %B|11274=%d de %B|16394=%d de %B|13322=%d de %B|9226=%d de %B|5130=%d de %B|7178=%d de %B|12298=%d de %B|17418=%d de %B|4106=%d de %B|18442=%d de %B|2058=%d de %B|19466=%d de %B|6154=%d de %B|15370=%d de %B|10250=%d de %B|20490=%d de %B|1034=%d de %B|3082=%d de %B|21514=%d de %B|14346=%d de %B|8202=%d de %B|2070=%d de %B|1046=%d de %B|1037=%d %B|1030=%d. %B|1044=%d. %B|1048=%d %B|5121=%d %B|15361=%d %B|3073=%d %B|2049=%d %B|11265=%d %B|13313=%d %B|12289=%d %B|4097=%d %B|6145=%d %B|8193=%d %B|16385=%d %B|10241=%d %B|7169=%d %B|14337=%d %B|9217=%d %B|1068=%d %B|1069=%B %d|8218=%d. %B|5146=%d. %B|1026=%d %B|1027=%d %B|4122=%d. %B|1050=%d. %B|1029=%d. %B|1061=%d. %B|1035=%d. %Bta|1110=%d de %B|1032=%d %B|1081=%d %B|1038=%B %d.|1057=%d %B|2108=%d %B|1062=%d. %B|1071=%d %B|2110=%d %B|1086=%d %B|2068=%d. %B|1045=%d %B|7194=%d. %B|12314=%d. %B|10266=%d. %B|6170=%d. %B|11290=%d. %B|9242=%d. %B|1051=%d. %B|1054=%d %B|1055=%d %B|1058=%d %B|1066=%d %B|1106=%B %d|1087=%d %B|1063=%B %d d."}, {"id": 544, "value": "2067=%d %B %Y|1043=%d %B %Y|2052=%y年%m月%d日|3076=%y年%m月%d日|5124=%y年%m月%d日|4100=%y年%m月%d日|1028=%y年%m月%d日|1053=den %d %B %Y|2077=den %d %B %Y|2060=%d %B %Y|3084=%d %B %Y|1036=%d %B %Y|5132=%d %B %Y|6156=%d %B %Y|4108=%d %B %Y|3079=%d.%m.%Y|1031=%d.%m.%Y|5127=%d.%m.%Y|4103=%d.%m.%Y|2055=%d.%m.%Y|1040=%d %B %Y|2064=%d %B %Y|1041=%Y年%Om月%d日|1042=%Y년 %m월 %d일|1049=%d.%m.%Y|11274=%d/%m/%Y|16394=%d/%m/%Y|13322=%d-%m-%Y|9226=%d/%m/%Y|5130=%d/%m/%Y|7178=%d/%m/%Y|12298=%d/%m/%Y|17418=%d/%m/%Y|4106=%d/%m/%Y|18442=%d/%m/%Y|2058=%d/%m/%Y|19466=%d/%m/%Y|6154=%m/%d/%Y|15370=%d/%m/%Y|10250=%d/%m/%Y|20490=%m/%d/%Y|1034=%d/%m/%Y|3082=%d/%m/%Y|21514=%m/%d/%Y|14346=%d/%m/%Y|8202=%d/%m/%Y|2070=%d/%m/%Y|1046=%d de %B de %Y|1037=%d %B %Y|1030=%d. %B %Y|1044=%d. %B %Y|1048=%d %B %Y|5121=%d-%m-%Y|15361=%d/%m/%Y|3073=%d/%m/%Y|2049=%d/%m/%Y|11265=%d/%m/%Y|13313=%d/%m/%Y|12289=%d/%m/%Y|4097=%d/%m/%Y|6145=%d-%m-%Y|8193=%d/%m/%Y|16385=%d/%m/%Y|10241=%d/%m/%Y|7169=%d-%m-%Y|14337=%d/%m/%Y|9217=%d/%m/%Y|1068=%d %B %Y|1069=%Y/%m/%d|8218=%d. %B %Y|5146=%d. %B %Y|1026=%d %B %Y г.|1027=%d/%m/%Y|4122=%d. %B %Y.|1050=%d. %B %Y.|1029=%d.%m.%Y|1061=%d. %B %Y|1035=%d.%m.%Y|1110=%d/%m/%Y|1032=%d %B %Y|1081=%d %B %Y|1038=%B %d., %Y|1057=%d %B %Y|2108=%d %B, %Y|1062=%Y. gada %d. %B|1071=%d %B %Y|2110=%d %B %Y|1086=%d %B %Y|2068=%d. %B %Y|1045=%d %B %Y|7194=%d. %B %Y|12314=%d. %B %Y|10266=%d. %B %Y|6170=%d. %B %Y|11290=%d. %B %Y|9242=%d. %B %Y|1051=%d. %m. %Y|1054=%d %B %Y|1055=%d %B %Y|1058=%d %B %Y р.|1066=%d %B %Y|1106=%B %d %Y|1087=%d %B %Y ж.|1063=%Y m. %B %d d."}, {"id": 547, "value": "https://clients.config.office.net/user/v1.0/tenantassociationkey"}, {"id": 549, "value": "43200"}, {"id": 550, "value": "3600"}, {"id": 551, "value": "30"}, {"id": 552, "value": "25.0"}, {"id": 553, "value": "600"}, {"id": 556, "value": "16393=%I.%M %p"}, {"id": 557, "value": "60"}, {"id": 560, "value": "1045=stycznia;lutego;marca;kwietnia;maja;czerwca;lipca;sierpnia;września;października;listopada;grudnia|1049=января;февраля;марта;апреля;мая;июня;июля;августа;сентября;октября;ноября;декабря|5121=يناير;فبراير;مارس;أبريل;مايو;يونيو;يوليه;أغسطس;سبتمبر;أكتوبر;نوفمبر;ديسمبر|2049=يناير;فبراير;مارس;أبريل;مايو;يونيو;يوليه;أغسطس;سبتمبر;أكتوبر;نوفمبر;ديسمبر|11265=يناير;فبراير;مارس;أبريل;مايو;يونيو;يوليه;أغسطس;سبتمبر;أكتوبر;نوفمبر;ديسمبر|12289=يناير;فبراير;مارس;أبريل;مايو;يونيو;يوليه;أغسطس;سبتمبر;أكتوبر;نوفمبر;ديسمبر|6145=يناير;فبراير;مارس;أبريل;مايو;يونيو;يوليه;أغسطس;سبتمبر;أكتوبر;نوفمبر;ديسمبر|10241=يناير;فبراير;مارس;أبريل;مايو;يونيو;يوليه;أغسطس;سبتمبر;أكتوبر;نوفمبر;ديسمبر|7169=يناير;فبراير;مارس;أبريل;مايو;يونيو;يوليه;أغسطس;سبتمبر;أكتوبر;نوفمبر;ديسمبر|1068=yanvar;fevral;mart;aprel;may;iyun;iyul;avqust;sentyabr;oktyabr;noyabr;dekabr|5146=januar;februar;mart;april;maj;juni;juli;avgust;septembar;oktobar;novembar;decembar|1027=de gener;de febrer;de març;d’abril;de maig;de juny;de juliol;d’agost;de setembre;d’octubre;de novembre;de desembre|4122=siječnja;veljače;ožujka;travnja;svibnja;lipnja;srpnja;kolovoza;rujna;listopada;studenoga;prosinca|1050=siječnja;veljače;ožujka;travnja;svibnja;lipnja;srpnja;kolovoza;rujna;listopada;studenog;prosinca|1029=ledna;února;března;dubna;května;června;července;srpna;září;října;listopadu;prosince|1032=Ιανουαρίου;Φεβρουαρίου;Μαρτίου;Απριλίου;Μαΐου;Ιουνίου;Ιουλίου;Αυγούστου;Σεπτεμβρίου;Οκτωβρίου;Νοεμβρίου;Δεκεμβρίου|1087=қаңтар;ақпан;наурыз;сәуір;мамыр;маусым;шілде;тамыз;қыркүйек;қазан;қараша;желтоқсан|1063=sausio;vasario;kovo;balandžio;gegužės;birželio;liepos;rugpjūčio;rugsėjo;spalio;lapkričio;gruodžio|1051=januára;februára;marca;apríla;mája;júna;júla;augusta;septembra;októbra;novembra;decembra|1058=січня;лютого;березня;квітня;травня;червня;липня;серпня;вересня;жовтня;листопада;грудня|8218=januar;februar;mart;april;maj;juni;juli;avgust;septembar;oktobar;novembar;decembar|1164=January;February;March;April;May;June;July;August;September;October;November;December|7194=јануар;фебруар;март;април;мај;јун;јул;август;септембар;октобар;новембар;децембар|12314=јануар;фебруар;март;април;мај;јун;јул;август;септембар;октобар;новембар;децембар"}, {"id": 561, "value": "{BE6CBA05-11C2-42E8-88B2-7D4B3E3E8734}_1193|{A3451E43-72DF-4DEE-BFFF-54DD4F76B889}_2222336362|{5B0C1EDE-DB41-41FE-98F2-17FF342847AD}_10201020|{5B0C1EDE-DB41-41FE-98F2-17FF342847AD}_1097|{A3451E43-72DF-4DEE-BFFF-54DD4F76B889}_2046313181|{BE6CBA05-11C2-42E8-88B2-7D4B3E3E8734}_3165353945|{5B0C1EDE-DB41-41FE-98F2-17FF342847AD}_1063|{BE6CBA05-11C2-42E8-88B2-7D4B3E3E8734}_1839700279|{35832F83-CE88-4F7D-B318-A1434A40E6D0}_868319934"}, {"id": 562, "value": "0.0"}, {"id": 563, "value": "1052=e paradites;e pasdites|1098=పూర్వాహ్న;అపరాహ్న"}, {"id": 564, "value": "8218=nedjelja;ponedjeljak;utorak;srijeda;četvrtak;petak;subota|1164=Sunday;Monday;Tuesday;Wednesday;Thursday;Friday;Saturday|2068=søndag;mandag;tirsdag;onsdag;torsdag;fredag;lørdag|7194=недеља;понедељак;уторак;среда;четвртак;петак;субота|12314=недеља;понедељак;уторак;среда;четвртак;петак;субота|6170=nedelja;ponedeljak;utorak;sreda;četvrtak;petak;subota|11290=nedelja;ponedeljak;utorak;sreda;četvrtak;petak;subota"}, {"id": 566, "value": "a5b7be8e-ef1e-404f-8930-3e88ea835d61|08485f1b-716b-48da-b475-1fae43ade4e0|2e394c55-0b61-4557-9062-e5a9677a9739|631e9c78-1ef5-44ef-bcdd-48660243df54|ea304ed0-90b0-42a5-94af-60c5c1541499"}, {"id": 567, "value": "8"}, {"id": 568, "value": "100,3,1,700"}, {"id": 57, "value": "200 "}, {"id": 572, "value": "RUS|BLR|"}, {"id": 575, "value": "850:650"}, {"id": 576, "value": "10"}, {"id": 577, "value": "0.01"}, {"id": 578, "value": "100000"}, {"id": 579, "value": "998"}, {"id": 58, "value": "5120"}, {"id": 580, "value": "250000"}, {"id": 582, "value": "Lists/GetItemById/Recycle|GetFolderById/Recycle|SP.MoveCopyUtil.MoveFolderByPath|SP.MoveCopyUtil.MoveFileByPath|GetCopyJobProgress|GetFileByServerRelativePath/CheckOut|GetFileByServerRelativePath/CheckIn|GetFileByServerRelativePath/UndoCheckOut|GetList/Items/ValidateUpdateFetchListItem"}, {"id": 583, "value": "25.0"}, {"id": 584, "value": "102400"}, {"id": 585, "value": "10240"}, {"id": 586, "value": "51200"}, {"id": 587, "value": "1.5"}, {"id": 588, "value": "10240"}, {"id": 589, "value": "2000"}, {"id": 59, "value": "64"}, {"id": 590, "value": "12000"}, {"id": 591, "value": "3600"}, {"id": 592, "value": "102400"}, {"id": 593, "value": "102400"}, {"id": 594, "value": "102400"}, {"id": 595, "value": "204800"}, {"id": 596, "value": "204800"}, {"id": 599, "value": "120"}, {"id": 60, "value": "172800"}, {"id": 601, "value": "10"}, {"id": 602, "value": "120"}, {"id": 603, "value": "20"}, {"id": 604, "value": "2"}, {"id": 607, "value": "22.217.1016"}, {"id": 608, "value": "9"}, {"id": 609, "value": "0"}, {"id": 61, "value": "350000"}, {"id": 610, "value": "0.00001"}, {"id": 611, "value": "0.0000001"}, {"id": 614, "value": "[\n{\"Req\": 1, \"<PERSON>\": 600, \"MC\": 960},\n{\"Req\": 3, \"<PERSON>\": 600, \"MC\": 480},\n{\"Req\": 4, \"<PERSON>\": 60, \"MC\": 180},\n{\"Req\": 11, \"<PERSON>\": 1800, \"MC\": 480},\n{\"Req\": 21, \"<PERSON>\": 60, \"MC\": 180}\n]"}, {"id": 615, "value": "50000"}, {"id": 616, "value": "50000"}, {"id": 617, "value": "32"}, {"id": 62, "value": "403"}, {"id": 620, "value": "3"}, {"id": 621, "value": "24"}, {"id": 625, "value": "7200000"}, {"id": 628, "value": "0.001"}, {"id": 635, "value": "2b0b9539-e749-436c-aa61-bd12acc9085d 2b0b9539e749436caa61bd12acc9085d_RNBoolSettingChanged|2b0b9539-e749-436c-aa61-bd12acc9085d 2b0b9539e749436caa61bd12acc9085d_RNNetworkSettingsChanged|2b0b9539-e749-436c-aa61-bd12acc9085d 2b0b9539e749436caa61bd12acc9085d_RNVaultSettingChanged|22bcab81-4b4c-4e52-81ac-cb41e0d8aa72 22bcab814b4c4e5281accb41e0d8aa72_OneDriveStorageProviderHandlerAPI|fa07b02c-55e1-4561-902c-e03204cc93e6 fa07b02c55e14561902ce03204cc93e6_OOBERequestHandlerClientEligibilityState|1d80fd3d-7eea-4a76-85b5-5ed80cd8275f 1d80fd3d7eea4a7685b55ed80cd8275f_KFMSilentFlowLauncherGPOState"}, {"id": 636, "value": "[{\"Req\":1,\"Cap\":8,\"NT\":8,\"P\":5000,\"RP\":300,\"T\":5},\n{\"Req\":3,\"Cap\":8,\"NT\":8,\"P\":10000,\"RP\":300,\"T\":5},\n{\"Req\":4,\"Cap\":3,\"NT\":3,\"P\":1000,\"RP\":300,\"T\":5},\n{\"Req\":11,\"Cap\":4,\"NT\":4,\"P\":15000,\"RP\":300,\"T\":5},\n{\"Req\":21,\"Cap\":3,\"NT\":3,\"P\":1000,\"RP\":300,\"T\":5}]"}, {"id": 639, "value": "5000"}, {"id": 643, "value": "168"}, {"id": 65, "value": "200"}, {"id": 652, "value": "604800"}, {"id": 653, "value": "7200"}, {"id": 656, "value": "300000"}, {"id": 657, "value": "150000"}, {"id": 66, "value": "60 "}, {"id": 67, "value": "60 "}, {"id": 671, "value": "31618,31609,31610"}, {"id": 672, "value": "1"}, {"id": 674, "value": "10560"}, {"id": 676, "value": "TypeG,TypeG,TypeG,false"}, {"id": 680, "value": "3,60000,300000"}, {"id": 681, "value": "5.0"}, {"id": 682, "value": "300000"}, {"id": 688, "value": "604800"}, {"id": 69, "value": "401Retry|OfficeConversion_NotSupported|BadArgument_CantGenerateThumbnail|FileTooLarge|OfficeConversion_RegionNotSupported|503Retry"}, {"id": 690, "value": "86400"}, {"id": 694, "value": "86400"}, {"id": 695, "value": "5"}, {"id": 698, "value": "5"}, {"id": 7, "value": "0x80080300|0x80070102|0x800704cf|0x00000102|0x80070002|0xcaa20005|0xcaa20008"}, {"id": 70, "value": "400 "}, {"id": 701, "value": "604800"}, {"id": 71, "value": "120 "}, {"id": 721, "value": "3600000"}, {"id": 723, "value": "1440"}, {"id": 726, "value": "5"}, {"id": 727, "value": "5"}, {"id": 729, "value": "30"}, {"id": 730, "value": "2,100"}, {"id": 731, "value": "2"}, {"id": 740, "value": "BlueCloudCritical_Win11.svg,1"}, {"id": 744, "value": "TypeG,TypeG,TypeG,false,RequireNoMSAPresent"}, {"id": 751, "value": "10080"}, {"id": 753, "value": "120"}, {"id": 757, "value": "1"}, {"id": 759, "value": "30"}, {"id": 760, "value": "1440"}, {"id": 761, "value": "2000 "}, {"id": 769, "value": "30000"}, {"id": 770, "value": "1,0"}, {"id": 772, "value": "5000 "}, {"id": 773, "value": "2621440 "}, {"id": 774, "value": "2621440 "}, {"id": 775, "value": "5242880 "}, {"id": 78, "value": "900"}, {"id": 780, "value": "3600"}, {"id": 781, "value": "120"}, {"id": 782, "value": "60"}, {"id": 787, "value": "0,1"}, {"id": 789, "value": "{BE6CBA05-11C2-42E8-88B2-7D4B3E3E8734}_1719712948"}, {"id": 79, "value": "30"}, {"id": 791, "value": "604800"}, {"id": 794, "value": "1"}, {"id": 796, "value": "604800"}, {"id": 797, "value": "0.03"}, {"id": 798, "value": "0.03"}, {"id": 799, "value": "0.09"}, {"id": 80, "value": "1296000"}, {"id": 800, "value": "0.09"}, {"id": 802, "value": "120"}, {"id": 803, "value": "Survey"}, {"id": 807, "value": "1209600000.00"}, {"id": 808, "value": "60"}, {"id": 81, "value": "10"}, {"id": 813, "value": "1,0"}, {"id": 82, "value": "120"}, {"id": 820, "value": "9000"}, {"id": 828, "value": "7"}, {"id": 829, "value": "3"}, {"id": 83, "value": "21600"}, {"id": 830, "value": "720"}, {"id": 832, "value": "300"}, {"id": 833, "value": "2100"}, {"id": 84, "value": "19.131.0704.0007"}, {"id": 844, "value": "300000"}, {"id": 85, "value": "05a0e69a-418a-47c1-9c25-9387261bf991|"}, {"id": 854, "value": "604800"}, {"id": 86, "value": "5 "}, {"id": 863, "value": "300000"}, {"id": 865, "value": "0.001"}, {"id": 866, "value": "0.1"}, {"id": 867, "value": "0.1"}, {"id": 869, "value": "30"}, {"id": 87, "value": "10 "}, {"id": 872, "value": "100"}, {"id": 873, "value": "0.03"}, {"id": 874, "value": "0.03"}, {"id": 875, "value": "20"}, {"id": 876, "value": "https://clients.config.gcc.office.net/odb/v1.0/synchealth"}, {"id": 88, "value": "<PERSON><PERSON>"}, {"id": 880, "value": "50"}, {"id": 881, "value": "10"}, {"id": 89, "value": "86400 "}, {"id": 896, "value": "120"}, {"id": 90, "value": "1"}, {"id": 905, "value": "20"}, {"id": 906, "value": "10"}, {"id": 909, "value": "**********"}, {"id": 91, "value": "10000 "}, {"id": 928, "value": "14"}, {"id": 93, "value": "604800"}, {"id": 934, "value": "https://onedrive.live.com/desktop?view=8&sw=bypass"}, {"id": 937, "value": "1000"}, {"id": 940, "value": "15"}, {"id": 953, "value": "-**********,-**********,-**********,-**********"}, {"id": 96, "value": "GetSignature_HighFrequency,OpenUploadSession_HighFrequency,OpenUploadSession,GetSignature_FrequencyBackoff,OpenUploadSession_FrequencyBackoff,UploadBatch,DownloadBlock,InlineBatchUpload_FrequencyBackoff,DiffInlineUploadBatch_FrequencyBackoff"}]}