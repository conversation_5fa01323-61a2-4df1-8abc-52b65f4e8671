<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007" xmlns:resource="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>BITSDiagnostic</ID>
    <Version>3.5</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-20400</Name>
    <Description>@diagpackage.dll,-20401</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>1.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.0</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>TS_Main.ps1</FileName>
      <ExtensionPoint>
        <InteractionIDList>
          <InteractionID>INT_EnableSFC</InteractionID>
        </InteractionIDList>
      </ExtensionPoint>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_BITSRegKeys</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-20406</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_BITSRegKeys</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-20407</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_BITSRegKeys.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>RC_BITSRegKeys.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_BITSDLL</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-20300</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_BITSDLL</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-20403</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_BITSDLL.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
      <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>RC_BITSDLL.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_BITSACL</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-19871</Name>
        <Description/>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_BITSACL</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>bitsAcl</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-19873</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>bitsAcl</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_BITSACL.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>RC_BITSACL.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions>
    </SingleResponseInteractions>
    <MultipleResponseInteractions/>
    <TextInteractions>
      <TextInteraction>
        <RegularExpression>
        </RegularExpression>
        <ID>INT_EnableSFC</ID>
        <DisplayInformation>
          <Parameters/>
          <Name>@diagpackage.dll,-20402</Name>
          <Description/>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
          <NoUI/>
          <Default>true</Default>
        </ExtensionPoint>
      </TextInteraction>
    </TextInteractions>
    <PauseInteractions>
      <PauseInteraction>
        <ID>INT_RestartReq</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-19827</Name>
          <Description>@diagpackage.dll,-19828</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
        </ExtensionPoint>
      </PauseInteraction>
    </PauseInteractions>
    <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
 	<Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-10</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-20</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-30</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-40</HelpKeywords>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>