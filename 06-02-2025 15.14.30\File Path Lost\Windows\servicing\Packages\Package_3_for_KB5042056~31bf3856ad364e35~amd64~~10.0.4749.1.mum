<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for KB5042056" displayName="default" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="http://support.microsoft.com/?kbid=5042056" creationTimeStamp="2024-07-16T22:32:32Z" lastUpdateTimeStamp="2024-07-16T22:32:32Z">
  <assemblyIdentity name="Package_3_for_KB5042056" version="10.0.4749.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5042056" releaseType="Update" restart="possible">
    <parent buildCompare="EQ" integrate="separate" disposition="detect">
      <assemblyIdentity name="Microsoft-Windows-NetFx4-OC-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-NetFx4-OC-Package" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-NetFx4-US-OC-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <parent buildCompare="EQ" integrate="standalone" disposition="detect">
        <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerAzureStackHCICorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerSolutionEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageWorkgroupEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineCorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerWebEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      </parent>
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="5042056-22_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="aspnet_regsql" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="aspnet_regsql" version="4.0.15805.730" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="de9300dd5d38506446eb901ce27dd717" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-23_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="PresentationCore" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
          <assemblyIdentity name="PresentationCore" version="4.0.15805.735" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="509e18b1618d5358aaeddfd551a14e27" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-24_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-compatjit_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="1a1832bb6a460f00c5444a858788afda" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-25_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Workflow.ComponentModel" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="c75b868a9480c3c9eafc22a628f67126" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-26_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-PresentationHostDLLMUI" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-PresentationHostDLLMUI" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d51e8c1ae97524e9f19d97a212cd1bae" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-27_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Windows.Forms.DataVisualization" version="4.0.15805.161" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="6798d0bd113f335c957c2cfc6135d5c3" version="4.0.15805.161" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-28_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-GlobalMonospaceCF" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-GlobalMonospaceCF" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="c3314451597b0ffa87ce6f15c92e2e3a" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-29_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="70cb36f25f2d2940b3b6180e8115cda6" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-30_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-mscorpehost_dll" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-mscorpehost_dll" version="4.0.15805.730" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="7b9c37f6aad20e0bda76e814e38b776e" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-31_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.Discovery" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="e05200a106f55f44d0b32fedb345f4b9" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-32_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="UIAutomationClientsideProviders" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="0d99a9ecb829981d07c56e929806d533" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-33_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Windows.Forms" version="4.0.15805.725" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="4fe235d96a68a9be4ff73b8c5a20a248" version="4.0.15805.725" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-34_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-GlobalSansSerifCF" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-GlobalSansSerifCF" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="1adf7bfb4cd04358c619bb5c2ecccf31" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-35_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-mscoreei_dll" version="4.0.15805.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-mscoreei_dll" version="4.0.15805.110" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="b38131f5ca54a2c4a70b75e3f36176d9" version="4.0.15805.110" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-36_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-dfdll_dll" version="4.0.15805.661" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-dfdll_dll" version="4.0.15805.661" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="02d4626918bbf84b9fef32132954e36b" version="4.0.15805.661" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-37_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-GlobalSerifCF" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-GlobalSerifCF" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="a2e7918a602e960c2e38f75581183e13" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-38_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Workflow.Runtime" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="85254a5e748c87144d5727b3e34ce9c7" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-39_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-webengine4_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-webengine4_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="4d79bea15f8d175aad0f3f4925728aa2" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-40_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="UIAutomationTypes" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="f7f05b6fbceb5098baadfbb964b3a71a" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-41_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-PresentationHostDLL" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-PresentationHostDLL" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="2271fe8175ecf9ea7429963708b41210" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-42_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.DirectoryServices" version="4.0.15805.451" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="be94ba2a59104a793e7959cd2ef46a93" version="4.0.15805.451" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-43_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-ngen_exe" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-ngen_exe" version="4.0.15805.285" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="f81d571c420d7bf9082cf90a27c93540" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-44_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Activities" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d03c280b2e207a777d3a15795c296e56" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-45_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Web.Extensions" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="5855ebdfa13d565ec632d34894440429" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-46_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Deployment" version="4.0.15805.661" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="74c7b17ffdd377dc799a9dc4c351937d" version="4.0.15805.661" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-47_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Drawing.Design" version="4.0.15805.161" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="94e489467c47649a59f13eeef5f8ba21" version="4.0.15805.161" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-48_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-peverify_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-peverify_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="8382efce488917c3374b84ac97a7742a" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-49_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-ServiceMonikerSupport_dll" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-ServiceMonikerSupport_dll" version="4.0.15805.721" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="80fb80273b6293291bcec38afffd709c" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-50_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-NGENTASK_EXE" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-NGENTASK_EXE" version="4.0.15805.285" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="58149f6af1ecede9e25e50e697ba1f10" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-51_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="UIAutomationProvider" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="883b0971634e64d8eec4e123300b968d" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-52_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Security" version="4.0.15805.236" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="217551614fc3a8643ab2a11a9e4e9706" version="4.0.15805.236" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-53_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-clr_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-clr_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="a1c36e7ea00b9bcea6fc447b030d6274" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-54_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-mscordbi_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-mscordbi_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="53925da38cb10d932b458bf99dfa8591" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-55_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-PenIMC_v0400" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-PenIMC_v0400" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="ae2010a749c7e5361fe5bdd14743dc46" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-56_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="UIAutomationClient" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="dcab691f3e05f89c9a6dbe6f8b13567a" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-57_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-mscordacwks_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-mscordacwks_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="885e9e521b5bdf88fbd85c8bb8bed12b" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-58_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="aspnet_regbrowsers" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="aspnet_regbrowsers" version="4.0.15805.730" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d631345253a3bc88aa48295e7471336f" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-59_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Speech" version="4.0.15805.161" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="bdb01a98bd0eea998e25c1bd235ae60f" version="4.0.15805.161" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-60_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-aspnet_wp_exe" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-aspnet_wp_exe" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="98a44e75b3d89bf75de1be1e80f72bb2" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-61_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Web.ApplicationServices" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="08077798610622b4dec5c174669d0762" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-62_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-aspnet_regiis_exe" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-aspnet_regiis_exe" version="4.0.15805.730" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="5b6662f7dbb0771ea5d2ea7358b11f0b" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-63_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.IdentityModel.Services" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="e56534e92e15bbd753c2c28d1428d82e" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-64_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-PenIMC" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-PenIMC" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="222fc358746c65669b4c56a957162bef" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-65_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="PresentationFramework-SystemData" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="996c7b989f33980bce46acc5f04884de" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-66_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-wpfgfx" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-wpfgfx" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="158117489bf4ba507cefd6c34afc457f" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-67_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Activities.Presentation" version="4.0.15805.470" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="dd127744f3bd5239897ee86e1038c15a" version="4.0.15805.470" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-68_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="PresentationFramework" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="d3a9c6eaa738b809bd79686bcc7e227c" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-69_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-aspnet_state_perf_ini" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-aspnet_state_perf_ini" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="025a16d7a49dda266d8934617dd7db60" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-70_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-sos_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-sos_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="76234aab36ff1af5339e89026953c4c7" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-71_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-vbc.rsp" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-vbc.rsp" version="4.0.15805.730" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="176acbfe815e55bf4c95f2a914a2ae12" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-72_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.Channels" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="b13247452c30564a3e34aa1b8289da08" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-73_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.WasHosting" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="cfd54072a65d7e1267b0f846c530b2a7" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-74_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-clrcompression_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-clrcompression_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="f626416614b5dab7b8e1c30e8069a383" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-75_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-webengine_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-webengine_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="9431272b1a5d937c83fed546afe5eda5" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-76_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.Internals" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="78b3993e249fd9e517243eaeceb86e37" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-77_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Configuration" version="4.0.15805.142" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="b9e3a99fd2db6807f45457afdc9f80d3" version="4.0.15805.142" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-78_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Web" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="System.Web" version="4.0.15805.735" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="45cbb739935a0429322c7ce965c0e35d" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-79_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.IdentityModel" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="23dbfcfc14e3913599940eff1e2bbbeb" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-80_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-aspnet_state_perf_h" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-aspnet_state_perf_h" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="83072ba41b7ef7e00b7bba8d68187705" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-81_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-WorkflowServiceHostPerformanceCounters_dll" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-WorkflowServiceHostPerformanceCounters_dll" version="4.0.15805.101" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="a3733f7ad9c076a49ee704874fdc3a6a" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-82_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Workflow.Activities" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="359afcc8af982f7d433daab4d28c1883" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-83_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Drawing" version="4.0.15805.740" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="ebfefb655112c74b7486d0bd3b5313f4" version="4.0.15805.740" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-84_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-PresentationNative" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-PresentationNative" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="fa6e5fcd726e5add1e5d22149dc4101d" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-85_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-diasymreader_dll" version="4.0.15805.671" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-diasymreader_dll" version="4.0.15805.671" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="4c24624b34cb2585602b093d50160404" version="4.0.15805.671" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-86_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-mscorlib_ni" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-mscorlib_ni" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="255a3d6fcc16162572acac1dee7be49a" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-87_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Windows.Controls.Ribbon" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="94c33ca40149fb908831b5c83d41e8fc" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-88_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-mscorsvc_dll" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-mscorsvc_dll" version="4.0.15805.285" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="1406b999ea1a1161f7f6f23c006bcc18" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-89_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Runtime.Serialization" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="f346f2aae1249f7743c8cbe0f62aefbd" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-90_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Printing" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
          <assemblyIdentity name="System.Printing" version="4.0.15805.101" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="af676c02657d2738eab0be3d68d99266" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-91_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="c2dcf8cf4852ce868a60c911cb4d6b71" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-92_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-aspnet_perf_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-aspnet_perf_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="373c84e7b8c81c9e1e73c6ec4bb0382c" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-93_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="mscorlib" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
          <assemblyIdentity name="mscorlib" version="4.0.15805.735" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="b99e3a4286db73aabe31ea0a7a7fef60" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-94_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-vbc_exe" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-vbc_exe" version="4.0.15805.730" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="1a558e7175c4a5e8656839bfd8ef9ad4" version="4.0.15805.730" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-95_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Management" version="4.0.15805.370" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="3f9303708c773be9e5449fd8354d3c4e" version="4.0.15805.370" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-96_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Runtime.Remoting" version="4.0.15805.698" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="0aa0aaf8fa26c1d63cece9dfabe4dad8" version="4.0.15805.698" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-97_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.Activities" version="4.0.15805.210" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="409ae9e5f17f722a3afd85cf16849242" version="4.0.15805.210" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-98_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Drawing" version="4.0.15805.340" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="594bfe2b3bfd6a69c8316e614559d333" version="4.0.15805.340" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-99_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-clrjit_dll" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-clrjit_dll" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="295bf2e33d2e7d635419651156603ffb" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-100_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-GlobalUserInterfaceCF" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-GlobalUserInterfaceCF" version="4.0.15805.735" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="31dfd7909cf57165e65270fb7c2e3932" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-101_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WindowsFormsIntegration" version="4.0.15805.598" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="948b0309b729a88288c221b21780dd46" version="4.0.15805.598" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-102_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx4-mscorsvw_exe" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
          <assemblyIdentity name="NetFx4-mscorsvw_exe" version="4.0.15805.285" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="f1afcfe6c4f7297cfed5e0405cbd1510" version="4.0.15805.285" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b03f5f7f11d50a3a" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-103_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Data" version="4.0.15805.698" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
          <assemblyIdentity name="System.Data" version="4.0.15805.698" processorArchitecture="wow64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="673a80646751664258ab79749456b675" version="4.0.15805.698" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-104_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Core" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="45ba1511956b576cce39601fad286ca2" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-105_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="SMDiagnostics" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="a0aa94bc7cd7d31bd3611eb665acf669" version="4.0.15805.721" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-106_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Xaml" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="6a2eeb25b9552a1dc90c69ad7133599c" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="b77a5c561934e089" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-107_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="ReachFramework" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="a35eee64be49ad0344d9e600f4462c97" version="4.0.15805.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-108_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WindowsBase" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="8ba51c5bcbf40b23bc47bc15cc3eacc3" version="4.0.15805.735" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-109_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.ServiceModel.Web" version="4.0.15805.210" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="c732dffc5bdc999d82428db86935c546" version="4.0.15805.210" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
  </package>
</assembly>
