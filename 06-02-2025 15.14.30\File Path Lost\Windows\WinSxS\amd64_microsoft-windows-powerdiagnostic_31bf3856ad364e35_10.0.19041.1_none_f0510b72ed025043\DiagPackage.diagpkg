<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>PowerDiagnostic</ID>
    <Version>1.5</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
  <PowerShellVersion>1.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>false</RequiresElevation>
      <RequiresInteractivity>false</RequiresInteractivity>
      <FileName>Power_Troubleshooter.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_MinimumProcessorState</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-227</Name>
        <Description>@diagpackage.dll,-229</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_ChangeProcessorState</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-220</Name>
            <Description>@diagpackage.dll,-221</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_ChangeProcessorState.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_MinProcessorState.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_DimDisplay</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-113</Name>
        <Description>@diagpackage.dll,-114</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_AdjustDimDisplay</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>P1</Name>
                <DefaultValue>default value1</DefaultValue>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-115</Name>
            <Description>@diagpackage.dll,-116</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_AdjustDimDisplay.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_DimDisplay.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
        <Parameter>
          <Name>P1</Name>
          <DefaultValue>default value1</DefaultValue>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_highperformance</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-8</Name>
        <Description>@diagpackage.dll,-24</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Balanced</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-25</Name>
            <Description>@diagpackage.dll,-26</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_Balanced.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_Balanced.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_DisplayIdleTimeouttoolong</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-3</Name>
        <Description>@diagpackage.dll,-4</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_ResetDisplayIdleTimeout</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>P1</Name>
                <DefaultValue>default value1</DefaultValue>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-5</Name>
            <Description>@diagpackage.dll,-6</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_ResetDisplayIdleTimeout.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_DisplayIdleTimeout.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
        <Parameter>
          <Name>P1</Name>
          <DefaultValue>default value1</DefaultValue>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_Idlesleepsettingistoolong</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-7</Name>
        <Description>@diagpackage.dll,-9</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_ResetIdleSleepsetting</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-10</Name>
            <Description>@diagpackage.dll,-11</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_ResetIdleSleepsetting.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_IdleSleepsetting.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_DiskIdleTimeout</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-12</Name>
        <Description>@diagpackage.dll,-13</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_ResetIdleDiskTimeout</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-14</Name>
            <Description>@diagpackage.dll,-15</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_ResetIdleDiskTimeout.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_IdleDiskTimeout.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_USBSelectiveSuspend</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-20</Name>
        <Description>@diagpackage.dll,-21</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_DisableUSBSelective</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-22</Name>
            <Description>@diagpackage.dll,-23</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_DisableUSBSelective.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_USBSelective.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_Wirelessadaptersettings</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-43</Name>
        <Description>@diagpackage.dll,-44</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Adjustwirelessadaptersettings</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-45</Name>
            <Description>@diagpackage.dll,-46</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_Adjustwirelessadaptersettings.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_Wirelessadaptersettings.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_ScreenSaver</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16</Name>
        <Description>@diagpackage.dll,-17</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_DisableScreensaver</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-18</Name>
            <Description>@diagpackage.dll,-19</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_DisableScreensaver.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>TS_ScreenSaver.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_ScreenBrightness</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-27</Name>
        <Description>@diagpackage.dll,-28</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_AdjustScreenBrightness</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-29</Name>
            <Description>@diagpackage.dll,-30</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_AdjustScreenBrightness.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>TS_ScreenBrightness.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
      <SingleResponseInteractions/>
      <MultipleResponseInteractions/>
      <TextInteractions/>
      <PauseInteractions/>
      <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Maintenance/>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-100</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-101</HelpKeywords>
    <Feedback>
      <ContextId>71</ContextId>
    </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>