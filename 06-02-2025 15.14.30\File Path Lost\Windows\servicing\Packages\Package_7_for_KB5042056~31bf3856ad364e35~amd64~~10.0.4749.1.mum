<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for KB5042056" displayName="default" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="http://support.microsoft.com/?kbid=5042056" creationTimeStamp="2024-07-16T22:32:33Z" lastUpdateTimeStamp="2024-07-16T22:32:33Z">
  <assemblyIdentity name="Package_7_for_KB5042056" version="10.0.4749.1" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5042056" releaseType="Update" restart="possible">
    <parent buildCompare="EQ" integrate="separate" disposition="staged">
      <assemblyIdentity name="Microsoft-Windows-NetFx3-OnDemand-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <parent buildCompare="EQ" integrate="standalone" disposition="detect">
        <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseGNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerAzureStackHCICorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerSolutionEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerStorageWorkgroupEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineCorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerTurbineEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
        <assemblyIdentity name="Microsoft-Windows-ServerWebEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      </parent>
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="5042056-139_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WWF-System.Workflow.ComponentModel" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
          <assemblyIdentity name="WWF-System.Workflow.ComponentModel" version="10.0.19200.101" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="c9531e019f9be234df9ecd4ac69e2922" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-140_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx35Linq-System.Web.Extensions" version="10.0.19200.912" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
          <assemblyIdentity name="NetFx35Linq-System.Web.Extensions" version="10.0.19200.912" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="f6366e4b9b191d81024628d17bf4c643" version="10.0.19200.912" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-141_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="NetFx35Linq-VB_Compiler_Orcas" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
          <assemblyIdentity name="NetFx35Linq-VB_Compiler_Orcas" version="10.0.19200.940" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="51a46fdb48bb366baabfd5a74947d196" version="10.0.19200.940" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-142_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Workflow.ComponentModel" version="10.0.19200.101" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="e577b6d9cc9749e1d45e5032e22339e4" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-143_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WWF-System.Workflow.Runtime" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
          <assemblyIdentity name="WWF-System.Workflow.Runtime" version="10.0.19200.101" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="1a28c4c4b9e7ee70c7c52823fef4e29e" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-144_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Web.Extensions" version="10.0.19200.912" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="b9e54b45ae04a0b15706da17e4928a4f" version="10.0.19200.912" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-145_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="WWF-System.Workflow.Activities" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
          <assemblyIdentity name="WWF-System.Workflow.Activities" version="10.0.19200.101" processorArchitecture="x86" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="76f94546a54cb08c0f0ababf7271171f" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-146_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Workflow.Activities" version="10.0.19200.101" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="2b838b169229f2497e39658e1af466af" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
    <update name="5042056-147_neutral">
      <applicable disposition="staged">
        <updateComponent elevate="build">
          <assemblyIdentity name="System.Workflow.Runtime" version="10.0.19200.101" processorArchitecture="msil" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
        </updateComponent>
      </applicable>
      <component>
        <assemblyIdentity name="75b64c1a956bca73de26fbfc182765c9" version="10.0.19200.101" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" versionScope="nonSxS" />
      </component>
    </update>
  </package>
</assembly>
