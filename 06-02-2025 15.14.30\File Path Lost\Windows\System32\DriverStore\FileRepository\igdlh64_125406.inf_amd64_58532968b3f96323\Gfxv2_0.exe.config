<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
      <section name="CUI6dot0.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <userSettings>
    <CUI6dot0.Properties.Settings>
      <setting name="WindowState" serializeAs="Xml">
        <value />
      </setting>
    </CUI6dot0.Properties.Settings>
  </userSettings>
  <startup>
    <supportedRuntime version="v2.0.50727"/>
  </startup>
  <runtime>
    <generatePublisherEvidence enabled="false"/>
  </runtime>
</configuration>