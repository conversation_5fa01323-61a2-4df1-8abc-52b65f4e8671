<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for KB5011048" displayName="default" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="http://support.microsoft.com/?kbid=5011048" creationTimeStamp="2023-09-25T13:31:24Z" lastUpdateTimeStamp="2023-09-25T13:31:24Z">
  <assemblyIdentity name="Package_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5011048" applicabilityEvaluation="deep" releaseType="Update" restart="possible">
    <parent buildCompare="GE" integrate="separate" disposition="detect">
      <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseGNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerAzureStackHCICorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerDatacenterACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerDatacenterCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerDatacenterEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerSolutionEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerStandardACorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerStandardCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalCorEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerStandardEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerStorageStandardEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerStorageWorkgroupEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerTurbineCorEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerTurbineEdition" version="10.0.20348.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ServerWebEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="5011048-575_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_1_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-576_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_2_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-577_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_3_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-578_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_4_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-579_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_5_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-580_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_6_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-581_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_7_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-582_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_8_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-583_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_9_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-584_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_10_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-585_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_11_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-586_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_12_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-587_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_13_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="5011048-588_neutral_PACKAGE">
      <package integrate="hidden">
        <assemblyIdentity name="Package_14_for_KB5011048" version="10.0.9195.7" language="neutral" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <mum2:packageExtended xmlns:mum2="urn:schemas-microsoft-com:asm.v3" completelyOfflineCapable="no" packageSize="275617294" />
  </package>
</assembly>