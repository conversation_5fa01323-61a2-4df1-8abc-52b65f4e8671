<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for ServicingStack 10.0.19041.4769" displayName="Servicing Stack 10.0.19041.4769" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="" creationTimeStamp="2024-07-25T14:34:35Z" lastUpdateTimeStamp="2024-07-25T14:34:35Z">
  <assemblyIdentity name="Package_for_ServicingStack_4769" version="19041.4769.1.1" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5041579" applicabilityEvaluation="deep" releaseType="Security Update" restart="possible" selfUpdate="true" permanence="permanent" psfName="SSU-19041.4769-x64.psf">
    <parent buildCompare="EQ" integrate="standalone" disposition="detect">
      <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-UtilityVMEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-WinPE-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="Wrapper-02B3DEC2C7ACC706B411B1E1F7443DAD853A211E6356D3FDBF840E8B3428BC59_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-02B3DEC2C7ACC706B411B1E1F7443DAD853A211E6356D3FDBF840E8B3428BC59" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-0900DF6576070605386BF64B1255BDCAFB48B854C10CCF367985197C1796707D_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-0900DF6576070605386BF64B1255BDCAFB48B854C10CCF367985197C1796707D" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-1E23CBBA94D0DD0829D61CEE4827EDB6E4D15CD432DE6F84F45BAAAE7CE74A58_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-1E23CBBA94D0DD0829D61CEE4827EDB6E4D15CD432DE6F84F45BAAAE7CE74A58" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-253403E64ED66E00F44E6AD604EC16C72E12645F075EA25432594A787C7AD9A6_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-253403E64ED66E00F44E6AD604EC16C72E12645F075EA25432594A787C7AD9A6" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-46733D9C0B6A9A9A7923527C003C5D229EFBAC2BE343951C7701EA8383DA7647_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-46733D9C0B6A9A9A7923527C003C5D229EFBAC2BE343951C7701EA8383DA7647" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-54DBFA2DA9A4A5E772F5622DB64E4A1BC8070313EF01CB0710ECF1C5F51F2BB9_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-54DBFA2DA9A4A5E772F5622DB64E4A1BC8070313EF01CB0710ECF1C5F51F2BB9" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-566394C217ED876AFA483E46B7E5BB331E7F92C7938089A6E25FB936AC9F2B8F_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-566394C217ED876AFA483E46B7E5BB331E7F92C7938089A6E25FB936AC9F2B8F" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-6A838E2091D1BB1B9D367B2542877E0753095420447A430A69202FC411DE4D43_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-6A838E2091D1BB1B9D367B2542877E0753095420447A430A69202FC411DE4D43" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-6BDD3085B066AD7ACB04FB6184C9E9C81A4F0BBD02507DA40CB59709F5926D9F_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-6BDD3085B066AD7ACB04FB6184C9E9C81A4F0BBD02507DA40CB59709F5926D9F" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-93A89763E62F86D21A69464172319A80E2238DB7AFB88AEE7EF5677E649AF475_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-93A89763E62F86D21A69464172319A80E2238DB7AFB88AEE7EF5677E649AF475" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-A5CB25B74F0286CF986E82B7D61691BB242C439EF1BA3DB2D959C0128EB6B165_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-A5CB25B74F0286CF986E82B7D61691BB242C439EF1BA3DB2D959C0128EB6B165" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-C8BCEA8B2DF5A4B85F4E676FE819ACC4BD5D07959EB5886713115A8673543319_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-C8BCEA8B2DF5A4B85F4E676FE819ACC4BD5D07959EB5886713115A8673543319" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-E57A0FE2DFB15165CBA2B3B05BF94AF8269C20EFC48C00CB5B6627D1F6A8E748_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-E57A0FE2DFB15165CBA2B3B05BF94AF8269C20EFC48C00CB5B6627D1F6A8E748" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-EE9226F89854004575701A8080BD4D8784CBB37ED145A2737DB81593034CEDF6_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-EE9226F89854004575701A8080BD4D8784CBB37ED145A2737DB81593034CEDF6" version="10.0.19041.4769" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <mum2:packageExtended xmlns:mum2="urn:schemas-microsoft-com:asm.v3" completelyOfflineCapable="undetermined" packageSize="55956503" />
  </package>
</assembly>
