﻿<?xml version="1.0" encoding="utf-8"?>
<R Id="230200" V="0" DC="SM" EN="Office.ClickToRun.WildFireC2R.DeviceScreen" ATT="0da1917aa56040d3a011c3813ca36107-76f080d8-b37f-4635-8054-5c133fcd04c4-6587" DCa="PSP PSU SS" xmlns="">
  <S>
    <UCSS T="1" C="WildFireC2R" S="Verbose Medium Monitorable Unexpected Assert" />
  </S>
  <C T="FT" I="0" O="true" N="TimeStamp">
    <S T="1" F="TimeStamp100ns" M="Ignore" />
  </C>
  <C T="W" I="1" O="true" N="ProcessID">
    <S T="1" F="ULS_Process" M="Ignore" />
  </C>
  <C T="U32" I="2" O="true" N="ThreadID">
    <S T="1" F="ULS_ThreadID" M="Ignore" />
  </C>
  <C T="TAG" I="3" O="true" N="EventID">
    <S T="1" F="ULS_Tag" M="Ignore" />
  </C>
  <C T="W" I="4" O="true" N="Severity">
    <S T="1" F="ULS_Severity" M="Ignore" />
  </C>
  <C T="W" I="5" O="true" N="Message">
    <S T="1" F="ContextData" M="Ignore" />
  </C>
  <C T="W" I="6" O="true" N="SQMMachineID">
    <S T="1" F="MachineId" M="Ignore" />
  </C>
  <C T="W" I="7" O="true" N="SUSClientID">
    <S T="1" F="SessionID" M="Ignore" />
  </C>
  <C T="U32" I="8" O="true" N="GeoID">
    <S T="1" F="GeoID" M="Ignore" />
  </C>
  <C T="W" I="9" O="true" N="Version">
    <S T="1" F="Ver" M="Ignore" />
  </C>
  <C T="W" I="10" O="true" N="Path">
    <S T="1" F="Path" M="Ignore" />
  </C>
  <C T="W" I="11" O="true" N="C2RClientVer">
    <S T="1" F="C2RClientVer" M="Ignore" />
  </C>
  <T>
    <S T="1" />
  </T>
</R>