<dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>PrinterDiagnostic</ID>
    <Version>4.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>MF_PrinterDiagnostic.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_SpoolerStartMode</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-101</Name>
        <Description>@diagpackage.dll,-102</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RC_SetSpoolertMode</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-103</Name>
            <Description>@diagpackage.dll,-104</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_SetSpoolerStartMode.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_SetSpoolerMode.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_SpoolerService</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-201</Name>
        <Description>@diagpackage.dll,-202</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_StartSpoolerService</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-203</Name>
            <Description>@diagpackage.dll,-204</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_StartSpoolerService.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_SpoolerService.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_SpoolerCrashing</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-301</Name>
        <Description>@diagpackage.dll,-302</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_SpoolerCrashing</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-303</Name>
            <Description>@diagpackage.dll,-304</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_SpoolerCrashing.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_SpoolerCrashing.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_NoPrinterInstalled</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-401</Name>
        <Description>@diagpackage.dll,-402</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_NoPrinterInstalled</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-403</Name>
            <Description>@diagpackage.dll,-404</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_NoPrinterInstalled.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
              <Parameter>
                <Name>PRINTERCOUNT</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_NoPrinterInstalled.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
        <Parameter>
          <Name>DEVICEID</Name>
          <DefaultValue/>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_WrongDefaultPrinter</ID>
      <DisplayInformation>
        <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
        <Name>@diagpackage.dll,-501</Name>
        <Description>@diagpackage.dll,-502</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_WrongDefaultPrinter</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-503</Name>
            <Description>@diagpackage.dll,-504</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_WrongDefaultPrinter.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_DefaultPrinter.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_CannotConnect</ID>
      <DisplayInformation>
          <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
        <Name>@diagpackage.dll,-601</Name>
        <Description>@diagpackage.dll,-602</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_CannotConnect</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-603</Name>
            <Description>@diagpackage.dll,-604</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_CannotConnect.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
        <Parameter>
          <Name>PRINTERTYPE</Name>
          <DefaultValue/>
        </Parameter>
        <Parameter>
          <Name>SMBSHARE</Name>
          <DefaultValue/>
        </Parameter>
        <Parameter>
          <Name>TCP_PRINTERADDRESS</Name>
          <DefaultValue/>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_PrinterTurnedOff</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-701</Name>
        <Description>@diagpackage.dll,-702</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_PrinterTurnedOff</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-703</Name>
            <Description>@diagpackage.dll,-704</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_PrinterTurnedOff.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>PRINTERNAME</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>VF_PrinterTurnedOff.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_OutOfToner</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-801</Name>
        <Description>@diagpackage.dll,-802</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_OutOfToner</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-803</Name>
            <Description>@diagpackage.dll,-804</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_OutOfPaper</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-901</Name>
        <Description>@diagpackage.dll,-902</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_OutOfPaper</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-903</Name>
            <Description>@diagpackage.dll,-904</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_PaperJam</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-1001</Name>
        <Description>@diagpackage.dll,-1002</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_PaperJam</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-1003</Name>
            <Description>@diagpackage.dll,-1004</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_PrintJobsStuck</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-1101</Name>
        <Description>@diagpackage.dll,-1102</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_ProcessPrinterjobs</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-1103</Name>
            <Description>@diagpackage.dll,-1104</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_ProcessPrinterjobs.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
          <ID>RS_RestartSpoolerService</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-1105</Name>
            <Description/>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_RestartSpoolerService.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
          <ID>RS_CancelAllJobs</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-1107</Name>
            <Description>@diagpackage.dll,-1108</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_CancelAllJobs.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
        <Resolver>
          <ID>RS_DeletePrintJobs</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-1109</Name>
            <Description>@diagpackage.dll,-1110</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_DeletePrintJobs.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>PRINTERNAME</Name>
              <DefaultValue/>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_PrintJobsStuck.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_PrinterDriver</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-1201</Name>
        <Description>@diagpackage.dll,-1202</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_PrinterDriver</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-1203</Name>
            <Description>@diagpackage.dll,-1204</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_PrinterDriver.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier/>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_PrinterDriverError</ID>
      <DisplayInformation>
          <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
        <Name>@diagpackage.dll,-1401</Name>
        <Description>@diagpackage.dll,-1402</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_PrinterDriverError</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-1403</Name>
            <Description>@diagpackage.dll,-1404</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script/>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_PrinterDriverError.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters>
        <Parameter>
          <Name>DEVICEID</Name>
          <DefaultValue/>
        </Parameter>
      </ContextParameters>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
      <SingleResponseInteractions>
        <SingleResponseInteraction>
          <AllowDynamicResponses>true</AllowDynamicResponses>
          <Choices>
            <Choice>
              <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-2103</Name>
                <Description>@diagpackage.dll,-2104</Description>
              </DisplayInformation>
              <Value>RESCAN</Value>
              <ExtensionPoint/>
            </Choice>
          </Choices>
          <ID>IT_SelectPrinter</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2101</Name>
            <Description>@diagpackage.dll,-2102</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint/>
        </SingleResponseInteraction>
        <SingleResponseInteraction>
          <AllowDynamicResponses>true</AllowDynamicResponses>
          <Choices/>
          <ID>IT_AutoSelectPrinter</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2105</Name>
            <Description>@diagpackage.dll,-2106</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <NoUI/>
          </ExtensionPoint>
        </SingleResponseInteraction>
      </SingleResponseInteractions>
      <MultipleResponseInteractions/>
      <TextInteractions/>
      <PauseInteractions>
        <PauseInteraction>
          <ID>IT_PrinterTurnedOff</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-705</Name>
            <Description>@diagpackage.dll,-706</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <InteractionIcon>Warning</InteractionIcon>
          </ExtensionPoint>
        </PauseInteraction>
      </PauseInteractions>
      <LaunchUIInteractions>
        <LaunchUIInteraction>
             <Parameters/>
             <CommandLine>rundll32 printui,PrintUIEntry /il</CommandLine>
          <ID>IT_AddPrinter</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2301</Name>
            <Description>@diagpackage.dll,-2302</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <CommandLinks/>
            <ButtonText>@diagpackage.dll,-2303</ButtonText>
          </ExtensionPoint>
        </LaunchUIInteraction>
        <LaunchUIInteraction>
             <Parameters>
              <Parameter>
                <Name>UPDATEDRIVER</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>DRIVERENTRY</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>PRINTERNAME</Name>
                <DefaultValue/>
              </Parameter>
             </Parameters>
             <CommandLine>rundll32 "%UPDATEDRIVER%",%DRIVERENTRY% "%PRINTERNAME%"</CommandLine>
          <ID>IT_UpdateDriver</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2401</Name>
            <Description>@diagpackage.dll,-2402</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <CommandLinks/>
            <ButtonText>@diagpackage.dll,-2403</ButtonText>
          </ExtensionPoint>
        </LaunchUIInteraction>
       </LaunchUIInteractions>
  </Interactions>
  <ExtensionPoint>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-10</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-11</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-12</HelpKeywords>
        <Feedback>
      <ContextId>72</ContextId>
    </Feedback>
    </ExtensionPoint>
</dcmPS:DiagnosticPackage>