<%
//----------------------------------------------------------
// Microsoft OLE DB
// Copyright (C), 1999 Microsoft Corporation.
//
// OLE DB error constant include file for JavaScript
//
//----------------------------------------------------------

var DB_E_BADACCESSORHANDLE           	= 0x80040E00;
var DB_E_ROWLIMITEXCEEDED            	= 0x80040E01;
var DB_E_READONLYACCESSOR            	= 0x80040E02;
var DB_E_SCHEMAVIOLATION             	= 0x80040E03;
var DB_E_BADROWHANDLE                	= 0x80040E04;
var DB_E_OBJECTOPEN                  	= 0x80040E05;
var DB_E_BADCHAPTER                  	= 0x80040E06;
var DB_E_CANTCONVERTVALUE            	= 0x80040E07;
var DB_E_BADBINDINFO                 	= 0x80040E08;
var DB_SEC_E_PERMISSIONDENIED        	= 0x80040E09;
var DB_E_NOTAREFERENCECOLUMN         	= 0x80040E0A;
var DB_E_LIMITREJECTED               	= 0x80040E0B;
var DB_E_NOCOMMAND                   	= 0x80040E0C;
var DB_E_COSTLIMIT                   	= 0x80040E0D;
var DB_E_BADBOOKMARK                 	= 0x80040E0E;
var DB_E_BADLOCKMODE                 	= 0x80040E0F;
var DB_E_PARAMNOTOPTIONAL            	= 0x80040E10;
var DB_E_BADCOLUMNID                 	= 0x80040E11;
var DB_E_BADRATIO                    	= 0x80040E12;
var DB_E_BADVALUES                   	= 0x80040E13;
var DB_E_ERRORSINCOMMAND             	= 0x80040E14;
var DB_E_CANTCANCEL                  	= 0x80040E15;
var DB_E_DIALECTNOTSUPPORTED         	= 0x80040E16;
var DB_E_DUPLICATEDATASOURCE         	= 0x80040E17;
var DB_E_CANNOTRESTART               	= 0x80040E18;
var DB_E_NOTFOUND                    	= 0x80040E19;
var DB_E_NEWLYINSERTED               	= 0x80040E1B;
var DB_E_CANNOTFREE                  	= 0x80040E1A;
var DB_E_GOALREJECTED                	= 0x80040E1C;
var DB_E_UNSUPPORTEDCONVERSION       	= 0x80040E1D;
var DB_E_BADSTARTPOSITION            	= 0x80040E1E;
var DB_E_NOQUERY                     	= 0x80040E1F;
var DB_E_NOTREENTRANT                	= 0x80040E20;
var DB_E_ERRORSOCCURRED              	= 0x80040E21;
var DB_E_NOAGGREGATION               	= 0x80040E22;
var DB_E_DELETEDROW                  	= 0x80040E23;
var DB_E_CANTFETCHBACKWARDS          	= 0x80040E24;
var DB_E_ROWSNOTRELEASED             	= 0x80040E25;
var DB_E_BADSTORAGEFLAG              	= 0x80040E26;
var DB_E_BADCOMPAREOP                	= 0x80040E27;
var DB_E_BADSTATUSVALUE              	= 0x80040E28;
var DB_E_CANTSCROLLBACKWARDS         	= 0x80040E29;
var DB_E_BADREGIONHANDLE             	= 0x80040E2A;
var DB_E_NONCONTIGUOUSRANGE          	= 0x80040E2B;
var DB_E_INVALIDTRANSITION           	= 0x80040E2C;
var DB_E_NOTASUBREGION               	= 0x80040E2D;
var DB_E_MULTIPLESTATEMENTS          	= 0x80040E2E;
var DB_E_INTEGRITYVIOLATION          	= 0x80040E2F;
var DB_E_BADTYPENAME                 	= 0x80040E30;
var DB_E_ABORTLIMITREACHED           	= 0x80040E31;
var DB_E_ROWSETINCOMMAND             	= 0x80040E32;
var DB_E_CANTTRANSLATE               	= 0x80040E33;
var DB_E_DUPLICATEINDEXID            	= 0x80040E34;
var DB_E_NOINDEX                     	= 0x80040E35;
var DB_E_INDEXINUSE                  	= 0x80040E36;
var DB_E_NOTABLE                     	= 0x80040E37;
var DB_E_CONCURRENCYVIOLATION        	= 0x80040E38;
var DB_E_BADCOPY                     	= 0x80040E39;
var DB_E_BADPRECISION                	= 0x80040E3A;
var DB_E_BADSCALE                    	= 0x80040E3B;
var DB_E_BADTABLEID                  	= 0x80040E3C;
var DB_E_BADTYPE                     	= 0x80040E3D;
var DB_E_DUPLICATECOLUMNID           	= 0x80040E3E;
var DB_E_DUPLICATETABLEID            	= 0x80040E3F;
var DB_E_TABLEINUSE                  	= 0x80040E40;
var DB_E_NOLOCALE                    	= 0x80040E41;
var DB_E_BADRECORDNUM                	= 0x80040E42;
var DB_E_BOOKMARKSKIPPED             	= 0x80040E43;
var DB_E_BADPROPERTYVALUE            	= 0x80040E44;
var DB_E_INVALID                     	= 0x80040E45;
var DB_E_BADACCESSORFLAGS            	= 0x80040E46;
var DB_E_BADSTORAGEFLAGS             	= 0x80040E47;
var DB_E_BYREFACCESSORNOTSUPPORTED   	= 0x80040E48;
var DB_E_NULLACCESSORNOTSUPPORTED    	= 0x80040E49;
var DB_E_NOTPREPARED                 	= 0x80040E4A;
var DB_E_BADACCESSORTYPE             	= 0x80040E4B;
var DB_E_WRITEONLYACCESSOR           	= 0x80040E4C;
var DB_SEC_E_AUTH_FAILED             	= 0x80040E4D;
var DB_E_CANCELED                    	= 0x80040E4E;
var DB_E_CHAPTERNOTRELEASED          	= 0x80040E4F;
var DB_E_BADSOURCEHANDLE             	= 0x80040E50;
var DB_E_PARAMUNAVAILABLE            	= 0x80040E51;
var DB_E_ALREADYINITIALIZED          	= 0x80040E52;
var DB_E_NOTSUPPORTED                	= 0x80040E53;
var DB_E_MAXPENDCHANGESEXCEEDED      	= 0x80040E54;
var DB_E_BADORDINAL                  	= 0x80040E55;
var DB_E_PENDINGCHANGES              	= 0x80040E56;
var DB_E_DATAOVERFLOW                	= 0x80040E57;
var DB_E_BADHRESULT                  	= 0x80040E58;
var DB_E_BADLOOKUPID                 	= 0x80040E59;
var DB_E_BADDYNAMICERRORID           	= 0x80040E5A;
var DB_E_PENDINGINSERT               	= 0x80040E5B;
var DB_E_BADCONVERTFLAG              	= 0x80040E5C;
var DB_E_BADPARAMETERNAME            	= 0x80040E5D;
var DB_E_MULTIPLESTORAGE             	= 0x80040E5E;
var DB_E_CANTFILTER                  	= 0x80040E5F;
var DB_E_CANTORDER                   	= 0x80040E60;
var DB_E_NOCOLUMN                    	= 0x80040E65;
var DB_E_COMMANDNOTPERSISTED         	= 0x80040E67;
var DB_E_DUPLICATEID                 	= 0x80040E68;
var DB_E_OBJECTCREATIONLIMITREACHED  	= 0x80040E69;
var DB_E_BADINDEXID                  	= 0x80040E72;
var DB_E_BADINITSTRING               	= 0x80040E73;
var DB_E_NOPROVIDERSREGISTERED       	= 0x80040E74;
var DB_E_MISMATCHEDPROVIDER          	= 0x80040E75;
var DB_E_BADCOMMANDID                	= 0x80040E76;
var DB_E_BADCONSTRAINTTYPE           	= 0x80040E77;
var DB_E_BADCONSTRAINTFORM           	= 0x80040E78;
var DB_E_BADDEFERRABILITY            	= 0x80040E79;
var DB_E_BADMATCHTYPE                	= 0x80040E80;
var DB_E_BADUPDATEDELETERULE         	= 0x80040E8A;
var DB_E_BADCONSTRAINTID             	= 0x80040E8B;
var DB_E_BADCOMMANDFLAGS             	= 0x80040E8C;
var DB_E_OBJECTMISMATCH              	= 0x80040E8D;
var DB_E_NOSOURCEOBJECT              	= 0x80040E91;
var DB_E_RESOURCELOCKED              	= 0x80040E92;
var DB_E_NOTCOLLECTION               	= 0x80040E93;
var DB_E_READONLY                    	= 0x80040E94;
var DB_E_ASYNCNOTSUPPORTED           	= 0x80040E95;
var DB_E_CANNOTCONNECT               	= 0x80040E96;
var DB_E_TIMEOUT                     	= 0x80040E97;
var DB_E_RESOURCEEXISTS              	= 0x80040E98;
var DB_E_RESOURCEOUTOFSCOPE          	= 0x80040E8E;
var DB_E_DROPRESTRICTED              	= 0x80040E90;
var DB_E_DUPLICATECONSTRAINTID       	= 0x80040E99;
var DB_E_OUTOFSPACE                  	= 0x80040E9A;
var DB_SEC_E_SAFEMODE_DENIED         	= 0x80040E9B;
var DB_E_NOSTATISTIC                 	= 0x80040E9C;
var DB_E_ALTERRESTRICTED             	= 0x80040E9D;
var DB_E_RESOURCENOTSUPPORTED        	= 0x80040E9E;
var DB_E_NOCONSTRAINT                	= 0x80040E9F;
var DB_E_COLUMNUNAVAILABLE           	= 0x80040EA0;
var DB_S_ROWLIMITEXCEEDED            	= 0x00040EC0;
var DB_S_COLUMNTYPEMISMATCH          	= 0x00040EC1;
var DB_S_TYPEINFOOVERRIDDEN          	= 0x00040EC2;
var DB_S_BOOKMARKSKIPPED             	= 0x00040EC3;
var DB_S_NONEXTROWSET                	= 0x00040EC5;
var DB_S_ENDOFROWSET                 	= 0x00040EC6;
var DB_S_COMMANDREEXECUTED           	= 0x00040EC7;
var DB_S_BUFFERFULL                  	= 0x00040EC8;
var DB_S_NORESULT                    	= 0x00040EC9;
var DB_S_CANTRELEASE                 	= 0x00040ECA;
var DB_S_GOALCHANGED                 	= 0x00040ECB;
var DB_S_UNWANTEDOPERATION           	= 0x00040ECC;
var DB_S_DIALECTIGNORED              	= 0x00040ECD;
var DB_S_UNWANTEDPHASE               	= 0x00040ECE;
var DB_S_UNWANTEDREASON              	= 0x00040ECF;
var DB_S_ASYNCHRONOUS                	= 0x00040ED0;
var DB_S_COLUMNSCHANGED              	= 0x00040ED1;
var DB_S_ERRORSRETURNED              	= 0x00040ED2;
var DB_S_BADROWHANDLE                	= 0x00040ED3;
var DB_S_DELETEDROW                  	= 0x00040ED4;
var DB_S_TOOMANYCHANGES              	= 0x00040ED5;
var DB_S_STOPLIMITREACHED            	= 0x00040ED6;
var DB_S_LOCKUPGRADED                	= 0x00040ED8;
var DB_S_PROPERTIESCHANGED           	= 0x00040ED9;
var DB_S_ERRORSOCCURRED              	= 0x00040EDA;
var DB_S_PARAMUNAVAILABLE            	= 0x00040EDB;
var DB_S_MULTIPLECHANGES             	= 0x00040EDC;
var DB_S_NOTSINGLETON                	= 0x00040ED7;
var DB_S_NOROWSPECIFICCOLUMNS        	= 0x00040EDD;
var XACT_S_ASYNC                     	= 0x0004D000;
var XACT_S_ABORTING                  	= 0x0004D008;
var XACT_E_CANTRETAIN                	= 0x8004D001;
var XACT_E_COMMITFAILED              	= 0x8004D002;
var XACT_E_ISOLATIONLEVEL            	= 0x8004D008;
var XACT_E_NOENLIST                  	= 0x8004D00A;
var XACT_E_NOISORETAIN               	= 0x8004D00B;
var XACT_E_NOTRANSACTION             	= 0x8004D00E;
var XACT_E_NOTSUPPORTED              	= 0x8004D00F;
var XACT_E_XTIONEXISTS               	= 0x8004D013;
var XACT_E_INDOUBT                   	= 0x8004D016;
var XACT_E_NOTIMEOUT                 	= 0x8004D017;
var XACT_E_ALREADYINPROGRESS         	= 0x8004D018;
var XACT_E_ABORTED                   	= 0x8004D019;
var XACT_E_LOGFULL                   	= 0x8004D01A;
var XACT_E_TMNOTAVAILABLE            	= 0x8004D01B;
var XACT_E_CONNECTION_DOWN           	= 0x8004D01C;
var XACT_E_CONNECTION_DENIED         	= 0x8004D01D;
var XACT_E_CONNECTION_REQUEST_DENIED 	= 0x8004D100;
%>
