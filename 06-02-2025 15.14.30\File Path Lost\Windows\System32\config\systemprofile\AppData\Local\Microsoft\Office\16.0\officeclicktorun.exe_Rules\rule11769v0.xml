<?xml version="1.0" encoding="utf-8"?>
<R Id="11769" V="0" DC="SM" EN="Office.Outlook.Desktop.PcxAndOsfHttpStatus" ATT="d807609276744245baf81bf7bc8033f6-2268e374-7766-4976-be44-b6ad5bddc5b6-7813" DCa="PSU" xmlns="">
  <S>
    <R T="1" R="11768" />
    <A T="2" E="TelemetryShutdown" />
    <TI T="3" I="Daily" />
    <F T="4">
      <O T="GE">
        <L>
          <S T="1" F="1" />
        </L>
        <R>
          <V V="1" T="U32" />
        </R>
      </O>
    </F>
    <F T="5">
      <O T="LE">
        <L>
          <S T="1" F="1" />
        </L>
        <R>
          <V V="0" T="U32" />
        </R>
      </O>
    </F>
    <F T="6">
      <O T="GE">
        <L>
          <S T="5" F="2" />
        </L>
        <R>
          <V V="1" T="U32" />
        </R>
      </O>
    </F>
    <F T="7">
      <O T="GE">
        <L>
          <S T="5" F="3" />
        </L>
        <R>
          <V V="1" T="U32" />
        </R>
      </O>
    </F>
    <F T="8">
      <O T="GE">
        <L>
          <S T="5" F="4" />
        </L>
        <R>
          <V V="1" T="U32" />
        </R>
      </O>
    </F>
    <F T="9">
      <O T="GE">
        <L>
          <S T="5" F="5" />
        </L>
        <R>
          <V V="1" T="U32" />
        </R>
      </O>
    </F>
    <F T="10">
      <O T="GE">
        <L>
          <S T="5" F="6" />
        </L>
        <R>
          <V V="1" T="U32" />
        </R>
      </O>
    </F>
  </S>
  <G>
    <S T="1">
      <F N="0" />
    </S>
    <S T="4">
      <F N="0" />
    </S>
    <S T="5">
      <F N="0" />
    </S>
    <S T="6">
      <F N="0" />
    </S>
    <S T="7">
      <F N="0" />
    </S>
    <S T="8">
      <F N="0" />
    </S>
    <S T="9">
      <F N="0" />
    </S>
    <S T="10">
      <F N="0" />
    </S>
  </G>
  <C T="W" I="0" O="false" N="Name">
    <S T="1" F="0" />
  </C>
  <C T="U32" I="1" O="false" N="CountOfSuccessfulRequests">
    <C>
      <S T="4" />
    </C>
  </C>
  <C T="U32" I="2" O="false" N="CountOfUnsuccessfulRequests">
    <C>
      <S T="5" />
    </C>
  </C>
  <C T="U32" I="3" O="false" N="CountOfUnsuccessfulWithUnauthorized">
    <C>
      <S T="6" />
    </C>
  </C>
  <C T="U32" I="4" O="false" N="CountOfUnsuccessfulWithForbidden">
    <C>
      <S T="7" />
    </C>
  </C>
  <C T="U32" I="5" O="false" N="CountOfUnsuccessfulWithNotFound">
    <C>
      <S T="8" />
    </C>
  </C>
  <C T="U32" I="6" O="false" N="CountOfUnsuccessfulWithClientErrors">
    <C>
      <S T="9" />
    </C>
  </C>
  <C T="U32" I="7" O="false" N="CountOfUnsuccessfulWithServerErrors">
    <C>
      <S T="10" />
    </C>
  </C>
  <T>
    <S T="2" />
    <S T="3" />
  </T>
</R>
