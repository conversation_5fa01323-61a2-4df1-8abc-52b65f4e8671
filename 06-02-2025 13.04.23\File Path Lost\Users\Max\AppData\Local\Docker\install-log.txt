-------------------------------------------------------------------------------->8
Version: 4.40.0 (187762)
Sha1: 
Started on: 2025/04/26 16:50:33.235
Resources: C:\Users\<USER>\Downloads\resources
OS: Windows 10 Pro
Edition: Professional
Id: 2009
Build: 19045
BuildLabName: 19041.1.amd64fre.vb_release.191206-1406
File: C:\Users\<USER>\AppData\Local\Docker\install-log.txt
CommandLine: "C:\Users\<USER>\Downloads\Docker Desktop Installer.exe" 
You can send feedback, including this log file, at https://github.com/docker/for-win/issues
[2025-04-26T16:50:33.782595100Z][ManifestAndExistingInstallationLoader][I] No install path specified, looking for default installation registry key
[2025-04-26T16:50:33.782595100Z][Installer][I] No installation found
[2025-04-26T16:50:33.798224700Z][ManifestAndExistingInstallationLoader][I] Not run as admin, relaunching with UAC prompt
