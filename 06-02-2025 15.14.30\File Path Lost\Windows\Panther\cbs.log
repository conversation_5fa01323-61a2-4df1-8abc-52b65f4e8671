﻿2024-09-09 08:44:51, Info                  DISM   PID=1596 TID=8 Scratch directory set to 'X:\WINDOWS\TEMP\'. - CDISMManager::put_ScratchDir
2024-09-09 08:44:51, Info                  DISM   PID=1596 TID=8 DismCore.dll version: 10.0.19041.746 - CDISMManager::FinalConstruct
2024-09-09 08:44:51, Info                  DISM   PID=1596 TID=8 Scratch directory set to 'W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3'. - CDISMManager::put_ScratchDir
2024-09-09 08:44:51, Info                  DISM   Initialized Panther logging at W:\$WINDOWS.~BT\Sources\Panther\cbs.log
2024-09-09 08:44:51, Info                  DISM   PID=1596 TID=8 Successfully loaded the ImageSession at "X:\Sources" - CDISMManager::LoadLocalImageSession
2024-09-09 08:44:51, Info                  DISM   Initialized Panther logging at W:\$WINDOWS.~BT\Sources\Panther\cbs.log
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   Initialized Panther logging at W:\$WINDOWS.~BT\Sources\Panther\cbs.log
2024-09-09 08:44:51, Info                  DISM   DISM Manager: PID=1596 TID=8 Successfully created the local image session and provider store. - CDISMManager::CreateLocalImageSession
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: 
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: <----- Starting Dism.exe session ----->
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: 
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Host machine information: OS Version=10.0.19041, Running architecture=amd64, Number of processors=8
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Dism.exe version: 10.0.19041.1949
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Executing command line: X:\Sources\dism.exe  /logpath:W:\$WINDOWS.~BT\Sources\Panther\cbs.log /scratchdir:W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3 /image:W:\ /is-serviceable
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Getting the collection of providers from a local provider store type. - CDISMProviderStore::GetProviderCollection
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Connecting to the provider located at X:\Sources\FolderProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Warning               DISM   DISM Provider Store: PID=1596 TID=8 Failed to load the provider: X:\Sources\SiloedPackageProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2024-09-09 08:44:51, Warning               DISM   DISM Provider Store: PID=1596 TID=8 Failed to load the provider: X:\Sources\FfuProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Connecting to the provider located at X:\Sources\WimProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Connecting to the provider located at X:\Sources\VHDProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Connecting to the provider located at X:\Sources\ImagingProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Warning               DISM   DISM Provider Store: PID=1596 TID=8 Failed to load the provider: X:\Sources\MetaDeployProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Got the collection of providers. Now enumerating them to build the command table.
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: DISM Log Provider
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: FolderManager
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: WimManager
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: WimManager.
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: VHDManager
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: GenericImagingManager
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: GenericImagingManager.
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Getting the collection of providers from a local provider store type. - CDISMProviderStore::GetProviderCollection
[1596] [0x80070002] FIOReadFileIntoBuffer:(1452): The system cannot find the file specified.
[1596] [0xc142011c] UnmarshallImageHandleFromDirectory:(641)
[1596] [0xc142011c] WIMGetMountedImageHandle:(2906)
2024-09-09 08:44:51, Info                  DISM   DISM WIM Provider: PID=1596 TID=8 [W:\] is not a WIM mount point. - CWimMountedImageInfo::Initialize
2024-09-09 08:44:51, Info                  DISM   DISM VHD Provider: PID=1596 TID=8 [W:\] is not recognized by the DISM VHD provider. - CVhdImage::Initialize
2024-09-09 08:44:51, Warning               DISM   DISM Provider Store: PID=1596 TID=8 Failed to retrieve the Provider Instance. - CDISMProviderStore::Internal_GetProvider(hr:0x80004005)
2024-09-09 08:44:51, Info                  DISM   DISM VHD Provider: PID=1596 TID=8 [W:\] is not recognized by the DISM VHD provider. - CVhdImage::Initialize
2024-09-09 08:44:51, Info                  DISM   DISM Imaging Provider: PID=1596 TID=8 The provider VHDManager does not support CreateDismImage on W:\ - CGenericImagingManager::CreateDismImage
[1596] [0x80070002] FIOReadFileIntoBuffer:(1452): The system cannot find the file specified.
[1596] [0xc142011c] UnmarshallImageHandleFromDirectory:(641)
[1596] [0xc142011c] WIMGetMountedImageHandle:(2906)
2024-09-09 08:44:51, Info                  DISM   DISM WIM Provider: PID=1596 TID=8 [W:\] is not a WIM mount point. - CWimMountedImageInfo::Initialize
2024-09-09 08:44:51, Info                  DISM   DISM Imaging Provider: PID=1596 TID=8 The provider WimManager does not support CreateDismImage on W:\ - CGenericImagingManager::CreateDismImage
2024-09-09 08:44:51, Info                  DISM   DISM Imaging Provider: PID=1596 TID=8 No imaging provider supported CreateDismImage for this path - CGenericImagingManager::CreateDismImage
2024-09-09 08:44:51, Info                  DISM   DISM Manager: PID=1596 TID=8 physical location path: W:\ - CDISMManager::CreateImageSession
2024-09-09 08:44:51, Info                  DISM   DISM Manager: PID=1596 TID=8 Event name for current DISM session is Global\__?_Volume{efaec0de-6242-4451-b502-894bbc580f88}__3798701321_65536_79426 - CDISMManager::CheckSessionAndLock
2024-09-09 08:44:51, Info                  DISM   DISM Manager: PID=1596 TID=8 Create session event 0x250 for current DISM session and event name is Global\__?_Volume{efaec0de-6242-4451-b502-894bbc580f88}__3798701321_65536_79426  - CDISMManager::CheckSessionAndLock
2024-09-09 08:44:51, Info                  DISM   DISM Manager: PID=1596 TID=8 Copying DISM from "W:\Windows\System32\Dism" - CDISMManager::CreateImageSessionFromLocation
2024-09-09 08:44:51, Info                  DISM   DISM Manager: PID=1596 TID=8 Successfully loaded the ImageSession at "W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232" - CDISMManager::LoadRemoteImageSession
2024-09-09 08:44:51, Info                  DISM   DISM Image Session: PID=1724 TID=312 Instantiating the Provider Store. - CDISMImageSession::get_ProviderStore
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Initializing a provider store for the IMAGE session type. - CDISMProviderStore::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\OSProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM OS Provider: PID=1724 TID=312 Defaulting SystemPath to W:\ - CDISMOSServiceManager::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   DISM OS Provider: PID=1724 TID=312 Defaulting Windows folder to W:\Windows - CDISMOSServiceManager::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Attempting to initialize the logger from the Image Session. - CDISMProviderStore::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\LogProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   Initialized Panther logging at W:\$WINDOWS.~BT\Sources\Panther\cbs.log
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Found and Initialized the DISM Logger. - CDISMProviderStore::Internal_InitializeLogger
2024-09-09 08:44:51, Warning               DISM   DISM Provider Store: PID=1724 TID=312 Failed to load the provider: W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\PEProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Failed to get and initialize the PE Provider.  Continuing by assuming that it is not a WinPE image. - CDISMProviderStore::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finished initializing the Provider Map. - CDISMProviderStore::Final_OnConnect
2024-09-09 08:44:51, Info                  DISM   Initialized Panther logging at W:\$WINDOWS.~BT\Sources\Panther\cbs.log
2024-09-09 08:44:51, Info                  DISM   Initialized Panther logging at W:\$WINDOWS.~BT\Sources\Panther\cbs.log
2024-09-09 08:44:51, Info                  DISM   DISM Manager: PID=1596 TID=8 Image session successfully loaded from the temporary location: W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232 - CDISMManager::CreateImageSession
2024-09-09 08:44:51, Info                  DISM   DISM OS Provider: PID=1724 TID=312 Setting SystemPath to W:\ - CDISMOSServiceManager::SetSystemPath
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Target image information: OS Version=10.0.19045.2006, Image architecture=amd64
2024-09-09 08:44:51, Info                  DISM   DISM.EXE: Image session version: 10.0.19041.746
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Getting the collection of providers from an image provider store type. - CDISMProviderStore::GetProviderCollection
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\CbsProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  CSI    00000001 Shim considered [l:126]'\??\W:\Windows\Servicing\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_OBJECT_PATH_NOT_FOUND
2024-09-09 08:44:51, Info                  CSI    00000002 Shim considered [l:123]'\??\W:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_SUCCESS
2024-09-09 08:44:51, Info                  DISM   DISM OS Provider: PID=1724 TID=312 Determined System directory to be W:\Windows\System32 - CDISMOSServiceManager::get_SystemDirectory
2024-09-09 08:44:51, Info                  DISM   DISM Package Manager: PID=1724 TID=312 Finished initializing the CbsConUI Handler. - CCbsConUIHandler::Initialize
2024-09-09 08:44:51, Info                  CSI    00000001 Shim considered [l:126]'\??\W:\Windows\Servicing\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_OBJECT_PATH_NOT_FOUND
2024-09-09 08:44:51, Info                  CSI    00000002 Shim considered [l:123]'\??\W:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_SUCCESS
2024-09-09 08:44:51, Info                  CSI    00000001 Shim considered [l:126]'\??\W:\Windows\Servicing\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_OBJECT_PATH_NOT_FOUND
2024-09-09 08:44:51, Info                  CSI    00000002 Shim considered [l:123]'\??\W:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_SUCCESS
2024-09-09 08:44:51, Info                  CBS    Universal Time is: 2024-09-09 16:44:51.974
2024-09-09 08:44:51, Info                  CBS    Failed to find a matching version for servicing stack: W:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\ [HRESULT = 0x80070490 - ERROR_NOT_FOUND]
2024-09-09 08:44:51, Info                  CBS    Failed to find servicing stack directory in online store. [HRESULT = 0x80070490 - ERROR_NOT_FOUND]
2024-09-09 08:44:51, Info                  CBS    Offline servicing, using stack version from: W:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\cbscore.dll
2024-09-09 08:44:51, Info                  CBS    Loaded Servicing Stack v10.0.19041.1940 with Core: W:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\cbscore.dll
2024-09-09 08:44:51, Info                  CBS    Setting core mode: CbsCoreModeOffline
2024-09-09 08:44:51, Info                  CSI    00000001@2024/9/9:16:44:51.974 WcpInitialize: wcp.dll version 10.0.19041.1940 (WinBuild.160101.0800)
2024-09-09 08:44:51, Info                  CBS    Lock: New lock added: CCbsSessionManager, level: 11, total lock:9
2024-09-09 08:44:51, Info                  CBS    Lock: New lock added: CSIInventoryCriticalSection, level: 64, total lock:10
2024-09-09 08:44:51, Info                  DISM   DISM Package Manager: PID=1724 TID=312 Loaded servicing stack for offline use only. - CDISMPackageManager::CreateCbsSession
2024-09-09 08:44:51, Info                  CBS    Lazy store initialization mode
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\MsiProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\IntlProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\IBSProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\DmiProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:51, Info                  CSI    00000001 Shim considered [l:126]'\??\W:\Windows\Servicing\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_OBJECT_PATH_NOT_FOUND
2024-09-09 08:44:51, Info                  CSI    00000002 Shim considered [l:123]'\??\W:\Windows\WinSxS\amd64_microsoft-windows-servicingstack_31bf3856ad364e35_10.0.19041.1940_none_7dd80d767cb5c7b0\wcp.dll' : got STATUS_SUCCESS
2024-09-09 08:44:52, Info                  DISM   DISM Driver Manager: PID=1724 TID=312 Further logs for driver related operations can be found in the target operating system at %WINDIR%\inf\setupapi.offline.log - CDriverManager::Initialize
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\UnattendProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\SmiProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Warning               DISM   DISM Provider Store: PID=1724 TID=312 Failed to load the provider: W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\EmbeddedProvider.dll. - CDISMProviderStore::Internal_GetProvider(hr:0x8007007e)
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\AppxProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\ProvProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\AssocProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\GenericProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\OfflineSetupProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\SysprepProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\TransmogProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Transmog Provider: PID=1724 TID=312 Current image session is [OFFLINE] - CTransmogManager::GetMode
2024-09-09 08:44:52, Info                  DISM   DISM Transmog Provider: PID=1724 TID=312 Determined WinDir path = [W:\Windows] - CTransmogManager::GetWinDirPath
2024-09-09 08:44:52, Info                  DISM   DISM Transmog Provider: PID=1724 TID=312 GetProductType: ProductType = [WinNT] - CTransmogManager::GetProductType
2024-09-09 08:44:52, Info                  DISM   DISM Transmog Provider: PID=1724 TID=312 Product Type: [WinNT] - CTransmogManager::Initialize
2024-09-09 08:44:52, Info                  DISM   DISM Transmog Provider: PID=1724 TID=312 Product Type ServerNT : [No] - CTransmogManager::Initialize
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Connecting to the provider located at W:\$WINDOWS.~LS\PackageTemp\229a20f0-be57-4cb5-af5f-19598a8226d3\3874F5B9-47D1-4502-930D-B8BFA639A232\SetupPlatformProvider.dll. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Encountered a servicing provider, performing additional servicing initializations. - CDISMProviderStore::Internal_LoadProvider
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Got the collection of providers. Now enumerating them to build the command table.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: DISM Log Provider
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: OSServices
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: DISM Package Manager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: DISM Package Manager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: MsiManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: MsiManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: IntlManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: IntlManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: IBSManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: DriverManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: DriverManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: DISM Unattend Manager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: DISM Unattend Manager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: SmiManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: AppxManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: AppxManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: ProvManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: ProvManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: AssocManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: AssocManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: GenericManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: GenericManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: OfflineSetupManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: OfflineSetupManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: SysprepManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: SysprepManager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: Edition Manager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: Edition Manager.
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Attempting to add the commands from provider: SetupPlatformManager
2024-09-09 08:44:52, Info                  DISM   DISM.EXE: Succesfully registered commands for the provider: SetupPlatformManager.
2024-09-09 08:44:52, Info                  DISM   DISM Package Manager: PID=1724 TID=312 Processing the top level command token(is-serviceable). - CPackageManagerCLIHandler::Private_ValidateCmdLine
2024-09-09 08:44:52, Info                  DISM   DISM Package Manager: PID=1724 TID=312 Attempting to route to appropriate command handler. - CPackageManagerCLIHandler::ExecuteCmdLine
2024-09-09 08:44:52, Info                  DISM   DISM Package Manager: PID=1724 TID=312 Routing the command... - CPackageManagerCLIHandler::ExecuteCmdLine
2024-09-09 08:44:52, Info                  CBS    Lazy store initialization mode, fully initialize.
2024-09-09 08:44:52, Info                  CBS    Offline image is writable
2024-09-09 08:44:52, Info                  CBS    Loading offline registry hive: SOFTWARE, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SOFTWARE' from path '\\?\W:\Windows\System32\config\SOFTWARE'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: SYSTEM, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SYSTEM' from path '\\?\W:\Windows\System32\config\SYSTEM'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: SECURITY, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SECURITY' from path '\\?\W:\Windows\System32\config\SECURITY'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: SAM, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SAM' from path '\\?\W:\Windows\System32\config\SAM'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: COMPONENTS, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/COMPONENTS' from path '\\?\W:\Windows\System32\config\COMPONENTS'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: DRIVERS, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/DRIVERS' from path '\\?\W:\Windows\System32\config\DRIVERS'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: DEFAULT, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/DEFAULT' from path '\\?\W:\Windows\System32\config\DEFAULT'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: ntuser.dat, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Users/<USER>/ntuser.dat' from path '\\?\W:\Users\Default\ntuser.dat'.
2024-09-09 08:44:53, Info                  CBS    Loading offline registry hive: schema.dat, into registry key '{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/smi/store/Machine/schema.dat' from path '\\?\W:\Windows\system32\smi\store\Machine\schema.dat'.
2024-09-09 08:44:54, Info                  CSI    00000002 CSI Store 2172612201936 initialized
2024-09-09 08:44:54, Info                  CBS    Build: 19041.1.amd64fre.vb_release.191206-1406
2024-09-09 08:44:54, Info                  CBS    Session: 1724_950703 initialized by client DISM Package Manager Provider, external staging directory: (null), external registry directory: (null)
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Found the PE Provider.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(DISM Package Manager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SOFTWARE
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SYSTEM
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SECURITY
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/SAM
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/COMPONENTS
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/DRIVERS
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/System32/config/DEFAULT
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Users/<USER>/ntuser.dat
2024-09-09 08:44:54, Info                  CBS    Force unloading offline registry hive: {bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/smi/store/Machine/schema.dat
2024-09-09 08:44:54, Info                  CSI    00000003 Direct SIL provider: Number of files opened: 4.
2024-09-09 08:44:54, Info                  DISM   DISM Package Manager: PID=1724 TID=312 Finalizing CBS core. - CDISMPackageManager::Finalize
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: ExecutionEngineFinalize
2024-09-09 08:44:54, Info                  CBS    Execution Engine Finalize
2024-09-09 08:44:54, Info                  CBS    Execution Engine Finalize
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: ComponentAnalyzerFinalize
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: PackageTrackerFinalize
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: CoreResourcesUnload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: SessionManagerFinalize
2024-09-09 08:44:54, Info                  CBS    Lock: Lock removed: CSIInventoryCriticalSection, level: 64, total lock:10
2024-09-09 08:44:54, Info                  CBS    Lock: Lock removed: CCbsSessionManager, level: 11, total lock:9
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: CapabilityManagerFinalize
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: GetPublicObjectMonitor::Audit
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: PublicObjectMonitorFinalize
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: Enter vCoreInitializeLock
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: WcpUnload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: DrupUnload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: CfgMgr32Unload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: DpxUnload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: SrUnload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: CbsEsdUnload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: CbsTraceInfoUninitialize
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: CbsEventUnregister
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: AppContainerUnload
2024-09-09 08:44:54, Info                  CBS    CbsCoreFinalize: WdsUnload, logging from cbscore will end.
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: DISM Package Manager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(MsiManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: MsiManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(IntlManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: IntlManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(IBSManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: IBSManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(DriverManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: DriverManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(DISM Unattend Manager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: DISM Unattend Manager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(SmiManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: SmiManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(AppxManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: AppxManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(ProvManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: ProvManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(AssocManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: AssocManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(GenericManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: GenericManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(OfflineSetupManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: OfflineSetupManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(SysprepManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: SysprepManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(Edition Manager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: Edition Manager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Finalizing the servicing provider(SetupPlatformManager) - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: SetupPlatformManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Releasing the local reference to OSServices. - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Disconnecting Provider: OSServices - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/config/SOFTWARE. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/config/SYSTEM. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/config/SECURITY. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/config/SAM. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/config/DEFAULT. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Users/<USER>/NTUSER.DAT. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/config/COMPONENTS. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   Offline Registry: PID=1724 TID=312 Hive is not mounted at HKLM\{bf1a281b-ad7b-4476-ac95-f47682990ce7}W:/Windows/system32/config/DRIVERS. - CRegistryMapping::Init
2024-09-09 08:44:54, Info                  DISM   DISM OS Provider: PID=1724 TID=312 Successfully unloaded all registry hives. - CDISMOSServiceManager::Final_OnDisconnect
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1724 TID=312 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Manager: PID=1596 TID=8 Closing session event handle 0x250 - CDISMManager::CleanupImageSessionEntry
2024-09-09 08:44:54, Info                  DISM   DISM.EXE: Image session has been closed. Reboot required=no.
2024-09-09 08:44:54, Info                  DISM   DISM.EXE: 
2024-09-09 08:44:54, Info                  DISM   DISM.EXE: <----- Ending Dism.exe session ----->
2024-09-09 08:44:54, Info                  DISM   DISM.EXE: 
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Found the OSServices.  Waiting to finalize it until all other providers are unloaded. - CDISMProviderStore::Final_OnDisconnect
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Disconnecting Provider: FolderManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Disconnecting Provider: WimManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Disconnecting Provider: VHDManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Disconnecting Provider: GenericImagingManager - CDISMProviderStore::Internal_DisconnectProvider
2024-09-09 08:44:54, Info                  DISM   DISM Provider Store: PID=1596 TID=8 Releasing the local reference to DISMLogger.  Stop logging. - CDISMProviderStore::Internal_DisconnectProvider
