<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007" xmlns:resource="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>BluetoothDiagnostic</ID>
    <Version>1.0</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>https://go.microsoft.com/fwlink/?LinkId=534597</PrivacyLink>
  <PowerShellVersion>2.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.0</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>true</RequiresElevation>
      <RequiresInteractivity>true</RequiresInteractivity>
      <FileName>TS_Main.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_CheckBT</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-3</Name>
        <Description>@diagpackage.dll,-4</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_CheckBT</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-5</Name>
            <Description>@diagpackage.dll,-6</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_CheckBT.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
        <ID>RC_PendingRestart</ID>
        <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-2002</Name>
            <Description></Description>
        </DisplayInformation>
        <Resolvers>
            <Resolver>
                <ID>RS_PendingRestart</ID>
                <DisplayInformation>
                    <Parameters/>
                    <Name>@diagpackage.dll,-2003</Name>
                    <Description></Description>
                </DisplayInformation>
                <RequiresConsent>true</RequiresConsent>
                <Script>
                    <Parameters/>
                    <ProcessArchitecture>Any</ProcessArchitecture>
                    <RequiresElevation>true</RequiresElevation>
                    <RequiresInteractivity>true</RequiresInteractivity>
                    <FileName>RS_PendingRestart.ps1</FileName>
                    <ExtensionPoint/>
                </Script>
                <ExtensionPoint/>
            </Resolver>
        </Resolvers>
        <Verifier/>
        <ContextParameters/>
        <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_Disabled</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>ProblemDeviceID</Name>
            <DefaultValue/>
          </Parameter>
          <Parameter>
            <Name>ProblemDeviceName</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-11</Name>
        <Description>@diagpackage.dll,-12</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Disabled</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>ProblemDeviceID</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>ProblemDeviceName</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
              <Name>@diagpackage.dll,-13</Name>
            <Description>@diagpackage.dll,-14</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>ProblemDeviceID</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>ProblemDeviceName</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_Disabled.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>ProblemDeviceID</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>ProblemDeviceName</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Action</Name>
              <DefaultValue>Verify</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>RC_Disabled.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_DriverProblem</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>ProblemDeviceID</Name>
            <DefaultValue/>
          </Parameter>
          <Parameter>
            <Name>ProblemDeviceName</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-15</Name>
        <Description>@diagpackage.dll,-16</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_DriverProblem</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>ProblemDeviceID</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>ProblemDeviceName</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-17</Name>
            <Description>@diagpackage.dll,-18</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>ProblemDeviceID</Name>
                <DefaultValue/>
              </Parameter>
              <Parameter>
                <Name>ProblemDeviceName</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_DriverProblem.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
            <Parameter>
              <Name>ProblemDeviceID</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>ProblemDeviceName</Name>
              <DefaultValue/>
            </Parameter>
            <Parameter>
              <Name>Action</Name>
              <DefaultValue>Verify</DefaultValue>
            </Parameter>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>RC_DriverProblem.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_OtherIssue</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>ProblemDeviceName</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
          <Parameter>
            <Name>ErrorCode</Name>
            <DefaultValue>-</DefaultValue>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-19</Name>
        <Description>@diagpackage.dll,-20</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_OtherIssue</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>ProblemDeviceName</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>ErrorCode</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-21</Name>
            <Description>@diagpackage.dll,-22</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>ProblemDeviceName</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
              <Parameter>
                <Name>ErrorCode</Name>
                <DefaultValue>-</DefaultValue>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_OtherIssue.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_BTRadioOff</ID>
      <DisplayInformation>
        <Parameters>
          <Parameter>
            <Name>BluetoothRadioState</Name>
            <DefaultValue/>
          </Parameter>
        </Parameters>
        <Name>@diagpackage.dll,-7</Name>
        <Description>@diagpackage.dll,-8</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_BTRadioOff</ID>
          <DisplayInformation>
            <Parameters>
              <Parameter>
                <Name>BluetoothRadioState</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <Name>@diagpackage.dll,-9</Name>
            <Description>@diagpackage.dll,-10</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters>
              <Parameter>
                <Name>BluetoothRadioState</Name>
                <DefaultValue/>
              </Parameter>
            </Parameters>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_BTRadioOff.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters>
          </Parameters>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>true</RequiresElevation>
          <RequiresInteractivity>true</RequiresInteractivity>
          <FileName>VF_BTRadioOff.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
    <SingleResponseInteractions>
    </SingleResponseInteractions>
    <MultipleResponseInteractions/>
    <TextInteractions>
    </TextInteractions>
    <PauseInteractions>
      <PauseInteraction>
        <ID>INT_OEM</ID>
        <DisplayInformation>
          <Parameters>
            <Parameter>
              <Name>ProblemDeviceName</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
            <Parameter>
              <Name>ErrorCode</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
          </Parameters>
          <Name>@diagpackage.dll,-23</Name>
          <Description>@diagpackage.dll,-24</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>INT_NotSupport</ID>
        <DisplayInformation>
          <Parameters>
          </Parameters>
          <Name>@diagpackage.dll,-25</Name>
          <Description>@diagpackage.dll,-26</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
        </ExtensionPoint>
      </PauseInteraction>
      <PauseInteraction>
        <ID>INT_PendingReboot</ID>
        <DisplayInformation>
            <Parameters>
            </Parameters>
            <Name>@diagpackage.dll,-30</Name>
            <Description>@diagpackage.dll,-31</Description>
        </DisplayInformation>
        <ContextParameters>
        </ContextParameters>
        <ExtensionPoint>
            <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
    </PauseInteraction>
    <PauseInteraction>
        <ID>INT_RebootRelaunchTS</ID>
        <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-32</Name>
            <Description>@diagpackage.dll,-33</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
            <InteractionIcon>warning</InteractionIcon>
        </ExtensionPoint>
    </PauseInteraction>
    </PauseInteractions>
    <LaunchUIInteractions>
      <LaunchUIInteraction>
        <Parameters/>
        <CommandLine>control.exe /name Microsoft.WindowsUpdate</CommandLine>
        <ID>INT_UpdateDriver</ID>
        <DisplayInformation>
          <Parameters>
            <Parameter>
              <Name>ProblemDeviceName</Name>
              <DefaultValue>-</DefaultValue>
            </Parameter>
          </Parameters>
          <Name>@diagpackage.dll,-27</Name>
          <Description>@diagpackage.dll,-28</Description>
        </DisplayInformation>
        <ContextParameters/>
        <ExtensionPoint>
          <ButtonText>@diagpackage.dll,-29</ButtonText>
        </ExtensionPoint>
      </LaunchUIInteraction>
    </LaunchUIInteractions>
  </Interactions>
  <ExtensionPoint>
    <Icon>@%windir%\system32\bthprops.cpl,-151</Icon>
    <HelpKeywords>@DiagPackage.dll,-100</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-101</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-102</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-103</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-104</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-105</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-106</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-107</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-108</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-109</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-110</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-111</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-112</HelpKeywords>
    <Feedback>
      <ContextId>4</ContextId>
    </Feedback>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>