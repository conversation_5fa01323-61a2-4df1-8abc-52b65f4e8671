<?xml version="1.0" encoding="UTF-8"?>
<libraryDescription xmlns="http://schemas.microsoft.com/windows/2009/library">
  <!-- IDS_RECORDEDTVLIBRARY -->
  <name>@shell32.dll,-34615</name>
  <!-- SHIDI_LIBRARYRECORDEDTV -->
  <iconReference>imageres.dll,-1008</iconReference>
  <isLibraryPinned>true</isLibraryPinned>
  <templateInfo>
    <!-- FOLDERTYPEID_Videos -->
    <folderType>{5fa96407-7e77-483c-ac93-691d05850de8}</folderType>
  </templateInfo>
  <searchConnectorDescriptionList>
    <searchConnectorDescription publisher="Microsoft" product="Windows">
      <!-- IDS_RECORDEDTVLOCATIONDESCRIPTION -->
      <description>@shell32.dll,-34617</description>
      <isDefaultSaveLocation>true</isDefaultSaveLocation>
      <isDefaultNonOwnerSaveLocation>true</isDefaultNonOwnerSaveLocation>
      <simpleLocation>
        <url>shell:public\Recorded TV</url>
      </simpleLocation>
    </searchConnectorDescription>
  </searchConnectorDescriptionList>
</libraryDescription>
