<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
    <DiagnosticIdentification>
        <ID>WindowsMediaPlayerConfigurationDiagnostic</ID>
        <Version>1.1</Version>
    </DiagnosticIdentification>
    <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-1</Name>
        <Description>@diagpackage.dll,-2</Description>
    </DisplayInformation>
    <PrivacyLink>http://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
    <PowerShellVersion>1.0</PowerShellVersion>
    <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
    <Troubleshooter>
        <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>TS_WindowsMediaPlayer.ps1</FileName>
            <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
    </Troubleshooter>
    <Rootcauses>
        <Rootcause>
            <ID>RC_ConfigurationErrors</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-3</Name>
                <Description>@diagpackage.dll,-4</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_ConfigurationErrors</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-5</Name>
                        <Description>@diagpackage.dll,-6</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script>
                        <Parameters/>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>false</RequiresElevation>
                        <RequiresInteractivity>true</RequiresInteractivity>
                        <FileName>RS_ConfigurationErrors.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_NetworkCacheCorrupted</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-22</Name>
                <Description>@diagpackage.dll,-23</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_NetworkCacheCorrupted</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-24</Name>
                        <Description>@diagpackage.dll,-25</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script>
                        <Parameters/>
                        <ProcessArchitecture>Any</ProcessArchitecture>
                        <RequiresElevation>false</RequiresElevation>
                        <RequiresInteractivity>false</RequiresInteractivity>
                        <FileName>RS_NetworkCacheCorrupted.ps1</FileName>
                        <ExtensionPoint/>
                    </Script>
                    <ExtensionPoint/>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
       <Rootcause>
            <ID>RC_WMPUnavailable</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-30</Name>
                <Description>@diagpackage.dll,-31</Description>
            </DisplayInformation>
            <Resolvers>
                <Resolver>
                    <ID>RS_InstallWMP</ID>
                    <DisplayInformation>
                        <Parameters/>
                        <Name>@diagpackage.dll,-32</Name>
                        <Description>@diagpackage.dll,-33</Description>
                    </DisplayInformation>
                    <RequiresConsent>false</RequiresConsent>
                    <Script/>
                    <ExtensionPoint>
                        <Link>http://www.microsoft.com/windows/windowsmedia/player/</Link>
                        <LinkText>@diagpackage.dll,-14</LinkText>
                    </ExtensionPoint>
                </Resolver>
            </Resolvers>
            <Verifier/>
            <ContextParameters/>
            <ExtensionPoint/>
        </Rootcause>
     </Rootcauses>
  <Interactions>
      <SingleResponseInteractions>
        <SingleResponseInteraction>
          <AllowDynamicResponses>false</AllowDynamicResponses>
          <Choices>
            <Choice>
              <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-34</Name>
                <Description/>
              </DisplayInformation>
              <Value>Yes</Value>
              <ExtensionPoint/>
            </Choice>
            <Choice>
              <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-35</Name>
                <Description>@diagpackage.dll,-36</Description>
              </DisplayInformation>
              <Value>No</Value>
              <ExtensionPoint/>
            </Choice>
          </Choices>
          <ID>IT_ResetConfiguration</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-37</Name>
            <Description>@diagpackage.dll,-38</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <CommandLinks/>
            <Link>http://go.microsoft.com/fwlink/?linkid=120722</Link>
            <LinkText>@diagpackage.dll,-55</LinkText>
          </ExtensionPoint>
        </SingleResponseInteraction>
      </SingleResponseInteractions>
      <MultipleResponseInteractions/>
      <TextInteractions/>
      <PauseInteractions>
        <PauseInteraction>
          <ID>IT_WMPRuning</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-63</Name>
            <Description>@diagpackage.dll,-64</Description>
          </DisplayInformation>
          <ContextParameters/>
          <ExtensionPoint>
            <RTFDescription>@diagpackage.dll,-67</RTFDescription>
          </ExtensionPoint>
        </PauseInteraction>
      </PauseInteractions>
      <LaunchUIInteractions/>
  </Interactions>
    <ExtensionPoint>
      <Maintenance/>
        <Icon>@DiagPackage.dll,-1001</Icon>
        <HelpKeywords>@DiagPackage.dll,-203</HelpKeywords>
        <HelpKeywords>@DiagPackage.dll,-204</HelpKeywords>
        <HelpKeywords>@DiagPackage.dll,-206</HelpKeywords>
    </ExtensionPoint>
</dcmPS:DiagnosticPackage>