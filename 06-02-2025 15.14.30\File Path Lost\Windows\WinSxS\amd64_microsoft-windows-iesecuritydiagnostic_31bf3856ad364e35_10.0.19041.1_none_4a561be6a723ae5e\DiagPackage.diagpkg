<dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
  <DiagnosticIdentification>
    <ID>IESecurityDiagnostic</ID>
    <Version>1.1</Version>
  </DiagnosticIdentification>
  <DisplayInformation>
    <Parameters/>
    <Name>@diagpackage.dll,-1</Name>
    <Description>@diagpackage.dll,-2</Description>
  </DisplayInformation>
  <PrivacyLink>http://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
  <PowerShellVersion>1.0</PowerShellVersion>
  <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
  <Troubleshooter>
    <Script>
      <Parameters/>
      <ProcessArchitecture>Any</ProcessArchitecture>
      <RequiresElevation>false</RequiresElevation>
      <RequiresInteractivity>false</RequiresInteractivity>
      <FileName>IESecurity_TroubleShooter.ps1</FileName>
      <ExtensionPoint/>
    </Script>
    <ExtensionPoint/>
  </Troubleshooter>
  <Rootcauses>
    <Rootcause>
      <ID>RC_Phishingfilter</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-16</Name>
        <Description>@diagpackage.dll,-17</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Phishingfilter</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-18</Name>
            <Description>@diagpackage.dll,-19</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>RS_PhishingFilter.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_PhishingFilter.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_Popupblocker</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-20</Name>
        <Description>@diagpackage.dll,-21</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_Blockpopups</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-22</Name>
            <Description>@diagpackage.dll,-23</Description>
          </DisplayInformation>
          <RequiresConsent>false</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_Blockpopups.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_Blockpopups.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
    <Rootcause>
      <ID>RC_IEsecuritylevels</ID>
      <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-32</Name>
        <Description>@diagpackage.dll,-33</Description>
      </DisplayInformation>
      <Resolvers>
        <Resolver>
          <ID>RS_IEsecuritylevels</ID>
          <DisplayInformation>
            <Parameters/>
            <Name>@diagpackage.dll,-34</Name>
            <Description>@diagpackage.dll,-35</Description>
          </DisplayInformation>
          <RequiresConsent>true</RequiresConsent>
          <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>true</RequiresElevation>
            <RequiresInteractivity>false</RequiresInteractivity>
            <FileName>RS_IEsecuritylevels.ps1</FileName>
            <ExtensionPoint/>
          </Script>
          <ExtensionPoint/>
        </Resolver>
      </Resolvers>
      <Verifier>
        <Script>
          <Parameters/>
          <ProcessArchitecture>Any</ProcessArchitecture>
          <RequiresElevation>false</RequiresElevation>
          <RequiresInteractivity>false</RequiresInteractivity>
          <FileName>TS_IEsecuritylevels.ps1</FileName>
          <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
      </Verifier>
      <ContextParameters/>
      <ExtensionPoint/>
    </Rootcause>
  </Rootcauses>
  <Interactions>
      <SingleResponseInteractions>
          <SingleResponseInteraction>
                <AllowDynamicResponses>false</AllowDynamicResponses>
                <Choices>
                    <Choice>
                        <DisplayInformation>
                            <Parameters/>
                            <Name>@diagpackage.dll,-50</Name>
                            <Description/>
                        </DisplayInformation>
                        <Value>Yes</Value>
                        <ExtensionPoint/>
                    </Choice>
                    <Choice>
                        <DisplayInformation>
                            <Parameters/>
                            <Name>@diagpackage.dll,-51</Name>
                            <Description>@diagpackage.dll,-52</Description>
                        </DisplayInformation>
                        <Value>No</Value>
                        <ExtensionPoint/>
                    </Choice>
                </Choices>
                <ID>IT_TurnOnPhishingFilter</ID>
                <DisplayInformation>
                    <Parameters/>
                    <Name>@diagpackage.dll,-53</Name>
                    <Description>@diagpackage.dll,-54</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint>
                    <CommandLinks/>
              	    <Link>http://go.microsoft.com/fwlink/?LinkId=219555</Link>
                    <LinkText>@diagpackage.dll,-55</LinkText>
                </ExtensionPoint>
            </SingleResponseInteraction>
      </SingleResponseInteractions>
      <MultipleResponseInteractions/>
      <TextInteractions/>
      <PauseInteractions/>
      <LaunchUIInteractions/>
  </Interactions>
  <ExtensionPoint>
    <Maintenance/>
    <Icon>@DiagPackage.dll,-1001</Icon>
    <HelpKeywords>@DiagPackage.dll,-109</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-118</HelpKeywords>
    <HelpKeywords>@DiagPackage.dll,-110</HelpKeywords>
  </ExtensionPoint>
</dcmPS:DiagnosticPackage>