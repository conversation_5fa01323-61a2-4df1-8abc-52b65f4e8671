<?xml version="1.0" encoding="utf-8"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v3" manifestVersion="1.0" description="Fix for ServicingStack 10.0.19041.5676" displayName="Servicing Stack 10.0.19041.5676" company="Microsoft Corporation" copyright="Microsoft Corporation" supportInformation="" creationTimeStamp="2025-03-12T05:32:03Z" lastUpdateTimeStamp="2025-03-12T05:32:03Z">
  <assemblyIdentity name="Package_for_ServicingStack_5676" version="19041.5676.1.3" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
  <package identifier="KB5054682" applicabilityEvaluation="deep" releaseType="Update" restart="possible" selfUpdate="true" permanence="permanent" psfName="SSU-19041.5676-x64.psf">
    <parent buildCompare="EQ" integrate="standalone" disposition="detect">
      <assemblyIdentity name="Microsoft-Windows-CoreCountrySpecificEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-CoreNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseGEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-EnterpriseSNEvalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-PPIProEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-ProfessionalNEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-UtilityVMEdition" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
      <assemblyIdentity name="Microsoft-Windows-WinPE-Package" version="10.0.19041.1" processorArchitecture="amd64" language="neutral" buildType="release" publicKeyToken="31bf3856ad364e35" />
    </parent>
    <installerAssembly name="Microsoft-Windows-ServicingStack" version="6.0.0.0" language="neutral" processorArchitecture="amd64" versionScope="nonSxS" publicKeyToken="31bf3856ad364e35" />
    <update name="Wrapper-07B4FF929A0F2EE7131890C6E3304DC0E698D42F70068BCEEC6422C457E588F9_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-07B4FF929A0F2EE7131890C6E3304DC0E698D42F70068BCEEC6422C457E588F9" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-31517817A85B4EFF169B9551B4BA478077262B8E066FF71835127F43D9E8FFA3_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-31517817A85B4EFF169B9551B4BA478077262B8E066FF71835127F43D9E8FFA3" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-49E35155AC47E595BDA6909FD6483CAD399EA8AD9B0986F4ABF650ED97C8D657_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-49E35155AC47E595BDA6909FD6483CAD399EA8AD9B0986F4ABF650ED97C8D657" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-5C78BE11C9A01E3798C26FD0B35EA4A1360C07A520B0EA52C0FEC78A39E4C9B6_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-5C78BE11C9A01E3798C26FD0B35EA4A1360C07A520B0EA52C0FEC78A39E4C9B6" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-61151E6F8ACEF19BEFB04B99A5CEADB1A2453C6546EBC43CB7E3ECFA87E4C621_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-61151E6F8ACEF19BEFB04B99A5CEADB1A2453C6546EBC43CB7E3ECFA87E4C621" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-87CCF0E83B482D1694F76ECE79F9E4FC381D073F1C759D4B33E937B2D6C02F1A_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-87CCF0E83B482D1694F76ECE79F9E4FC381D073F1C759D4B33E937B2D6C02F1A" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-A0489A2D3F8093C7BF8106EA31DC9B6438D970FA79190A2347FDD56C63DAAA20_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-A0489A2D3F8093C7BF8106EA31DC9B6438D970FA79190A2347FDD56C63DAAA20" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-BF9998228C2FC0F1645F0E069E6FCC0FCB095C20E6E4FFD2E739EE7AFAE0824A_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-BF9998228C2FC0F1645F0E069E6FCC0FCB095C20E6E4FFD2E739EE7AFAE0824A" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-C4AE5B5E1DCB2D8C75C99F7D738ED8F7E7EC12F5EFD14B96BD66447E639DC2B8_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-C4AE5B5E1DCB2D8C75C99F7D738ED8F7E7EC12F5EFD14B96BD66447E639DC2B8" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-C7D56E726742306D8FE543E974196F38FD649FF23C4D271EFA8175BCFBE19CA3_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-C7D56E726742306D8FE543E974196F38FD649FF23C4D271EFA8175BCFBE19CA3" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-DF3875977D2566FFF9623DF3765BFB670EFA43FB0C834FEBB8D0EDC4CE401943_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-DF3875977D2566FFF9623DF3765BFB670EFA43FB0C834FEBB8D0EDC4CE401943" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-EA74A17BB97DA6228B246E2BF0F304F57ACACADFE47A379C678A58677070F64A_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-EA74A17BB97DA6228B246E2BF0F304F57ACACADFE47A379C678A58677070F64A" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-F1927E1F8B0C53503803D14442E1592AD064B60D40C6056CF6239F6C5630DB2B_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-F1927E1F8B0C53503803D14442E1592AD064B60D40C6056CF6239F6C5630DB2B" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <update name="Wrapper-F41154201C5AF100199EEAE180DB01EC5A5E6A736D4A847991CDCEA3598726AB_amd64">
      <package integrate="hidden">
        <assemblyIdentity name="Wrapper-F41154201C5AF100199EEAE180DB01EC5A5E6A736D4A847991CDCEA3598726AB" version="10.0.19041.5676" processorArchitecture="amd64" language="neutral" publicKeyToken="31bf3856ad364e35" />
      </package>
    </update>
    <mum2:packageExtended xmlns:mum2="urn:schemas-microsoft-com:asm.v3" completelyOfflineCapable="undetermined" packageSize="56745439" />
  </package>
</assembly>
