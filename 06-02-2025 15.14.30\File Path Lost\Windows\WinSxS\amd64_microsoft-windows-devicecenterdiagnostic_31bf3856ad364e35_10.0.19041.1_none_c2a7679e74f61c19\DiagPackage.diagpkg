<?xml version="1.0" encoding="utf-8"?><dcmPS:DiagnosticPackage SchemaVersion="1.0" Localized="true" xmlns:dcmPS="http://www.microsoft.com/schemas/dcm/package/2007" xmlns:dcmRS="http://www.microsoft.com/schemas/dcm/resource/2007">
    <DiagnosticIdentification>
        <ID>DeviceCenterDiagnostic</ID>
        <Version>3.0</Version>
    </DiagnosticIdentification>
    <DisplayInformation>
        <Parameters/>
        <Name>@diagpackage.dll,-1</Name>
        <Description>@diagpackage.dll,-2</Description>
    </DisplayInformation>
    <PrivacyLink>https://go.microsoft.com/fwlink/?LinkID=534597</PrivacyLink>
    <PowerShellVersion>1.0</PowerShellVersion>
    <SupportedOSVersion clientSupported="true" serverSupported="true">6.1</SupportedOSVersion>
    <Troubleshooter>
        <Script>
            <Parameters/>
            <ProcessArchitecture>Any</ProcessArchitecture>
            <RequiresElevation>false</RequiresElevation>
            <RequiresInteractivity>true</RequiresInteractivity>
            <FileName>TS_DeviceCenter.ps1</FileName>
            <ExtensionPoint/>
        </Script>
        <ExtensionPoint/>
    </Troubleshooter>
    <Rootcauses>
        <Rootcause>
            <ID>RC_ProblematicPNPDevice</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-7</Name>
                <Description>@diagpackage.dll,-8</Description>
            </DisplayInformation>
            <Resolvers/>
            <Verifier/>
            <ContextParameters>
                <Parameter>
                    <Name>DEVICEID</Name>
                    <DefaultValue/>
                </Parameter>
            </ContextParameters>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_ProblematicPrinter</ID>
            <DisplayInformation>
                <Parameters>
                    <Parameter>
                        <Name>PRINTERNAME</Name>
                        <DefaultValue/>
                    </Parameter>
                </Parameters>
                <Name>@diagpackage.dll,-9</Name>
                <Description>@diagpackage.dll,-10</Description>
            </DisplayInformation>
            <Resolvers/>
            <Verifier/>
            <ContextParameters>
                <Parameter>
                    <Name>PRINTERNAME</Name>
                    <DefaultValue/>
                </Parameter>
            </ContextParameters>
            <ExtensionPoint/>
        </Rootcause>
        <Rootcause>
            <ID>RC_ProblematicUSBDevice</ID>
            <DisplayInformation>
                <Parameters/>
                <Name>@diagpackage.dll,-13</Name>
                <Description>@diagpackage.dll,-14</Description>
            </DisplayInformation>
            <Resolvers/>
            <Verifier/>
            <ContextParameters>
                <Parameter>
                    <Name>USBDEVICEID</Name>
                    <DefaultValue/>
                </Parameter>
            </ContextParameters>
            <ExtensionPoint/>
        </Rootcause>
    </Rootcauses>
    <Interactions>
        <SingleResponseInteractions/>
        <MultipleResponseInteractions/>
        <TextInteractions>
            <TextInteraction>
                <RegularExpression/>
                <ID>IT_DeviceInfo</ID>
                <DisplayInformation>
                    <Parameters/>
                    <Name>@diagpackage.dll,-11</Name>
                    <Description>@diagpackage.dll,-12</Description>
                </DisplayInformation>
                <ContextParameters/>
                <ExtensionPoint>
                    <NoUI/>
                </ExtensionPoint>
            </TextInteraction>
        </TextInteractions>
        <PauseInteractions/>
        <LaunchUIInteractions/>
    </Interactions>
    <ExtensionPoint/>
</dcmPS:DiagnosticPackage>