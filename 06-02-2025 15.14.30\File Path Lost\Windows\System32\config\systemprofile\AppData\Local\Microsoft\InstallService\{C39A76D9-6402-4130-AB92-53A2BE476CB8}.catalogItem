{"catalogItem": {"entryId": "{C39A76D9-6402-4130-AB92-53A2BE476CB8}", "userSID": "S-1-5-21-246032611-354955300-348842393-1002", "userIdentity": "", "productId": "9N5TDP8VCMHS", "packageFamilyName": "Microsoft.WebMediaExtensions_8wekyb3d8bbwe", "mainPackageFamilyName": "", "contentId": "", "categoryId": "b4f3025d-a236-44b0-8d4c-849bed549259", "fulfillmentId": "", "publisherCertificateName": "CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US", "packageIdentityName": "Microsoft.WebMediaExtensions", "platformDependencyXmlBlob": "{\"blob.version\":1688867040526336,\"content.bundledPackages\":[\"Microsoft.WebMediaExtensions_1.1.1295.0_x64__8wekyb3d8bbwe\",\"Microsoft.WebMediaExtensions_1.1.1295.0_x86__8wekyb3d8bbwe\",\"Microsoft.WebMediaExtensions_1.1.1295.0_arm64__8wekyb3d8bbwe\"],\"content.isMain\":false,\"content.packageId\":\"Microsoft.WebMediaExtensions_1.1.1295.0_neutral_~_8wekyb3d8bbwe\",\"content.productId\":\"4bae062b-5361-4e9e-8406-6a086d2e5671\",\"content.targetPlatforms\":[{\"platform.maxVersionTested\":2814751249596416,\"platform.minVersion\":2814750835277824,\"platform.target\":0}],\"content.type\":7,\"policy\":{\"category.first\":\"app\",\"category.second\":\"Utilities & tools\",\"optOut.backupRestore\":false,\"optOut.removeableMedia\":true},\"policy2\":{\"ageRating\":0,\"optOut.DVR\":false,\"thirdPartyAppRatings\":[{\"level\":0,\"systemId\":0}]}}", "packageFullName": "Microsoft.WebMediaExtensions_1.1.1295.0_neutral_~_8wekyb3d8bbwe", "images": [{"ImagePurpose": "Logo", "Uri": "//store-images.s-microsoft.com/image/apps.56008.14027181851903689.03886706-608a-47b1-b14c-dc5e34f84938.28821e51-f2d2-4cb6-b7b1-96f1f88a1713", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 50, "Height": 50}, {"ImagePurpose": "Logo", "Uri": "//store-images.s-microsoft.com/image/apps.39957.14027181851903689.8f05f6b9-b575-44f7-83ab-998f490f2417.4265ece4-600e-426a-a5a0-cc36cdd18f76", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 75, "Height": 75}, {"ImagePurpose": "Logo", "Uri": "//store-images.s-microsoft.com/image/apps.23697.14027181851903689.819c7487-4021-4451-8868-7f68370666e3.61c0edf5-d5ed-4a18-b99e-8c78bc0a4953", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 100, "Height": 100}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.45904.14027181851903689.37a1043e-6104-4b63-b6a2-1bbd484317db.c4e1a280-85d3-4934-a330-c8feee8edd5e", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 150, "Height": 150}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.57350.14027181851903689.785a2e5d-2e42-41cd-b8f9-edab0bdbdb02.32c95ced-cf0a-47e6-ac2a-52d6137b88ce", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 225, "Height": 225}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.34199.14027181851903689.be8e4ea8-f703-45a2-a354-9d178b2b6aa2.0ab62392-ef61-47e8-80c4-606e56028a0d", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 300, "Height": 300}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.52598.14027181851903689.2f7f940f-6ab2-45ea-8586-cd7ce5e4e0c3.390fff0f-3a4c-41e1-b58d-d751192704a9", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 44, "Height": 44}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.47585.14027181851903689.ce9a23d0-34d0-47bc-85f8-ea33a6554e4c.a463487e-90b4-4bb3-a541-140a45319116", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 66, "Height": 66}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.29953.14027181851903689.18ff1616-dac4-44bf-836c-c72f62fcecf5.303b9bd4-9550-4f01-a88e-dd7675d389f5", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 88, "Height": 88}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.59153.14027181851903689.74028a6c-d7ae-420b-b747-d9c16186d623.aa53b7ea-df6b-483a-9fd1-cce3c3a94264", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 310, "Height": 310}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.11542.14027181851903689.80f19d6e-3d84-4426-a146-427a91576a14.33706151-e233-40b3-b73c-54e7e8fa2faf", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 465, "Height": 465}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.59369.14027181851903689.1203e452-8ff5-4961-9ca9-6272603f16c6.8b4555a1-4b4c-4ead-b0a0-38c83302e000", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 620, "Height": 620}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.46146.14027181851903689.6adb0e71-3a67-487d-b26f-f3e2d8501ad6.e45932f2-5e79-468a-ac5f-8bb1c7ad9ae3", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 71, "Height": 71}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.29989.14027181851903689.25eb7043-2890-4637-9d46-6b756fd95f4a.5ecf16e1-89f1-43a1-bd28-1eb0646df0b6", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 107, "Height": 107}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.59551.14027181851903689.0d178f80-9d06-4c30-b002-cd5ac0bbc78e.a8d8c80f-c3ab-4eda-810b-5cb79194a3aa", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 142, "Height": 142}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.22190.14027181851903689.a742836b-be4a-4961-befb-4f46962a1222.51097cff-e425-4716-a2a0-51cdfc020bda", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 310, "Height": 150}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.31937.14027181851903689.6e30303d-62e0-4dab-ad10-7f2ab94ce9f9.45f6f9cb-ad15-4f9e-9e92-e0d0fb820665", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 465, "Height": 225}, {"ImagePurpose": "Tile", "Uri": "//store-images.s-microsoft.com/image/apps.31213.14027181851903689.db10b8e7-51be-4fa7-840d-582b8a6672a5.c0c268fd-623a-4ab6-bd11-4e6fd200eae4", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 620, "Height": 300}, {"ImagePurpose": "Screenshot", "Uri": "//store-images.s-microsoft.com/image/apps.62268.14027181851903689.ddc9ccf7-94d2-42a9-9362-d29f445f4ee8.b84fc79c-5b9c-49f1-a39e-fe1070726a60", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 1501, "Height": 845}, {"ImagePurpose": "Screenshot", "Uri": "//store-images.s-microsoft.com/image/apps.62268.14027181851903689.ddc9ccf7-94d2-42a9-9362-d29f445f4ee8.b84fc79c-5b9c-49f1-a39e-fe1070726a60", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 1501, "Height": 845}, {"ImagePurpose": "Screenshot", "Uri": "//store-images.s-microsoft.com/image/apps.62268.14027181851903689.ddc9ccf7-94d2-42a9-9362-d29f445f4ee8.b84fc79c-5b9c-49f1-a39e-fe1070726a60", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 1501, "Height": 845}, {"ImagePurpose": "Screenshot", "Uri": "//store-images.s-microsoft.com/image/apps.62268.14027181851903689.ddc9ccf7-94d2-42a9-9362-d29f445f4ee8.b84fc79c-5b9c-49f1-a39e-fe1070726a60", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 1501, "Height": 845}, {"ImagePurpose": "Screenshot", "Uri": "//store-images.s-microsoft.com/image/apps.62268.14027181851903689.ddc9ccf7-94d2-42a9-9362-d29f445f4ee8.b84fc79c-5b9c-49f1-a39e-fe1070726a60", "BackgroundColor": "#0078D7", "ForegroundColor": "", "Width": 1501, "Height": 845}], "bundledProducts": [], "packageFormat": "AppxBundle", "productTitle": "Web Media Extensions", "fulfillmentPluginId": "WU", "fulfillmentData": "{\"ProductId\":\"9N5T<PERSON><PERSON>VCMHS\",\"WuBundleId\":\"5319d8ab-5fd7-468e-b80d-9b78f53657b1\",\"WuCategoryId\":\"b4f3025d-a236-44b0-8d4c-849bed549259\",\"PackageFamilyName\":\"Microsoft.WebMediaExtensions_8wekyb3d8bbwe\",\"SkuId\":\"0010\",\"Content\":null,\"PackageFeatures\":null}", "packageRelativeAppIds": ["Microsoft.WebMediaExtensions"], "canFulfill": true, "hrInstallAllowed": 0, "created": "2025-04-09T14:30:47-05:00", "requiresElevation": false, "market": "", "isVisibleInAppList": false, "isExclusivityFailed": false, "isCompanionApp": false, "isPlugin": false, "skuId": "0010", "availabilityId": "9NPDBJXB42WD", "catalogId": "", "workId": ""}}