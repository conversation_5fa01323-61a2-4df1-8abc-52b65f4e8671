{"character-map": "charactermap.json", "ime": "ime.json", "models": [{"path": "fr_CA.lm", "type": "term", "version": "2"}, {"path": "fr_CA_word_c.lm1", "type": "term", "version": "2"}, {"path": "emoji_bg_c.lm2", "tags": ["emoji"], "type": "term", "version": "1"}], "punctuation": "punctuation.json", "tags": ["french", "fr", "CA", "id:fr_CA", "id:fr", "name:<PERSON><PERSON><PERSON> (Canada)"], "timestamp": "2019/03/20 14:19", "version": "120"}