<?xml version="1.0" encoding="utf-8"?>
<R Id="10819" V="1" DC="SM" EN="Office.Outlook.Desktop.ContactCardTimestampInformation" ATT="d807609276744245baf81bf7bc8033f6-2268e374-7766-4976-be44-b6ad5bddc5b6-7813" DCa="PSU" xmlns="">
  <S>
    <R T="1" R="10818" />
    <A T="2" E="TelemetryShutdown" />
    <TI T="3" I="Daily" />
    <F T="4">
      <O T="LE">
        <L>
          <S T="1" F="0" />
        </L>
        <R>
          <V V="500" T="U32" />
        </R>
      </O>
    </F>
    <F T="5">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="500" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="1000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="6">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="1000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="1500" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="7">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="1500" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="2000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="8">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="2000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="4000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="9">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="4000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="6000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="10">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="6000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="8000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="11">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="8000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="10000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="12">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="10000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="20000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="13">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="20000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="30000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="14">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="30000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="60000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="15">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="60000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="1800000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="16">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="1800000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="3600000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="17">
      <O T="AND">
        <L>
          <O T="GT">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="3600000" T="U32" />
            </R>
          </O>
        </L>
        <R>
          <O T="LE">
            <L>
              <S T="1" F="0" />
            </L>
            <R>
              <V V="86400000" T="U32" />
            </R>
          </O>
        </R>
      </O>
    </F>
    <F T="18">
      <O T="GT">
        <L>
          <S T="1" F="0" />
        </L>
        <R>
          <V V="86400000" T="U32" />
        </R>
      </O>
    </F>
  </S>
  <C T="U32" I="0" O="false" N="Opened_0ms_500ms">
    <C>
      <S T="4" />
    </C>
  </C>
  <C T="U32" I="1" O="false" N="Opened_500ms_1000ms">
    <C>
      <S T="5" />
    </C>
  </C>
  <C T="U32" I="2" O="false" N="Opened_1000ms_1500ms">
    <C>
      <S T="6" />
    </C>
  </C>
  <C T="U32" I="3" O="false" N="Opened_1500ms_2000ms">
    <C>
      <S T="7" />
    </C>
  </C>
  <C T="U32" I="4" O="false" N="Opened_2s_4s">
    <C>
      <S T="8" />
    </C>
  </C>
  <C T="U32" I="5" O="false" N="Opened_4s_6s">
    <C>
      <S T="9" />
    </C>
  </C>
  <C T="U32" I="6" O="false" N="Opened_6s_8s">
    <C>
      <S T="10" />
    </C>
  </C>
  <C T="U32" I="7" O="false" N="Opened_8s_10s">
    <C>
      <S T="11" />
    </C>
  </C>
  <C T="U32" I="8" O="false" N="Opened_10s_20s">
    <C>
      <S T="12" />
    </C>
  </C>
  <C T="U32" I="9" O="false" N="Opened_20s_30s">
    <C>
      <S T="13" />
    </C>
  </C>
  <C T="U32" I="10" O="false" N="Opened_30s_1m">
    <C>
      <S T="14" />
    </C>
  </C>
  <C T="U32" I="11" O="false" N="Opened_1m_30m">
    <C>
      <S T="15" />
    </C>
  </C>
  <C T="U32" I="12" O="false" N="Opened_30m_60m">
    <C>
      <S T="16" />
    </C>
  </C>
  <C T="U32" I="13" O="false" N="Opened_60m_24h">
    <C>
      <S T="17" />
    </C>
  </C>
  <C T="U32" I="14" O="false" N="Opened_24h_Plus">
    <C>
      <S T="18" />
    </C>
  </C>
  <C T="U64" I="15" O="false" N="CardVersion">
    <S T="1" F="1" />
  </C>
  <T>
    <S T="2" />
    <S T="3" />
  </T>
  <ST>
    <S T="1" />
  </ST>
</R>
